I'll analyze the entire backend structure and frontend API structure to create a comprehensive migration plan from Express to Next.js API routes. Let me start by examining the current backend structure.

Now let me examine some key backend routes and controllers to understand the structure:

Now let me examine the current frontend API structure more thoroughly:

Let me check some key files to understand the database connection and environment setup:

Now let me examine a few key controllers and models to understand the patterns:

Now let me check the current frontend API structure to see what's already been migrated:

Based on my comprehensive analysis of both the Express backend and Next.js frontend structures, I can now provide you with a detailed, phased migration plan to transform the Express backend into Next.js API routes.

# 🚀 **Complete Express to Next.js API Migration Plan**

## **📊 Current Architecture Analysis**

### **Backend Structure (Express):**
- **Routes**: 8 main route files (admin, auth, buyer, notification, payment, profile, supplier, tender)
- **Controllers**: 17 controller files handling business logic
- **Models**: 22 Mongoose models for database schemas
- **Services**: 4 service files (Email, Notification, S3, SMS)
- **Middleware**: 3 middleware files (auth, authorize, activityLogger)
- **Utils**: 3 utility files (logger, token, user)

### **Frontend API Structure (Next.js):**
- **Partial migration**: Some routes already exist as proxy routes
- **Current pattern**: Most routes proxy to Express backend

---

## **🎯 Migration Strategy: 5-Phase Approach**

### **Phase 1: Foundation & Database Setup** ⚡
**Duration: 1-2 days**

#### **1.1 Environment & Dependencies**
```bash
# Add to frontend package.json
npm install mongoose bcryptjs jsonwebtoken nodemailer aws-sdk multer
npm install ioredis bullmq winston express-validator
npm install @types/bcryptjs @types/jsonwebtoken @types/multer
```

#### **1.2 Database Connection**
```typescript
// lib/mongodb.ts - Create database connection utility
import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI!;
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

export async function connectToDatabase() {
  if (cached.conn) return cached.conn;
  
  if (!cached.promise) {
    cached.promise = mongoose.connect(MONGODB_URI).then((mongoose) => {
      return mongoose;
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}
```

#### **1.3 Copy Models (Direct Copy)**
```bash
# These can be copied as-is with minimal changes
cp -r sb_tender_backend/src/models/* sb_tender_frontend/lib/models/
```

**Models to copy directly:**
- All 22 model files (sp_company.js, sp_user.js, etc.)
- Convert `.js` to `.ts` and update import syntax

---

### **Phase 2: Core Services & Utilities** 🔧
**Duration: 2-3 days**

#### **2.1 Copy Services (Direct Copy with Adaptation)**
```bash
# Copy service files
cp -r sb_tender_backend/src/services/* sb_tender_frontend/lib/services/
cp -r sb_tender_backend/src/utils/* sb_tender_frontend/lib/utils/
```

**Services to migrate:**
- **EmailService.js** → `lib/services/emailService.ts`
- **s3Service.js** → `lib/services/s3Service.ts`
- **smsService.js** → `lib/services/smsService.ts`
- **notificationService.js** → `lib/services/notificationService.ts`

#### **2.2 Copy Utilities**
- **logger.js** → `lib/utils/logger.ts`
- **token.js** → `lib/utils/token.ts`
- **user.js** → `lib/utils/user.ts`

#### **2.3 Middleware Adaptation**
```typescript
// lib/middleware/auth.ts
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

export async function authenticateToken(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '');
  if (!token) throw new Error('No token provided');
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    return decoded;
  } catch (error) {
    throw new Error('Invalid token');
  }
}
```

---

### **Phase 3: Authentication & Core APIs** 🔐
**Duration: 3-4 days**

#### **3.1 Authentication Routes**
**Migrate from:** `sb_tender_backend/src/routes/auth.js`
**To:** `app/api/auth/`

```typescript
// app/api/auth/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();
    const { email, password } = await request.json();
    
    const user = await User.findOne({ email });
    if (!user || !await bcrypt.compare(password, user.password)) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }
    
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET!);
    return NextResponse.json({ token, user });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

#### **3.2 Profile Routes**
**Migrate:** `sb_tender_backend/src/routes/profile.js` → `app/api/profile/`

#### **3.3 Company Management**
**Migrate:** Company-related routes from admin.js → `app/api/companies/`

---

### **Phase 4: Business Logic APIs** 💼
**Duration: 5-7 days**

#### **4.1 Admin Routes**
**Migrate:** `sb_tender_backend/src/routes/admin.js` → `app/api/admin/`

**Structure:**
```
app/api/admin/
├── companies/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   └── documents/
│       └── download/route.ts
├── categories/
├── jobs/
├── applications/
├── orders/
├── payments/
└── users/
```

#### **4.2 Supplier Routes**
**Migrate:** `sb_tender_backend/src/routes/supplier.js` → `app/api/supplier/`

#### **4.3 Buyer Routes**
**Migrate:** `sb_tender_backend/src/routes/buyer.js` → `app/api/buyer/`

#### **4.4 Controller Logic Integration**
```typescript
// Example: app/api/admin/companies/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Company from '@/lib/models/sp_company';
import { authenticateToken } from '@/lib/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    await authenticateToken(request);
    await connectToDatabase();
    
    const companies = await Company.find().populate('categories');
    return NextResponse.json(companies);
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 401 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await authenticateToken(request);
    await connectToDatabase();
    
    const formData = await request.formData();
    // Handle file uploads and company creation
    // Integrate logic from companyController.js
    
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
```

---

### **Phase 5: Advanced Features & Cleanup** 🎨
**Duration: 3-4 days**

#### **5.1 Payment Integration**
**Migrate:** `sb_tender_backend/src/routes/payment.js` → `app/api/payment/`
- M-Pesa STK Push
- Paystack integration
- Order management

#### **5.2 Notification System**
**Migrate:** `sb_tender_backend/src/routes/notification.js` → `app/api/notifications/`
- Email notifications
- SMS notifications
- In-app notifications

#### **5.3 File Upload & S3 Integration**
```typescript
// lib/middleware/upload.ts
import { NextRequest } from 'next/server';
import { uploadToS3 } from '@/lib/services/s3Service';

export async function handleFileUpload(request: NextRequest, fieldName: string) {
  const formData = await request.formData();
  const file = formData.get(fieldName) as File;
  
  if (file) {
    const buffer = Buffer.from(await file.arrayBuffer());
    const result = await uploadToS3(buffer, 'documents', file.name);
    return result;
  }
  return null;
}
```

#### **5.4 Background Jobs & Cron**
```typescript
// lib/jobs/notificationWorker.ts
import { Queue, Worker } from 'bullmq';
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL!);
const notificationQueue = new Queue('notifications', { connection: redis });

export { notificationQueue };
```

---

## **📁 Final Directory Structure**

```
sb_tender_frontend/
├── app/
│   ├── api/
│   │   ├── auth/
│   │   │   ├── login/route.ts
│   │   │   ├── register/route.ts
│   │   │   └── logout/route.ts
│   │   ├── admin/
│   │   │   ├── companies/
│   │   │   ├── categories/
│   │   │   ├── jobs/
│   │   │   ├── applications/
│   │   │   ├── orders/
│   │   │   ├── payments/
│   │   │   └── users/
│   │   ├── supplier/
│   │   │   ├── [companyId]/
│   │   │   │   ├── applications/
│   │   │   │   ├── orders/
│   │   │   │   ├── metrics/
│   │   │   │   └── notifications/
│   │   │   └── jobs/
│   │   ├── buyer/
│   │   │   ├── [companyId]/
│   │   │   │   ├── jobs/
│   │   │   │   ├── categories/
│   │   │   │   └── applications/
│   │   │   └── setup/
│   │   ├── payment/
│   │   │   ├── checkout/
│   │   │   │   ├── stk/
│   │   │   │   └── webhook/
│   │   │   └── verify/
│   │   ├── notifications/
│   │   │   ├── route.ts
│   │   │   └── public/
│   │   └── profile/
├── lib/
│   ├── models/ (22 model files)
│   ├── services/
│   │   ├── emailService.ts
│   │   ├── s3Service.ts
│   │   ├── smsService.ts
│   │   └── notificationService.ts
│   ├── middleware/
│   │   ├── auth.ts
│   │   ├── authorize.ts
│   │   └── activityLogger.ts
│   ├── utils/
│   │   ├── logger.ts
│   │   ├── token.ts
│   │   └── user.ts
│   ├── jobs/
│   │   └── notificationWorker.ts
│   └── mongodb.ts
```

---

## **🔄 Migration Checklist**

### **Phase 1: Foundation** ✅
- [ ] Install required dependencies
- [ ] Set up database connection
- [ ] Copy and convert all models
- [ ] Set up environment variables

### **Phase 2: Services** ✅
- [ ] Migrate EmailService
- [ ] Migrate S3Service
- [ ] Migrate SMSService
- [ ] Migrate NotificationService
- [ ] Convert utilities (logger, token, user)

### **Phase 3: Core APIs** ✅
- [ ] Authentication routes (login, register, logout)
- [ ] Profile management
- [ ] Company management
- [ ] User management

### **Phase 4: Business Logic** ✅
- [ ] Admin routes (companies, categories, jobs, applications)
- [ ] Supplier routes (applications, orders, metrics)
- [ ] Buyer routes (jobs, categories, applications)
- [ ] File upload handling

### **Phase 5: Advanced Features** ✅
- [ ] Payment integration (M-Pesa, Paystack)
- [ ] Notification system
- [ ] Background jobs
- [ ] Activity logging

### **Final Steps** ✅
- [ ] Update all frontend API calls to use new routes
- [ ] Remove Express backend dependencies
- [ ] Update deployment configuration
- [ ] Test all functionality
- [ ] Performance optimization

---

## **⚠️ Important Considerations**

### **1. Environment Variables**
```bash
# .env.local (Frontend)
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-secret
AWS_KEY=your-aws-key
AWS_SECRET=your-aws-secret
REDIS_URL=your-redis-url
# ... all other backend env vars
```

### **2. File Upload Handling**
- Use Next.js built-in FormData handling
- Integrate with existing S3Service
- Maintain multer-like functionality

### **3. Middleware Adaptation**
- Convert Express middleware to Next.js compatible functions
- Maintain authentication and authorization logic
- Preserve activity logging functionality

### **4. Database Connections**
- Use connection pooling for MongoDB
- Implement proper connection caching
- Handle connection errors gracefully

### **5. Background Jobs**
- Set up Redis connection for BullMQ
- Migrate notification worker
- Implement cron job functionality

---

## **🎯 Benefits of This Migration**

1. **Simplified Architecture**: Single codebase for frontend and backend
2. **Better Performance**: Reduced network latency, better caching
3. **Easier Deployment**: Single deployment target
4. **Shared Code**: Reuse types, utilities, and validation logic
5. **Better Developer Experience**: Unified development environment
6. **Cost Reduction**: Single hosting solution

This migration plan ensures zero functionality loss while providing a more maintainable and efficient architecture. Each phase can be completed independently, allowing for gradual migration and testing.
