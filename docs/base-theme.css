/* ===== TENDERASILI THEME SYSTEM ===== */

/* Root Variables for Theme Colors */
:root {
  /* Light Theme Colors - Gold and Deep Red */
  --bg-primary-light: #FFF8E1; /* Light Gold */
  --bg-secondary-light: #B71C1C; /* Dark Red */
  --bg-tertiary-light: #FFF8E1; /* Light Gold */
  --bg-card-light: rgba(255, 248, 225, 0.8); /* Light Gold with Transparency */
  --border-light: #FFECB3; /* Light Gold Border */
  --text-primary-light: #B71C1C; /* Dark Red Primary Text */
  --text-secondary-light: #D32F2F; /* Medium Dark Red Secondary Text */
  --text-muted-light: #F44336; /* Lighter Red Muted Text */

  /* Dark Theme Colors - Gold and Deep Red */
  --bg-primary-dark: #330000; /* Very Dark Red */
  --bg-secondary-dark: #FFD700; /* Bright Gold */
  --bg-tertiary-dark: #330000; /* Very Dark Red */
  --bg-card-dark: rgba(51, 0, 0, 0.4); /* Very Dark Red with Transparency */
  --border-dark: rgba(255, 215, 0, 0.1); /* Bright Gold Border with Transparency */
  --text-primary-dark: #FFD700; /* Bright Gold Primary Text */
  --text-secondary-dark: #FBC02D; /* Medium Gold Secondary Text */
  --text-muted-dark: #FFEB3B; /* Lighter Gold Muted Text */

  /* Brand Colors - Based on the provided image */
  --tenderasili-red-400: #E53935; /* A shade lighter than the main red */
  --tenderasili-red-500: #B71C1C; /* The deep red from the logo */
  --tenderasili-red-600: #A1171B; /* A shade darker than the main red */
  --tenderasili-gold-400: #FFD700; /* Bright Gold */
  --tenderasili-gold-500: #FFC107; /* A slightly less bright gold */
  --tenderasili-gold-600: #FFB300; /* A darker gold */
}

/* ===== THEME BACKGROUNDS ===== */

/* Light Theme Background - Red and Gold Gradient */
.theme-bg-light {
  background: linear-gradient(to bottom right,
    var(--bg-primary-light),
    var(--bg-secondary-light),
    var(--bg-tertiary-light)
  );
}

/* Dark Theme Background - Gold and Dark Red Gradient */
.theme-bg-dark {
  background: linear-gradient(to bottom right,
    var(--bg-primary-dark),
    var(--bg-secondary-dark),
    var(--bg-tertiary-dark)
  );
}

/* ===== ANIMATED BACKGROUND ELEMENTS - Red and Gold Orbs */

.theme-bg-orb-1-light {
  background: linear-gradient(to bottom right,
    rgba(255, 193, 7, 0.4), /* Gold */
    rgba(183, 28, 28, 0.4)  /* Red */
  );
}

.theme-bg-orb-1-dark {
  background: linear-gradient(to bottom right,
    rgba(255, 204, 0, 0.25), /* Slightly darker Gold */
    rgba(229, 57, 53, 0.25) /* Slightly lighter Red */
  );
}

.theme-bg-orb-2-light {
  background: linear-gradient(to bottom right,
    rgba(255, 235, 59, 0.3),  /* Yellowish Gold */
    rgba(198, 40, 40, 0.3)   /* Another Red Shade */
  );
}

.theme-bg-orb-2-dark {
  background: linear-gradient(to bottom right,
    rgba(255, 215, 0, 0.2),   /* Brighter Gold */
    rgba(244, 67, 54, 0.2)    /* Another Red Shade */
  );
}

.theme-bg-orb-3-light {
  background: linear-gradient(to bottom right,
    rgba(183, 28, 28, 0.3),  /* Red */
    rgba(255, 193, 7, 0.3)   /* Gold */
  );
}

.theme-bg-orb-3-dark {
  background: linear-gradient(to bottom right,
    rgba(229, 57, 53, 0.2),  /* Lighter Red */
    rgba(255, 204, 0, 0.2)  /* Slightly darker Gold */
  );
}

/* ===== CARD STYLES - Gold and Red Tones with Blur */

.theme-card-light {
  background: var(--bg-card-light);
  border: 1px solid var(--border-light);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(12px);
}

.theme-card-dark {
  background: var(--bg-card-dark);
  border: 1px solid var(--border-dark);
  backdrop-filter: blur(12px);
}

.theme-card-hover-light:hover {
  border-color: rgba(198, 40, 40, 0.5); /* Red hover effect */
}

.theme-card-hover-dark:hover {
  border-color: rgba(255, 204, 0, 0.3); /* Gold hover effect */
}

/* ===== TEXT STYLES - Red and Gold Text Colors */

.theme-text-primary-light {
  color: var(--text-primary-light);
}

.theme-text-primary-dark {
  color: var(--text-primary-dark);
}

.theme-text-secondary-light {
  color: var(--text-secondary-light);
}

.theme-text-secondary-dark {
  color: var(--text-secondary-dark);
}

.theme-text-muted-light {
  color: var(--text-muted-light);
}

.theme-text-muted-dark {
  color: var(--text-muted-dark);
}

/* ===== GRADIENT TEXT - Red to Gold */

.theme-gradient-text {
  background: linear-gradient(to right, var(--tenderasili-red-400), var(--tenderasili-gold-400));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.theme-gradient-text-light {
  background: linear-gradient(to right, var(--tenderasili-red-600), var(--tenderasili-gold-600));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ===== HEADER STYLES - Translucent Gold/Dark Red */

.theme-header-light {
  background: rgba(255, 248, 225, 0.8); /* Light Gold */
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(255, 224, 130, 0.2); /* Light Gold Border */
}

.theme-header-dark {
  background: rgba(51, 0, 0, 0.2); /* Very Dark Red */
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(255, 215, 0, 0.1); /* Bright Gold Border */
}

/* ===== NAVIGATION STYLES - Red/Gold Links */

.theme-nav-link-light {
  color: var(--text-secondary-light);
  transition: color 0.3s ease;
}

.theme-nav-link-light:hover {
  color: var(--tenderasili-red-600);
}

.theme-nav-link-dark {
  color: var(--text-secondary-dark);
  transition: color 0.3s ease;
}

.theme-nav-link-dark:hover {
  color: var(--tenderasili-gold-400);
}

.theme-nav-link-active {
  color: var(--tenderasili-red-400);
}

.theme-nav-underline {
  position: absolute;
  bottom: -4px;
  left: 0;
  height: 2px;
  width: 0;
  background: linear-gradient(to right, var(--tenderasili-red-400), var(--tenderasili-gold-400));
  transition: width 0.3s ease;
}

.theme-nav-underline-active {
  width: 100%;
}

/* ===== BUTTON STYLES - Gold/Red Gradients and Borders */

.theme-btn-primary {
  background: linear-gradient(to right, var(--tenderasili-gold-500), var(--tenderasili-red-600));
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.theme-btn-primary:hover {
  background: linear-gradient(to right, var(--tenderasili-gold-600), var(--tenderasili-red-500));
  box-shadow: 0 10px 25px -5px rgba(183, 28, 28, 0.25); /* Red shadow */
  transform: scale(1.05);
}

.theme-btn-secondary-light {
  border: 2px solid var(--text-secondary-light);
  color: var(--text-primary-light);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.theme-btn-secondary-light:hover {
  border-color: var(--tenderasili-red-500);
  background: rgba(183, 28, 28, 0.05); /* Light red background on hover */
}

.theme-btn-secondary-dark {
  border: 2px solid rgba(255, 215, 0, 0.2); /* Gold border */
  color: var(--text-primary-dark);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.theme-btn-secondary-dark:hover {
  border-color: rgba(255, 204, 0, 0.5); /* Brighter gold hover border */
  background: rgba(255, 215, 0, 0.05); /* Light gold background on hover */
}

/* ===== THEME SWITCHER - Gold/Dark Red Accents */

.theme-switcher-light {
  background: rgba(255, 248, 225, 0.8); /* Light Gold */
}

.theme-switcher-dark {
  background: rgba(51, 0, 0, 0.5); /* Very Dark Red */
}

.theme-switcher-btn-active-light {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(183, 28, 28, 0.1); /* Red shadow */
}

.theme-switcher-btn-active-dark {
  background: rgba(255, 215, 0, 0.1); /* Gold background */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* ===== FOOTER STYLES - Gold/Dark Red Borders and Backgrounds */

.theme-footer-light {
  border-top: 1px solid var(--border-light);
  background: rgba(255, 248, 225, 0.8); /* Light Gold */
  backdrop-filter: blur(12px);
}

.theme-footer-dark {
  border-top: 1px solid var(--border-dark);
  background: rgba(51, 0, 0, 0.4); /* Very Dark Red */
  backdrop-filter: blur(12px);
}

/* ===== UTILITY CLASSES - Consistent Transitions and Shadows */

.theme-transition {
  transition: all 0.5s ease;
}

.theme-hover-scale:hover {
  transform: scale(1.05);
}

.theme-rounded-card {
  border-radius: 24px;
}

.theme-rounded-button {
  border-radius: 12px;
}

.theme-shadow-card {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.theme-shadow-hover:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== ANIMATIONS - Consistent Pulse Animation */

@keyframes theme-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.theme-animate-pulse {
  animation: theme-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.theme-animate-pulse-delay-1 {
  animation: theme-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 1s;
}

.theme-animate-pulse-delay-2 {
  animation: theme-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 2s;
}

/* ===== RESPONSIVE DESIGN - Consistent Adjustments */

@media (max-width: 768px) {
  .theme-btn-primary,
  .theme-btn-secondary-light,
  .theme-btn-secondary-dark {
    padding: 10px 20px;
    font-size: 14px;
  }

  .theme-rounded-card {
    border-radius: 16px;
  }
}

/* ===== ACCESSIBILITY - Consistent Reduced Motion Preference */

@media (prefers-reduced-motion: reduce) {
  .theme-transition,
  .theme-animate-pulse,
  .theme-animate-pulse-delay-1,
  .theme-animate-pulse-delay-2,
  .theme-nav-underline {
    animation: none;
    transition: none;
  }
}

/* ===== FOCUS STYLES - Red Focus Outline */

.theme-focus:focus {
  outline: 2px solid var(--tenderasili-red-500);
  outline-offset: 2px;
}

.theme-focus:focus:not(:focus-visible) {
  outline: none;
}