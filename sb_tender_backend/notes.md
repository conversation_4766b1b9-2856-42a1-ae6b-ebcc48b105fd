
Here are the JSON payloads for SMS and email API requests, formatted for single and bulk notifications, based on the provided Mongoose schema and controller logic.

Key Considerations for the Payloads:

type and channel: These will always be sms for SMS and email for email.
recipient: For SMS, phone is required. For email, email is required.
title and message: These are required for all notifications.
richContent (for Email): This is where you'd put HTML content or specify a template.
attachments (for Email): An array of attachment objects.
companyId and userId: These can be included if the API consumer knows them. If not provided in the payload, the NotificationController will attempt to populate them from req.user.
options (for Bulk): The sendBulkNotifications function accepts an options object, which can include useQueue and delay to apply to all notifications in the bulk request. For single notifications, these are top-level properties.
SMS API Request Payloads
1. Single SMS Notification
Endpoint: POST /api/notifications/send (assuming this is your endpoint for single notifications)

JSON

{
  "type": "sms",
  "channel": "sms",
  "priority": "medium",
  "title": "Your Order Update",
  "message": "Hi <PERSON>, your order #12345 has been shipped and will arrive within 2 business days. Track here: [link]",
  "recipient": {
    "phone": "+************"
  },
  "sender": {
    "name": "Acme Corp"
  },
  "metadata": {
    "category": "order_updates",
    "referenceId": "60d0fe4f3b6c2d001f3b6c2d"
  },
  "useQueue": true,
  "delay": 0
}
2. Bulk SMS Notifications
Endpoint: POST /api/notifications/bulk-send (assuming this is your endpoint for bulk notifications)

JSON

{
  "notifications": [
    {
      "type": "sms",
      "channel": "sms",
      "priority": "high",
      "title": "Account Alert",
      "message": "Dear Jane, a new device logged into your account. If this wasn't you, please secure your account.",
      "recipient": {
        "phone": "+************"
      },
      "sender": {
        "name": "Security Team"
      },
      "metadata": {
        "category": "security_alerts"
      }
    },
    {
      "type": "sms",
      "channel": "sms",
      "priority": "low",
      "title": "Promotional Offer",
      "message": "Flash Sale! Get 20% off all items for the next 24 hours. Use code FLASH20 at checkout.",
      "recipient": {
        "phone": "+************"
      },
      "sender": {
        "name": "ShopX"
      },
      "metadata": {
        "category": "marketing",
        "tags": ["promo", "sale"]
      }
    },
    {
      "type": "sms",
      "channel": "sms",
      "priority": "medium",
      "title": "Appointment Reminder",
      "message": "Reminder: Your appointment with Dr. Smith is tomorrow, June 21st, at 10:00 AM.",
      "recipient": {
        "phone": "+************",
        "userId": "60d0fe4f3b6c2d001f3b6c2a"
      },
      "sender": {
        "name": "Clinic Services"
      },
      "metadata": {
        "category": "appointments",
        "referenceId": "60d0fe4f3b6c2d001f3b6c2b"
      }
    }
  ],
  "options": {
    "useQueue": true,
    "delay": 1000
  }
}
Email API Request Payloads
1. Single Email Notification
Endpoint: POST /api/notifications/send

JSON

{
  "type": "email",
  "channel": "email",
  "priority": "high",
  "title": "Welcome to Our Service!",
  "message": "Thank you for registering with us. We're excited to have you on board!",
  "richContent": {
    "html": "<p>Thank you for registering with us. We're excited to have you on board! Click <a href=\"https://your-service.com/welcome\">here</a> to get started.</p>",
    "template": "welcome-email",
    "templateData": {
      "userName": "New User",
      "activationLink": "https://your-service.com/activate/xyz123"
    }
  },
  "recipient": {
    "email": "<EMAIL>",
    "userId": "60d0fe4f3b6c2d001f3b6c2f"
  },
  "sender": {
    "name": "Your App Support",
    "email": "<EMAIL>"
  },
  "attachments": [
    {
      "filename": "guide.pdf",
      "path": "/path/to/server/files/guide.pdf",
      "contentType": "application/pdf"
    }
  ],
  "metadata": {
    "category": "onboarding",
    "tags": ["welcome", "new_user"]
  },
  "useQueue": true,
  "delay": 0
}
2. Bulk Email Notifications
Endpoint: POST /api/notifications/bulk-send

JSON

{
  "notifications": [
    {
      "type": "email",
      "channel": "email",
      "priority": "medium",
      "title": "Monthly Newsletter - June 2025",
      "message": "Check out our latest news and updates!",
      "richContent": {
        "html": "<h3>Our Latest News</h3><p>Read about our new features and upcoming events in this month's newsletter.</p>",
        "template": "newsletter",
        "templateData": {
          "month": "June 2025",
          "articles": [
            { "title": "New Feature A", "link": "linkA" },
            { "title": "Event B", "link": "linkB" }
          ]
        }
      },
      "recipient": {
        "email": "<EMAIL>"
      },
      "sender": {
        "name": "Info Team",
        "email": "<EMAIL>"
      },
      "metadata": {
        "category": "newsletter"
      }
    },
    {
      "type": "email",
      "channel": "email",
      "priority": "low",
      "title": "Product Update: Version 2.0 Released!",
      "message": "We are excited to announce the release of Version 2.0 with many new improvements.",
      "richContent": {
        "html": "<p>Discover the exciting new features in Version 2.0!</p><p>Check out the details <a href=\"https://your-product.com/v2-release\">here</a>.</p>"
      },
      "recipient": {
        "email": "<EMAIL>",
        "userId": "60d0fe4f3b6c2d001f3b6c2e"
      },
      "sender": {
        "name": "Product Team",
        "email": "<EMAIL>"
      },
      "metadata": {
        "category": "product_updates"
      }
    },
    {
      "type": "email",
      "channel": "email",
      "priority": "urgent",
      "title": "Critical Security Notice",
      "message": "Please review your account security settings immediately.",
      "richContent": {
        "html": "<p style=\"color: red;\"><strong>Important:</strong> We have detected unusual activity on your account. Please log in and review your security settings.</p>"
      },
      "recipient": {
        "email": "<EMAIL>"
      },
      "sender": {
        "name": "Security Alerts",
        "email": "<EMAIL>"
      },
      "metadata": {
        "category": "security",
        "tags": ["critical", "alert"]
      }
    }
  ],
  "options": {
    "useQueue": true,
    "delay": 5000
  }
}





### sample notifications json payloads
{
  "sms_api_requests": {
    "single_sms": {
      "method": "POST",
      "endpoint": "/api/notifications/send",
      "payload": {
        "type": "sms",
        "channel": "sms",
        "priority": "medium",
        "title": "Account Alert",
        "message": "Your account has been successfully updated. If this wasn't you, please contact support immediately.",
        "recipient": {
          "phone": "+**********",
          "userId": "60d5ecb54b24a**********a"
        },
        "sender": {
          "name": "MyApp Support",
          "userId": "60d5ecb54b24a**********b"
        },
        "metadata": {
          "category": "security",
          "tags": ["account", "security", "update"],
          "reference": "ACC_UPDATE_001",
          "referenceId": "60d5ecb54b24a**********c",
          "customData": {
            "actionType": "account_update",
            "ipAddress": "***********"
          }
        },
        "delivery": {
          "maxAttempts": 3,
          "scheduledAt": "2025-06-20T10:30:00Z"
        },
        "settings": {
          "retryOnFailure": true,
          "expiresAt": "2025-06-21T10:30:00Z",
          "silent": false
        },
        "companyId": "60d5ecb54b24a**********d",
        "userId": "60d5ecb54b24a**********a",
        "useQueue": false,
        "delay": 0
      }
    },
    "bulk_sms": {
      "method": "POST",
      "endpoint": "/api/notifications/bulk",
      "payload": {
        "notifications": [
          {
            "type": "sms",
            "channel": "sms",
            "priority": "high",
            "title": "Promotional Offer",
            "message": "Hi John! Special 20% discount on your next purchase. Use code SAVE20. Valid until midnight. Shop now!",
            "recipient": {
              "phone": "+**********",
              "userId": "60d5ecb54b24a**********a"
            },
            "metadata": {
              "category": "marketing",
              "tags": ["promotion", "discount"],
              "reference": "PROMO_CAMPAIGN_001"
            }
          },
          {
            "type": "sms",
            "channel": "sms",
            "priority": "high",
            "title": "Promotional Offer",
            "message": "Hi Sarah! Special 20% discount on your next purchase. Use code SAVE20. Valid until midnight. Shop now!",
            "recipient": {
              "phone": "+**********",
              "userId": "60d5ecb54b24a**********b"
            },
            "metadata": {
              "category": "marketing",
              "tags": ["promotion", "discount"],
              "reference": "PROMO_CAMPAIGN_001"
            }
          },
          {
            "type": "sms",
            "channel": "sms",
            "priority": "urgent",
            "title": "Security Alert",
            "message": "URGENT: Suspicious login detected on your account from new device. If this wasn't you, secure your account immediately.",
            "recipient": {
              "phone": "+**********",
              "userId": "60d5ecb54b24a**********c"
            },
            "metadata": {
              "category": "security",
              "tags": ["security", "alert", "urgent"],
              "reference": "SEC_ALERT_001"
            }
          }
        ],
        "options": {
          "useQueue": true,
          "batchSize": 100,
          "delayBetweenBatches": 1000,
          "continueOnError": true
        }
      }
    }
  },
  "email_api_requests": {
    "single_email": {
      "method": "POST",
      "endpoint": "/api/notifications/send",
      "payload": {
        "type": "email",
        "channel": "email",
        "priority": "medium",
        "title": "Welcome to Our Platform",
        "message": "Thank you for joining our platform. We're excited to have you on board!",
        "richContent": {
          "html": "<h2>Welcome to Our Platform!</h2><p>Thank you for joining us. Here's what you can do next:</p><ul><li>Complete your profile</li><li>Explore our features</li><li>Connect with others</li></ul>",
          "template": "welcome_email",
          "templateData": {
            "userName": "John Doe",
            "platformName": "MyAwesomeApp",
            "activationLink": "https://myapp.com/activate?token=abc123",
            "supportEmail": "<EMAIL>"
          }
        },
        "recipient": {
          "email": "<EMAIL>",
          "userId": "60d5ecb54b24a**********a"
        },
        "sender": {
          "name": "MyApp Team",
          "email": "<EMAIL>",
          "userId": "60d5ecb54b24a**********b"
        },
        "attachments": [
          {
            "filename": "welcome_guide.pdf",
            "path": "/uploads/attachments/welcome_guide.pdf",
            "contentType": "application/pdf",
            "size": 1024576
          }
        ],
        "metadata": {
          "category": "onboarding",
          "tags": ["welcome", "new_user", "onboarding"],
          "reference": "WELCOME_001",
          "referenceId": "60d5ecb54b24a**********c",
          "customData": {
            "registrationSource": "web",
            "userTier": "free"
          }
        },
        "delivery": {
          "maxAttempts": 3,
          "scheduledAt": "2025-06-20T09:00:00Z"
        },
        "settings": {
          "retryOnFailure": true,
          "expiresAt": "2025-06-27T09:00:00Z",
          "silent": false
        },
        "companyId": "60d5ecb54b24a**********d",
        "userId": "60d5ecb54b24a**********a",
        "useQueue": true,
        "delay": 300000
      }
    },
    "bulk_email": {
      "method": "POST",
      "endpoint": "/api/notifications/bulk",
      "payload": {
        "notifications": [
          {
            "type": "email",
            "channel": "email",
            "priority": "low",
            "title": "Monthly Newsletter - June 2025",
            "message": "Check out our latest updates and features in this month's newsletter.",
            "richContent": {
              "html": "<h1>Monthly Newsletter</h1><p>Dear {{userName}},</p><p>Here are this month's highlights...</p>",
              "template": "monthly_newsletter",
              "templateData": {
                "userName": "John Doe",
                "month": "June",
                "year": "2025",
                "unsubscribeLink": "https://myapp.com/unsubscribe?token=xyz789"
              }
            },
            "recipient": {
              "email": "<EMAIL>",
              "userId": "60d5ecb54b24a**********a"
            },
            "metadata": {
              "category": "newsletter",
              "tags": ["newsletter", "monthly", "updates"]
            }
          },
          {
            "type": "email",
            "channel": "email",
            "priority": "low",
            "title": "Monthly Newsletter - June 2025",
            "message": "Check out our latest updates and features in this month's newsletter.",
            "richContent": {
              "html": "<h1>Monthly Newsletter</h1><p>Dear {{userName}},</p><p>Here are this month's highlights...</p>",
              "template": "monthly_newsletter",
              "templateData": {
                "userName": "Sarah Smith",
                "month": "June",
                "year": "2025",
                "unsubscribeLink": "https://myapp.com/unsubscribe?token=abc456"
              }
            },
            "recipient": {
              "email": "<EMAIL>",
              "userId": "60d5ecb54b24a**********b"
            },
            "metadata": {
              "category": "newsletter",
              "tags": ["newsletter", "monthly", "updates"]
            }
          },
          {
            "type": "email",
            "channel": "email",
            "priority": "high",
            "title": "Important Account Security Update",
            "message": "We've updated our security policies. Please review the changes.",
            "richContent": {
              "html": "<h2>Security Policy Update</h2><p>Dear {{userName}},</p><p>We've made important updates to our security policies...</p>",
              "template": "security_update",
              "templateData": {
                "userName": "Mike Johnson",
                "updateDate": "2025-06-20",
                "policyLink": "https://myapp.com/security-policy"
              }
            },
            "recipient": {
              "email": "<EMAIL>",
              "userId": "60d5ecb54b24a**********c"
            },
            "metadata": {
              "category": "security",
              "tags": ["security", "policy", "important"]
            },
            "priority": "high"
          }
        ],
        "options": {
          "useQueue": true,
          "batchSize": 50,
          "delayBetweenBatches": 2000,
          "continueOnError": true,
          "trackOpens": true,
          "trackClicks": true
        }
      }
    }
  },
  "response_examples": {
    "single_notification_success": {
      "success": true,
      "message": "Notification sent successfully",
      "data": {
        "_id": "60d5ecb54b24a**********f",
        "type": "sms",
        "status": "pending",
        "title": "Account Alert",
        "message": "Your account has been successfully updated...",
        "recipient": {
          "phone": "+**********"
        },
        "createdAt": "2025-06-20T10:30:00Z",
        "delivery": {
          "scheduledAt": "2025-06-20T10:30:00Z",
          "attempts": 0,
          "maxAttempts": 3
        }
      }
    },
    "bulk_notification_success": {
      "success": true,
      "message": "Bulk notifications processed: 2 succeeded, 1 failed",
      "data": {
        "results": [
          {
            "success": true,
            "notificationId": "60d5ecb54b24a**********g",
            "status": "pending"
          },
          {
            "success": true,
            "notificationId": "60d5ecb54b24a**********h",
            "status": "pending"
          },
          {
            "success": false,
            "error": "Invalid phone number format",
            "originalData": {}
          }
        ],
        "summary": {
          "total": 3,
          "successful": 2,
          "failed": 1
        }
      }
    },
    "validation_error": {
      "success": false,
      "message": "Validation failed",
      "errors": [
        {
          "field": "recipient.email",
          "message": "Valid email address is required"
        },
        {
          "field": "message",
          "message": "Message cannot be empty"
        }
      ]
    }
  }
}




Deployment
---------
development: https://sb-tender-backend-git-development-weza-projects.vercel.app
staging: sb-tender-backend-git-staging-weza-projects.vercel.app
production: https://sb-tender-backend.vercel.app/


Development
-----------
Frontend: vercel dev --debug
Backend: vercl dev




commands
-------*
docker-compose build frontend --no-cache
docker-compose run --rm frontend /bin/sh

*MEMO: Buyer/Supplier portal*
The following is the story we are looking forward to demonstrate!

In lay mans terms, think of the TenderAsili supplier buyer portal as an online examination system where the teacher or admin creeates an exam(buyer Job) that has different subjects(buyer categories). The teacher adds questions(criteria/templates) to each subject. then makes it available for students(suppliers) to complete(supplier registration application). They mark (evaluation of applications) the exam subjects and give results to parents(system nottifications). The following is the exact process we are looking forward to demonstrate.

1-Tendersili discusses and creates partnership witth a potential Buyer eg. Kinari Hospital
2-A contract is drawn and Tenderasili asks for statutory documents from Kinari
3-TenderAsili discuss with the buyer their required Categories and criteria for evaluating each (works, constructions, medical supplies etc and add these to an excel file)
4-A Tendersili employee Admin User logs into TenderAsili Admin portal.
5-The Tendersili employee creates a new Buyer Kinari in the platform.
6-The Tendersili employee adds the contract & Kinari statutory documents in 2 above.
7-The Tendersili employee adds the finalizd categories and criteria in 3 above to create specific KINARI CATEGORIES & CRITERIA to be used for any future sourcing jobs.
8-The Tendersili employee finetunes these categories MANUALLY to ensure  they fit the exact specific criteria specified by the buyer.
9-The TenderAsili employee downloads the created categories & criteria excel and shares it with Kinari for approval.
10-Kinari is now ready to list Jobs for its categories. Kinari now has KINARI CATEGORIES & TEMPLATES/CRITERIA
11. Kinari is ready and says List supplier registration Jobs for the following 16 categories and supplier prequalifications for these other 20 categories. A contract is signed to start this job listing (contained agreed charges to suppliers).
12. Tendersili employee logs in and goes to Buyers, selects Kinari, selects create a new JOB.
13. Tenderasili employee selects the specified categories from the already existing KINARI CATEGORIES & TEMPLATES/CRITERIA (created in no. 10 above), uploads contract in no. 11 above and clicks Create JOB!
14. Now a JOB with several categories with prices are created and ready for another TenderAsili admin to approve.
15. Suppliers can now see the Kinari Job and can view all categories.

16. We shall continue with the MEMO for supplier side of things...






	
AQ_2025_27_000: 	
	
Declaration
Company Information-Contact
Company Information-bank
Company Information-Contact
Registration and Statutory Requirements	N/A
Director/Shareholder Information	Director 1
Director/Shareholder Information	Shareholders 1
Business Integrity, Risk and Litigation- RISK	
Business Integrity- Risk
Sustainable Procurement (ESG)- ESG	Environmental
Sustainable Procurement (ESG)- ESG	Social
Sustainable Procurement (ESG)- ESG	Governance and Continuity
Technical Specifications- TECH	N/A
Service Level Requirements- SLA	N/A
Human Resource- HR	N/A
Business Experience- EXP	N/A
Trade Reference	Ref 1
Trade Reference	Ref 2
Trade Reference	Ref 3
Financial Performance- FIN	General
Submission	N/A




	
AQ_2025_27_000: 	
	
Declaration
Company-Contact
Company-bank
Company Information-Contact
Registration and Statutory Requirements	N/A
Director/Shareholder Information	Director 1
Director/Shareholder Information	Shareholders 1
Business Integrity, Risk and Litigation- RISK	
Business Integrity- Risk
Sustainable Procurement (ESG)- ESG	Environmental
Sustainable Procurement (ESG)- ESG	Social
Sustainable Procurement (ESG)- ESG	Governance and Continuity
Technical Specifications- TECH	N/A
Service Level Requirements- SLA	N/A
Human Resource- HR	N/A
Business Experience- EXP	N/A
Trade Reference	Ref 1
Trade Reference	Ref 2
Trade Reference	Ref 3
Financial Performance- FIN	General
Submission	N/A


	
AQ_2025_27_000: 	
a job is made up of a form with sections/groups containing fields to be filled during an application.
for ease of creating these fields, editable templates(criteria) are available for all system/major categories
Logic:
each field belongs to a group.
a field can belong to a subgroup.
a title field of the group has isParent set.
if a field has isParent and has subGroup set, its the subgroup's title.
group title field: group + isParent
subgroup title field: group + subgroup + isParent
normal fillable field: group + subgroup(optional)

Declaration
Company-information
Company-bank
Company-Contact
Registration
Director/Shareholder-Director 1
Director/Shareholder-Shareholders 1
Integrity-Risk
Integrity-Litigation
Sustainable-Environmental
Sustainable-Social
Sustainable-Governance
Technical
SLA
HR
EXP
Ref-1
Ref-2
Ref-3
FIN-General
Submission
	
	


Overview
Here's an overview of features in this application:

Styling: The different ways to style your application in Next.js.
Optimizations: How to optimize images, links, and fonts.
Routing: How to create nested layouts and pages using file-system routing.
Data Fetching: How to set up a Postgres database on Vercel, and best practices for fetching and streaming.
Search and Pagination: How to implement search and pagination using URL search params.
Mutating Data: How to mutate data using React Server Actions, and revalidate the Next.js cache.
Error Handling: How to handle general and 404 not found errors.
Form Validation and Accessibility: How to do server-side form validation and tips for improving accessibility.
Authentication: How to add authentication to your application using NextAuth.js and Middleware.
Metadata: How to add metadata and prepare your application for social sharing.


### AUTHENTICATION

To build a scalable authentication system across multiple clients (Next.js web app, React Native app, and Node.js/Express backend), we need to understand the handoff points between the frontend, server-side code, and backend API. Here's a detailed walkthrough:

Authentication Flow Overview
Next.js Frontend:

Handles user interactions (e.g., login, registration, logout).
Sends requests to the Next.js server or directly to the Node.js API.
Stores authentication tokens (e.g., JWT) in cookies or local storage.
Next.js Server:

Acts as a middle layer between the frontend and backend API.
Handles server-side rendering (SSR) and session management.
Validates tokens stored in cookies for SSR-protected pages.
Node.js/Express Backend:

Provides APIs for authentication (e.g., login, registration, token validation).
Issues JWT tokens and manages refresh tokens for session persistence.
React Native App:

Similar to the Next.js frontend but communicates directly with the Node.js API.
Stores tokens securely (e.g., SecureStore or AsyncStorage).
Step-by-Step Walkthrough
1. Register a User
Next.js Frontend: The user submits a registration form. The frontend sends the data to the Next.js server or directly to the Node.js API.

Next.js Server: The server forwards the request to the Node.js API.

Node.js API: The API creates a new user in the database and returns a JWT token.

Code Example:

Next.js Frontend:

register.js+28-1
Next.js Server:

register.js+13-1
Node.js API:

auth.js
2. Login a User
Next.js Frontend: The user submits login credentials. The frontend sends the data to the Next.js server or directly to the Node.js API.

Next.js Server: The server forwards the request to the Node.js API.

Node.js API: The API validates the credentials and returns a JWT token.

Code Example:

Next.js Frontend:

login.js+24-1
Next.js Server:

login.js+13-1
Node.js API:

auth.js
3. Logout a User
Next.js Frontend: Clear the token from cookies or local storage.
Code Example:

logout.js+9-1
4. Maintaining Sessions
Use HTTP-only cookies to store JWT tokens securely.
Validate tokens on the Next.js server for SSR-protected pages.
Example:

dashboard.js+18-1
React Native App
Use SecureStore or AsyncStorage to store tokens.
Communicate directly with the Node.js API for authentication.
Example:

This setup ensures a scalable, secure authentication system across multiple clients.


### AUTHORIZATION







## CODE GEN AUTOMATION
For all designs I ask you to make, have them be beautiful, not cookie cutter. Make webpages that are fully featured and worthy for production.

When using client-side hooks (useState and useEffect) in a component that's being treated as a Server Component by Next.js, always add the "use client" directive at the top of the file.

Do not write code that will trigger this error: "Warning: Extra attributes from the server: %s%s""class,style"

By default, this template supports JSX syntax with Tailwind CSS classes, the shadcn/ui library, React hooks, and Lucide React for icons. Do not install other packages for UI themes, icons, etc unless absolutely necessary or I request them.
If in doubt, ask me.
Do not use any other libraries for UI components, styling, or icons unless I specifically request them.
Use thee examples provided in the code snippets below as a guide for how to structure your components and pages.

1.Rememeber to use es7 imports and exports. remember this is a nextjs app and next api and a backend node api.
we are creating management of category templates for the admin page.



2.update the page admin/categories/templates/page.tsx with: a neat table with all category templates.
ability to search and filter. button to add category templates via modal. add a button to delete category. sample code is provided in number 7 below.
modal to allow editing a category templates.form will submit to the following next js api route:

3.update the next js route: app/api/admin/categories/templates/route.js to allow adding, deleting, updating category templates. 
see sample route code in number 8 below.


4.update the node js route: server/src/routes/admin.js to allow adding, deleting, fetching, updating category templates..
utilize the following imports from categoryTemplateController inside the route file server/src/routes/admin.js
import { createCategoryTemplate, getCategoryTemplates, getCategoryTemplateById, updateCategoryTemplate, deleteCategoryTemplate } from '../controllers/categoryTemplateController.js';

eg.

router.post('/categories', auth, createCategory);
router.get('/categories', auth, getCategories);
router.get('/categories/:id', auth, getCategoryById);
router.put('/categories/:id', auth, updateCategory);
router.delete('/categories/:id', auth, deleteCategory);

5.update server/src/controllers/categoryTemplateController with all required methods.


6.the category_template model is provided below server/src/models/sp_category_template.js


import mongoose from 'mongoose';

const categoryTemplateSchema = new mongoose.Schema({

  name: { 
    type: String, 
    required: true 
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCategory',
    required: true
  },
  fields: [{
    name: {
      type: String,
      required: true
    },
    group: {
      type: String,
      enum: ['company', 'contact', 'bank', 'declaration', 'statutory', 'director', 'risk', 'esg','sla','exp', 'reference', 'fin', 'tech', 'hr', 'submission', 'system', 'compliance', 'attachments', 'outcome', 'contacts'],
      required: true
    },
    isParent: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
    },
    order: {
      type: Number,
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    helpText: {
      type: String,
      required: true
    },
    tooltip: {
      type: String,
      required: true
    },
    isVisible: {
      type: Boolean,
      default: true
    },
    isEditable: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      enum: ['text', 'textarea', 'number', 'date', 'file', 'checkbox', 'radio', 'select'],
      required: true
    },
    label: {
      type: String,
      required: true
    },
    options: [{
      label: String,
      value: String
    }],
    isRequired: {
      type: Boolean,
      default: false
    },
    isScoreable: {
      type: Boolean,
      default: false
    },
    maxScore: {
      type: Number,
      default: 0
    },
    validations: {
      min: Number,
      max: Number,
      pattern: String,
      fileTypes: [String],
      maxFileSize: Number
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SpCategoryTemplate = mongoose.model('SpCategoryTemplate', categoryTemplateSchema);
export default SpCategoryTemplate;

7.Use the following sample code page structure for the app/admin/category/templates/page.tsx  but replace fields with those in sp_category_template model. :
# sample code
'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { ShoppingCart, Plus, Filter } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Input } from '@/app/components/ui/input';
import { Modal } from '@/app/components/ui/modal';
import { Label } from '@/app/components/ui/label';
import { Textarea } from '@/app/components/ui/textarea';
import { Checkbox } from '@/app/components/ui/checkbox';
import { Switch } from '@/app/components/ui/switch';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/app/components/ui/form';
import { useRouter } from 'next/navigation';

const CategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  
  const form = useForm({
    defaultValues: {
      name: '',
      description: '',
      type: '',
      status: 'active'
    }
  });

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (editingCategory) {
      form.reset({
        name: editingCategory.name,
        description: editingCategory.description,
        type: editingCategory.type,
        status: editingCategory.status
      });
    } else {
      form.reset({
        name: '',
        description: '',
        type: '',
        status: 'active'
      });
    }
  }, [editingCategory, form]);

  const token = document.cookie
    .split('; ')
    .find((row) => row.startsWith('token='))
    ?.split('=')[1];


  const fetchCategories = async () => {
    try {
      const { data } = await axios.get('/api/admin/categories', {
        headers: {
          Authorization: `Bearer ${token}`,
        }});
      setCategories(data);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    }
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddCategory = () => {
    setEditingCategory(null);
    setIsModalVisible(true);
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setIsModalVisible(true);
  };

  const handleDeleteCategory = async (id) => {
    try {
      await axios.delete(`/api/admin/categories/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`, // Add the token to the Authorization header
        }
      });
      fetchCategories();
    } catch (error) {
      console.error("Failed to delete category:", error);
    }
  };

  const onSubmit = async (values) => {
    try {
      if (editingCategory) {
        await axios.put(`/api/admin/categories/${editingCategory._id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      } else {
        await axios.post('/api/admin/categories', values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      setIsModalVisible(false);
      fetchCategories();
    } catch (error) {
      console.error("Failed to save category:", error);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <div className="relative w-64">
          <input
            type="text"
            placeholder="Search categories"
            value={search}
            onChange={handleSearch}
            className="w-full p-2 border rounded"
          />
        </div>
        <Button onClick={handleAddCategory}>
          <Plus className="mr-2 h-4 w-4" /> Add Category
        </Button>
      </div>
      
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCategories.map((category) => (
              <TableRow key={category._id}>
                <TableCell>{category.name}</TableCell>
                <TableCell>{category.description}</TableCell>
                <TableCell>{category.type}</TableCell>
                <TableCell>{category.status}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleEditCategory(category)}>
                      Edit
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDeleteCategory(category._id)}>
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>


      <Modal
        open={isModalVisible}
        onOpenChange={setIsModalVisible}
        title={editingCategory ? 'Edit Category' : 'Add Category'}
        size="lg"
      >
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Category name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Category description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="service">Service</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsModalVisible(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </div>
          </form>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoriesPage;


8.sample code structure to use for the api/admin/category/templates/route.js. update it to allow adding, deleting, updating categories.


export const dynamic = 'force-dynamic';

import { NextResponse } from 'next/server';
import axios from 'axios';

const API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const response = await axios.post(`${API_URL}/api/admin/companies`, body);
    return NextResponse.json(response.data);
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating buyer.' }, { status: 401 });
  }
}

export async function GET(request: Request) {
  try {
    console.log('Fetching companies...');
    const response = await axios.get(`${API_URL}/api/admin/companies`);
    return NextResponse.json(response.data);
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error fetching companies.' }, { status: 401 });
  }
}
export async function DELETE(request: Request) {
  try {
    const body = await request.json();
    const response = await axios.delete(`${API_URL}/api/admin/companies/${body.id}`);
    return NextResponse.json(response.data);
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error deleting buyer.' }, { status: 401 });
  }
}
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const response = await axios.put(`${API_URL}/api/admin/companies/${body.id}`, body);
    return NextResponse.json(response.data);
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating buyer.' }, { status: 401 });
  }
}


