import { Worker } from 'bullmq'; // Import Worker from 'bullmq'
import Redis from 'ioredis'; // Import Redis from 'ioredis'
import SpNotification from './models/sp_notification.js'; // Adjust path as needed
import nodemailer from 'nodemailer'; // Needed for email transporter in worker

// Define your email transporter here, or pass it from a main worker setup
const emailTransporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT || 587,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  },
  tls: {
    rejectUnauthorized: false,
  },
});

// Create an ioredis connection instance for the worker
const workerRedisConnection = new Redis(process.env.REDIS_URL, { maxRetriesPerRequest: null });

// Function to send notifications immediately (copied/adapted from NotificationService)
// This should ideally be a separate utility or part of a worker-specific helper.
async function sendImmediately(notification) {
  try {
    let result;
    
    switch (notification.channel) {
      case 'email':
        const mailOptions = {
          from: process.env.SMTP_USER,
          to: notification.recipient,
          subject: notification.subject,
          html: notification.body
        };
        const info = await emailTransporter.sendMail(mailOptions);
        console.log('Email sent by worker:', info.messageId);
        result = info;
        break;
      case 'sms':
        // Implement your SMS sending logic here
        throw new Error('SMS sending not implemented in worker');
      case 'app':
        // Implement your App notification logic here
        throw new Error('App notification sending not implemented in worker');
      case 'push':
        // Implement your Push notification logic here
        throw new Error('Push notification sending not implemented in worker');
      default:
        throw new Error(`Unsupported notification channel: ${notification.channel}`);
    }

    await notification.markAsDelivered();
    return { success: true, notification, result };
  } catch (error) {
    await notification.markAsFailed(error.message);
    throw error; // Re-throw to mark job as failed in BullMQ
  }
}

// Define the processor function that the BullMQ Worker will execute
const processQueuedNotification = async (job) => {
  const { notificationId } = job.data;
  
  try {
    const notification = await SpNotification.findById(notificationId);
    if (!notification) {
      console.warn(`Notification with ID ${notificationId} not found. Skipping.`);
      return { status: 'skipped', reason: 'Notification not found' }; // BullMQ jobs can return data
    }

    if (notification.isExpired) {
      console.warn(`Notification with ID ${notificationId} is expired. Skipping.`);
      return { status: 'skipped', reason: 'Notification expired' };
    }

    notification.processing.processedAt = new Date();
    await notification.save();

    return await sendImmediately(notification); // Call the helper function
  } catch (error) {
    console.error('Queue processing error:', error);
    throw error; // BullMQ expects you to re-throw the error for job to fail
  }
};

// Create a new BullMQ Worker instance
const notificationWorker = new Worker('notification processing', processQueuedNotification, {
  connection: workerRedisConnection, // Pass the ioredis connection
  concurrency: 5, // Process 5 jobs concurrently (adjust as needed)
});

notificationWorker.on('completed', (job) => {
  console.log(`Worker: Job ${job.id} completed successfully!`);
});

notificationWorker.on('failed', (job, err) => {
  console.error(`Worker: Job ${job.id} failed with error:`, err);
});

notificationWorker.on('active', (job) => {
  console.log(`Worker: Job ${job.id} is active.`);
});

notificationWorker.on('error', (err) => {
  // Errors from the worker itself, not from individual job failures
  console.error('Worker error:', err);
});

// Start listening for jobs
console.log('BullMQ Notification Worker started, listening for jobs...');

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await notificationWorker.close();
  await workerRedisConnection.quit();
  console.log('BullMQ Worker shut down.');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await notificationWorker.close();
  await workerRedisConnection.quit();
  console.log('BullMQ Worker shut down.');
  process.exit(0);
});