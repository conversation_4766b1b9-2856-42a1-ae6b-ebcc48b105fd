// api/cron/process-notifications.js

import { Worker } from 'bullmq';
import Redis from 'ioredis';
// Adjust paths as needed based on where your models/utilities are relative to this file
import SpNotification from '../../models/sp_notification.js'; 
import nodemailer from 'nodemailer';

// Main handler function for Vercel Cron Job
export default async function handler(req, res) {
  // Ensure this endpoint is only accessible via GET requests (typical for cron)
  // or add authentication if you want to prevent external manual triggers.
  if (req.method !== 'GET') {
    return res.status(405).send('Method Not Allowed');
  }

  let worker;
  let redisConnection;
  let emailTransporter;

  try {
    console.log('Vercel Cron Job: Starting notification worker...');

    // 1. Initialize Redis Connection for this invocation
    redisConnection = new Redis(process.env.REDIS_URL, { 
        maxRetriesPerRequest: null, // Recommended for BullMQ
        enableOfflineQueue: false // Crucial for short-lived functions, don't queue commands
    });

    // 2. Initialize Email Transporter for this invocation
    emailTransporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    // 3. Define the processor function (adapted for the worker's context)
    const processQueuedNotification = async (job) => {
      const { notificationId } = job.data;
      
      try {
        const notification = await SpNotification.findById(notificationId);
        if (!notification) {
          console.warn(`Notification with ID ${notificationId} not found. Skipping.`);
          return { status: 'skipped', reason: 'Notification not found' };
        }

        if (notification.isExpired) {
          console.warn(`Notification with ID ${notificationId} is expired. Skipping.`);
          return { status: 'skipped', reason: 'Notification expired' };
        }

        notification.processing.processedAt = new Date();
        await notification.save();

        // Use the emailTransporter initialized within this invocation
        return await sendImmediately(notification, emailTransporter); 
      } catch (error) {
        console.error('Queue processing error:', error);
        throw error; 
      }
    };

    // 4. Create a new BullMQ Worker instance for this invocation
    worker = new Worker('notification processing', processQueuedNotification, {
      connection: redisConnection, 
      concurrency: 5, // How many jobs to process in parallel within THIS function invocation
      limiter: {
          max: 10, // Maximum 10 jobs per worker instance. Adjust based on expected load & timeout.
          duration: 30000 // In 30 seconds, don't process more than 10 jobs
      },
      // Ensure the worker doesn't wait indefinitely if no jobs
      skipWaiting: true, // If true, the worker will not wait for jobs to be added to the queue
      drainDelay: 100 // How long to wait for more jobs after processing the last one, before closing (ms)
    });

    // --- Worker Event Listeners (Optional, for logging/monitoring within the function) ---
    worker.on('completed', (job) => {
      console.log(`Job ${job.id} completed. Result: ${JSON.stringify(job.returnvalue)}`);
    });

    worker.on('failed', (job, err) => {
      console.error(`Job ${job.id} failed:`, err);
      // Re-fetch and update notification status if needed (worker context)
      // This logic was in your original onJobFailed, but should be handled by the processor
      // itself or within this scope if you need to log it specifically.
    });

    worker.on('active', (job) => {
      console.log(`Job ${job.id} is active.`);
    });

    worker.on('error', (err) => {
      console.error('Worker-level error:', err);
    });

    // --- Critical: Manage the worker's lifespan within the serverless function ---
    // The worker will connect and start processing automatically.
    // We need to give it a finite amount of time to work, then close it.
    // Vercel function timeout is the ultimate limit.
    const PROCESSING_DURATION_MS = 25 * 1000; // e.g., 25 seconds. Adjust this based on Vercel's max duration (e.g., 60s for hobby, 15m for pro)
                                            // Leave buffer for setup/teardown and network latency.

    console.log(`Worker will process jobs for approximately ${PROCESSING_DURATION_MS / 1000} seconds.`);
    await new Promise(resolve => setTimeout(resolve, PROCESSING_DURATION_MS));

    console.log('Time limit reached for worker. Attempting graceful shutdown...');

    // 5. Graceful shutdown
    await worker.close();
    console.log('Worker closed.');
    await redisConnection.quit();
    console.log('Redis connection quit.');

    res.status(200).json({ message: 'Notification worker executed successfully for a limited duration.' });

  } catch (error) {
    console.error('Vercel Cron Job Error:', error);
    // Ensure connections are closed even on error
    if (worker) await worker.close().catch(e => console.error("Error closing worker on error:", e));
    if (redisConnection) await redisConnection.quit().catch(e => console.error("Error quitting Redis on error:", e));
    res.status(500).json({ error: 'Failed to process notifications.', details: error.message });
  }
}

// --- Helper function (copied from your original worker.js) ---
async function sendImmediately(notification, transporter) {
  try {
    let result;
    
    switch (notification.channel) {
      case 'email':
        const mailOptions = {
          from: process.env.SMTP_USER,
          to: notification.recipient,
          subject: notification.subject,
          html: notification.body
        };
        const info = await transporter.sendMail(mailOptions); // Use passed transporter
        console.log('Email sent by worker:', info.messageId);
        result = info;
        break;
      case 'sms':
        throw new Error('SMS sending not implemented in worker');
      case 'app':
        throw new Error('App notification sending not implemented in worker');
      case 'push':
        throw new Error('Push notification sending not implemented in worker');
      default:
        throw new Error(`Unsupported notification channel: ${notification.channel}`);
    }

    await notification.markAsDelivered();
    return { success: true, notification, result };
  } catch (error) {
    // BullMQ worker expects the error to be re-thrown for job failure handling
    await notification.markAsFailed(error.message); 
    throw error; 
  }
}