import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import dotenv from 'dotenv';
import authRoutes from '../src/routes/auth.js';
import authorize from '../src/middleware/authorize.js';
import supplierRoutes from '../src/routes/supplier.js';
import buyerRoutes from '../src/routes/buyer.js'
import auth from '../src/middleware/auth.js';
import adminRoutes from '../src/routes/admin.js';
import notificationRoutes from '../src/routes/notification.js';
import profileRoutes from '../src/routes/profile.js';
import paymentRoutes from '../src/routes/payment.js';
import session from "express-session";
import passport from "passport";

dotenv.config();

const app = express();

app.use(cors());
app.use(express.json());


var sess = {
  secret: 'keyboard cat',
  cookie: {},
  //store: sessionStore,
  secret: "tender-secret-key",
  resave: false,
  saveUninitialized: false,
}

if (app.get('env') === 'production') {
  app.set('trust proxy', 1) // trust first proxy
  sess.cookie.secure = true // serve secure cookies
}

app.use(session(sess))

// Passport Middleware for web routes
app.use(passport.initialize());
app.use(passport.session());


mongoose
  .connect(process.env.MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    //app.listen(PORT, () => { console.log(`Server running on port ${PORT}`); });
  })
  .catch((error) => {
    console.error('Error connecting to MongoDB:', error);
  });
  app.get("/", (req, res) => res.send("Express on Vercel API"));

app.use('/api', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/profile', profileRoutes);

app.use('/api/payment/checkout', paymentRoutes); //stk/webhook, mobile/:companyId, card

app.use('/api/supplier', supplierRoutes);
app.use('/api/buyer', buyerRoutes);
app.use('/api/notifications', notificationRoutes);



app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

//const PORT = process.env.PORT || 5000;

export default app;