services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./:/app
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=**********************************************************************
      - JWT_SECRET=your_jwt_secret_key_here
      - AWS_KEY=${AWS_KEY}
      - AWS_SECRET=${AWS_SECRET}
      - AWS_REGION=${AWS_REGION}
      - AWS_BUCKET=${AWS_BUCKET}
      - AWS_ENDPOINT=${AWS_ENDPOINT}
      - TWILIO_ACCOUNT_SID=your_twilio_account_sid
      - TWILIO_AUTH_TOKEN=your_twilio_auth_token
      - PORT=3000
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    
  #frontend:
  #  build:
  #    context: .
  #    dockerfile: Dockerfile
  #  ports:
  #    - "3000:3000"
  #  volumes:
  #    - .:/app
  #    - /app/node_modules
  #  depends_on:
  #    - backend
  #  environment:
  #    - NODE_API_URL=${NODE_API_URL}
  #    - NEXT_PUBLIC_API_URL=http://localhost:3000

volumes:
  mongodb_data: