{"name": "tender<PERSON>li-server", "version": "1.0.0", "description": "Tenderasili Backend API", "main": "index.js", "type": "module", "license": "MIT", "author": "<PERSON>", "private": true, "scripts": {"test": "jest"}, "dependencies": {"@radix-ui/react-switch": "^1.2.5", "archiver": "^7.0.1", "aws-sdk": "^2.1467.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bullmq": "^5.54.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^7.5.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-google-oidc": "^0.1.0", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}