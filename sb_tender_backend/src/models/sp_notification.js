import mongoose from 'mongoose';

const SpNotificationSchema = new mongoose.Schema({
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCompany',
    required: false,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    index: true
  },
  type: {
    type: String,
    enum: ['marketing', 'reminders', 'order', 'tender', 'system', 'other'],
    required: true,
    index: true
  },
  channel: {
    type: String,
    enum: ['app', 'email', 'sms', 'push', 'internal'],
    required: true
  },
  bulkId: {
    type: String,
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  }, sendAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'read', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  richContent: {
    html: String,
    template: String,
    templateData: mongoose.Schema.Types.Mixed
  },
  recipient: {
    email: String,
    phone: String,
    deviceToken: String,
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  sender: {
    name: String,
    email: String,
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  attachments: [{
    filename: String,
    path: String,
    contentType: String,
    size: Number
  }],
  metadata: {
    category: String,
    location: String,
    tags: [String],
    reference: String,
    referenceId: mongoose.Schema.Types.ObjectId,
    customData: mongoose.Schema.Types.Mixed,
    tenderInformation: {
      ref: String, // e.g., RS/23/2025-2027
      location: String, // e.g., Nairobi
      category: String, // e.g., SERVICES-Information technology
      description: String,
      website: String,
      link: String, // Full internal link
      organization: String, // PE: Naromoru Water & Sanitation Company
      closes: Date, // Close Date
      published: Date, // Published Date
      ocid: String, // Optional OCID
      tenderNo: String, // Optional Tender No
      procurementMethod: String, // Optional Procurement Method
      submissionMethod: String, // Optional Submission Method
      openingVenue: String, // Optional Opening Venue
      tenderFee: String, // Optional Tender Fee
      closingTime: String, // Optional Closing Time
      portalPublishDate: Date, // Optional Portal Publish Date
      daysToClose: Number, // Optional Days to Close
      financialYear: String, // Optional Financial year
      attachments: [{
        name: String,
        url: String
      }],
    }
  },
  delivery: {
    attempts: {
      type: Number,
      default: 0
    },
    maxAttempts: {
      type: Number,
      default: 3
    },
    scheduledAt: Date,
    sentAt: Date,
    deliveredAt: Date,
    readAt: Date,
    failedAt: Date,
    errorMessage: String,
    providerResponse: mongoose.Schema.Types.Mixed
  },
  processing: {
    isQueued: {
      type: Boolean,
      default: false
    },
    queueId: String,
    jobId: String,
    delay: Number,
    processedAt: Date
  },
  settings: {
    retryOnFailure: {
      type: Boolean,
      default: true
    },
    expiresAt: Date,
    silent: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
SpNotificationSchema.index({ companyId: 1, status: 1 });
SpNotificationSchema.index({ userId: 1, status: 1 });
SpNotificationSchema.index({ 'recipient.email': 1 });
SpNotificationSchema.index({ 'recipient.phone': 1 });
SpNotificationSchema.index({ createdAt: -1 });
SpNotificationSchema.index({ 'delivery.scheduledAt': 1 });
SpNotificationSchema.index({ 'settings.expiresAt': 1 }, { expireAfterSeconds: 0 });

// Virtual for checking if notification is expired
SpNotificationSchema.virtual('isExpired').get(function() {
  return this.settings.expiresAt && this.settings.expiresAt < new Date();
});

// Virtual for checking if notification can be retried
SpNotificationSchema.virtual('canRetry').get(function() {
  return this.status === 'failed' && 
         this.settings.retryOnFailure && 
         this.delivery.attempts < this.delivery.maxAttempts;
});

// Pre-save middleware
SpNotificationSchema.pre('save', function(next) {
  // Set recipient userId if not provided but userId is available
  if (this.userId && !this.recipient.userId) {
    this.recipient.userId = this.userId;
  }
  
  // Auto-set scheduled time if not provided
  if (!this.delivery.scheduledAt) {
    this.delivery.scheduledAt = new Date();
  }
  
  next();
});

// Static methods
SpNotificationSchema.statics.findByUser = function(userId, options = {}) {
  const query = { userId };
  if (options.status) query.status = options.status;
  if (options.type) query.type = options.type;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

SpNotificationSchema.statics.findByCompany = function(companyId, options = {}) {
  const query = { companyId };
  if (options.status) query.status = options.status;
  if (options.type) query.type = options.type;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

SpNotificationSchema.statics.markAsRead = function(notificationIds, userId) {
  const query = { _id: { $in: notificationIds } };
  if (userId) query.userId = userId;
  
  return this.updateMany(query, {
    status: 'read',
    'delivery.readAt': new Date()
  });
};

SpNotificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    userId,
    status: { $in: ['sent', 'delivered'] }
  });
};

SpNotificationSchema.statics.getAllUnreadCount = function() {
  return this.countDocuments({
    status: { $in: ['sent', 'delivered'] }
  });
};


// Instance methods
SpNotificationSchema.methods.markAsSent = function() {
  this.status = 'sent';
  this.delivery.sentAt = new Date();
  return this.save();
};

SpNotificationSchema.methods.markAsDelivered = function() {
  this.status = 'delivered';
  this.delivery.deliveredAt = new Date();
  return this.save();
};

SpNotificationSchema.methods.markAsRead = function() {
  this.status = 'read';
  this.delivery.readAt = new Date();
  return this.save();
};

SpNotificationSchema.methods.markAsFailed = function(errorMessage) {
  this.status = 'failed';
  this.delivery.failedAt = new Date();
  this.delivery.errorMessage = errorMessage;
  this.delivery.attempts += 1;
  return this.save();
};

const SpNotification = mongoose.model('SpNotification', SpNotificationSchema);
export default SpNotification;