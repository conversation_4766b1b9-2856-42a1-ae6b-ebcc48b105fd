const defaultPrequalificationFields = [
  {
    "name": "declaration_section",
    "group": "declaration",
    "subGroup": "declaration", // Added based on error message
    "isParent": true,
    "title": "Declaration Section",
    "description": "Please review and respond to the declaration requirements below.",
    "order": 0,
    "label": "Declaration Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "This section contains important declarations.", // Added based on error message
    "helpText": "Please review the declarations below.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "legal_acknowledgement",
    "group": "declaration",
    "subGroup": "declaration", // Added based on error message
    "order": 1,
    "label": "Legal Acknowledgement",
    "type": "checkbox",
    "placeholder": "Tick to acknowledge",
    "description": "Confirm that all provided information is accurate and legally binding.",
    "helpText": "Ticking this means you agree to the terms and confirm authenticity.",
    "tooltip": "Mandatory for submission",
    "isRequired": true
  },
  {
    "name": "company_info_section",
    "group": "company",
    "subGroup": "info",
    "isParent": true,
    "title": "Company Information",
    "description": "Provide key identity details of your company.",
    "order": 10,
    "label": "Company Information", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Details about your company identity.", // Added based on error message
    "helpText": "Fill in the required company details.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "company_name",
    "group": "company",
    "subGroup": "info",
    "order": 11,
    "label": "Company Name",
    "type": "text",
    "placeholder": "Enter registered name",
    "description": "Registered legal name of the company.",
    "helpText": "Use official business registration records.",
    "tooltip": "This will be displayed on official records",
    "isRequired": true
  },
  {
    "name": "registration_number",
    "group": "company",
    "subGroup": "info",
    "order": 12,
    "label": "Registration Number",
    "type": "text",
    "placeholder": "Enter company registration number",
    "description": "The number assigned by the registering authority.",
    "helpText": "As shown on your certificate of incorporation.",
    "tooltip": "Unique identifier",
    "isRequired": true
  },
  {
    "name": "year_of_incorporation",
    "group": "company",
    "subGroup": "info",
    "order": 13,
    "label": "Year of Incorporation",
    "type": "number",
    "placeholder": "e.g., 2015",
    "description": "The year your business was legally incorporated.",
    "helpText": "Check your registration documents.",
    "tooltip": "This helps assess company experience",
    "validations": {
      "min": 1900,
      "max": 2025
    }
  },
  {
    "name": "company_bank_section",
    "group": "company",
    "subGroup": "bank",
    "isParent": true,
    "title": "Bank Details",
    "description": "Information about your company’s main bank account.",
    "order": 20,
    "label": "Bank Details Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Provide your company bank account information.", // Added based on error message
    "helpText": "Enter details of your main bank account.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "bank_name",
    "group": "company",
    "subGroup": "bank",
    "order": 21,
    "label": "Bank Name",
    "type": "text",
    "placeholder": "Enter name of bank",
    "description": "The financial institution where the company holds an account.",
    "helpText": "Use the official name of the bank.",
    "tooltip": "This should match your bank documents",
    "isRequired": true
  },
  {
    "name": "account_number",
    "group": "company",
    "subGroup": "bank",
    "order": 22,
    "label": "Account Number",
    "type": "text",
    "placeholder": "Enter bank account number",
    "description": "The company’s main bank account number.",
    "helpText": "Double-check for accuracy.",
    "tooltip": "Used for payment processing",
    "isRequired": true,
    "validations": {
      "pattern": "^[0-9]{6,20}$"
    }
  },
  {
    "name": "bank_branch",
    "group": "company",
    "subGroup": "bank",
    "order": 23,
    "label": "Bank Branch",
    "type": "text",
    "placeholder": "Enter branch name or code",
    "description": "Branch where the account is held.",
    "helpText": "Get this from your bank statement.",
    "tooltip": "Used to verify bank info"
  },
  {
    "name": "company_contact_section",
    "group": "company",
    "subGroup": "contact",
    "isParent": true,
    "title": "Contact Information",
    "description": "Provide contact details for your company.",
    "order": 30,
    "label": "Company Contact Information", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Details for contacting your company.", // Added based on error message
    "helpText": "Provide accurate contact details.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "phone_number",
    "group": "company",
    "subGroup": "contact",
    "order": 31,
    "label": "Phone Number",
    "type": "text",
    "placeholder": "e.g., +************",
    "description": "Main telephone number for the company.",
    "helpText": "Use a working phone number.",
    "tooltip": "Used for official communication",
    "isRequired": true,
    "validations": {
      "pattern": "^\\+?[0-9]{9,15}$"
    }
  },
  {
    "name": "email_address",
    "group": "company",
    "subGroup": "contact",
    "order": 32,
    "label": "Email Address",
    "type": "text",
    "placeholder": "e.g., <EMAIL>",
    "description": "Main email for correspondence.",
    "helpText": "Use a frequently checked inbox.",
    "tooltip": "Important updates will be sent here",
    "isRequired": true,
    "validations": {
      "pattern": "^[\\w.-]+@[\\w.-]+\\.\\w{2,}$"
    }
  },
  {
    "name": "physical_address",
    "group": "company",
    "subGroup": "contact",
    "order": 33,
    "label": "Physical Address",
    "type": "textarea",
    "placeholder": "Enter company location address",
    "description": "The physical location of your company headquarters.",
    "helpText": "Include street name, building, city, etc.",
    "tooltip": "Used for verification and correspondence"
  },
  {
    "name": "registration_statutory",
    "group": "statutory",
    "subGroup": "statutory", // Added based on error message
    "isParent": true,
    "title": "Registration & Statutory Documents",
    "description": "Details and documents required for statutory compliance.",
    "order": 0,
    "label": "Registration & Statutory Documents", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section for required legal and statutory documents.", // Added based on error message
    "helpText": "Upload copies of official registration and tax documents.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "cert_of_incorporation",
    "group": "statutory",
    "subGroup": "statutory",
    "order": 1,
    "type": "file",
    "label": "Certificate of Incorporation",
    "placeholder": "Upload certificate",
    "description": "The official certificate issued during company registration.",
    "helpText": "Upload as PDF.",
    "tooltip": "Required document for registered entities.",
    "isRequired": true,
    "validations": {
      "fileTypes": ["pdf"],
      "maxFileSize": 2048
    }
  },
  {
    "name": "kra_pin_certificate",
    "group": "statutory",
    "subGroup": "statutory",
    "order": 2,
    "type": "file",
    "label": "KRA PIN Certificate",
    "placeholder": "Upload PIN certificate",
    "description": "Tax identification issued by the Kenya Revenue Authority.",
    "helpText": "Upload scanned copy.",
    "tooltip": "Should be current and valid.",
    "isRequired": true,
    "validations": {
      "fileTypes": ["pdf"],
      "maxFileSize": 2048
    }
  },
  {
    "name": "director1_details",
    "group": "shareholders",
    "subGroup": "director1",
    "isParent": true,
    "title": "Director 1 Information",
    "description": "Details of the primary company director.",
    "order": 10,
    "label": "Director 1 Information", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Provide details for the first director.", // Added based on error message
    "helpText": "Enter information for the primary director.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "director1_name",
    "group": "shareholders",
    "subGroup": "director1",
    "order": 11,
    "type": "text",
    "label": "Full Name",
    "placeholder": "Enter full name",
    "description": "Full legal name of the director.",
    "helpText": "Should match official ID.",
    "tooltip": "First, middle and last name.",
    "isRequired": true
  },
  {
    "name": "director1_id",
    "group": "shareholders",
    "subGroup": "director1",
    "order": 12,
    "type": "file",
    "label": "ID/Passport",
    "placeholder": "Upload ID or Passport",
    "description": "Government-issued identification.",
    "helpText": "Clear scanned copy.",
    "tooltip": "Valid national ID or passport.",
    "isRequired": true,
    "validations": {
      "fileTypes": ["pdf", "jpg", "png"],
      "maxFileSize": 2048
    }
  },
  {
    "name": "shareholder1_details",
    "group": "shareholders",
    "subGroup": "shareholder1",
    "isParent": true,
    "title": "Shareholder 1 Information",
    "description": "Information about the primary shareholder.",
    "order": 20,
    "label": "Shareholder 1 Information", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Provide details for the first shareholder.", // Added based on error message
    "helpText": "Enter information for the primary shareholder.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "shareholder1_name",
    "group": "shareholders",
    "subGroup": "shareholder1",
    "order": 21,
    "type": "text",
    "label": "Full Name",
    "placeholder": "Enter shareholder name",
    "description": "Full name of the shareholder.",
    "helpText": "As appears on legal documents.",
    "tooltip": "Include all names.",
    "isRequired": true
  },
  {
    "name": "shareholder1_shareholding",
    "group": "shareholders",
    "subGroup": "shareholder1",
    "order": 22,
    "type": "number",
    "label": "Shareholding (%)",
    "placeholder": "Enter percentage",
    "description": "Shareholder's stake in the company.",
    "helpText": "Should be a valid percentage.",
    "tooltip": "Numeric value between 0 - 100.",
    "isRequired": true,
    "validations": {
      "min": 0,
      "max": 100
    }
  },
  {
    "name": "integrity_risk",
    "group": "integrity",
    "subGroup": "risk",
    "isParent": true,
    "title": "Risk Disclosures",
    "description": "Information on any known risks, conflicts of interest, or affiliations that could impact integrity.",
    "order": 30,
    "label": "Risk Disclosures Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Declare potential risks and conflicts.", // Added based on error message
    "helpText": "Provide information on risks and conflicts of interest.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "conflict_of_interest",
    "group": "integrity",
    "subGroup": "risk",
    "order": 31,
    "type": "radio",
    "label": "Any Conflict of Interest?",
    "placeholder": "Select one",
    "description": "Declare any relationships or affiliations that could result in a conflict of interest.",
    "helpText": "Choose 'Yes' or 'No'. If yes, provide explanation.",
    "tooltip": "Includes business ties with evaluators, government, etc.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 0 },
      { "label": "No", "value": "no", "score": 5 }
    ]
  },
  {
    "name": "conflict_of_interest_explanation",
    "group": "integrity",
    "subGroup": "risk",
    "order": 32,
    "type": "textarea",
    "label": "Explanation of Conflict",
    "placeholder": "Provide details of conflict if any",
    "description": "If a conflict of interest was declared, explain it here.",
    "helpText": "Mandatory if 'Yes' was selected above.",
    "tooltip": "Explain the nature of the conflict clearly.",
    "isVisible": true,
    "isEditable": true
  },
  {
    "name": "integrity_litigation",
    "group": "integrity",
    "subGroup": "litigation",
    "isParent": true,
    "title": "Litigation History",
    "description": "Disclosure of any legal actions involving the company or its directors/shareholders.",
    "order": 40,
    "label": "Litigation History Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section for disclosing past legal cases.", // Added based on error message
    "helpText": "Provide details of any past litigation.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "past_litigation",
    "group": "integrity",
    "subGroup": "litigation",
    "order": 41,
    "type": "radio",
    "label": "Any Past Litigation?",
    "placeholder": "Select one",
    "description": "Have you or your company been involved in litigation over the last 5 years?",
    "helpText": "Indicate 'Yes' or 'No'.",
    "tooltip": "Covers commercial, civil, criminal, or labor-related cases.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 0 },
      { "label": "No", "value": "no", "score": 5 }
    ]
  },
  {
    "name": "litigation_explanation",
    "group": "integrity",
    "subGroup": "litigation",
    "order": 42,
    "type": "textarea",
    "label": "Litigation Explanation",
    "placeholder": "Explain any litigation cases",
    "description": "Provide a detailed explanation of each case, including outcomes.",
    "helpText": "Mandatory if 'Yes' was selected above.",
    "tooltip": "Include case names, dates, and resolutions if applicable.",
    "isVisible": true,
    "isEditable": true
  },
  {
    "name": "integrity_summary",
    "group": "integrity",
    "subGroup": "declaration",
    "isParent": true,
    "title": "Integrity Declarations",
    "description": "Overall declaration regarding integrity, ethics, and legal compliance.",
    "order": 10,
    "label": "Integrity Declarations Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Make declarations about company integrity and ethics.", // Added based on error message
    "helpText": "Respond to the integrity-related questions.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "code_of_ethics",
    "group": "integrity",
    "subGroup": "declaration",
    "order": 11,
    "type": "radio",
    "label": "Does the company have a Code of Ethics?",
    "placeholder": "Select one",
    "description": "Indicate whether the company has an established code of ethics.",
    "helpText": "This relates to documented ethical guidelines followed by your company.",
    "tooltip": "A Code of Ethics may cover anti-corruption, fair labor, conflict of interest, etc.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 5 },
      { "label": "No", "value": "no", "score": 0 }
    ]
  },
  {
    "name": "whistleblower_policy",
    "group": "integrity",
    "subGroup": "declaration",
    "order": 12,
    "type": "radio",
    "label": "Do you have a Whistleblower Policy?",
    "placeholder": "Select one",
    "description": "Policy that protects employees or stakeholders who report misconduct.",
    "helpText": "Indicate 'Yes' or 'No'.",
    "tooltip": "Supports accountability and internal controls.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 5 },
      { "label": "No", "value": "no", "score": 0 }
    ]
  },
  {
    "name": "esg_environmental",
    "group": "esg",
    "subGroup": "env",
    "isParent": true,
    "title": "Environmental Responsibility",
    "description": "Assessment of the company's environmental policies, impacts, and sustainability practices.",
    "order": 10,
    "label": "Environmental Responsibility Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on environmental policies and practices.", // Added based on error message
    "helpText": "Provide information on your environmental initiatives.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "environmental_policy",
    "group": "esg",
    "subGroup": "env",
    "order": 11,
    "type": "radio",
    "label": "Do you have an Environmental Policy?",
    "placeholder": "Select one",
    "description": "Declared policy outlining the company’s commitment to environmental management.",
    "helpText": "Include waste management, pollution control, conservation, etc.",
    "tooltip": "Attach policy under 'Attachments' if applicable.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 5 },
      { "label": "No", "value": "no", "score": 0 }
    ]
  },
  {
    "name": "recycling_initiatives",
    "group": "esg",
    "subGroup": "env",
    "order": 12,
    "type": "text",
    "label": "List any Recycling Initiatives",
    "placeholder": "E.g., plastic reuse, composting, e-waste management",
    "description": "List sustainability or recycling practices the company is engaged in.",
    "helpText": "Mention specific programs, timelines, or goals.",
    "tooltip": "These show environmental impact awareness.",
    "isScoreable": true,
    "maxScore": 5
  },
  {
    "name": "esg_social",
    "group": "esg",
    "subGroup": "social",
    "isParent": true,
    "title": "Social Impact",
    "description": "Evaluation of how the company supports social responsibility, diversity, equity, and inclusion.",
    "order": 20,
    "label": "Social Impact Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on social responsibility and impact.", // Added based on error message
    "helpText": "Provide information on your social initiatives.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "community_programs",
    "group": "esg",
    "subGroup": "social",
    "order": 21,
    "type": "textarea",
    "label": "Describe your community programs",
    "placeholder": "E.g., youth empowerment, education, food relief",
    "description": "Outline community outreach or social initiatives undertaken by the company.",
    "helpText": "Describe key projects or partnerships.",
    "tooltip": "May include non-profit collaborations, donations, or education drives.",
    "isScoreable": true,
    "maxScore": 5
  },
  {
    "name": "diversity_policy",
    "group": "esg",
    "subGroup": "social",
    "order": 22,
    "type": "radio",
    "label": "Do you have a diversity and inclusion policy?",
    "placeholder": "Select one",
    "description": "Policy or initiatives promoting a diverse and inclusive workplace.",
    "helpText": "Required in many modern procurement and ESG evaluations.",
    "tooltip": "Attach copy if available.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 5 },
      { "label": "No", "value": "no", "score": 0 }
    ]
  },
  {
    "name": "esg_governance",
    "group": "esg",
    "subGroup": "governance",
    "isParent": true,
    "title": "Corporate Governance",
    "description": "Insight into company structure, control mechanisms, and oversight practices.",
    "order": 30,
    "label": "Corporate Governance Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on company governance structure and practices.", // Added based on error message
    "helpText": "Provide information on your governance framework.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "board_structure",
    "group": "esg",
    "subGroup": "governance",
    "order": 31,
    "type": "text",
    "label": "Board Structure",
    "placeholder": "E.g., number of independent directors, roles",
    "description": "Describe how the board of directors is composed and operates.",
    "helpText": "Include names and positions if relevant.",
    "tooltip": "Shows level of governance and internal oversight.",
    "isScoreable": true,
    "maxScore": 5
  },
  {
    "name": "audit_committee",
    "group": "esg",
    "subGroup": "governance",
    "order": 32,
    "type": "radio",
    "label": "Is there an Audit Committee in place?",
    "placeholder": "Select one",
    "description": "Committee responsible for overseeing financial reporting and audit processes.",
    "helpText": "Useful for assessing governance maturity.",
    "tooltip": "Attach Terms of Reference if applicable.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ],
    "optionScore": [
      { "label": "Yes", "value": "yes", "score": 5 },
      { "label": "No", "value": "no", "score": 0 }
    ]
  },
  {
    "name": "technical-capacity",
    "group": "technical",
    "subGroup": "technical",
    "isParent": true,
    "title": "Technical Capacity",
    "description": "Demonstrate your organization’s technical know-how and capacity to perform the work.",
    "order": 0,
    "label": "Technical Capacity Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on technical capabilities.", // Added based on error message
    "helpText": "Describe your technical capacity and resources.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "technical-approach",
    "group": "technical",
    "subGroup": "technical",
    "order": 1,
    "type": "textarea",
    "label": "Technical Approach",
    "placeholder": "Explain your methodology or technical approach...",
    "description": "How will the task be executed from a technical point of view?",
    "helpText": "Provide a step-by-step outline.",
    "tooltip": "Methodology or work plan to complete tasks",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 10
  },
  {
    "name": "equipment-details",
    "group": "technical",
    "subGroup": "technical",
    "order": 2,
    "type": "textarea",
    "label": "Equipment Details",
    "placeholder": "List key equipment/tools...",
    "description": "Indicate the type and number of equipment/tools relevant to the assignment.",
    "helpText": "Include ownership or leasing details.",
    "tooltip": "Provide equipment names, quantities, and capacity.",
    "isScoreable": true,
    "maxScore": 5
  },
  {
    "name": "sla-overview",
    "group": "sla",
    "subGroup": "sla",
    "isParent": true,
    "title": "Service Level Agreement (SLA)",
    "description": "Specify performance and service delivery commitments.",
    "order": 0,
    "label": "Service Level Agreement (SLA) Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on service level commitments.", // Added based on error message
    "helpText": "Outline your service level expectations.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "response-times",
    "group": "sla",
    "subGroup": "sla",
    "order": 1,
    "type": "text",
    "label": "Response Times",
    "placeholder": "e.g., Within 2 hours of request...",
    "description": "Expected response time for queries, issues or requests.",
    "helpText": "Include priority-based expectations if available.",
    "tooltip": "Specify turnaround times for different priorities.",
    "isScoreable": true,
    "maxScore": 3
  },
  {
    "name": "uptime-guarantee",
    "group": "sla",
    "subGroup": "sla",
    "order": 2,
    "type": "text",
    "label": "Uptime Guarantee",
    "placeholder": "e.g., 99.9% uptime...",
    "description": "Level of availability expected from your services.",
    "helpText": "State percentage and duration.",
    "tooltip": "Uptime expectations such as 99.9% monthly uptime.",
    "isScoreable": true,
    "maxScore": 2
  },
  {
    "name": "hr-capacity",
    "group": "hr",
    "subGroup": "hr",
    "isParent": true,
    "title": "Human Resource Capacity",
    "description": "Demonstrate HR capabilities relevant to the project.",
    "order": 0,
    "label": "Human Resource Capacity Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on human resource capabilities.", // Added based on error message
    "helpText": "Describe your key personnel and HR resources.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "key-personnel",
    "group": "hr",
    "subGroup": "hr",
    "order": 1,
    "type": "textarea",
    "label": "Key Personnel",
    "placeholder": "List key personnel, qualifications and roles...",
    "description": "Provide a list of staff members and their experience related to the task.",
    "helpText": "Names, qualifications, and years of experience.",
    "tooltip": "Include CVs where required.",
    "isScoreable": true,
    "maxScore": 10
  },
  {
    "name": "past-performance",
    "group": "experience",
    "subGroup": "experience",
    "isParent": true,
    "title": "Relevant Experience",
    "description": "Describe relevant past work and performance track record.",
    "order": 0,
    "label": "Relevant Experience Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on past project experience.", // Added based on error message
    "helpText": "Provide details of relevant projects.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "previous-projects",
    "group": "experience",
    "subGroup": "experience",
    "order": 1,
    "type": "textarea",
    "label": "Previous Projects",
    "placeholder": "List completed similar projects...",
    "description": "Include client names, contact details, and project value.",
    "helpText": "You may attach reference letters or completion certificates.",
    "tooltip": "Be detailed about timeline, deliverables, and value.",
    "isScoreable": true,
    "maxScore": 10
  },
  {
    "name": "reference1-details",
    "group": "reference",
    "subGroup": "reference1",
    "isParent": true,
    "title": "Reference 1",
    "description": "Provide contact and details for your first reference.",
    "order": 0,
    "label": "Reference 1 Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Details for your first reference.", // Added based on error message
    "helpText": "Enter contact information for your first reference.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "reference1-contact",
    "group": "reference",
    "subGroup": "reference1",
    "order": 1,
    "type": "text",
    "label": "Reference Contact",
    "placeholder": "Full Name & Contact Info",
    "description": "Name, organization, phone number or email.",
    "helpText": "Reference must be from a similar project.",
    "tooltip": "Include verifiable references.",
    "isScoreable": false
  },
  {
    "name": "reference2-details",
    "group": "reference",
    "subGroup": "reference2",
    "isParent": true,
    "title": "Reference 2",
    "description": "Provide contact and details for your second reference.",
    "order": 10,
    "label": "Reference 2 Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Details for your second reference.", // Added based on error message
    "helpText": "Enter contact information for your second reference.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "reference2-contact",
    "group": "reference",
    "subGroup": "reference2",
    "order": 11,
    "type": "text",
    "label": "Reference Contact",
    "placeholder": "Full Name & Contact Info",
    "description": "Name, organization, phone number or email.",
    "helpText": "Reference must be from a similar project.",
    "tooltip": "Include verifiable references.",
    "isScoreable": false
  },
  {
    "name": "reference3-details",
    "group": "reference",
    "subGroup": "reference3",
    "isParent": true,
    "title": "Reference 3",
    "description": "Provide contact and details for your third reference.",
    "order": 20,
    "label": "Reference 3 Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Details for your third reference.", // Added based on error message
    "helpText": "Enter contact information for your third reference.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "reference3-contact",
    "group": "reference",
    "subGroup": "reference3",
    "order": 21,
    "type": "text",
    "label": "Reference Contact",
    "placeholder": "Full Name & Contact Info",
    "description": "Name, organization, phone number or email.",
    "helpText": "Reference must be from a similar project.",
    "tooltip": "Include verifiable references.",
    "isScoreable": false
  },
  {
    "name": "financial-overview",
    "group": "financial",
    "subGroup": "financial",
    "isParent": true,
    "title": "Financial Capacity",
    "description": "Provide financial records and declarations relevant to your organization's viability.",
    "order": 0,
    "label": "Financial Capacity Section", // Added based on error message
    "type": "section", // Added a plausible type for a section field
    "tooltip": "Section on financial information.", // Added based on error message
    "helpText": "Provide required financial details and documents.", // Added based on error message
    "placeholder": "N/A" // Added a placeholder for a section field
  },
  {
    "name": "financial-statements",
    "group": "financial",
    "subGroup": "financial",
    "order": 1,
    "type": "file",
    "label": "Audited/Management Accounts",
    "placeholder": "Attach financial documents...",
    "description": "For limited companies: most recent audited accounts, balance sheet and P&L. For sole proprietors: management accounts for the last 2 years.",
    "helpText": "Attach files in PDF format.",
    "tooltip": "Required for financial due diligence.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 5,
    "validations": {
      "fileTypes": ["pdf"],
      "maxFileSize": ********
    }
  },
  {
    "name": "creditor-obligations",
    "group": "financial",
    "subGroup": "financial",
    "order": 2,
    "type": "radio",
    "label": "Met Creditor Obligations",
    "placeholder": "Yes/No",
    "description": "Has your organization met all its obligations to pay its creditors during the past year?",
    "helpText": "Select Yes or No.",
    "tooltip": "Indicate financial responsibility.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 2,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ]
  },
  {
    "name": "staff-obligations",
    "group": "financial",
    "subGroup": "financial",
    "order": 3,
    "type": "radio",
    "label": "Met Staff Obligations",
    "placeholder": "Yes/No",
    "description": "Has your organization met all its obligations to pay its staff during the past year?",
    "helpText": "Select Yes or No.",
    "tooltip": "Indicate payment status.",
    "isRequired": true,
    "isScoreable": true,
    "maxScore": 2,
    "options": [
      { "label": "Yes", "value": "yes" },
      { "label": "No", "value": "no" }
    ]
  },
  {
    "name": "financial-details-if-no",
    "group": "financial", // Inferred group
    "subGroup": "financial", // Inferred subGroup
    "order": 4, // Inferred order
    "type": "textarea", // Added based on error message and common use case
    "label": "Details if Obligations Not Met", // Added based on error message
    "placeholder": "Provide explanation if answered No...", // Added based on error message
    "description": "If you answered No to the previous questions, please explain.",
    "helpText": "Provide a detailed explanation of why obligations were not met.", // Completed helpText
    "tooltip": "Explanation required if financial or staff obligations were not met.", // Added based on error message
    "isVisible": true, // Assuming this should be visible/editable when needed
    "isEditable": true,
    "isRequired": false // Assuming this is only required if the radio buttons are 'No'
  }
];

  export default defaultPrequalificationFields;
   // This is a sample data structure for a form configuration. 
    // It contains various fields with properties like name, group, type, label, etc.
    // The fields are organized into sections for better readability and organization.