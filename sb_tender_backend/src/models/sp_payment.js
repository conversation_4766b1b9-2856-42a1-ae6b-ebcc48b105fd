// models/Payment.js

import mongoose from 'mongoose';

const paymentSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    default: null,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  spOrderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpOrder',
    default: null,
  },
  source: {
    type: String,
    default: null,
  },
  interval: {
    type: String,
     enum: ['one-off','monthly', 'yearly'],
    default: 'one-off',
  },
  channel: {
    type: String,
    default: null,
  },
  description: {
    type: String,
    default: null,
  },
  code: {
    type: String,
    default: null,
  },
  payer: {
    type: String,
    default: null,
  },
  payee: {
    type: String,
    default: null,
  },
  memo: {
    type: String,
    default: null,
  },
  paymentDate: {
    type: Date,
    default: null,
  },
  reference: {
    type: String,
  },
  amount: {
    type: mongoose.Schema.Types.Decimal128,
    required: true,
  },
  balance: {
    type: mongoose.Schema.Types.Decimal128,
    default: 0.00,
  },
  currency: {
    type: String,
    default: null,
  },
  ipAddress: {
    type: String,
    default: null,
  },
  respcode: {
    type: String,
    default: null,
  },
  respDesc: {
    type: String,
    default: null,
  },
  customerMessage: {
    type: String,
    default: null,
  },
  transactionType: {
    type: String,
    default: null,
  },
  transID: {
    type: String,
    default: null,
  },
  transTime: {
    type: Date,
    default: null,
  },
  transAmount: {
    type: mongoose.Schema.Types.Decimal128,
    default: null,
  },
  businessShortCode: {
    type: String,
    default: null,
  },
  billRefNumber: {
    type: String,
    default: null,
  },
  invoiceNumber: {
    type: String,
    default: null,
  },
  orgAccountBalance: {
    type: mongoose.Schema.Types.Decimal128,
    default: null,
  },
  thirdPartyTransID: {
    type: String,
    default: null,
  },
  mobile: {
    type: String,
    default: null,
  },
  status: {
    type: String,
    enum: ['abandoned', 'success', 'failed', 'ongoing', 'pending', 'processing', 'reversed', 'cancelled'],
    default: 'pending',
  },
  resultCode: {
    type: String,
    default: null,
  },
  resultDesc: {
    type: String,
    default: null,
  },
  detail: {
    type: String,
    default: null,
  },
  email: {
    type: String,
    default: null,
  },
  hasInvoice: {
    type: Boolean,
    default: false,
  },
  dueDate: {
    type: Date,
    default: null,
  },
  offlineReference: {
    type: String,
    default: null,
  },
  authorization: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },
  authorizationUrl: {
    type: String,
    default: null,
  },
  accessCode: {
    type: String,
    default: null,
  },  
  fees: {
    type: Number,
    default: null,
  },
  customer: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },  
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },  
  log: {
    type: mongoose.Schema.Types.Mixed,
    default: null,
  },
  notifications: [{
    type: mongoose.Schema.Types.Mixed,
    default: [],
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  paidAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

const SpPayment = mongoose.model('SpPayment', paymentSchema);
export default SpPayment;
