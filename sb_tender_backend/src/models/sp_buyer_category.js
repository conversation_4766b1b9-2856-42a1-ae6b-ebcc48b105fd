import mongoose from 'mongoose';

const buyerCategorySchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  type: {
    type: String,
    enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
    required: true,
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active',
  },
  spCategoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCategory',
    required: true
  },
    passMark: {
        type: Number,
        default: 0
    },
  spCompanyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCompany',
    required: true
  },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
}, { timestamps: true });

const SpBuyerCategory = mongoose.model('SpBuyerCategory', buyerCategorySchema);
export default SpBuyerCategory;
