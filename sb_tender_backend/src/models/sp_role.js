import mongoose from 'mongoose';

const roleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },  
  context: {
    type: String,
    enum: ['buyer', 'supplier', 'admin'],
    required: true,
  },
  type: {
    type: String,
    enum: ['registration', 'prequalification', 'settings', 'rfq', 'tender'],
    required: true,
  },
  permissions: [
    {
      type: String,
      enum: ['r', 'w', 'd'], // r: read, w: write, d: delete
      required: true,
    },
  ],
  description: {
    type: String,
    required: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

const SpRole = mongoose.model('SpRole', roleSchema);
export default SpRole;
