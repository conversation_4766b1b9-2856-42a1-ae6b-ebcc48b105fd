import SpU<PERSON><PERSON><PERSON> from '../models/sp_user_role.js';
import SpRole from '../models/sp_role.js';

const authorize = (type, requiredPermissions = [], context = null) => {
  /*console.log('[AUTHORIZE INIT]', { 
    type, 
    requiredPermissions, 
    context,
    timestamp: new Date().toISOString() 
  });
*/
  return async (req, res, next) => {
    const requestId = Math.random().toString(36).substring(2, 8);
   // console.log(`\n[${requestId}] STARTING AUTHORIZATION CHECK`, {
   //   path: req.path,
   //   method: req.method,
   //   timestamp: new Date().toISOString()
   // });

    try {
      // 1. Validate required parameters
     // console.log(`[${requestId}] Validating parameters...`);
      if (!type) {
        //console.error(`[${requestId}] ERROR: Missing authorization type`);
        return res.status(500).json({ error: 'Authorization type is required' });
      }

      // 2. Get user and company information
      //console.log(`[${requestId}] Extracting user/company data...`, {
      //  userId: req.user?.userId,
      //  paramsCompanyId: req.params.companyId,
      //  bodyCompanyId: req.body.companyId
     // });

      const userId = req.user.userId;
      const companyId = req.params.companyId || req.body.companyId;

      if (!companyId) {
       // console.error(`[${requestId}] ERROR: Missing company ID`, {
        //  params: Object.keys(req.params),
        //  bodyKeys: Object.keys(req.body)
       // });
        return res.status(400).json({ error: 'Company ID is required' });
      }

      // 3. Fetch user roles
      //console.log(`[${requestId}] Fetching user roles...`, { userId, companyId });
      const userRoles = await SpUserRole.find({ 
        userId, 
        companyId 
      }).populate({
        path: 'roleId',
        model: SpRole
      });

     // console.log(`[${requestId}] Found ${userRoles.length} user roles`, {
     //   roleIds: userRoles.map(ur => ur.roleId?._id)
     // });

      if (!userRoles.length) {
       // console.error(`[${requestId}] ERROR: No roles found for user`, {
      //    userId,
      ///    companyId
      //  });
        return res.status(403).json({ error: 'No roles assigned for this company' });
      }

      // 4. Check permissions
    //  console.log(`[${requestId}] Checking permissions...`, {
   //     requiredType: type,
    //    requiredContext: context,
    //    requiredPermissions
   //   });

      const hasPermission = userRoles.some(userRole => {
        const role = userRole.roleId;
       // console.log(`[${requestId}] Checking role ${role._id}`, {
        //  roleType: role.type,
       //   roleContext: role.context,
       //   rolePermissions: role.permissions
       // });

        // Check type match
        const typeMatch = role.type === type;
        if (!typeMatch) {
         // console.log(`[${requestId}] Role type mismatch`, {
          //  expected: type,
          //  actual: role.type
          //});
          return false;
        }
        
        // Check context if provided
        if (context && role.context !== context) {
         // console.log(`[${requestId}] Role context mismatch`, {
         //   expected: context,
         //   actual: role.context
        //  });
          return false;
        }
        
        // Check permissions
        const hasRequiredPermissions = requiredPermissions.every(perm => {
          const hasPerm = role.permissions.includes(perm);
          if (!hasPerm) {
           // console.log(`[${requestId}] Missing permission`, { required: perm });
          }
          return hasPerm;
        });
        
        return hasRequiredPermissions;
      });

      // 5. Handle authorization result
     // console.log(`[${requestId}] Authorization result:`, { hasPermission });
      if (!hasPermission) {
    /*    console.error(`[${requestId}] FAIL: Insufficient permissions`, {
          userRoles: userRoles.map(ur => ({
            roleId: ur.roleId._id,
            type: ur.roleId.type,
            context: ur.roleId.context,
            permissions: ur.roleId.permissions
          })),
          required: { type, context, permissions: requiredPermissions }
        });
        */
        return res.status(403).json({ 
          error: 'Access denied. Insufficient permissions.',
          required: { type, context, permissions: requiredPermissions }
        });
      }

    //  console.log(`[${requestId}] SUCCESS: Authorization granted`);
      next();
    } catch (error) {
     // console.error(`[${requestId}] AUTHORIZATION ERROR:`, {
     //   message: error.message,
     //   stack: error.stack,
    //    fullError: JSON.stringify(error, Object.getOwnPropertyNames(error))
   //   });
      res.status(500).json({ 
        error: 'Internal authorization error',
        requestId // Include in response for debugging
      });
    } finally {
      console.log(`[${requestId}] AUTHORIZATION CHECK COMPLETED`);
    }
  };
};

export default authorize;