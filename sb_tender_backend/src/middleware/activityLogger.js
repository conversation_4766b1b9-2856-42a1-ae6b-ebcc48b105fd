import SpActivityLog from '../models/sp_activity_log.js';

const createActivity = async (logData) => {
  try {
    const activity = new SpActivityLog(logData);
    await activity.save();
    console.log('Logged');
  } catch (error) {
    console.error('Failed to log activity:', error);
    // Don't throw - logging shouldn't break the main operation
  }
};

export const logActivity = (action, resource, description) => {
  return async (req, res, next) => {
    // Store original res.json to intercept successful responses
    const originalJson = res.json;

    res.json = function(data) {
      // Only log on successful responses (status < 400)
      //if (res.statusCode < 400) {
        const logData = {
          companyId: req.user.companyId || req.params.companyId,
          userId: req.user.userId,
          action,
          resource,
          resourceId: req.params.id || data?.id,
          description: typeof description === 'function' ? description(req, data) : description,
          metadata: {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            additionalInfo: {
              method: req.method,
              url: req.originalUrl,
              params: req.params,
              query: req.query
            }
          }
        };
          createActivity(logData);

      //}

      return originalJson.call(this, data);
    };

    next();
  };
};

