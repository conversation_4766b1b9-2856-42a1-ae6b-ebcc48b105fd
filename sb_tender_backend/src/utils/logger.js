import { createLogger, format, transports } from 'winston';

const logger = createLogger({
  level: 'info', // Or set this dynamically based on environment variable if needed
  format: format.combine(
    // Using format.simple() or format.cli() might be nicer for console output than json
    // format.simple(),
    // format.cli(),
    format.timestamp(),
    format.json() // Keeping json as per your original code
  ),
  transports: [
    // ONLY log to the console for Vercel Serverless
    new transports.Console({
        // Optional: Add format specifically for console if different from file format
        // format: format.combine(format.colorize(), format.simple())
    }),
    // REMOVED: new transports.File({ filename: 'logs/activity.log' }), // This causes errors on Vercel
  ],
  // Optional: Add exception/rejection handlers if you want to log unhandled errors
  // exceptionHandlers: [
  //   new transports.Console()
  // ],
  // rejectionHandlers: [
  //   new transports.Console()
  // ]
});

export default logger;
