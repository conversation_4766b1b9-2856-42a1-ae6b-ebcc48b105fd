import express from 'express';
import {getOrderDetails, getCompanyOrders, getAllOrders, verifyOrderPayment, retryOrderPayment} from '../controllers/paymentController.js';
import auth from '../middleware/auth.js';
import { getCompanyApplications, getCompanyApplicationById, uploadApplicationFile, downloadApplicationDocument, updateCompanyApplication } from '../controllers/applicationController.js'
import {getCompanyAcivities, getSupplierDashboardMetrics, getCompanyNotifications, getSupplierBidActivityData} from '../controllers/dashboardController.js'
import {logActivity} from '../middleware/activityLogger.js';
import {createBuyerJob, getBuyerJobs, updateBuyerJob, deleteBuyerJob} from '../controllers/jobController.js';
import {getJobCategories, updateJobCategory, deleteJobCategory} from '../controllers/jobCategoryController.js';
import {getCompanyUsers, getCompanyRoles, updateCompanyUser, createCompanyUser, deleteCompanyUser } from '../controllers/supplierController.js'
import authorize from '../middleware/authorize.js';
import multer from 'multer';

const router = express.Router();


// Configure multer for in-memory storage
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });



  router.get('/:companyId/metrics/basic', auth, authorize('settings', ['r'], 'supplier'), logActivity('view', 'metrics', (req, data) =>
    `Viewed metrics ${req.params.companyId}` ), getSupplierDashboardMetrics);
  router.get('/:companyId/metrics/bid-activity', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'metrics', (req, data) =>
    `Viewed bid activity ${req.params.companyId}` ), getSupplierBidActivityData);

  router.get('/:companyId/activities', auth, logActivity('view', 'acivities', (req, data) =>
    `Viewed activity logs ${req.params.companyId}` ), authorize('settings', ['r'], 'supplier'), getCompanyAcivities);

  router.get('/:companyId/notifications', auth, logActivity('view', 'notifications', (req, data) =>
    `Viewed notifications ${req.params.companyId}` ), authorize('registration', ['r'], 'supplier'), getCompanyNotifications);


  router.get('/:companyId/users', auth, authorize('settings', ['r'], 'supplier'), logActivity('view', 'users', (req, data) =>
    `Viewed users ${req.params.companyId}` ), getCompanyUsers);
  router.get('/:companyId/roles', auth, authorize('settings', ['r'], 'supplier'), logActivity('view', 'roles', (req, data) =>
    `Viewed roles ${req.params.companyId}` ), getCompanyRoles);
  router.put('/:companyId/users/:userId', auth, authorize('settings', ['r'], 'supplier'), logActivity('update', 'user', (req, data) =>
    `Updated user ${req.params.userId}` ), updateCompanyUser);
  router.post('/:companyId/users', auth, authorize('settings', ['r'], 'supplier'), logActivity('create', 'user', (req, data) =>
    `Created user ${req.body.name}` ), createCompanyUser);
  router.delete('/:companyId/users/:userId', auth, authorize('settings', ['r'], 'supplier'), deleteCompanyUser);


//orders
  //router.get('/orders/', auth, getCompanyOrders);
   
  // Get all orders with pagination and filtering
  router.get('/:companyId/orders', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'orders', (req, data) =>
    `Viewed categories orders ${req.params.companyId}` ), getCompanyOrders);

router.get('/:companyId/orders/:orderId', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'order', (req, data) =>
    `Viewed order ${req.params.orderId}` ), getOrderDetails);
router.get('/:companyId/orders/:orderId/verify', auth, authorize('registration', ['r'], 'supplier'), logActivity('update', 'order', (req, data) =>
    `Verified order ${req.params.orderId}` ), verifyOrderPayment);
router.post('/:companyId/orders/:orderId/retry-payment', auth, authorize('registration', ['r'], 'supplier'), logActivity('update', 'order', (req, data) =>
    `Retried order ${req.params.orderId}` ), retryOrderPayment);


//applications
router.get('/:companyId/applications', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'applications', (req, data) =>
    `Viewed applications ${req.params.companyId}` ), getCompanyApplications);
router.get('/:companyId/applications/:applicationId', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'application', (req, data) =>
    `Viewed application ${req.params.applicationId}` ), getCompanyApplicationById);

router.put('/:companyId/applications/:applicationId', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'application', (req, data) =>
  `Updated application ${req.params.applicationId}` ), updateCompanyApplication);

//router.post('/:companyId/applications/:applicationId/documents', auth, authorize('registration', ['r'], 'supplier'), logActivity('create', 'application', (req, data) =>
//    `Uploaded document to application ${req.params.applicationId}` ), uploadApplicationFile);
//router.get('/:companyId/applications/:applicationId/documents/:documentId', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'application', (req, data) =>
//    `Viewed application document ${req.params.documentId}` ), downloadApplicationDocument);

// POST /api/supplier:companyId/applications/:applicationId/documents
router.post(
  '/:companyId/applications/:applicationId/documents/upload',
  auth,
  authorize('registration', ['r'], 'supplier'),
  logActivity('create', 'application', (req, data) =>
    `Uploaded document to application ${req.params.applicationId}` ),
  upload.single('file'), // Handles a single file upload from a field named 'file'
  uploadApplicationFile
);
// GET /supplier/684f1c17d7037f4d234d4641/applications/685357a9b3d82c7bbc62f5eb/fields/cert_of_incorporation/documents/BOA-2023-2024_047_2023_7_c3nbgqQ.pdf
// GET /api/supplier:companyId/applications/:applicationId/fields/:fieldName/documents/:fileName
router.get(
    '/:companyId/applications/:applicationId/fields/:fieldName/documents/:fileName',
    auth,
    authorize('registration', ['r'], 'supplier'),
    logActivity('view', 'application', (req, data) =>
      `Downloaded application document ${req.params.documentId}` ),
    downloadApplicationDocument
);



//jobs
router.post('/:companyId/jobs', auth, createBuyerJob);//create a job for specified categories
router.get('/:companyId?/jobs', auth, getBuyerJobs);
router.put('/jobs/:jobId', auth, updateBuyerJob);
router.delete('/:companyId/jobs/:jobId', auth, deleteBuyerJob);
//job categories
router.get('/jobs/:jobId/categories', auth, getJobCategories);
router.put('/jobs/:jobId/categories/:categoryId', auth, updateJobCategory);


export default router;
