import express from 'express';
import {getOrderDetails, getCompanyOrders, getAllOrders, verifyOrderPayment, retryOrderPayment} from '../controllers/paymentController.js';
import auth from '../middleware/auth.js';
import { getCompanyApplications, updateCompanyApplication, getCompanyApplicationById, downloadApplicationDocument, getCategoryApplications, getJobApplications,  downloadCategoryApplicationsZip, downloadCategoryApplicationsExcel, downloadCategorySectionDocuments, downloadJobApplicationsExcel } from '../controllers/applicationController.js'
import {getCompanyAcivities, getBuyerDashboardMetrics} from '../controllers/dashboardController.js'
import {logActivity} from '../middleware/activityLogger.js'
import {createBuyerJob, getBuyerJobs, getBuyerJobById, updateBuyerJob, deleteBuyerJob} from '../controllers/jobController.js';
import {getJobCategories, updateJobCategory, getJobCategoryById, getBuyerCategories, deleteJobCategory} from '../controllers/jobCategoryController.js';

import {getCompanyUsers, getCompanyRoles, updateCompanyUser, createCompanyUser, deleteCompanyUser } from '../controllers/buyerController.js'
import authorize from '../middleware/authorize.js';
import multer from 'multer';



const router = express.Router();

// Configure multer for in-memory storage
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

//router.get('/orders/', auth, getCompanyOrders);
   
  // Get all orders with pagination and filtering
  router.get('/:companyId/orders', auth, getAllOrders);
  router.get('/:companyId/jobs', auth, getBuyerJobs);


  router.get('/:companyId/metrics/basic', auth, getBuyerDashboardMetrics);
  router.get('/:companyId/metrics/analytic', auth, getBuyerDashboardMetrics);

  router.get('/:companyId/activities', auth, logActivity('view', 'acivities', (req, data) =>
    `Viewed activity logs ${req.params.companyId}` ), getCompanyAcivities);


  router.get('/:companyId/activities', auth, logActivity('view', 'acivities', (req, data) =>
    `Viewed activity logs ${req.params.companyId}` ), getCompanyAcivities);


  router.get('/:companyId/users', auth, authorize('settings', ['r'], 'buyer'), logActivity('view', 'users', (req, data) =>
    `Viewed users ${req.params.companyId}` ), getCompanyUsers);
  router.get('/:companyId/roles', auth, authorize('settings', ['r'], 'buyer'), logActivity('view', 'roles', (req, data) =>
    `Viewed roles ${req.params.companyId}` ), getCompanyRoles);
  router.put('/:companyId/users/:userId', auth, authorize('settings', ['r'], 'buyer'), logActivity('update', 'user', (req, data) =>
    `Updated user ${req.params.userId}` ), updateCompanyUser);
  router.post('/:companyId/users', auth, authorize('settings', ['r'], 'buyer'), logActivity('create', 'user', (req, data) =>
    `Created user ${req.body.name}` ), createCompanyUser);
  router.delete('/:companyId/users/:userId', auth, authorize('settings', ['r'], 'buyer'), deleteCompanyUser);



//applications
router.get('/:companyId/applications', auth, authorize('registration', ['r'], 'buyer'), logActivity('view', 'applications', (req, data) =>
  `Viewed applications ${req.params.companyId}` ), getCompanyApplications);
router.get('/:companyId/applications/:applicationId', auth, authorize('registration', ['r'], 'buyer'), logActivity('view', 'application', (req, data) =>
  `Viewed application ${req.params.applicationId}` ), getCompanyApplicationById);

router.put('/:companyId/applications/:applicationId', auth, authorize('registration', ['r'], 'buyer'), logActivity('view', 'application', (req, data) =>
`Updated application ${req.params.applicationId}` ), updateCompanyApplication);


// GET /api/supplier:companyId/applications/:applicationId/fields/:fieldName/documents/:fileName
router.get(
  '/:companyId/applications/:applicationId/fields/:fieldName/documents/:fileName',
  auth,
  authorize('registration', ['r'], 'supplier'),
  logActivity('view', 'application', (req, data) =>
    `Downloaded application document ${req.params.documentId}` ),
  downloadApplicationDocument
);

//jobs
router.post('/:companyId/jobs', auth, createBuyerJob);//create a job for specified categories
router.get('/:companyId/jobs/:jobId', auth, getBuyerJobById);//get a specific job by ID
router.put('/:companyId/jobs/:jobId', auth, updateBuyerJob);
router.delete('/:companyId/jobs/:jobId', auth, deleteBuyerJob);
//job categories
router.get('/:companyId/jobs/:jobId/categories', auth, getJobCategories);
router.get('/:companyId/jobcategories', auth, getBuyerCategories);

router.put('/:companyId/jobs/:jobId/categories/:categoryId', auth, updateJobCategory);
router.get('/:companyId/jobs/:jobId/categories/:categoryId', auth, getJobCategoryById);

/////          `${BACKEND_API_URL}/api/buyer/${companyId}/categories/${categoryId}`,

// ===== BUYER APPLICATION VIEWING ROUTES =====

// Get all applications for a specific job category (Buyer view - read only)
router.get('/:companyId/categories/:categoryId/applications',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getCategoryApplications
);

// Get all applications for a specific job category (Buyer view - read only)
router.get('/:companyId/jobs/:jobId/applications',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getJobApplications
);

// Download application document (Buyer view)
router.get('/:companyId/applications/:applicationId/fields/:fieldName/documents/:fileName',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('view', 'application', (req, data) =>
        `Downloaded document ${req.params.fileName} from application ${req.params.applicationId}`),
    downloadApplicationDocument
);

// ===== BUYER DOWNLOAD AND REPORT ROUTES =====

// Download all applications for a category as ZIP (Buyer view)
router.get('/:companyId/categories/:categoryId/applications/download/zip',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded ZIP of applications for category ${req.params.categoryId}`),
    downloadCategoryApplicationsZip
);

// Download applications Excel report for a category (Buyer view)
router.get('/:companyId/categories/:categoryId/applications/download/excel',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded Excel report for category ${req.params.categoryId}`),
    downloadCategoryApplicationsExcel
);

// Download documents from a specific section for all applicants (Buyer view)
router.get('/:companyId/categories/:categoryId/applications/download/section/:sectionName',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded section ${req.params.sectionName} documents for category ${req.params.categoryId}`),
    downloadCategorySectionDocuments
);

// Download job-level applications report (Buyer view)
router.get('/:companyId/jobs/:jobId/applications/download/excel',
    auth,
    authorize('registration', ['r'], 'buyer'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded job report for job ${req.params.jobId}`),
    downloadJobApplicationsExcel
);

export default router;