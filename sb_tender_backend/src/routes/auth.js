import express from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/sp_user.js';
import NotificationHelper from '../helpers/notificationHelper.js'
import bcrypt from 'bcryptjs'; 
const router = express.Router();

import SpCompany from '../models/sp_company.js';
import SpRole from '../models/sp_role.js';
import SpUserRole from '../models/sp_user_role.js';
import {getUserCompanyRoles}from '../utils/user.js';
import FederatedCredential from '../models/FederatedCredential.js';
import { Strategy as GoogleStrategy } from 'passport-google-oidc';
import passport from "passport";
//import { ConfigurationServicePlaceholders } from 'aws-sdk/lib/config_service_placeholders.js';
const TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];

export const ensureDefaultRolesExist = async (context, createdBy) => {
  const existingRoles = await SpRole.find({ context });
  const existingKeys = new Set(existingRoles.map(r => r.type));

  const missingTypes = TYPES.filter(type => !existingKeys.has(type));

  if (missingTypes.length === 0) return; // All roles exist

  const newRoles = missingTypes.map(type => ({
    name: `${context.toUpperCase()} - ${type}`,
    context,
    type,
    permissions: DEFAULT_PERMISSIONS,
    description: `Auto-created role for ${context} - ${type}`,
    createdBy,
  }));

  await SpRole.insertMany(newRoles);
};


// Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: process.env['GOOGLE_CLIENT_ID'],
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: process.env.GOOGLE_REDIRECT_URL,
  scope: ['email', 'profile'],
  passReqToCallback: true // To access req.query in the verify function
}, async function verify(req, issuer, profile, cb) {
  try {
    console.log('Profile from Google:', profile);
    const userEmail = profile.emails[0].value;
    console.log(req.query);
    const { type, companyName, phone } = req.query; // Get query params
    
    // Check for existing federated credential
    let federatedCredential = await FederatedCredential.findOne({
      provider: issuer,
      subject: profile.id
    }).populate('userId');

    // If credential exists, return user
    if (federatedCredential) {
      if (!federatedCredential.userId) {
        console.error('GOOGLE_AUTH_ERROR: Associated user not found for credential. Provider:', issuer, 'Subject:', profile.id);
        return cb(new Error('Associated user not found'));
      }
      return cb(null, federatedCredential.userId);
    }

    // Check if user exists by email
    let user = await User.findOne({ email: userEmail });

    // Handle registration flow if type=register
    if (type === 'register' && companyName) {
      if (user) { 
        console.error('GOOGLE_AUTH_ERROR: Email already registered during registration attempt:', userEmail);
        return cb(new Error('Email already registered'));
      }

      // Create new user (similar to register route)
      const apiKey = await generateUniqueUID();
      user = new User({
        firstName: profile.name?.givenName || profile.displayName.split(' ')[0],
        lastName: profile.name?.familyName || profile.displayName.split(' ')[1] || '',
        email: userEmail,
        phone: phone,
        apiKey: apiKey,
        type: 'supplier'
      });
      await user.save();

      // Create company (similar to register route)
      const company = new SpCompany({
        name: companyName,
        registrationNumber: `REG-${Date.now()}-${Math.random().toString(36).substring(2, 7).toUpperCase()}`,
        type: 'supplier',
        address: 'N/A',
        contactPerson: profile.displayName,
        email: userEmail,
        phone: phone,
        createdBy: user._id
      });
      await company.save();

      // Ensure default roles and assign them
      await ensureDefaultRolesExist(company.type, user._id);
      const supplierRoles = await SpRole.find({ context: 'supplier' });
      if (supplierRoles.length > 0) {
        const SpUserRoles = supplierRoles.map(role => ({
          userId: user._id,
          companyId: company._id,
          roleId: role._id,
        }));
        await SpUserRole.insertMany(SpUserRoles);
      }

    } else if (!user) {
      // For login flow, user must exist
      console.error('GOOGLE_AUTH_ERROR: User not found during login attempt. Email:', userEmail, 'Query type:', type);
      return cb(new Error('User not found. Please register first.'));
    }

    // Create federated credential
    federatedCredential = new FederatedCredential({
      provider: issuer,
      subject: profile.id,
      userId: user._id,
      profile: profile
    });
    await federatedCredential.save();

    return cb(null, user);

  } catch (err) {
    console.error('Google OAuth error:', err);
    return cb(err);
  }
}));

// Google auth routes
router.get('/login/federated/google', passport.authenticate('google'));


/*
router.get('/oauth2/redirect/google', passport.authenticate('google', {
  successRedirect: '/dashboard',
  failureRedirect: '/login'
}));

*/
router.get('/oauth2/redirect/google', passport.authenticate('google', {
  failureRedirect: `${process.env.NEXT_FRONTEND_URL}/auth/login?error=callback-error`,
  session: false
}), async (req, res) => {
  try {
    // Generate JWT token
    const token = jwt.sign({ userId: req.user._id }, process.env.JWT_SECRET, { expiresIn: '72h'});
    
    // Get user with company/roles data
    const userDataWithRoles = await getUserCompanyRoles(req.user._id);
    console.log(userDataWithRoles);
     // Set the JWT as an HttpOnly cookie
     res.cookie('token', token, {
      httpOnly: true, // Crucial for security
      secure: process.env.NODE_ENV === 'production', // Use secure in production (HTTPS)
      sameSite: 'Lax', // Protects against CSRF in some cases
      maxAge: 3600000 // 1 hour (in milliseconds)
    });

    // Redirect the browser back to your Next.js dashboard/success page
    console.log(`${process.env.NEXT_FRONTEND_URL}`);


    if (userDataWithRoles?.companies?.length > 0) {
      const company = userDataWithRoles.companies[0];
      const redirectUrl = `${process.env.NEXT_FRONTEND_URL}/${company.type}/${company._id}/dashboard`;
      console.log('Redirecting to dashboard:', redirectUrl);
      return res.redirect(redirectUrl);
    } else {
      const redirectUrl = `${process.env.NEXT_FRONTEND_URL}/dashbooard`;
      console.log('Redirecting to complete registration:', redirectUrl);
      res.redirect(redirectUrl);
    }



  } catch (error) {
    console.error('Google callback error:', error);
    res.redirect(`${process.env.NEXT_FRONTEND_URL}/auth/login?error=auth_failed`);
   // res.status(500).json({ message: 'Google authentication failed', error: error.message });
  }
});




router.post('/register', async (req, res) => {
  console.log('------------------------------------');
  console.log('Register endpoint hit');
  console.log('Request body:', req.body);

  const { firstName, lastName, email, password, phone, companyName } = req.body;

  // --- Input Validation Logs ---
  if (!firstName || !lastName || !email || !password || !phone || !companyName) {
      console.log('Validation failed: Missing required fields');
      return res.status(400).json({ error: 'Missing required fields' }); // Use message for consistency
   }
   console.log('Input validation passed');


  try {
    // --- Check for existing user email/phone ---
    console.log('Checking for existing email:', email);
    const emailExists = await User.findOne({ email });
    console.log('Email exists check result:', !!emailExists);

    console.log('Checking for existing phone:', phone);
    const phoneExists = await User.findOne({ phone });
    console.log('Phone exists check result:', !!phoneExists);


    if (phoneExists) {
      console.error('Registration failed: Phone number already exists');
      return res.status(409).json({ error: 'Phone number already exists' }); // Use 409 for conflict
    }
    if (emailExists) {
      console.error('Registration failed: Email already exists');
      return res.status(409).json({ error: 'Email already exists' }); // Use 409 for conflict
    }
    console.log('Existing user/phone checks passed');


    // --- Step 1: Create user ---
    console.log('Attempting to create user...');
    // Assuming you handle password hashing before creating the user model instance
    // You need to hash the password here before saving
    const salt = await bcrypt.genSalt(10); // Assuming bcrypt is imported
    const hashedPassword = await bcrypt.hash(password, salt);

    const user = new User({
        firstName,
        lastName,
        email,
        password: hashedPassword, // Save hashed password
        phone,
        type: 'supplier' // Assuming 'supplier' type for the user creating the company
    });
    await user.save();
    console.log(`Step 1 Successful: User created with ID: ${user._id}`);
    console.log('Created user object:', user);


    // --- Step 2: Create company with minimal required fields ---
    console.log('Attempting to create company...');
     // Note: You need to ensure companyType is handled correctly, possibly derived from user type or input
     const companyType = 'supplier'; // Or derive from user.type or request body

    const company = new SpCompany({
      name: companyName,
      registrationNumber: `REG-${Date.now()}-${Math.random().toString(36).substring(2, 7).toUpperCase()}`, // More unique reg number
      type: companyType, // Use the determined company type
      address: 'N/A', // Provide actual data if possible
      contactPerson: `${firstName} ${lastName}`, // Provide actual data if possible
      email: email, // Using user's email for company email
      phone: phone, // Using user's phone for company phone
      createdBy: user._id,
       // Add other required fields if your schema requires them on creation and they are not set by default
       // E.g., verified: false (if not default), status: 'active' (if not default)
    });
    await company.save();
    console.log(`Step 2 Successful: Company created with ID: ${company._id}, Name: ${company.name}, Type: ${company.type}`);
    console.log('Created company object:', company);


    // --- Ensure default roles exist (if applicable) ---
    // This function should ideally run during setup, not every registration.
    // If roles might not exist, ensure this function creates them.
    console.log(`Ensuring default roles exist for company type: ${company.type}...`);
    // Assuming ensureDefaultRolesExist is defined elsewhere and works
     await ensureDefaultRolesExist(company.type, user._id);
    console.log('Ensure default roles check/creation finished.');


    // --- Step 3: Assign supplier roles to user ---
    console.log('Attempting to find supplier roles for assignment...');
    // Criteria: Roles with context matching the company type, OR context 'supplier' if that's how your supplier roles are structured
    // Based on your previous code and requirement "supplier user role for all roles", let's find roles with context matching company.type AND context 'supplier'. This might mean finding roles with *either* context. Or maybe find roles with name 'Company Supplier' and context matching company.type?
    // Let's refine based on your earlier code finding `context: 'supplier'`:
    const supplierRoles = await SpRole.find({ context: 'supplier' });
     console.log(`Step 3a: Found ${supplierRoles.length} roles with context 'supplier'.`);
     console.log('Found supplier roles objects:', supplierRoles.map(r => ({ _id: r._id, name: r.name, context: r.context, type: r.type })));


     // *** Potential Issue: If you intended to find roles with context matching company.type,
     // the query should be: const supplierRoles = await SpRole.find({ context: company.type });
     // Or if you have a specific supplier role name: const supplierRoles = await SpRole.find({ name: 'Company Supplier', context: company.type });
     // The current query `context: 'supplier'` finds roles specifically defined for an 'supplier' context,
     // which might be site supplier roles, not necessarily company-specific supplier roles.
     // Review your SpRole data to confirm which query is correct for the role you want to assign.
     // For now, proceeding with `context: 'supplier'` as per your code.


    if (supplierRoles.length === 0) {
        console.warn(`No supplier roles found with context 'supplier'. Cannot assign any roles.`);
         // Decide if registration should fail here if no supplier role can be assigned.
         // For now, it will proceed without assigning roles.
    } else {
        console.log(`Mapping ${supplierRoles.length} found supplier roles to SpUserRole objects.`);
        const SpUserRoles = supplierRoles.map(role => ({
          userId: user._id,
          companyId: company._id,
          roleId: role._id,
        }));
        console.log('Step 3b: Prepared SpUserRoles array for insertion:', SpUserRoles);
        console.log('Number of SpUserRoles entries to insert:', SpUserRoles.length);


        console.log('Attempting to insert SpUserRoles into the database...');
        // Ensure your model is correctly imported as SpUserRole, not SpUserRole
        await SpUserRole.insertMany(SpUserRoles);
        console.log(`Step 3 Successful: Successfully inserted ${SpUserRoles.length} SpUserRole documents.`);
    }

    // Send welcome notification using helper
    try {
      await NotificationHelper.sendWelcomeEmail(user, company._id);
      console.log('Welcome email sent successfully');
    } catch (notificationError) {
      console.error('Failed to send welcome email:', notificationError);
      // Don't fail the registration if notification fails
    }


    // --- Step 4: Prepare and Return Response ---
    console.log('Generating JWT token...');
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '72h' });
    console.log('JWT token generated.');


    console.log('Attempting to fetch user data with company/roles using getUserCompanyRoles...');
    const userDataWithRoles = await getUserCompanyRoles(user._id);
    console.log('getUserCompanyRoles finished.');
    console.log('Result of getUserCompanyRoles:', userDataWithRoles ? 'Data fetched' : 'No data fetched');


    if (!userDataWithRoles || !userDataWithRoles.companies || userDataWithRoles.companies.length === 0) {
      console.error(`Could not fetch complete company/role data for user ID: ${user._id}. Check getUserCompanyRoles logic and if UserRoles were actually created and linked correctly.`);
       console.log('Sending minimal user info in response.');
        // Send minimal user info if getUserCompanyRoles failed or returned no companies/roles
         return res.status(200).json({
            token,
            user: {
                _id: user._id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                type: user.type,
                companies: [], // Explicitly empty if not found/linked
            },
            message: 'Registration successful, but could not retrieve company/role details.'
         });
    }

     console.log('Step 4 Successful: User data with companies/roles fetched.');
     console.log('Responding with token and full user data.');
     // Respond with token and structured user data including companies and roles
     res.status(201).json({ // Use 201 for successful creation
         token,
         user: userDataWithRoles, // Contains user details, the new company, and assigned roles
         message: 'Registration successful'
     });

} catch (error) {
    console.error('------------------------------------');
    console.error('Error during registration process:');
    console.error(error); // Log the full error object

     // Log details if it's a Mongoose duplicate key error
    if (error.code === 11000) {
        console.error('Caught Mongoose duplicate key error (code 11000).');
        const field = Object.keys(error.keyValue)[0];
        const value = error.keyValue[field];
         let message = `Duplicate value for ${field}: ${value}`;
         if (field === 'email') message = 'Email already registered';
         if (field === 'phone') message = 'Phone number already exists';
         if (field === 'registrationNumber') message = 'Company with this registration number already exists';
        console.error(`Duplicate field: ${field}, value: ${value}`);
        return res.status(409).json({ message, error: error.message });
    }

    // Log details for other errors
     console.error('Caught other error:', error.name, error.message);

    // Handle potential errors from helper functions that threw with a status
     if (error.status) {
         console.error(`Error seems to have custom status: ${error.status}`);
         return res.status(error.status).json({ message: error.message || 'An error occurred', error: error.message });
     }


    // Generic internal server error
    res.status(500).json({ message: 'Registration failed due to a server error', error: error.message });
  } finally {
      console.log('------------------------------------');
      console.log('Register endpoint processing finished.');
  }
});

router.post('/login', async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Assuming you are using bcrypt for password comparison
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
         return res.status(401).json({ message: 'Invalid credentials' });
    }

    // --- Authentication successful ---

    // Generate JWT token
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '72h' });

    // Fetch user's company and role data using the reusable function
    const userDataWithRoles = await getUserCompanyRoles(user._id);

    if (!userDataWithRoles || user.type === 'admin') {
         // This case means user was found but details couldn't be fetched - unlikely but possible
         console.error(`Could not fetch company/role data for user ID or the user is an admin: ${user._id}`);
         // Decide how to handle: maybe still send token but no user data? Or error?
         // Sending minimal info might be safer if company data is non-critical for basic access.
         // For now, let's send the token and a simplified user object.
          return res.status(200).json({
             token,
             user: { // Send minimal user info
                 _id: user._id,
                 firstName: user.firstName,
                 lastName: user.lastName,
                 email: user.email,
                 type: user.type, // Include user type (supplier, buyer, supplier)
             }
          });
    }
    console.log('userDataWithRoles', userDataWithRoles);



    // Respond with token and structured user data including companies and roles
    res.status(200).json({
        token,
        user: userDataWithRoles // Contains user details, companies, and roles
    });

} catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Login failed', error: error.message });
}


});


router.post('/setup/buyer', async (req, res) => {
  console.log('------------------------------------');
  console.log('Buyer setup endpoint hit');
  console.log('Request body:', req.body);

  const { email, password, companyId } = req.body;

  // Input validation
  if (!email || !password || !companyId) {
    console.log('Validation failed: Missing required fields');
    return res.status(400).json({ error: 'Missing required fields' });
  }
  console.log('Input validation passed');

  try {
    // Step 1: Verify company exists with the provided ID
    console.log(`Checking if company exists with ID: ${companyId}`);
    const company = await SpCompany.findById(companyId);
    
    if (!company) {
      console.error(`Company with ID ${companyId} not found`);
      return res.status(404).json({ error: 'Company not found' });
    }
    console.log(`Company found: ${company.name}`);

    // Step 2: Check if the email is associated with the company
    if (company.email !== email) {
      console.error(`Email ${email} is not associated with company ${companyId}`);
      return res.status(400).json({ error: 'Email is not associated with this company' });
    }
    console.log('Email verification passed');

    // Step 3: Check if user already exists with this email
    console.log(`Checking if user exists with email: ${email}`);
    let user = await User.findOne({ email });
    let isNewUser = false;

    if (user) {
      console.log(`User found with ID: ${user._id}`);
      if (!user.password) {
        // User exists but doesn't have a password yet (e.g., was pre-created)
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        
        user.password = hashedPassword;
        await user.save();
        console.log(`Updated password for existing user ID: ${user._id}`);
      }
    } else {
      console.log('User not found, creating new user');
      isNewUser = true;
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Create new user
      user = new User({
        firstName: company.contactPerson?.split(' ')[0] || 'Buyer',
        lastName: company.contactPerson?.split(' ')[1] || 'User',
        email,
        password: hashedPassword,
        phone: company.phone || '',
        type: 'buyer'
      });
      
      await user.save();
      console.log(`New user created with ID: ${user._id}`);
    }


    // Step 4: Get or ensure buyer roles exist
    console.log('Checking for buyer roles');
    const buyerRoles = await SpRole.find({ context: 'buyer' });
    
    if (buyerRoles.length === 0) {
      console.log('No buyer roles found, creating default buyer roles');
      // Define default buyer role types and permissions
      const BUYER_TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
      const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];
      
      // Create default buyer roles
      const newRoles = BUYER_TYPES.map(type => ({
        name: `BUYER - ${type}`,
        context: 'buyer',
        type,
        permissions: DEFAULT_PERMISSIONS,
        description: `Auto-created role for buyer - ${type}`,
        createdBy: user._id,
      }));
      
      await SpRole.insertMany(newRoles);
      console.log(`Created ${newRoles.length} default buyer roles`);
      
      // Refresh buyer roles list
      const freshBuyerRoles = await SpRole.find({ context: 'buyer' });
      buyerRoles.push(...freshBuyerRoles);
    }

    // Step 5: Check if user is already associated with the company
    console.log(`Checking if user ${user._id} is already associated with company ${companyId}`);
    const existingUserRole = await SpUserRole.findOne({ 
      userId: user._id,
      companyId: company._id
    });

    if (!existingUserRole) {
      console.log('No existing user-company association found, creating new associations');
      
      // Associate user with company by creating user roles
      const userRolesToCreate = buyerRoles.map(role => ({
        userId: user._id,
        companyId: company._id,
        roleId: role._id,
      }));
      
      await SpUserRole.insertMany(userRolesToCreate);
      console.log(`Created ${userRolesToCreate.length} role associations for user`);
    } else {
      console.log('User is already associated with this company');
    }

    // Send welcome notification using helper
    try {
      await NotificationHelper.sendWelcomeEmail(user, company._id);
      console.log('Welcome email sent successfully');
    } catch (notificationError) {
      console.error('Failed to send welcome email:', notificationError);
      // Don't fail the registration if notification fails
    }

    // Step 6: Generate token and prepare response
    console.log('Generating JWT token');
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '72h' });
    
    // Get complete user data with company/roles
    console.log('Fetching complete user data with companies/roles');
    const userDataWithRoles = await getUserCompanyRoles(user._id);
    
    if (!userDataWithRoles || !userDataWithRoles.companies || userDataWithRoles.companies.length === 0) {
      console.error(`Could not fetch complete company/role data for user ID: ${user._id}`);
      // Provide minimal user info response
      return res.status(200).json({
        token,
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          type: user.type,
          companies: [],
        },
        message: 'Buyer setup successful, but could not retrieve company/role details.'
      });
    }

    // Return successful response with token and user data
    console.log('Buyer setup completed successfully');
    res.status(200).json({
      token,
      user: userDataWithRoles,
      message: isNewUser ? 'Buyer account created successfully' : 'Buyer account updated successfully'
    });

  } catch (error) {
    console.error('Error during buyer setup:', error);
    
    // Handle duplicate key errors
    if (error.code === 11000) {
      console.error('Caught Mongoose duplicate key error (code 11000).');
      const field = Object.keys(error.keyValue)[0];
      const value = error.keyValue[field];
      let message = `Duplicate value for ${field}: ${value}`;
      return res.status(409).json({ error: message });
    }
    
    // Handle specific error status if available
    if (error.status) {
      return res.status(error.status).json({ error: error.message || 'An error occurred' });
    }
    
    // Generic server error
    res.status(500).json({ error: 'Server error during buyer setup' });
  } finally {
    console.log('------------------------------------');
    console.log('Buyer setup endpoint processing finished.');
  }
});





// Helper function to generate unique API key (same as in example)
async function generateUniqueUID() {
  let apiKey;
  let isUnique = false;
  
  while (!isUnique) {
    apiKey = crypto.randomBytes(32).toString('hex');
    const existingUser = await User.findOne({ apiKey });
    if (!existingUser) isUnique = true;
  }
  return apiKey;
}

// POST /auth/forgot-password - Request password reset
router.post('/auth/forgot-password', async (req, res) => {
  try {
    console.log('🔐 [FORGOT_PASSWORD] Request received');
    const { email } = req.body;

    if (!email) {
      console.log('❌ [FORGOT_PASSWORD] Email is required');
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.log('❌ [FORGOT_PASSWORD] Invalid email format');
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    console.log(`🔍 [FORGOT_PASSWORD] Looking for user with email: ${email}`);

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      console.log('❌ [FORGOT_PASSWORD] User not found');
      // Don't reveal if email exists or not for security
      return res.status(200).json({
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.'
      });
    }

    console.log(`✅ [FORGOT_PASSWORD] User found: ${user._id}`);

    // Generate reset token
    const resetToken = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        type: 'password_reset'
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' } // Token expires in 1 hour
    );

    // Save reset token to user (optional - for additional security)
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = new Date(Date.now() + 3600000); // 1 hour from now
    await user.save();

    console.log(`🔑 [FORGOT_PASSWORD] Reset token generated for user: ${user._id}`);

    // Create reset link
    const resetLink = `${process.env.FRONTEND_URL || 'http://localhost:3001'}/auth/reset-password?token=${resetToken}`;

    // Send password reset email
    try {
      const emailData = {
        to: user.email,
        subject: 'Password Reset Request - TenderAsili',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">Password Reset Request</h2>
            <p>Dear ${user.firstName} ${user.lastName},</p>
            <p>We received a request to reset your password for your TenderAsili account.</p>
            <p>Click the button below to reset your password:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}"
                 style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Reset Password
              </a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${resetLink}</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
            <p>If you didn't request this password reset, please ignore this email.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 12px;">
              This is an automated message from TenderAsili. Please do not reply to this email.
            </p>
          </div>
        `
      };

      await NotificationHelper.sendEmail(emailData);
      console.log(`📧 [FORGOT_PASSWORD] Reset email sent to: ${user.email}`);

    } catch (emailError) {
      console.error('❌ [FORGOT_PASSWORD] Failed to send email:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Failed to send password reset email. Please try again later.'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Password reset link has been sent to your email address.'
    });

  } catch (error) {
    console.error('💥 [FORGOT_PASSWORD] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error. Please try again later.'
    });
  }
});

// POST /auth/reset-password - Reset password with token
router.post('/auth/reset-password', async (req, res) => {
  try {
    console.log('🔐 [RESET_PASSWORD] Request received');
    const { token, newPassword, confirmPassword } = req.body;

    if (!token || !newPassword || !confirmPassword) {
      console.log('❌ [RESET_PASSWORD] Missing required fields');
      return res.status(400).json({
        success: false,
        message: 'Token, new password, and confirm password are required'
      });
    }

    if (newPassword !== confirmPassword) {
      console.log('❌ [RESET_PASSWORD] Passwords do not match');
      return res.status(400).json({
        success: false,
        message: 'Passwords do not match'
      });
    }

    if (newPassword.length < 6) {
      console.log('❌ [RESET_PASSWORD] Password too short');
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      });
    }

    console.log('🔍 [RESET_PASSWORD] Verifying token');

    // Verify and decode token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);

      if (decoded.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }
    } catch (tokenError) {
      console.log('❌ [RESET_PASSWORD] Invalid or expired token');
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    console.log(`✅ [RESET_PASSWORD] Token valid for user: ${decoded.userId}`);

    // Find user and verify token
    const user = await User.findById(decoded.userId);

    if (!user) {
      console.log('❌ [RESET_PASSWORD] User not found');
      return res.status(400).json({
        success: false,
        message: 'Invalid reset token'
      });
    }

    // Check if token matches and hasn't expired (additional security)
    if (user.passwordResetToken !== token ||
        (user.passwordResetExpires && user.passwordResetExpires < new Date())) {
      console.log('❌ [RESET_PASSWORD] Token mismatch or expired');
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    console.log(`🔑 [RESET_PASSWORD] Updating password for user: ${user._id}`);

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user password and clear reset token
    user.password = hashedPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    user.updatedAt = new Date();
    await user.save();

    console.log(`✅ [RESET_PASSWORD] Password updated successfully for user: ${user._id}`);

    // Send confirmation email
    try {
      const emailData = {
        to: user.email,
        subject: 'Password Reset Successful - TenderAsili',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #16a34a;">Password Reset Successful</h2>
            <p>Dear ${user.firstName} ${user.lastName},</p>
            <p>Your password has been successfully reset for your TenderAsili account.</p>
            <p>You can now log in with your new password.</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3001'}/auth/signin"
                 style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Sign In
              </a>
            </div>
            <p>If you didn't reset your password, please contact our support team immediately.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 12px;">
              This is an automated message from TenderAsili. Please do not reply to this email.
            </p>
          </div>
        `
      };

      await NotificationHelper.sendEmail(emailData);
      console.log(`📧 [RESET_PASSWORD] Confirmation email sent to: ${user.email}`);

    } catch (emailError) {
      console.error('⚠️ [RESET_PASSWORD] Failed to send confirmation email:', emailError);
      // Don't fail the request if confirmation email fails
    }

    res.status(200).json({
      success: true,
      message: 'Password has been reset successfully. You can now log in with your new password.'
    });

  } catch (error) {
    console.error('💥 [RESET_PASSWORD] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error. Please try again later.'
    });
  }
});

// Export the router as the default export
export default router;
