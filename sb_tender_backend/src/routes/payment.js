import express from 'express';
import {mpesaCheckout, mpesaCheckoutWebhook, cardCheckout, cardCheckoutWebhook } from '../controllers/paymentController.js';
import auth from '../middleware/auth.js';

const router = express.Router();

router.post('/mobile', auth, mpesaCheckout);
router.post('/stk/webhook', mpesaCheckoutWebhook);

router.post('/card', auth, cardCheckout);
router.post('/card/webhook',cardCheckoutWebhook);


export default router;