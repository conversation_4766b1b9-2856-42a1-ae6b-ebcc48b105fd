import express from 'express';
import tenderController from '../controllers/tenderController.js';
import auth from '../middleware/auth.js';
import authorize from '../middleware/authorize.js';

const router = express.Router();

// Buyer routes
router.post('/', auth, tenderController.createTender);
router.put('/:id', auth, tenderController.updateTender);
router.delete('/:id', auth, tenderController.deleteTender);

// Public routes
router.get('/', auth, tenderController.getTenders);
router.get('/:id', auth, tenderController.getTenderById);

// Example: Create a tender (requires 'w' permission for type 't' and context 'buyer')
router.post('/create/:companyId', authorize('t', ['w'], 'buyer'), async (req, res) => {
  try {
    // ...existing logic to create a tender...
    res.status(201).json({ message: 'Tender created successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Example: View tenders (requires 'r' permission for type 't')
router.get('/view/:companyId', authorize('t', ['r']), async (req, res) => {
  try {
    // ...existing logic to fetch tenders...
    res.status(200).json({ tenders });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Example: Delete a tender (requires 'd' permission)
router.delete('/delete/:companyId/:tenderId', authorize(['comp', 'd']), async (req, res) => {
  try {
    // ...existing logic to delete a tender...
    res.status(200).json({ message: 'Tender deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Export the router as the default export
export default router;