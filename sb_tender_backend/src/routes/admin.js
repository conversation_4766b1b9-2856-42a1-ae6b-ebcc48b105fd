import express from 'express';
import multer from 'multer';
import { createCompany, getCompanies, getCompanyById, updateCompany, deleteCompany, downloadCompanyDocument } from '../controllers/companyController.js';
import { createCategory, getCategories, getCategoryById, updateCategory, deleteCategory } from '../controllers/categoryController.js';
import {createCategoryTemplate, getCategoryTemplates, getCategoryTemplateById, updateCategoryTemplate, deleteCategoryTemplate, getCategoryTemplate} from '../controllers/categoryTemplateController.js';
import {createBuyerCategory, getBuyerCategories, updateBuyerCategory, deleteBuyerCategory} from '../controllers/buyerCategoryController.js';
import {getBuyerCategoryTemplate, updateBuyerCategoryTemplate} from '../controllers/buyerCategoryTemplateController.js';
import {createBuyer<PERSON>ob, getBuyerJobs, getBuyerJobById, updateBuyerJob, deleteBuyerJob} from '../controllers/jobController.js';
import {getJobCategories,getJobCategoryById, updateJobCategory, deleteJobCategory} from '../controllers/jobCategoryController.js';
import { getSupplierApplications, getSupplierApplicationById, adminReviewApplication, getCategoryApplications, getJobApplications, downloadApplicationDocument, downloadCategoryApplicationsZip, downloadCategoryApplicationsExcel, downloadCategorySectionDocuments, downloadJobApplicationsExcel } from '../controllers/applicationController.js';
////import {createUser, getAllUsers, getUserById, updateUser, deleteUser} from '../controllers/userController.js'
//import {createBuyerCategory, getBuyerCategories, getBuyerCategoryById, updateBuyerCategory, deleteBuyerCategory} from '../controllers/buyerCategoryController.js';
//import {createBuyerCategoryTemplate, getBuyerCategoryTemplates, getBuyerCategoryTemplateById, updateBuyerCategoryTemplate, deleteBuyerCategoryTemplate} from '../controllers/buyerCategoryTemplateController.js';
import auth from '../middleware/auth.js';
import {getCompanyUsers, getCompanyRoles, updateCompanyUser, createCompanyUser, deleteCompanyUser } from '../controllers/userController.js'
import {getOrderDetails, getAllOrders, updateOrder, verifyOrderPayment, retryOrderPayment, getAllPayments, updatePayment, createManualPayment } from '../controllers/paymentController.js';
import {getSystemAcivities } from '../controllers/dashboardController.js'
import {logActivity} from '../middleware/activityLogger.js'
import authorize from '../middleware/authorize.js';

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

router.post(
  '/companies',
  auth,
  upload.fields([
    { name: 'logoUrl', maxCount: 1 },
    { name: 'contract', maxCount: 1 },
    { name: 'kraPinCertificate', maxCount: 1 },
    { name: 'certificateOfIncorporation', maxCount: 1 },
    { name: 'tradingLicense', maxCount: 1 },
    { name: 'companyCR12', maxCount: 1 },
    { name: 'taxComplianceCertificate', maxCount: 1 },
  ]),
  createCompany
);

router.get('/companies', auth, getCompanies);
//router.get('/companies/:id', auth, getCompanyById);
router.put(
  '/companies/:id',
  auth,
  upload.fields([
    { name: 'logoUrl', maxCount: 1 },
    { name: 'contract', maxCount: 1 },
    { name: 'kraPinCertificate', maxCount: 1 },
    { name: 'certificateOfIncorporation', maxCount: 1 },
    { name: 'tradingLicense', maxCount: 1 },
    { name: 'companyCR12', maxCount: 1 },
    { name: 'taxComplianceCertificate', maxCount: 1 },
  ]),
  updateCompany
);
router.delete('/companies/:id', auth, deleteCompany);
router.get('/companies/documents/download', auth, downloadCompanyDocument);


router.get('/activities', auth, getSystemAcivities);

router.get('/categories/:id/template', auth, getCategoryTemplate);

router.post('/categories', auth, createCategory);
router.get('/categories', auth, getCategories);
//router.get('/categories/:id', auth, getCategoryById);
router.put('/categories/:id', auth, updateCategory);
router.delete('/categories/:id', auth, deleteCategory);


// Category template routes
router.post('/categories/templates', auth, createCategoryTemplate);
router.get('/categories/templates', auth, getCategoryTemplates);
router.put('/categories/:cid/templates/:tid', auth, updateCategoryTemplate);
router.delete('/categories/templates/:id', auth, deleteCategoryTemplate);


router.post('/companies/:companyId/categories', auth, createBuyerCategory);
router.get('/companies/:companyId/categories', auth, getBuyerCategories);
//router.get('/categories/:id', auth, getCategoryById);
router.put('/companies/:companyId/categories/:categoryId', auth, updateBuyerCategory);
router.delete('/companies/:companyId/categories/:id', auth, deleteBuyerCategory);

// Buyer category template routes
router.get('/companies/:companyId/categories/:categoryId/template', auth, getBuyerCategoryTemplate);
router.put('/companies/:companyId/categories/:categoryId/templates/:templateId', auth, updateBuyerCategoryTemplate);

//buyer jobs

router.post('/companies/:companyId/jobs', auth, createBuyerJob);//create a job for specified categories
router.get('/companies/:companyId?/jobs', auth, getBuyerJobs);
router.get('/jobs/:jobId', auth, getBuyerJobById);//get a specific job by ID (admin can access any job)
router.get('/buyer/:companyId/jobs/:jobId', auth, getBuyerJobById);//get a specific job by ID

router.put('/jobs/:jobId', auth, updateBuyerJob);
router.delete('/companies/:companyId/jobs/:jobId', auth, deleteBuyerJob);

router.get('/jobs/:jobId/categories', auth, getJobCategories);
router.get('/buyer/:companyId/jobs/:jobId/categories', auth, getJobCategories); /////
router.get('/buyer/:companyId/jobs/:jobId/categories/:categoryId', auth, getJobCategoryById); /////

router.put('/jobs/:jobId/categories/:categoryId', auth, updateJobCategory);
//pre>Cannot GET /api/admin/buyer/684bef15975c07c04b5ea969/jobs/68529fd06fe9c5e7ce55bb9b/categories/68529fd36fe9c5e7ce55bca6</pre>

//router.post('/users', auth, createUser);
//router.get('/users', auth, getAllUsers);
////router.get('/users/:id', auth, getUserById);
//router.put('/users/:userId', auth, updateUser);
//router.delete('/users/:userId', auth, deleteUser);


router.get('/companies/:companyId/users', auth, getCompanyUsers);
router.get('/companies/:companyId/roles', auth, getCompanyRoles);
router.put('/companies/:companyId/users/:userId', auth, updateCompanyUser);
router.post('/companies/:companyId/users', auth, createCompanyUser);
router.delete('/companies/:companyId/users/:userId', auth, deleteCompanyUser);


// Get all orders with pagination and filtering
router.get('/orders', auth, logActivity('view', 'orders', (req, data) =>
    `Admin Viewed sorders` ), getAllOrders);
router.put('/orders/:orderId', auth, logActivity('view', 'orders', (req, data) =>
    `Admin Updated an order ${req.params.orderId}` ), updateOrder);
router.get('/orders/:orderId', auth, authorize('registration', ['r'], 'supplier'), logActivity('view', 'order', (req, data) =>
    `Admin Viewed order ${req.params.orderId}` ), getOrderDetails);
router.get('/orders/:orderId/verify', auth, authorize('registration', ['r'], 'supplier'), logActivity('update', 'order', (req, data) =>
    `Admin Verified order ${req.params.orderId}` ), verifyOrderPayment);
router.post('/orders/:orderId/retry-payment', auth, authorize('registration', ['r'], 'supplier'), logActivity('update', 'order', (req, data) =>
    `Admin Retried order ${req.params.orderId}` ), retryOrderPayment);


// Get all orders with pagination and filtering
router.get('/transactions', auth, logActivity('view', 'orders', (req, data) =>
    `Admin Viewed transactions` ), getAllPayments);
router.post('/transactions', auth, logActivity('create', 'transaction', (req, data) =>
    `Admin Created a transaction` ), createManualPayment);
router.put('/transactions/:transactionId', auth, logActivity('view', 'transaction', (req, data) =>
    `Admin Updated an transaction ${req.params.transactionId}` ), updatePayment);



// ===== ADMIN APPLICATION MANAGEMENT ROUTES =====

// Get all applications for a specific supplier company
router.get('/suppliers/:companyId/applications',
    auth,
    authorize('registration', ['r'], 'admin'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for supplier company ${req.params.companyId}`),
    getSupplierApplications
);

// Get specific application by ID for a supplier company
router.get('/suppliers/:companyId/applications/:applicationId',
    auth,
    authorize('registration', ['r'], 'admin'),
    logActivity('view', 'application', (req, data) =>
        `Viewed application ${req.params.applicationId} for supplier ${req.params.companyId}`),
    getSupplierApplicationById
);

// Admin review application
router.put('/suppliers/:companyId/applications/:applicationId/review',
    auth,
    authorize('registration', ['w'], 'admin'),
    logActivity('update', 'application', (req, data) =>
        `Reviewed application ${req.params.applicationId} for supplier ${req.params.companyId}`),
    adminReviewApplication
);



// ===== Admin APPLICATION VIEWING ROUTES =====

// Get all applications for a specific job category (Buyer view - read only)
router.get('/buyer/:companyId/categories/:categoryId/applications',
    auth,
    //authorize('registration', ['r'], 'buyer'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getCategoryApplications
);

router.get('/buyer/:companyId/jobs/:jobId/categories/:categoryId/applications',
    auth,
    //authorize('registration', ['r'], 'buyer'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getCategoryApplications
);


// Get all applications for a specific job category
router.get('/categories/:categoryId/applications',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getCategoryApplications
);
// Get all applications for a specific job category (Buyer view - read only)
router.get('/buyer/:companyId/jobs/:jobId/applications',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('view', 'application', (req, data) =>
        `Viewed applications for category ${req.params.categoryId}`),
    getJobApplications
);

// Download application document
router.get('/applications/:applicationId/fields/:fieldName/documents/:fileName',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('view', 'application', (req, data) =>
        `Downloaded document ${req.params.fileName} from application ${req.params.applicationId}`),
    downloadApplicationDocument
);

// ===== DOWNLOAD AND REPORT ROUTES =====

// Download all applications for a category as ZIP
router.get('/categories/:categoryId/applications/download/zip',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded ZIP of applications for category ${req.params.categoryId}`),
    downloadCategoryApplicationsZip
);

// Download applications Excel report for a category
router.get('/categories/:categoryId/applications/download/excel',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded Excel report for category ${req.params.categoryId}`),
    downloadCategoryApplicationsExcel
);

// Download documents from a specific section for all applicants
router.get('/categories/:categoryId/applications/download/section/:sectionName',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded section ${req.params.sectionName} documents for category ${req.params.categoryId}`),
    downloadCategorySectionDocuments
);

// Download job-level applications report
router.get('/jobs/:jobId/applications/download/excel',
    auth,
    //authorize('registration', ['r'], 'admin'),
    logActivity('download', 'application', (req, data) =>
        `Downloaded job report for job ${req.params.jobId}`),
    downloadJobApplicationsExcel
);

export default router;