import { body, param, query } from 'express-validator';
import notificationController from '../controllers/notificationController.js';
import auth from '../middleware/auth.js';
import express from 'express';
import multer from 'multer';
import User from '../models/sp_user.js';
const router = express.Router();


// Configure multer for in-memory storage
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

// Validation middleware
const sendNotificationValidation = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 255 })
    .withMessage('Title must not exceed 255 characters'),
  body('message')
    .notEmpty()
    .withMessage('Message is required'),
  body('channel')
    .isIn(['email', 'sms', 'app', 'push'])
    .withMessage('Invalid channel. Must be one of: email, sms, app, push'),
  body('type')
    .optional()
    .isIn(['app', 'email', 'sms', 'push'])
    .withMessage('Invalid type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('recipient.email')
    .if(body('channel').equals('email'))
    .isEmail()
    .withMessage('Valid email address is required for email notifications'),
  body('recipient.phone')
    .if(body('channel').equals('sms'))
    .isMobilePhone()
    .withMessage('Valid phone number is required for SMS notifications'),
  body('recipient.deviceToken')
    .if(body('channel').equals('push'))
    .notEmpty()
    .withMessage('Device token is required for push notifications')
];

const bulkNotificationValidation = [
  body('notifications')
    .isArray({ min: 1 })
    .withMessage('Notifications array is required and cannot be empty'),
  body('notifications.*.title')
    .notEmpty()
    .withMessage('Title is required for each notification'),
  body('notifications.*.message')
    .notEmpty()
    .withMessage('Message is required for each notification'),
  body('notifications.*.channel')
    .isIn(['email', 'sms', 'app', 'push'])
    .withMessage('Invalid channel for notification')
];

const scheduleNotificationValidation = [
  ...sendNotificationValidation,
  body('scheduledAt')
    .isISO8601()
    .withMessage('Valid scheduled date is required')
    .custom((value) => {
      const scheduleDate = new Date(value);
      if (scheduleDate <= new Date()) {
        throw new Error('Scheduled time must be in the future');
      }
      return true;
    })
];

const markAsReadValidation = [
  body('notificationIds')
    .isArray({ min: 1 })
    .withMessage('Notification IDs array is required and cannot be empty'),
  body('notificationIds.*')
    .isMongoId()
    .withMessage('Invalid notification ID format')
];

const objectIdValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid notification ID format')
];

const userIdValidation = [
  param('userId')
    .optional()
    .isMongoId()
    .withMessage('Invalid user ID format')
];

const companyIdValidation = [
  param('companyId')
    .optional()
    .isMongoId()
    .withMessage('Invalid company ID format')
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(['pending', 'sent', 'delivered', 'read', 'failed', 'cancelled'])
    .withMessage('Invalid status filter'),
  query('type')
    .optional()
    .isIn(['app', 'email', 'sms', 'push'])
    .withMessage('Invalid type filter')
];

// Routes

// POST /notifications - Send single notification
router.post('/', 
  auth, 
  sendNotificationValidation, 
  notificationController.sendNotification
);

// POST /notifications - Send admin notification
router.post('/send', 
  auth, 
  //sendNotificationValidation,
  upload.array('attachments'), // Handles a single file upload from a field named 'file'
  notificationController.sendAdminNotification.bind(notificationController)
);


// POST /notifications/bulk - Send bulk notifications
router.post('/bulk', 
  auth, 
  bulkNotificationValidation, 
  notificationController.sendBulkNotifications
);

// POST /notifications/schedule - Schedule notification
router.post('/schedule', 
  auth, 
  scheduleNotificationValidation, 
  notificationController.scheduleNotification
);

// GET /notifications/user/:userId? - Get user notifications
router.get('/user/:userId?', 
  auth, 
  userIdValidation, 
  paginationValidation, 
  notificationController.getUserNotifications
);

// GET /notifications/user/:userId? - Get user notifications
router.get('/admin/all', 
  auth, 
  userIdValidation, 
  paginationValidation, 
  notificationController.getAllNotifications
);

//download notification attachments         `${BACKEND_API_URL}/api/notifications/${notificationId}/attachments/download`,
// GET /notifications/:notificationId/attachments/download - Download notification attachments
router.get('/:notificationId/attachments/download', 
  auth, 
  objectIdValidation, 
  notificationController.downloadNotificationAttachments
);

router.get('/:notificationId/resend',
  auth,
  notificationController.resendNotification
);




// GET /notifications/company/:companyId? - Get company notifications
router.get('/company/:companyId?', 
  auth, 
  companyIdValidation, 
  paginationValidation, 
  notificationController.getCompanyNotifications
);

// GET /notifications/stats - Get notification statistics
router.get('/stats', 
  auth, 
  notificationController.getNotificationStats
);
// GET all admin /notifications/stats - Get all notification statistics
router.get('/stats/admin/all', 
  auth, 
  notificationController.getAdminNotificationStats
);



// GET /notifications/unread-count/:userId? - Get unread count
router.get('/unread-count/:userId?', 
  auth, 
  userIdValidation, 
  notificationController.getUnreadCount
);

// GET /notifications/:id - Get single notification
router.get('/:id', 
  auth, 
  objectIdValidation, 
  notificationController.getNotification
);

// PATCH /notifications/mark-read - Mark multiple notifications as read
router.patch('/mark-read', 
  auth, 
  markAsReadValidation, 
  notificationController.markAsRead
);

// PATCH /notifications/:id/read - Mark single notification as read
router.patch('/:id/read', 
  auth, 
  objectIdValidation, 
  notificationController.markSingleAsRead
);

// POST /notifications/retry-failed - Retry failed notifications
router.post('/retry-failed', 
  auth, 
  notificationController.retryFailedNotifications
);

// DELETE /notifications/:id/cancel - Cancel scheduled notification
router.delete('/:id/cancel', 
  auth, 
  objectIdValidation, 
  notificationController.cancelScheduledNotification
);

// DELETE /notifications/:id - Delete notification
router.delete('/:id',
  auth,
  objectIdValidation,
  notificationController.deleteNotification
);

// POST /notifications/public - Handle public notification requests (no auth required)
router.post('/public',
  notificationController.handlePublicNotification.bind(notificationController)
);

export default router;