import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import dotenv from 'dotenv';
import authRoutes from './routes/auth.js';
import logger from './middleware/logger.js';//shows this file isn in use
import authorize from './middleware/authorize.js';
import supplierRoutes from './routes/supplier.js';
import buyerRoutes from './routes/buyer.js';
import auth from './middleware/auth.js';
import adminRoutes from './routes/admin.js';
import notificationRoutes from './routes/notification.js';
import profileRoutes from './routes/profile.js';
import paymentRoutes from './routes/payment.js';
import session from "express-session";
import passport from "passport";

dotenv.config();

const app = express();

app.use(cors());
app.use(express.json());


var sess = {
  secret: 'keyboard cat',
  cookie: {},
  //store: sessionStore,
  secret: "tender-secret-key",
  resave: false,
  saveUninitialized: false,
}

if (app.get('env') === 'production') {
  app.set('trust proxy', 1) // trust first proxy
  sess.cookie.secure = true // serve secure cookies
}

app.use(session(sess))

//Passport Middleware for web routes
app.use(passport.initialize());
app.use(passport.session());

mongoose
  .connect(process.env.MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => {
    console.log('Connected to MongoDB');
    app.listen(PORT, () => { console.log(`Server running on port ${PORT}`); });
  })
  .catch((error) => {
    console.error('Error connecting to MongoDB:', error);
  });

app.use('/api', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/profile', profileRoutes);

app.use('/api/payment/checkout', paymentRoutes); //stk/webhook, mobile/:companyId, card

app.use('/api/supplier', supplierRoutes);
app.use('/api/buyer', buyerRoutes);
app.use('/api/notifications', notificationRoutes);




app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const PORT = process.env.PORT || 5000;

export default app;