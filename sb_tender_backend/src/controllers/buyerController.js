
import SpCompany from '../models/sp_company.js';
import { uploadToS3, deleteFromS3, getObjectFromS3 } from '../services/s3Service.js';

import bcrypt from 'bcryptjs';
import User from '../models/sp_user.js'; 
import SpRole from '../models/sp_role.js';
import SpUserRole from '../models/sp_user_role.js';


const TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];

export const ensureDefaultRolesExist = async (context, createdBy) => {
  const existingRoles = await SpRole.find({ context });
  const existingKeys = new Set(existingRoles.map(r => r.type));

  const missingTypes = TYPES.filter(type => !existingKeys.has(type));

  if (missingTypes.length === 0) return; // All roles exist

  const newRoles = missingTypes.map(type => ({
    name: `${context.toUpperCase()} - ${type}`,
    context,
    type,
    permissions: DEFAULT_PERMISSIONS,
    description: `Auto-created role for ${context} - ${type}`,
    createdBy,
  }));

  try{
    await SpRole.insertMany(newRoles);
    console.log('Saved buyer roles')
  } catch(error)  {
    console.error('Error saving roles', error);
  }

};

// Helper function to exclude sensitive fields from the user object
const excludeSensitiveFields = (user) => {
    if (!user) return null;
    const userObject = user.toObject ? user.toObject() : user; // Convert Mongoose doc to plain object
    delete userObject.password;
    delete userObject.otp;
    delete userObject.__v; // Mongoose version key
    return userObject;
};


// Get all users
export const getCompanyUsers = async (req, res) => {
    try {
        const { companyId } = req.params;

        // Find all SpUserRoles where companyId matches
        const companyUserRoles = await SpUserRole.find({ companyId });

        if (!companyUserRoles || companyUserRoles.length === 0) {
            return res.status(404).json({ message: 'No roles found for the given company' });
        }

        // Extract userIds and roleIds from companyRoles
        const userIds = companyUserRoles.map(role => role.userId);
        const companyRoleIds = companyUserRoles.map(role => role.roleId);

        // Find all users whose _id matches the userIds from companyRoles
        const companyUsers = await User.find({ _id: { $in: userIds } }).select('-password -otp');
        const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });

        // Map roles to users - CONVERT TO STRINGS for proper comparison
        const userRolesMap = companyUserRoles.reduce((map, userRole) => {
            const userIdStr = userRole.userId.toString();
            if (!map[userIdStr]) {
                map[userIdStr] = [];
            }
            map[userIdStr].push(userRole.roleId.toString());
            return map;
        }, {});

        const companyUsersWithRoles = companyUsers.map(user => ({
            ...user.toObject(),
            // FIXED: Convert ObjectIds to strings for comparison
            roles: companyRoles.filter(role => 
                userRolesMap[user._id.toString()]?.includes(role._id.toString())
            )
        }));

        res.status(200).json({ users: companyUsersWithRoles });
    } catch (error) {
        console.error('Error getting all users:', error);
        res.status(500).json({ message: 'Failed to fetch users', error: error.message });
    }
};


// Create a new user
export const createCompanyUser = async (req, res) => {
  try {
    const { companyId } = req.params;
    const {
      firstName,
      lastName,
      email,
      password, // Password is required for creation
      phone,
      type,     // 'admin', 'buyer', 'buyer'
      status,   // 'active', 'inactive', 'suspended' - optional, will use default if not provided
      roleIds,
      emailVerified, // Optional boolean, will use default
      phoneVerified, // Optional boolean, will use default
      otpVerified,   // Optional boolean, will use default
      mustUpdatePassword // Optional boolean, will use default
    } = req.body;

    console.log(req.body);

    // Basic validation
    if (!firstName || !lastName || !email || !password || !phone || !type) {
      return res.status(400).json({ message: 'Missing required fields (firstName, lastName, email, password, phone, type)' });
    }

    // Check if user with email already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({ message: 'User with this email already exists' });
    }
     // Check if user with phone already exists
    const existingPhoneUser = await User.findOne({ phone });
    if (existingPhoneUser) {
      return res.status(409).json({ message: 'User with this phone number already exists' });
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const newUser = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword, // Save the hashed password
      phone,
      type,
      status: status || 'active', // Use provided status or default
      emailVerified: emailVerified ?? false, // Use provided or default
      phoneVerified: phoneVerified ?? false, // Use provided or default
      otpVerified: otpVerified ?? false,   // Use provided or default
      mustUpdatePassword: mustUpdatePassword ?? false, // Use provided or default
      createdBy: req.user.userId, // Assuming auth middleware adds user ID to req.user
      // Mongoose Timestamps will handle createdAt and updatedAt
    });

    // Save the user to the database
    await newUser.save();


         await ensureDefaultRolesExist('buyer', req.user.userId);
        console.log('Ensure default roles check/creation finished.');
    

   // const buyerRoles = await SpRole.find({ context: 'buyer', _id: roleIds  });
    const buyerRoles = await SpRole.find({ context: 'buyer' });

         console.log(`Step 3a: Found ${buyerRoles.length} roles with context 'buyer'.`);
         console.log('Found buyer roles objects:', buyerRoles.map(r => ({ _id: r._id, name: r.name, context: r.context, type: r.type })));
    
    
        if (buyerRoles.length === 0) {
            console.warn(`No buyer roles found with context 'buyer'. Cannot assign any roles.`);
            await ensureDefaultRolesExist('buyer', newUser._id);

           
        } else {
            console.log(`Mapping ${buyerRoles.length} found buyer roles to SpUserRole objects.`);
            const SpUserRoles = buyerRoles.map(role => ({
              userId: newUser._id,
              companyId,
              roleId: role._id,
            }));
            console.log('Step 3b: Prepared SpUserRoles array for insertion:', SpUserRoles);
            console.log('Number of SpUserRoles entries to insert:', SpUserRoles.length);
    
            console.log('Attempting to insert SpUserRoles into the database...');
            // Ensure your model is correctly imported as SpUserRole, not SpUserRole
            await SpUserRole.insertMany(SpUserRoles);
            console.log(`Step 3 Successful: Successfully inserted ${SpUserRoles.length} SpUserRole documents.`);
        }
    
    // Return the created user, excluding sensitive fields
    res.status(201).json(excludeSensitiveFields({newUser, message: 'Created a company user successfully.'}));

  } catch (error) {
    console.error('Error creating user:', error);
    // Handle potential duplicate key errors for email/phone if Mongoose schema doesn't catch it first
     if (error.code === 11000) {
         return res.status(409).json({ message: 'Duplicate field value entered', error: error.message });
     }
    res.status(500).json({ message: 'Failed to create user', error: error.message });
  }
};

export const getCompanyUser = async (req, res) => {
    console.log('get company user');
    try {
      const userId = req.user.userId;
      console.log(userId);
      const user = await User.findById(userId).select('-password -otp');
  
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
  
      res.status(200).json(user);
    } catch (error) {
      console.error('Error getting user by ID:', error);
      // Handle invalid ID format errors
      if (error.kind === 'ObjectId') {
           return res.status(400).json({ message: 'Invalid User ID format' });
      }
      res.status(500).json({ message: 'Failed to fetch user', error: error.message });
    }
  };

  export const getCompanyRoles = async (req, res) => {
    console.log('get buyer company roles');
    try {
     
        await ensureDefaultRolesExist('buyer', req.user.userId);
        console.log('Ensure default roles check/creation finished.');
        const { companyId } = req.params;

        const companyUserRoles = await SpUserRole.find({ companyId });
        if (!companyUserRoles || companyUserRoles.length === 0) {
            return res.status(404).json({ message: 'No buyer roles found for the given company' });
        }
        const userIds = companyUserRoles.map(role => role.userId);
        const companyRoleIds = companyUserRoles.map(role => role.roleId);
        const companyUsers = await User.find({ _id: { $in: userIds } }).select('-password -otp');
        const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });
        //console.log('Users', companyUsers);
        console.log('Roles', companyRoles);

        res.status(200).json({ roles: companyRoles });


    } catch (error) {
      console.error('Error getting roles:', error);
      if (error.kind === 'ObjectId') {
           return res.status(400).json({ message: 'Invalid ID format' });
      }
      res.status(500).json({ message: 'Failed to fetch role', error: error.message });
    }
  };
  
  
// Update an existing company user
export const updateCompanyUser = async (req, res) => {
    try {
      const { companyId, userId } = req.params;
      const {
        firstName,
        lastName,
        email,
        password, // Optional for updates
        phone,
        type,     // 'admin', 'buyer', 'buyer'
        status,   // 'active', 'inactive', 'suspended'
        roleIds,  // Array of role IDs to assign
        emailVerified,
        phoneVerified,
        otpVerified,
        mustUpdatePassword
      } = req.body;
  
      console.log('Update request body:', req.body);
      console.log('User ID to update:', userId);
      console.log('Company ID:', companyId);
  
      // Check if user exists
      const existingUser = await User.findById(userId);
      if (!existingUser) {
        return res.status(404).json({ message: 'User not found' });
      }
  
      // Check for email conflicts (if email is being updated)
      if (email && email !== existingUser.email) {
        const emailConflict = await User.findOne({ email, _id: { $ne: userId } });
        if (emailConflict) {
          return res.status(409).json({ message: 'User with this email already exists' });
        }
      }
  
      // Check for phone conflicts (if phone is being updated)
      if (phone && phone !== existingUser.phone) {
        const phoneConflict = await User.findOne({ phone, _id: { $ne: userId } });
        if (phoneConflict) {
          return res.status(409).json({ message: 'User with this phone number already exists' });
        }
      }
  
      // Prepare update object with only provided fields
      const updateData = {};
      
      if (firstName !== undefined) updateData.firstName = firstName;
      if (lastName !== undefined) updateData.lastName = lastName;
      if (email !== undefined) updateData.email = email;
      if (phone !== undefined) updateData.phone = phone;
      if (type !== undefined) updateData.type = type;
      if (status !== undefined) updateData.status = status;
      if (emailVerified !== undefined) updateData.emailVerified = emailVerified;
      if (phoneVerified !== undefined) updateData.phoneVerified = phoneVerified;
      if (otpVerified !== undefined) updateData.otpVerified = otpVerified;
      if (mustUpdatePassword !== undefined) updateData.mustUpdatePassword = mustUpdatePassword;
  
      // Handle password update if provided
      if (password) {
        const salt = await bcrypt.genSalt(10);
        updateData.password = await bcrypt.hash(password, salt);
      }
  
      // Add updatedBy field
      updateData.updatedBy = req.user.userId;
  
      console.log('Update data prepared:', Object.keys(updateData));
  
      // Update the user
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      );
  
      if (!updatedUser) {
        return res.status(404).json({ message: 'Failed to update user' });
      }
  
      console.log('User updated successfully');
  
      // Handle role updates if roleIds are provided
      if (roleIds !== undefined) {
        console.log('Processing role updates...');
        console.log('New role IDs:', roleIds);
  
        // Remove existing SpUserRole entries for this user and company
        console.log('Step 1: Removing existing SpUserRole entries...');
        const deleteResult = await SpUserRole.deleteMany({
          userId: userId,
          companyId: companyId
        });
        console.log(`Removed ${deleteResult.deletedCount} existing SpUserRole entries.`);
  
        // Add new roles if roleIds array is not empty
        if (Array.isArray(roleIds) && roleIds.length > 0) {
          console.log('Step 2: Adding new roles...');
          
          // Find the roles based on provided roleIds and appropriate context
          let contextFilter = {};
          if (updatedUser.type === 'buyer') {
            contextFilter = { context: 'buyer' };
          } else if (updatedUser.type === 'buyer') {
            contextFilter = { context: 'buyer' };
          } else if (updatedUser.type === 'admin') {
            contextFilter = { context: { $in: ['admin', 'buyer', 'buyer'] } }; // Admin can have multiple contexts
          }
  
          const validRoles = await SpRole.find({ 
            _id: { $in: roleIds },
            ...contextFilter
          });
  
          console.log(`Found ${validRoles.length} valid roles for user type '${updatedUser.type}'.`);
          console.log('Valid roles:', validRoles.map(r => ({ 
            _id: r._id, 
            name: r.name, 
            context: r.context, 
            type: r.type 
          })));
  
          if (validRoles.length === 0) {
            console.warn(`No valid roles found for user type '${updatedUser.type}' with provided roleIds.`);
          } else {
            // Create new SpUserRole entries
            const newSpUserRoles = validRoles.map(role => ({
              userId: userId,
              companyId: companyId,
              roleId: role._id,
            }));
  
            console.log('Prepared SpUserRoles for insertion:', newSpUserRoles.length);
            
            await SpUserRole.insertMany(newSpUserRoles);
            console.log(`Successfully inserted ${newSpUserRoles.length} new SpUserRole entries.`);
          }
  
          // Warn about invalid role IDs if any
          const validRoleIds = validRoles.map(r => r._id.toString());
          const invalidRoleIds = roleIds.filter(id => !validRoleIds.includes(id.toString()));
          if (invalidRoleIds.length > 0) {
            console.warn(`Invalid or incompatible role IDs provided: ${invalidRoleIds.join(', ')}`);
          }
        } else {
          console.log('No new roles to assign (empty or invalid roleIds array).');
        }
      }
  
      // Get updated user with current roles for response
      const userRoles = await SpUserRole.find({ 
        userId: userId, 
        companyId: companyId 
      }).populate('roleId');
  
      console.log(`User update completed. User now has ${userRoles.length} roles assigned.`);
  
      // Return the updated user, excluding sensitive fields
      res.status(200).json(excludeSensitiveFields({
        user: updatedUser,
        roles: userRoles,
        message: 'Company user updated successfully.'
      }));
  
    } catch (error) {
      console.error('Error updating user:', error);
      
      // Handle potential duplicate key errors
      if (error.code === 11000) {
        return res.status(409).json({ 
          message: 'Duplicate field value entered', 
          error: error.message 
        });
      }
      
      // Handle validation errors
      if (error.name === 'ValidationError') {
        return res.status(400).json({ 
          message: 'Validation error', 
          error: error.message 
        });
      }
      
      res.status(500).json({ 
        message: 'Failed to update user', 
        error: error.message 
      });
    }
  };
  

// Delete a user
export const deleteCompanyUser = async (req, res) => {
    try {
      //const userId = req.user.userId;
      const { companyId, userId } = req.params;

      const user = await User.findById(userId);
        user.status = 'inactive';
        await user.save();
  
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      res.status(200).json({ message: 'User deleted successfully' });
    } catch (error) {
      console.error('Error deleting user:', error);
       // Handle invalid ID format errors
      if (error.kind === 'ObjectId') {
           return res.status(400).json({ message: 'Invalid User ID format' });
      }
      res.status(500).json({ message: 'Failed to delete user', error: error.message });
    }
  };

