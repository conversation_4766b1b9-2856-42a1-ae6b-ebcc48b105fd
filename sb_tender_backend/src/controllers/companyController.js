import SpCompany from '../models/sp_company.js';
import { uploadToS3, deleteFromS3, getObjectFromS3 } from '../services/s3Service.js';

export const createCompany = async (req, res) => {
  try {
    console.log('creating company');

    console.log('Request body:', req.body);
    const { name, registrationNumber, address, contactPerson, email, phone } = req.body;
    const company = new SpCompany({
      name,
      registrationNumber,
      type: 'buyer',
      address,
      contactPerson,
      email,
      phone,
      createdBy: req.user.userId,
    });

    await company.save();
    const documents = [];

    if (req.files) {
      for (const [key, fileArray] of Object.entries(req.files)) {
        const file = fileArray[0];
        console.log('File:', file);
        console.log('File buffer:', file.buffer);
        console.log('File name:', file.originalname);
        const fileUrl = await uploadToS3(file.buffer, 'company-documents', file.fieldname, company._id);
        documents.push({
          url: fileUrl,
          filePath: fileUrl,
          fileName: file.originalname,
          fileType: file.mimetype,
          fileSize: file.size,
          type: file.fieldname,
          name: file.originalname,
          expiresAt: null,
        });
        console.log('Uploaded file:', documents[key]);
        console.log('File URL:', fileUrl);
         //update logoUrl if fieldname is logo, update company logoUrl field
        if (file.fieldname === 'logoUrl') {
          company.logoUrl = fileUrl;
        }
      }
    }
    company.documents = documents;
    company.updatedBy = req.user.userId;
    await company.save();

   
    res.status(201).json({ message: 'Company created successfully', company });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getCompanies = async (req, res) => {
  try {
    const filter = {};
    
    if (req.query.type === 'buyer' || req.query.type === 'supplier') {
      filter.type = req.query.type;
    }
    // else if (req.query.type) {
    //   return res.status(400).json({ error: 'Invalid type parameter' });
    // }
    
    const companies = await SpCompany.find(filter);
    res.status(200).json(companies);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getCompanyById = async (req, res) => {
  try {
    const { id } = req.params;
    const company = await SpCompany.findById(id);
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }
    res.status(200).json(company);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
export const updateCompany = async (req, res) => {
  console.log('updating company');
  try {
    const { id } = req.params;
    const { name, registrationNumber, address, contactPerson, email, phone } = req.body;

    const company = await SpCompany.findById(id);
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }   

    // Start with existing documents
    const updatedDocuments = [...company.documents];

    if (req.files) {
      console.log('Has uploaded files:');
      for (const [key, fileArray] of Object.entries(req.files)) {
        const file = fileArray[0];
        console.log('File:', file);
        console.log('File buffer:', file.buffer);
        console.log('File name:', file.originalname);
        
        const fileUrl = await uploadToS3(file.buffer, 'company-documents', file.fieldname, company._id);
        
        // Check if a document of this type already exists
        const existingDocIndex = updatedDocuments.findIndex(doc => doc.type === file.fieldname);
        
        const newDocument = {
          url: fileUrl,
          filePath: fileUrl,
          fileName: file.originalname,
          fileType: file.mimetype,
          fileSize: file.size,
          type: file.fieldname,
          name: file.originalname,
          expiresAt: null,
        };

        //update logoUrl if fieldname is logo, update company logoUrl field
        if (file.fieldname === 'logoUrl') {
          company.logoUrl = fileUrl;
        }

        if (existingDocIndex >= 0) {
          // Replace existing document of this type
          updatedDocuments[existingDocIndex] = newDocument;
        } else {
          // Add new document
          updatedDocuments.push(newDocument);
        }

        console.log('Uploaded file:', newDocument);
        console.log('File URL:', fileUrl);
      }
    }
   
    // Only update fields that were provided
    company.name = name || company.name;
    company.registrationNumber = registrationNumber || company.registrationNumber;
    company.address = address || company.address;
    company.contactPerson = contactPerson || company.contactPerson;
    company.email = email || company.email;
    company.phone = phone || company.phone;
    company.documents = updatedDocuments; // Use the merged documents array
    company.updatedBy = req.user.userId;

    await company.save();
    res.status(200).json({ message: 'Company updated successfully', company });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const deleteCompany = async (req, res) => {
  try {
    const { id } = req.params;
    const company = await SpCompany.findById(id);

    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    if (company.documents) {
      console.log('Documents:', company.documents);
      for (const document of company.documents) {
        try {
          await deleteFromS3(document.filePath);
          console.log('Deleted from s3 successfully');
        } catch (error) {
          console.error('Error deleting from s3', error);
          return res.status(500).json({ error: 'Error deleting file from S3' });
        }
      }
    }

    await SpCompany.deleteOne({ _id: id });
    res.status(200).json({ message: 'Company deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
  }

  export const downloadCompanyDocument = async (req, res) => {
   console.log('Request query:', req.query);
    console.log('Request params:', req.params);
      const { companyId, documentType, fileName } = req.query;
      console.log('Company ID:', companyId);
      const company = await SpCompany.findById(companyId);
      if (!company) {
        return res.status(404).json({ error: 'Company not found' });
      }
  
      const document = company.documents.find(doc => doc.type === documentType);
      if (!document) {
        return res.status(404).json({ error: 'Document not found' });
      }
  
      const fileUrl = document.filePath;
  
      if (!fileUrl || !fileName) {
        return res.status(400).send('Missing fileUrl or fileName');
      }
    
    
      try {
        console.log('File URL:', fileUrl);
        const s3Response = await getObjectFromS3(fileUrl);
        if (!s3Response) {
          return res.status(404).send('File not found in S3');
        }
    
        console.log('S3 object conttent length', s3Response.ContentLength);

        res.setHeader('Content-Type', s3Response.ContentType || 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    
        s3Response.Body.pipe(res); // stream back to client
      } catch (error) {
        console.error('S3 Download Error:', error);
        res.status(500).send('Failed to fetch file from S3');
      }
    }

  

