import SpJob from '../models/sp_tender.js';
import SpForm from '../models/sp_form.js';

export const createTender = async (req, res) => {
  try {
    const tender = new SpJob({
      ...req.body,
      createdBy: req.user._id,
    });
    await tender.save();
    res.status(201).json(tender);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getTenders = async (req, res) => {
  try {
    const filters = {};
    if (req.user.type === 'buyer') {
      filters.buyerCompany = req.user.roles.find((r) => r.companyType === 'SpBuyerCompany')?.company;
    }

    const tenders = await SpJob.find(filters)
      .populate('category')
      .populate('buyerCompany')
      .sort({ createdAt: -1 });

    res.json(tenders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const getTenderById = async (req, res) => {
  try {
    const tender = await SpJob.findById(req.params.id)
      .populate('category')
      .populate('buyerCompany');

    if (!tender) {
      return res.status(404).json({ error: 'Tender not found' });
    }

    res.json(tender);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const updateTender = async (req, res) => {
  try {
    const tender = await SpJob.findById(req.params.id);

    if (!tender) {
      return res.status(404).json({ error: 'Tender not found' });
    }

    if (tender.status !== 'draft') {
      return res.status(400).json({ error: 'Cannot update published tender' });
    }

    Object.assign(tender, req.body, { updatedBy: req.user._id });
    await tender.save();

    res.json(tender);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const deleteTender = async (req, res) => {
  try {
    const tender = await SpJob.findById(req.params.id);

    if (!tender) {
      return res.status(404).json({ error: 'Tender not found' });
    }

    if (tender.status !== 'draft') {
      return res.status(400).json({ error: 'Cannot delete published tender' });
    }

    await tender.remove();
    res.json({ message: 'Tender deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Export all functions as named exports and provide a default export
export default {
  createTender,
  updateTender,
  deleteTender,
  getTenders,
  getTenderById,
};