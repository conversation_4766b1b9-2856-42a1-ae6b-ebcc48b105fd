import fs from 'fs';
import path from 'path';
import SpCategoryTemplate from '../models/sp_category_template.js';
import SpCategory from '../models/sp_category.js';

// Utility to load default fields
const loadDefaultFields = (type) => {
  const filePath = path.join(process.cwd(), 'models', 'default_'+type+'_fields.json');
  const data = fs.readFileSync(filePath, 'utf-8');
  return JSON.parse(data);
};

// Create a new template
export const createCategoryTemplate = async (req, res) => {
  try {
    const { name, spCategoryId, type, createdBy, useDefaultFields = false, fields = [] } = req.body;

    const spCategory = await SpCategory.findById(spCategoryId);
    if (!spCategory || spCategory.type !== type) {
      return res.status(400).json({ message: 'Invalid SpCategory or mismatched type.' });
    }

    const templateFields = useDefaultFields ? loadDefaultFields(spCategory.type) : fields;

    const template = new SpCategoryTemplate({
      name,
      spCategoryId,
      type,
      fields: templateFields,
      createdBy,
    });

    await template.save();
    res.status(201).json(template);
  } catch (err) {
    console.error('Create Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};

// Get all templates
export const getCategoryTemplates = async (req, res) => {
  try {
    const templates = await SpCategoryTemplate.find().populate('spCategoryId');
    res.status(200).json(templates);
  } catch (err) {
    console.error('Get Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};
// Get a template by ID
export const getCategoryTemplateById = async (req, res) => {
  try {
    const { id } = req.params;
    const template = await SpCategoryTemplate.findById(id).populate('spCategoryId');
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }
    res.status(200).json(template);
  } catch (err) {
    console.error('Get by ID Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};

export const getCategoryTemplate = async (req, res) => {
  try {
    console.log('Template Request Params:', req.params);
    console.log('Request Body:', req.body);
    const { id } = req.params;
    const template = await SpCategoryTemplate.findOne({ spCategoryId: id });
    if (!template) {
      console.error('Template not found for ID:', id);
      return res.status(404).json({ message: 'Template not found' });
    }
    console.log('Template found:', template.name);
    res.status(200).json(template);
  } catch (err) {
    console.error('Get by ID Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};

export const updateCategoryTemplate = async (req, res) => {
  try {
    const { cid, tid } = req.params; // Extract category ID and template ID
    // Get the actual updates - they're nested inside categoryTemplate
    const updateData = req.body.categoryTemplate || req.body;
    console.log('Update data structure:', Object.keys(updateData));
    // Find the template by ID
    const template = await SpCategoryTemplate.findOne({ _id: tid, spCategoryId: cid });
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }
    console.log('Template found');

    // Remove restricted fields from updates
    const { _id, spCategoryId, createdAt, createdBy, __v, ...allowedUpdates } = updateData;
    
    console.log('Fields being updated:', Object.keys(allowedUpdates));
    
    // Update fields array if it exists
    if (allowedUpdates.fields) {
      console.log('Fields array length:', allowedUpdates.fields.length);
      template.fields = allowedUpdates.fields;
    }
    
    // Update other properties
    for (const [key, value] of Object.entries(allowedUpdates)) {
      if (key !== 'fields') {
        template[key] = value;
      }
    }
    
    // Set updatedBy
    template.updatedBy = req.user.userId;
    
    // Log the template before saving
    console.log('Template before save - Fields count:', template.fields?.length);
    
    // Save the updated template
    await template.save();
    console.log('Template saved');

    // Respond with the updated template
    res.status(200).json(template);
  } catch (err) {
    console.error('Update Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};


// Delete a template
export const deleteCategoryTemplate = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await SpCategoryTemplate.findByIdAndDelete(id);
    if (!result) {
      return res.status(404).json({ message: 'Template not found' });
    }

    res.status(200).json({ message: 'Template deleted successfully' });
  } catch (err) {
    console.error('Delete Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};













