import SpBuyerCategory from '../models/sp_buyer_category.js';
import defaultPrequalificationFields from '../models/default_prequalification_fields.js';
import defaultRegistrationFields from '../models/default_registration_fields.js';
import SpBuyerCategoryTemplate from '../models/sp_buyer_category_template.js';
import SpCategoryTemplate from '../models/sp_category_template.js';

const createCategoryTemplate = async function(buyerCategory, userId) {
  console.log('Creating category template for buyer category ID:', buyerCategory._id);
  let fields = null;
// 1. First verify the ID you're searching with
console.log('Searching spCategoryId with id:', buyerCategory.spCategoryId);

// 2. Use findOne() if expecting single document, or handle array from find()
const categoryTemplate = await SpCategoryTemplate.findOne({
  spCategoryId: buyerCategory.spCategoryId
});

// 3. Check the complete result
  if(!categoryTemplate) {
    console.log('No category template found for this category');
    return null;
  }
  
    fields = categoryTemplate.fields;
    const spCategoryTemplateId = categoryTemplate._id;
    console.log('Category template found:', spCategoryTemplateId);

  try {
    const buyerCategoryTemplate = new SpBuyerCategoryTemplate({
      name: buyerCategory.name,
      spBuyerCategoryId: buyerCategory._id,
      spCategoryId: buyerCategory.spCategoryId,
      spCategoryTemplateId: spCategoryTemplateId,
      spCompanyId: buyerCategory.spCompanyId,
      type: buyerCategory.type,
      status: buyerCategory.status,
      createdBy: userId,
      fields: fields,
    });
    await buyerCategoryTemplate.save();
    console.log('Buyer Category template created:', buyerCategoryTemplate._id);
    console.log('Buyer Category template created for buyer category:', buyerCategoryTemplate.spBuyerCategoryId);

    return buyerCategoryTemplate; 
  } catch (error) {
    console.error('Error creating buyer category:', error);
    return null;
  }



}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * This function creates a new buyer category from the request body and template of the category
 * and saves it to the database. It also creates a new category template for the buyer category.
 */
export const createBuyerCategory = async (req, res) => {
    const { companyId } = req.params;
   
  try {
    const { name, description, type, status, spCategoryId  } = req.body;
    console.log(name, description, type, status, spCategoryId, companyId);
    const buyerCategory = new SpBuyerCategory({
      name,
      description,
      type,
      status,
      spCategoryId,
      spCompanyId: companyId,
      createdBy: req.user.userId,
    });
    await buyerCategory.save();
    //create category template fields
    const categoryTemplate = await createCategoryTemplate(buyerCategory, req.user.userId);

    if (categoryTemplate) {
        console.log('Category template created:', categoryTemplate._id);
      res.status(201).json(categoryTemplate);

    } else {
        console.log('No category template created');
      res.status(201).json(categoryTemplate);
    }
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: error.message });
  }
};


export const getBuyerCategories = async (req, res) => {
    const { companyId } = req.params;
    console.log('Company ID:', companyId);

  try {
    const categories = await SpBuyerCategory.find({spCompanyId: companyId});
    if (!categories || categories.length === 0) {
      return res.status(404).json({ error: 'No categories found' });
    }
    console.log('Categories found:', categories.length);
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const getBuyerCategoryById = async (req, res) => {
  try {
    const category = await SpBuyerCategory.findById(req.params.id);
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const updateBuyerCategory = async (req, res) => {
  console.log('Updating category server');
  console.log('Body server', req.body);
  console.log('Params server', req.params);
  try {
    const { companyId, categoryId } = req.params;
    console.log('ID', categoryId);
    const { name, description, type, status } = req.body;
    const category = await SpBuyerCategory.findByIdAndUpdate(categoryId,
      { name, description, type, status, updatedBy: req.user.userId },
      { new: true }
    );
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json(category);
  } catch (error) {
    console.error('Error', error);
    res.status(500).json({ error: error.message });
  }
};

export const deleteBuyerCategory = async (req, res) => {
  try {
    const category = await SpBuyerCategory.findByIdAndDelete(req.params.id);
    const categoryTemplate = await SpBuyerCategoryTemplate.findOneAndDelete({spBuyerCategoryId: category._id});
    if (categoryTemplate) {
      console.log('Category template deleted:', categoryTemplate);
    } else {
      console.log('No category template found for this category');
    }
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
