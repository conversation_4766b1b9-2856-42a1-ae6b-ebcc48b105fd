import SpNotification from '../models/sp_notification.js';
import notificationService from '../services/notificationService.js';
import NotificationHelper from '../helpers/notificationHelper.js';
import User from '../models/sp_user.js';
import SpCompany from '../models/sp_company.js';
import { uploadToS3, getObjectFromS3 } from '../services/s3Service.js';
import { validationResult } from 'express-validator';

class notificationController {
  // Send single notification
  async sendNotification(req, res) {
    try {
      console.log(req.body);

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const notificationData = {
        ...req.body,
        companyId: req.body.companyId || req.user?.companyId,
        userId: req.body.userId || req.user?._id
      };

      const options = {
        useQueue: req.body.useQueue || false,
        delay: req.body.delay || 0
      };

      const result = await notificationService.sendNotification(notificationData, options);
      
      res.status(201).json({
        success: true,
        message: 'Notification sent successfully',
        data: result
      });
    } catch (error) {
      console.error('Send notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send notification',
        error: error.message
      });
    }
  }

  // Send bulk notifications
  async sendBulkNotifications(req, res) {
    try {
      const { notifications, options = {} } = req.body;
      
      if (!Array.isArray(notifications) || notifications.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Notifications array is required and cannot be empty'
        });
      }

      // Add company and user context to each notification
      const enrichedNotifications = notifications.map(notification => ({
        ...notification,
        companyId: notification.companyId || req.user?.companyId,
        userId: notification.userId || req.user?._id
      }));

      const results = await notificationService.sendBulkNotifications(enrichedNotifications, options);
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      res.status(200).json({
        success: true,
        message: `Bulk notifications processed: ${successCount} succeeded, ${failureCount} failed`,
        data: {
          results,
          summary: {
            total: results.length,
            successful: successCount,
            failed: failureCount
          }
        }
      });
    } catch (error) {
      console.error('Bulk notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send bulk notifications',
        error: error.message
      });
    }
  }

  // Get user notifications
  async getUserNotifications(req, res) {
    try {
      const userId = req.params.userId || req.user?._id;
      const {
        status,
        type,
        page = 1,
        limit = 20,
        sort = '-createdAt'
      } = req.query;

      const options = {
        status,
        type,
        limit: parseInt(limit),
        skip: (parseInt(page) - 1) * parseInt(limit)
      };

      const notifications = await SpNotification.findByUser(userId, options);
      const total = await SpNotification.countDocuments({
        userId,
        ...(status && { status }),
        ...(type && { type })
      });

      const unreadCount = await SpNotification.getUnreadCount(userId);

      res.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          },
          unreadCount
        }
      });
    } catch (error) {
      console.error('Get user notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch notifications',
        error: error.message
      });
    }
  }

  // Get company notifications
  async getCompanyNotifications(req, res) {
    try {
      const companyId = req.params.companyId || req.user?.companyId;
      const {
        status,
        type,
        page = 1,
        limit = 20
      } = req.query;

      const options = {
        status,
        type,
        limit: parseInt(limit),
        skip: (parseInt(page) - 1) * parseInt(limit)
      };

      const notifications = await SpNotification.findByCompany(companyId, options);
      const total = await SpNotification.countDocuments({
        companyId,
        ...(status && { status }),
        ...(type && { type })
      });

      res.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      console.error('Get company notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch company notifications',
        error: error.message
      });
    }
  }

  //get all notifications
  async getAllNotifications(req, res) {
    try {
      const {
        status,
        type,
        page = 1,
        limit = 20,
        sort = '-createdAt'
      } = req.query;

      const options = {
        status,
        type,
        limit: parseInt(limit),
        skip: (parseInt(page) - 1) * parseInt(limit)
      };

      const notifications = await SpNotification.find();
      console.log('Notifications:', notifications.length);
      const total = await SpNotification.countDocuments({
        ...(status && { status }),
        ...(type && { type })
      });
      console.log('Notifications total:', total);

      const unreadCount = await SpNotification.getAllUnreadCount();

      res.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          },
          unreadCount
        }
      });
    } catch (error) {
      console.error('Get user notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch notifications',
        error: error.message
      });
    }
  }

  // Get single notification
  async getNotification(req, res) {
    try {
      const { id } = req.params;
      const notification = await SpNotification.findById(id)
        .populate('userId', 'name email')
        .populate('companyId', 'name');

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      // Check access permissions
      const hasAccess = notification.userId && notification.userId.equals(req.user?._id) ||
                       notification.companyId && notification.companyId.equals(req.user?.companyId) ||
                       req.user?.role === 'admin';

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      res.json({
        success: true,
        data: notification
      });
    } catch (error) {
      console.error('Get notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch notification',
        error: error.message
      });
    }
  }

  // Mark notifications as read
  async markAsRead(req, res) {
    try {
      const { notificationIds } = req.body;
      const userId = req.user?._id;

      if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Notification IDs array is required'
        });
      }

      const result = await SpNotification.markAsRead(notificationIds, userId);

      res.json({
        success: true,
        message: 'Notifications marked as read',
        data: {
          modifiedCount: result.modifiedCount
        }
      });
    } catch (error) {
      console.error('Mark as read error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark notifications as read',
        error: error.message
      });
    }
  }

  // Mark single notification as read
  async markSingleAsRead(req, res) {
    try {
      const { id } = req.params;
      const notification = await SpNotification.findById(id);

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      // Check access permissions
      const hasAccess = notification.userId && notification.userId.equals(req.user?._id) ||
                       req.user?.role === 'admin';

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      await notification.markAsRead();

      res.json({
        success: true,
        message: 'Notification marked as read',
        data: notification
      });
    } catch (error) {
      console.error('Mark single as read error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark notification as read',
        error: error.message
      });
    }
  }

  // Delete notification
  async deleteNotification(req, res) {
    try {
      const { id } = req.params;
      const notification = await SpNotification.findById(id);

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      // Check access permissions
      const hasAccess = notification.userId && notification.userId.equals(req.user?._id) ||
                       notification.companyId && notification.companyId.equals(req.user?.companyId) ||
                       req.user?.role === 'admin';

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      await SpNotification.findByIdAndDelete(id);

      res.json({
        success: true,
        message: 'Notification deleted successfully'
      });
    } catch (error) {
      console.error('Delete notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete notification',
        error: error.message
      });
    }
  }

  // Get notification statistics
  async getNotificationStats(req, res) {
    try {
      const filters = {
        companyId: req.query.companyId || req.user?.companyId,
        userId: req.query.userId,
        dateFrom: req.query.dateFrom,
        dateTo: req.query.dateTo
      };

      const stats = await notificationService.getStats(filters);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Get stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch notification statistics',
        error: error.message
      });
    }
  }

    // Get notification statistics
  async getAdminNotificationStats(req, res) {
    try {
      const filters = {
        dateFrom: req.query.dateFrom,
        dateTo: req.query.dateTo
      };

      const stats = await notificationService.getStats(filters);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Get stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch notification statistics',
        error: error.message
      });
    }
  }

  // Retry failed notifications
  async retryFailedNotifications(req, res) {
    try {
      const options = {
        maxAttempts: req.body.maxAttempts || 3,
        limit: req.body.limit || 100
      };

      const results = await notificationService.retryFailedNotifications(options);
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      res.json({
        success: true,
        message: `Retry completed: ${successCount} succeeded, ${failureCount} failed`,
        data: {
          results,
          summary: {
            total: results.length,
            successful: successCount,
            failed: failureCount
          }
        }
      });
    } catch (error) {
      console.error('Retry failed notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retry notifications',
        error: error.message
      });
    }
  }

  // Resend notification
  async resendNotification(req, res) {
    try {
      const id  = req.params.notificationId;
      const notification = await SpNotification.findById(id);

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      await notificationService.sendNotification(notification);

      res.json({
        success: true,
        message: 'Notification resent successfully'
      });
    } catch (error) {
      console.error('Resend notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to resend notification',
        error: error.message
      });
    }
  }

  // Get unread count for user
  async getUnreadCount(req, res) {
    try {
      const userId = req.params.userId || req.user?._id;
      const count = await SpNotification.getUnreadCount(userId);

      res.json({
        success: true,
        data: { unreadCount: count }
      });
    } catch (error) {
      console.error('Get unread count error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch unread count',
        error: error.message
      });
    }
  }

  // Schedule notification
  async scheduleNotification(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { scheduledAt, ...notificationData } = req.body;
      
      if (!scheduledAt) {
        return res.status(400).json({
          success: false,
          message: 'scheduledAt is required for scheduled notifications'
        });
      }

      const scheduleDate = new Date(scheduledAt);
      if (scheduleDate <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Scheduled time must be in the future'
        });
      }

      const enrichedData = {
        ...notificationData,
        companyId: notificationData.companyId || req.user?.companyId,
        userId: notificationData.userId || req.user?._id,
        delivery: {
          ...notificationData.delivery,
          scheduledAt: scheduleDate
        }
      };

      const delay = scheduleDate.getTime() - Date.now();
      const result = await notificationService.sendNotification(enrichedData, {
        useQueue: true,
        delay
      });

      res.status(201).json({
        success: true,
        message: 'Notification scheduled successfully',
        data: result
      });
    } catch (error) {
      console.error('Schedule notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to schedule notification',
        error: error.message
      });
    }
  }

  // Cancel scheduled notification
  async cancelScheduledNotification(req, res) {
    try {
      const { id } = req.params;
      const notification = await SpNotification.findById(id);

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      if (notification.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: 'Only pending notifications can be cancelled'
        });
      }

      // Check access permissions
      const hasAccess = notification.userId && notification.userId.equals(req.user?._id) ||
                       notification.companyId && notification.companyId.equals(req.user?.companyId) ||
                       req.user?.role === 'admin';

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      // Cancel queue job if exists
      if (notification.processing.jobId) {
        const job = await notificationService.notificationQueue.getJob(notification.processing.jobId);
        if (job) {
          await job.remove();
        }
      }

      notification.status = 'cancelled';
      await notification.save();

      res.json({
        success: true,
        message: 'Notification cancelled successfully',
        data: notification
      });
    } catch (error) {
      console.error('Cancel notification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel notification',
        error: error.message
      });
    }
  }

  // Send admin notification with audience resolution and attachments
  async sendAdminNotification(req, res) {
    try {
      console.log('🚀 [ADMIN_NOTIFICATION] Request received');
      console.log('📋 [ADMIN_NOTIFICATION] Request body:', JSON.stringify(req.body, null, 2));
      console.log('📎 [ADMIN_NOTIFICATION] Files attached:', req.files ? req.files.length : 0);

      const validationErrors = validationResult(req);
      if (!validationErrors.isEmpty()) {
        console.log('❌ [ADMIN_NOTIFICATION] Validation failed:', validationErrors.array());
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors.array()
        });
      }

      console.log('✅ [ADMIN_NOTIFICATION] Validation passed');
      console.log('Request body:', req.body);
      let {
        title,
        message,
        type,
        channels,
        priority = 'medium',
        audiences = [],
        manualEmails = [],
        manualPhones = [],
        scheduledAt,
        searchUsers
      } = req.body;

      // Parse arrays if they are strings (from FormData)
      channels = typeof channels === 'string' ? JSON.parse(channels) : channels;
      audiences = typeof audiences === 'string' ? JSON.parse(audiences) : audiences;
      manualEmails = typeof manualEmails === 'string' ? JSON.parse(manualEmails) : manualEmails;
      manualPhones = typeof manualPhones === 'string' ? JSON.parse(manualPhones) : manualPhones;
      console.log('manualmail', manualEmails);


      console.log('📝 [ADMIN_NOTIFICATION] Extracted parameters:');
      console.log(`   Title: "${title}"`);
      console.log(`   Type: ${type}`);
      console.log(`   Channels: [${channels}]`);
      console.log(`   Priority: ${priority}`);
      console.log(`   Audiences: [${audiences}]`);
      console.log(`   Manual Emails: ${manualEmails.length} provided`);
      console.log(`   Manual Phones: ${manualPhones.length} provided`);
      console.log(`   Scheduled: ${scheduledAt ? 'Yes' : 'No'}`);
      console.log(`   Search Users: ${searchUsers ? 'Yes' : 'No'}`);

      // Handle file attachments if present
      let attachments = [];
      if (req.files && req.files.length > 0) {
        console.log(`📎 [ADMIN_NOTIFICATION] Processing ${req.files.length} attachments`);

        for (const file of req.files) {
          try {
            console.log(`📎 [ADMIN_NOTIFICATION] Uploading: ${file.originalname} (${file.size} bytes)`);

            // Upload to S3
            const folder = `notification-attachments/${Date.now()}`;
            const modifiedName = `${Date.now()}-${file.originalname}`;
            const fileUrl = await uploadToS3(file.buffer, folder, modifiedName, req.user.userId);

            console.log(`✅ [ADMIN_NOTIFICATION] Uploaded: ${fileUrl}`);

            // Add to attachments array
            attachments.push({
              filename: file.originalname,
              path: fileUrl,
              contentType: file.mimetype,
              size: file.size
            });
          } catch (uploadError) {
            console.error('❌ [ADMIN_NOTIFICATION] Upload failed:', uploadError);
            return res.status(500).json({
              success: false,
              message: `Failed to upload attachment: ${file.originalname}`,
              error: uploadError.message
            });
          }
        }
      }

      console.log(`📎 [ADMIN_NOTIFICATION] Attachments processed: ${attachments.length} files`);

      // Resolve audiences to recipients
      console.log('👥 [ADMIN_NOTIFICATION] Resolving audiences...');
      const recipients = await this.resolveAudiences(audiences, manualEmails, manualPhones, searchUsers);
      console.log('recipients', recipients);

      if (recipients.length === 0) {
        console.log('❌ [ADMIN_NOTIFICATION] No recipients found');
        return res.status(400).json({
          success: false,
          message: 'No valid recipients found'
        });
      }

      console.log(`✅ [ADMIN_NOTIFICATION] Resolved ${recipients.length} recipients`);
      console.log('👥 [ADMIN_NOTIFICATION] Recipients summary:');
      recipients.forEach((recipient, index) => {
        console.log(`   ${index + 1}. ${recipient.email || recipient.phone || 'Unknown'} (${recipient.name || 'No name'})`);
      });

      // Generate bulk ID for grouping notifications
      const bulkId = `bulk_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      console.log(`🆔 [ADMIN_NOTIFICATION] Generated bulk ID: ${bulkId}`);

      const results = [];
      const errors = [];

      // Process each channel
      for (const channel of channels) {
        console.log(`📡 [ADMIN_NOTIFICATION] Processing channel: ${channel}`);

        // Process each recipient
        for (const recipient of recipients) {
          try {
            console.log(`👤 [ADMIN_NOTIFICATION] Processing recipient: ${recipient.email || recipient.phone || 'Unknown'}`);

            // Check user preferences if userId is available
            if (recipient.userId) {
              console.log(`🔍 [ADMIN_NOTIFICATION] Checking preferences for user: ${recipient.userId}`);
              const shouldSend = await NotificationHelper.shouldSendNotification(
                recipient.userId,
                channel,
                type
              );

              if (!shouldSend) {
                console.log(`⏭️ [ADMIN_NOTIFICATION] Skipped - preferences disabled for user ${recipient.userId}`);
                continue;
              }
              console.log(`✅ [ADMIN_NOTIFICATION] Preferences allow sending to user ${recipient.userId}`);
            } else {
              console.log(`ℹ️ [ADMIN_NOTIFICATION] No userId - skipping preference check`);
            }

            // Use specific helper for tender notifications
            if (type === 'tender' && channel === 'email') {
              try {
                console.log(`📧 [ADMIN_NOTIFICATION] Using tender alert helper for ${recipient.email}`);
                const tenderData = {
                  ref: `TENDER-${Date.now()}`,
                  category: 'General',
                  location: 'Various',
                  description: message,
                  link: process.env.FRONTEND_URL || 'https://app.example.com',
                  attachments: attachments.map(att => ({
                    name: att.filename,
                    url: att.path
                  }))
                };

                const result = await NotificationHelper.sendNewTenderAlert(tenderData);
                console.log(`✅ [ADMIN_NOTIFICATION] Tender alert sent successfully to ${recipient.email}`);
                results.push(result);
                continue;
              } catch (tenderError) {
                console.error(`❌ [ADMIN_NOTIFICATION] Tender notification failed for ${recipient.email}:`, tenderError);
                // Fall through to regular notification
              }
            }

            // Prepare notification data
            console.log(`📝 [ADMIN_NOTIFICATION] Preparing regular notification for ${recipient.email || recipient.phone}`);
            const processedMessage = this.replacePlaceholders(message, recipient);
            console.log(`🔄 [ADMIN_NOTIFICATION] Message after placeholder replacement: "${processedMessage}"`);

            const notificationData = {
              companyId: recipient.companyId || null,
              userId: recipient.userId || null,
              channel,
              type,
              bulkId,
              priority,
              title,
              message: processedMessage,
              recipient: {
                email: recipient.email,
                phone: recipient.phone,
                userId: recipient.userId
              },
              attachments,
              metadata: {
                adminSent: true,
                sentBy: req.user?.userId || req.user?.id,
                audiences: audiences,
                originalMessage: message
              }
            };

            // Handle scheduling
            const options = {
              useQueue: !!scheduledAt,
              delay: scheduledAt ? new Date(scheduledAt).getTime() - Date.now() : 0
            };

            console.log(`📤 [ADMIN_NOTIFICATION] Sending via notificationService (scheduled: ${!!scheduledAt})`);
            const result = await notificationService.sendNotification(notificationData, options);
            console.log(`✅ [ADMIN_NOTIFICATION] Successfully sent to ${recipient.email || recipient.phone}`);
            results.push(result);

          } catch (recipientError) {
            console.error(`❌ [ADMIN_NOTIFICATION] Failed to send to ${recipient.email || recipient.phone}:`, recipientError);
            errors.push({
              recipient: recipient.email || recipient.phone,
              error: recipientError.message
            });
          }
        }
      }

      console.log('📊 [ADMIN_NOTIFICATION] Processing complete:');
      console.log(`   ✅ Successful sends: ${results.length}`);
      console.log(`   ❌ Failed sends: ${errors.length}`);
      console.log(`   📎 Attachments: ${attachments.length}`);
      console.log(`   🆔 Bulk ID: ${bulkId}`);

      if (errors.length > 0) {
        console.log('❌ [ADMIN_NOTIFICATION] Errors encountered:');
        errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error.recipient}: ${error.error}`);
        });
      }

      const responseData = {
        bulkId,
        totalSent: results.length,
        totalErrors: errors.length,
        results: results.slice(0, 5), // Limit response size
        errors: errors.slice(0, 5),
        attachments: attachments.length
      };

      console.log('🎉 [ADMIN_NOTIFICATION] Sending success response');
      res.status(201).json({
        success: true,
        message: `Notification ${scheduledAt ? 'scheduled' : 'sent'} successfully`,
        data: responseData
      });

    } catch (error) {
      console.error('💥 [ADMIN_NOTIFICATION] Fatal error:', error);
      console.error('💥 [ADMIN_NOTIFICATION] Stack trace:', error.stack);
      res.status(500).json({
        success: false,
        message: 'Failed to send notification',
        error: error.message
      });
    }
  }

  // Helper method to resolve audiences to recipients
  async resolveAudiences(audiences, manualEmails, manualPhones, searchUsers) {
    console.log('👥 [RESOLVE_AUDIENCES] Starting audience resolution');
    console.log(`👥 [RESOLVE_AUDIENCES] Audiences: [${audiences}]`);
    console.log(`👥 [RESOLVE_AUDIENCES] Manual emails: ${manualEmails.length}`);
    console.log(`👥 [RESOLVE_AUDIENCES] Manual phones: ${manualPhones.length}`);

    const recipients = [];
    const processedEmails = new Set();
    const processedPhones = new Set();

    // Process role-based audiences
    for (const audience of audiences) {
      try {
        console.log(`👥 [RESOLVE_AUDIENCES] Processing audience: ${audience}`);
        switch (audience) {
          case 'all_users':
            console.log('👥 [RESOLVE_AUDIENCES] Fetching all active users...');
            const allUsers = await User.find({ status: 'active' }).select('email phone firstName lastName _id spCompanyId');
            console.log(`👥 [RESOLVE_AUDIENCES] Found ${allUsers.length} active users`);

            for (const user of allUsers) {
              if (user.email && !processedEmails.has(user.email)) {
                recipients.push({
                  email: user.email,
                  phone: user.phone,
                  userId: user._id,
                  companyId: user.spCompanyId,
                  name: `${user.firstName} ${user.lastName}`.trim()
                });
                processedEmails.add(user.email);
              }
            }
            console.log(`👥 [RESOLVE_AUDIENCES] Added ${allUsers.length} users to recipients`);
            break;

          case 'all_admins':
            console.log('👥 [RESOLVE_AUDIENCES] Fetching all admin users...');
            const adminUsers = await User.find({ type: 'admin', status: 'active' }).select('email phone firstName lastName _id spCompanyId');
            console.log(`👥 [RESOLVE_AUDIENCES] Found ${adminUsers.length} admin users`);

            for (const user of adminUsers) {
              if (user.email && !processedEmails.has(user.email)) {
                recipients.push({
                  email: user.email,
                  phone: user.phone,
                  userId: user._id,
                  companyId: user.spCompanyId,
                  name: `${user.firstName} ${user.lastName}`.trim()
                });
                processedEmails.add(user.email);
              }
            }
            console.log(`👥 [RESOLVE_AUDIENCES] Added ${adminUsers.length} admin users to recipients`);
            break;

          case 'all_suppliers':
            const supplierCompanies = await SpCompany.find({ type: 'supplier', status: 'active' }).populate('users');
            for (const company of supplierCompanies) {
              if (company.users) {
                for (const user of company.users) {
                  if (user.email && !processedEmails.has(user.email)) {
                    recipients.push({
                      email: user.email,
                      phone: user.phone,
                      userId: user._id,
                      companyId: company._id,
                      name: `${user.firstName} ${user.lastName}`.trim()
                    });
                    processedEmails.add(user.email);
                  }
                }
              }
            }
            break;

          case 'all_buyers':
            const buyerCompanies = await SpCompany.find({ type: 'buyer', status: 'active' }).populate('users');
            for (const company of buyerCompanies) {
              if (company.users) {
                for (const user of company.users) {
                  if (user.email && !processedEmails.has(user.email)) {
                    recipients.push({
                      email: user.email,
                      phone: user.phone,
                      userId: user._id,
                      companyId: company._id,
                      name: `${user.firstName} ${user.lastName}`.trim()
                    });
                    processedEmails.add(user.email);
                  }
                }
              }
            }
            break;
        }
      } catch (audienceError) {
        console.error(`Error resolving audience ${audience}:`, audienceError);
      }
    }

    // Process manual emails
    console.log(`👥 [RESOLVE_AUDIENCES] Processing ${manualEmails.length} manual emails`);
    for (const email of manualEmails) {
      if (email && !processedEmails.has(email)) {
        recipients.push({
          email: email.trim(),
          name: email.trim()
        });
        processedEmails.add(email);
        console.log(`👥 [RESOLVE_AUDIENCES] Added manual email: ${email.trim()}`);
      }
    }

    // Process manual phones
    console.log(`👥 [RESOLVE_AUDIENCES] Processing ${manualPhones.length} manual phones`);
    for (const phone of manualPhones) {
      if (phone && !processedPhones.has(phone)) {
        recipients.push({
          phone: phone.trim(),
          name: phone.trim()
        });
        processedPhones.add(phone);
        console.log(`👥 [RESOLVE_AUDIENCES] Added manual phone: ${phone.trim()}`);
      }
    }

    // Process search users (if implemented)
    if (searchUsers) {
      try {
        console.log(`👥 [RESOLVE_AUDIENCES] Searching users with query: "${searchUsers}"`);
        const searchResults = await User.find({
          $or: [
            { firstName: { $regex: searchUsers, $options: 'i' } },
            { lastName: { $regex: searchUsers, $options: 'i' } },
            { email: { $regex: searchUsers, $options: 'i' } }
          ],
          status: 'active'
        }).select('email phone firstName lastName _id spCompanyId');

        console.log(`👥 [RESOLVE_AUDIENCES] Found ${searchResults.length} users matching search`);

        for (const user of searchResults) {
          if (user.email && !processedEmails.has(user.email)) {
            recipients.push({
              email: user.email,
              phone: user.phone,
              userId: user._id,
              companyId: user.spCompanyId,
              name: `${user.firstName} ${user.lastName}`.trim()
            });
            processedEmails.add(user.email);
            console.log(`👥 [RESOLVE_AUDIENCES] Added search result: ${user.email}`);
          }
        }
      } catch (searchError) {
        console.error('❌ [RESOLVE_AUDIENCES] Error searching users:', searchError);
      }
    }

    console.log(`✅ [RESOLVE_AUDIENCES] Resolution complete: ${recipients.length} total recipients`);
    console.log(`👥 [RESOLVE_AUDIENCES] Breakdown: ${processedEmails.size} emails, ${processedPhones.size} phones`);

    return recipients;
  }

  // Helper method to replace placeholders in message
  replacePlaceholders(message, recipient) {
    let processedMessage = message;

    // Replace {{name}} with recipient name
    if (recipient.name) {
      processedMessage = processedMessage.replace(/\{\{name\}\}/g, recipient.name);
    }

    // Replace {{order}} with order information (if available)
    // This could be enhanced to fetch actual order data
    processedMessage = processedMessage.replace(/\{\{order\}\}/g, 'your recent order');

    return processedMessage;
  }

  // Download notification attachments
  async downloadNotificationAttachments(req, res) {
    try {
      const { notificationId } = req.params;

      // Find the notification
      const notification = await SpNotification.findById(notificationId);
      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      if (!notification.attachments || notification.attachments.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No attachments found for this notification'
        });
      }

      // For single attachment, download directly
      if (notification.attachments.length === 1) {
        const attachment = notification.attachments[0];
        const s3Key = attachment.path;

        if (!s3Key) {
          return res.status(400).json({
            success: false,
            message: 'Attachment metadata is missing the S3 file path'
          });
        }

        const s3Response = await getObjectFromS3(s3Key);
        if (!s3Response) {
          return res.status(404).json({
            success: false,
            message: 'File could not be found in storage'
          });
        }

        // Set headers to trigger a download
        res.setHeader('Content-Type', attachment.contentType || 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${attachment.filename}"`);
        res.setHeader('Content-Length', s3Response.ContentLength);

        // Stream the file body from S3 directly to the client's response
        s3Response.Body.pipe(res);
      } else {
        // For multiple attachments, return list of download URLs
        const attachmentUrls = notification.attachments.map((attachment, index) => ({
          filename: attachment.filename,
          downloadUrl: `${req.protocol}://${req.get('host')}/api/notifications/${notificationId}/attachments/${index}/download`,
          size: attachment.size,
          contentType: attachment.contentType
        }));

        res.json({
          success: true,
          data: {
            notificationId,
            attachments: attachmentUrls
          }
        });
      }
    } catch (error) {
      console.error('Download notification attachments error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download attachments',
        error: error.message
      });
    }
  }

  // Handle public notification requests (no auth required)
  async handlePublicNotification(req, res) {
    try {
      console.log('🌐 [PUBLIC_NOTIFICATION] Request received');
      console.log('📋 [PUBLIC_NOTIFICATION] Request body:', JSON.stringify(req.body, null, 2));

      const { type, name, email, phone, company, message, subject } = req.body;

      // Validate required fields
      if (!type || !name || !email || !message) {
        console.log('❌ [PUBLIC_NOTIFICATION] Missing required fields');
        return res.status(400).json({
          success: false,
          message: 'Missing required fields: type, name, email, and message are required'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.log('❌ [PUBLIC_NOTIFICATION] Invalid email format');
        return res.status(400).json({
          success: false,
          message: 'Invalid email format'
        });
      }

      console.log(`📝 [PUBLIC_NOTIFICATION] Processing ${type} request from ${email}`);

      // Create notification record
      const notificationData = {
        type: 'system',
        channel: 'email',
        priority: 'high',
        title: type === 'demo' ? 'Demo Request Received' : 'Buyer Inquiry Received',
        message: `
          ${type === 'demo' ? 'Demo Request' : 'Buyer Inquiry'} Details:

          Name: ${name}
          Email: ${email}
          ${phone ? `Phone: ${phone}` : ''}
          ${company ? `Company: ${company}` : ''}
          ${subject ? `Subject: ${subject}` : ''}

          Message:
          ${message}
        `,
        recipient: {
          email: email,
          name: name
        },
        status: 'delivered',
        delivery: {
          attempts: 1,
          maxAttempts: 1,
          deliveredAt: new Date()
        },
        metadata: {
          publicRequest: true,
          requestType: type,
          originalData: {
            name,
            email,
            phone,
            company,
            message,
            subject
          }
        }
      };

      // Save notification to database
      const notification = new SpNotification(notificationData);
      await notification.save();

      console.log(`✅ [PUBLIC_NOTIFICATION] Notification saved with ID: ${notification._id}`);

      // Send confirmation email to requester
      try {
        const confirmationMessage = type === 'demo'
          ? `Thank you for requesting a personalized demo of TenderAsili. We have received your request and our team will contact you within 24 hours to schedule your demo session.`
          : `Thank you for your interest in TenderAsili. We have received your inquiry and our team will respond to you within 24 hours.`;

        const confirmationData = {
          type: 'system',
          channel: 'email',
          priority: 'medium',
          title: type === 'demo' ? 'Demo Request Confirmation' : 'Inquiry Confirmation',
          message: `
            Dear ${name},

            ${confirmationMessage}

            Your request details:
            ${subject ? `Subject: ${subject}` : ''}
            Message: ${message}

            Best regards,
            TenderAsili Team
          `,
          recipient: {
            email: email,
            name: name
          },
          metadata: {
            publicConfirmation: true,
            originalRequestId: notification._id
          }
        };

        // Send confirmation via notification service
        await notificationService.sendNotification(confirmationData);
        console.log(`📧 [PUBLIC_NOTIFICATION] Confirmation email sent to ${email}`);

      } catch (confirmationError) {
        console.error('⚠️ [PUBLIC_NOTIFICATION] Failed to send confirmation email:', confirmationError);
        // Don't fail the main request if confirmation fails
      }

      // Send internal notification to admin team
      try {
        const adminNotificationData = {
          type: 'system',
          channel: 'email',
          priority: 'high',
          title: `New ${type === 'demo' ? 'Demo Request' : 'Buyer Inquiry'}`,
          message: `
            A new ${type === 'demo' ? 'demo request' : 'buyer inquiry'} has been received:

            Name: ${name}
            Email: ${email}
            ${phone ? `Phone: ${phone}` : ''}
            ${company ? `Company: ${company}` : ''}
            ${subject ? `Subject: ${subject}` : ''}

            Message:
            ${message}

            Please follow up within 24 hours.
          `,
          recipient: {
            email: process.env.ADMIN_EMAIL || '<EMAIL>'
          },
          metadata: {
            internalNotification: true,
            requestType: type,
            originalRequestId: notification._id
          }
        };

        await notificationService.sendNotification(adminNotificationData);
        console.log(`📧 [PUBLIC_NOTIFICATION] Admin notification sent`);

      } catch (adminError) {
        console.error('⚠️ [PUBLIC_NOTIFICATION] Failed to send admin notification:', adminError);
        // Don't fail the main request if admin notification fails
      }

      console.log('🎉 [PUBLIC_NOTIFICATION] Request processed successfully');

      res.status(201).json({
        success: true,
        message: type === 'demo'
          ? 'Demo request submitted successfully. You will receive a confirmation email shortly.'
          : 'Inquiry submitted successfully. You will receive a confirmation email shortly.',
        data: {
          requestId: notification._id,
          type: type,
          status: 'received'
        }
      });

    } catch (error) {
      console.error('💥 [PUBLIC_NOTIFICATION] Error processing request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process request. Please try again later.',
        error: error.message
      });
    }
  }
}

export default new notificationController();