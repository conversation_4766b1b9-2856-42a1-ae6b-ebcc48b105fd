const generateToken = require('../utils/token');
const User = require('../models/sp_user');

const login = async (req, res) => {
  try {
    // ...existing code for validating user credentials...
    const user = await User.findOne({ email: req.body.email });
    if (!user || user.password !== req.body.password) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const token = generateToken(user._id);
    res.status(200).json({ token, user });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

const register = async (req, res) => {
  // ...existing code for user registration...
  const { name, email, password, role } = req.body;
  const user = new User({ name, email, password, role });
  try {
    await user.save();
    const token = generateToken(user._id); // Generate JWT token after registration
    res.status(201).json({ message: 'User registered successfully', token, user });
  } catch (error) {
    res.status(400).json({ error: 'Error registering user' });
  }
};

// Add more functions as needed for user management, password reset, etc.
// Example function to get user profile
const getUserProfile = async (req, res) => {
  try {
    const userId = req.user._id; // Assuming you have middleware to set req.user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.status(200).json(user);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Example function to update user profile
const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user._id; // Assuming you have middleware to set req.user
    const updates = req.body;
    const user = await User.findByIdAndUpdate(userId, updates, { new: true });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.status(200).json(user);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Example function to delete user account  
const deleteUserAccount = async (req, res) => {
  try {
    const userId = req.user._id; // Assuming you have middleware to set req.user
    const user = await User.findByIdAndDelete(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.status(200).json({ message: 'User account deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Example function to reset password
const resetPassword = async (req, res) => {
  try {
    const userId = req.user._id; // Assuming you have middleware to set req.user
    const { oldPassword, newPassword } = req.body;
    const user = await User.findById(userId);
    if (!user || user.password !== oldPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    user.password = newPassword;
    await user.save();
    res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Example function to send password reset email

module.exports = { login };
