import mongoose from 'mongoose';
import SpApplication from '../models/sp_application.js';
import SpCompany from '../models/sp_company.js';
import SpJobCategory from '../models/sp_job_category.js';
import SpJob from '../models/sp_job.js';
import archiver from 'archiver';
import ExcelJS from 'exceljs';

import { uploadToS3, deleteFromS3, getObjectFromS3 } from '../services/s3Service.js';

// Get applications for a specific company
export const getCompanyApplications = async (req, res) => {
    try {
        const { companyId } = req.params;
        const filter = {spSupplierCompanyId: companyId};
    
        if (req.query.type === 'supplier_prequalification' || req.query.type === 'supplier_registration') {
          filter.type = req.query.type;
        } else {
            //return res.status(404).json({ 
            //    message: 'Specify either supplier registtration or prequalification for the company',
            //    success: false,
            //    applicationsData: [] 
            //});
        }
        // Validate companyId
        if (!mongoose.Types.ObjectId.isValid(companyId)) {
            return res.status(400).json({ 
                message: 'Invalid company ID',
                success: false 
            });
        }

        // Find applications for the specified company
        const applications = await SpApplication.find( filter ).sort({ createdAt: -1 });

        // If no applications found
        if (!applications || applications.length === 0) {
            return res.status(404).json({ 
                message: 'No applications found for this company',
                success: false,
                applicationsData: [] 
            });
        }

        // Fetch buyer company details for each application
        const applicationsWithBuyerDetails = await Promise.all(
            applications.map(async (application) => {
                // Find buyer company details
                const buyerCompany = await SpCompany.findById(application.spBuyerCompanyId)
                    .select('name logoUrl address');

                // Create a plain object to modify
                const appObject = application.toObject();
                
                // Add buyer company details
                appObject.buyerData =  {
                        name: buyerCompany.name,
                        logoUrl: buyerCompany.logoUrl,
                        address: buyerCompany.address
                };

                return appObject;
            })
        );

        res.status(200).json({ 
            message: 'Applications retrieved successfully',
            success: true,
            applicationsData: applicationsWithBuyerDetails 
        });

    } catch (error) {
        console.error('Error retrieving company applications:', error);
        res.status(500).json({ 
            message: 'Error retrieving applications',
            success: false,
            error: error.message 
        });
    }
};

// Get a single application by ID for a specific company
export const getCompanyApplicationById = async (req, res) => {
    try {
        const { companyId, applicationId } = req.params;

        // Validate companyId and applicationId
        if (!mongoose.Types.ObjectId.isValid(companyId)) {
            return res.status(400).json({ 
                message: 'Invalid company ID',
                success: false 
            });
        }

        if (!mongoose.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({ 
                message: 'Invalid application ID',
                success: false 
            });
        }

        // Find the specific application for the specified company
        const application = await SpApplication.findOne({ 
            _id: applicationId, 
            spSupplierCompanyId: companyId 
        });

        // If application not found
        if (!application) {
            return res.status(404).json({ 
                message: 'Application not found',
                success: false 
            });
        }

        // Find buyer company details
        const buyerCompany = await SpCompany.findById(application.spBuyerCompanyId)
            .select('name logoUrl address');

        // Create a plain object to modify
        const appObject = application.toObject();
        
        // Add buyer company details
        appObject.buyerData = {
                name: buyerCompany.name,
                logoUrl: buyerCompany.logoUrl,
                address: buyerCompany.address
        };

        res.status(200).json({ 
            message: 'Application retrieved successfully',
            success: true,
            applicationData: appObject 
        });

    } catch (error) {
        console.error('Error retrieving company application:', error);
        res.status(500).json({ 
            message: 'Error retrieving application',
            success: false,
            error: error.message 
        });
    }
};

export const updateCompanyApplication = async (req, res) => {
    try {
        const { companyId, applicationId } = req.params;
        const { fields, status } = req.body; // Extract fields and the target status from the payload

        // 1. --- Validation and Authorization ---
        if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({ message: 'Invalid company or application ID.' });
        }

        // Find the application, ensuring it belongs to the correct company
        const application = await SpApplication.findOne({ 
            _id: applicationId, 
            spSupplierCompanyId: companyId 
        });

        if (!application) {
            return res.status(404).json({ message: 'Application not found.' });
        }

        // 2. --- State Management Check ---
        // Crucial: Only allow updates if the application is in 'draft' status.
        // Once submitted, it should not be editable from this endpoint.
        if (application.status !== 'draft') {
            return res.status(403).json({ 
                message: `Application cannot be updated because its status is '${application.status}'.` 
            });
        }
        
        // 3. --- Update the Application Document ---
        
        // Replace the fields array with the new data from the frontend
        application.fields = fields;

        // Update the status
        application.status = status;
        
        // Set 'updatedBy' to the current user
        application.updatedBy = req.user.userId; // Assumes auth middleware provides req.user.userId

        // If the application is being submitted, set the submission timestamp
        if (status === 'submitted') {
            application.submittedAt = new Date();
            // The 'submittedBy' field should already be set, but you could re-affirm it here if needed
            application.submittedBy = req.user.userId;
            // **Call the scoring helper to calculate the initial system score**
            calculateAndUpdateScores(application);
        }

        // 4. --- Save and Respond ---
        // Save the updated document to the database
        const updatedApplication = await application.save();
        console.log('Updated pplication');

        res.status(200).json({
            message: `Application successfully ${status === 'submitted' ? 'submitted' : 'saved'}.`,
            success: true,
            applicationData: updatedApplication // Send the complete, updated application back to the client
        });

    } catch (error) {
        // Handle potential validation errors from Mongoose or other server errors
        console.error('Error updating company application:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({ message: 'Validation Error', error: error.message });
        }
        res.status(500).json({ 
            message: 'An error occurred while updating the application.',
            error: error.message 
        });
    }
};
/**
 * @route   POST /api/applications/:applicationId/documents
 * @desc    Uploads a document to a specific field within an application. Replaces any existing document in that field.
 * @access  Private
 *
 * The request must be multipart/form-data and contain:
 * - A file field named 'file'.
 * - A text field named 'fieldName' which is the unique name of the target field.
 */
export const uploadApplicationFile = async (req, res) => {
    console.log('Uploading application file:', req.file);

    console.log('Uploading application body:', req.body);
  try {
    const { companyId, applicationId } = req.params;

    const { fieldName } = req.body; // The unique name of the field, e.g., 'companyRegistration'

    if (!req.file) {
        console.log('No file was uploaded.');
      return res.status(400).json({ error: 'No file was uploaded.' });
    }
    if (!fieldName) {
      console.log('The "fieldName" is required to identify the document\'s destination.');
      return res.status(400).json({ error: 'The "fieldName" is required to identify the document\'s destination.' });
    }
    console.log('fieldName', fieldName);

    const application = await SpApplication.findById(applicationId);
    if (!application) {
      return res.status(404).json({ error: 'Application not found.' });
    }

    // Find the specific field within the application's 'fields' array
    const targetField = application.fields.find(f => f.name === fieldName);
    if (!targetField) {
        coonsole.log(`Field '${fieldName}' does not exist in this application.`)
      return res.status(404).json({ error: `Field '${fieldName}' does not exist in this application.` });
    }

    // --- Replacement Logic: Delete old files from S3 and clear the documents array ---
    if (targetField.documents && targetField.documents.length > 0) {
      console.log(`Replacing ${targetField.documents.length} existing document(s) in field '${fieldName}'.`);
      // Create an array of deletion promises
      try {
        const deletionPromises = targetField.documents.map(doc => deleteFromS3(doc.filePath));
        await Promise.all(deletionPromises);
      } catch (error) {
        console.error(`Could not deleete existing file`, error);
      }
    }
    // Clear the documents array for the target field
    targetField.documents = [];
    // --- End of replacement logic ---

    const file = req.file;
    
    // Upload the new file to S3
    const folder = `application-documents/${applicationId}/${fieldName}`;
    const modifiedName = `${Date.now()}-${file.originalname}`;
    const fileUrl = await uploadToS3(file.buffer, folder, modifiedName, companyId);
    console.log(`FILE URL`, fileUrl);

    // Create the new document object according to your schema
    const newDocument = {
      filePath: fileUrl, // The S3 Key for future retrieval/deletion
      fileName: file.originalname,
      fileType: file.mimetype,
      fileSize: file.size,
      uploadedAt: new Date(),
      // 'url' is not in your sub-schema, so we omit it. filePath is the key.
    };

    // Add the new document to the target field's documents array
    targetField.documents.push(newDocument);
    
    application.updatedBy = req.user.userId; // Assuming auth middleware
    
    // Mark the path as modified since it's a nested array
    application.markModified('fields');

    await application.save();
    console.log('Application document updated successfully.')

    res.status(200).json({
      message: `File uploaded successfully to field '${fieldName}'.`,
      document: newDocument,
    });

  } catch (error) {
    console.error('Error uploading application file:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};


/**
 * @route   GET /api/applications/:applicationId/fields/:fieldName/documents/:fileName
 * @desc    Downloads a specific document from a specific field in an application.
 * @access  Private
 */
export const downloadApplicationDocument = async (req, res) => {
    try {
        console.log('Downloading application');
      const { applicationId, fieldName, fileName } = req.params;
  
      const application = await SpApplication.findById(applicationId);
      if (!application) {
        return res.status(404).json({ error: 'Application not found.' });
      }
  
      // Find the specific field that holds the document
      const targetField = application.fields.find(f => f.name === fieldName);
      if (!targetField) {
        return res.status(404).json({ error: `Field '${fieldName}' not found.` });
      }
  
      // Find the specific document within that field by its file name
      const document = targetField.documents.find(doc => doc.fileName === fileName);
      if (!document) {
        return res.status(404).json({ error: `Document '${fileName}' not found in field '${fieldName}'.` });
      }
  
      // Use the S3 key (filePath) to retrieve the object
      const s3Key = document.filePath;
      if (!s3Key) {
          return res.status(400).send('Document metadata is missing the S3 file path.');
      }
  
      const s3Response = await getObjectFromS3(s3Key);
      if (!s3Response) {
        return res.status(404).send('File could not be found in storage.');
      }
  
      // Set headers to trigger a download in the browser
      res.setHeader('Content-Type', document.fileType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${document.fileName}"`);
      res.setHeader('Content-Length', s3Response.ContentLength);
  
      // Stream the file body from S3 directly to the client's response
      s3Response.Body.pipe(res);
  
    } catch (error) {
      console.error('S3 Download Error:', error);
      res.status(500).send('Failed to fetch file from storage.');
    }
  };

  /**
 * Adds reviewer information, sets the compliance score, and calculates the final total score for an application.
 * @route   PUT /api/applications/:applicationId/review
 * @access  Private (for reviewers)
 */
export const addReviewToApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { reviewerNotes, complianceScore, reviewStatus } = req.body;

        // --- Input Validation ---
        if (!mongoose.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({ message: 'Invalid application ID.' });
        }
        if (!['approved', 'rejected'].includes(reviewStatus)) {
            return res.status(400).json({ message: 'Invalid review status. Must be "approved" or "rejected".' });
        }
        if (complianceScore === undefined || isNaN(parseFloat(complianceScore))) {
             return res.status(400).json({ message: 'A valid numeric complianceScore is required.' });
        }

        const application = await SpApplication.findById(applicationId);
        if (!application) {
            return res.status(404).json({ message: 'Application not found.' });
        }

        // --- State Validation ---
        // Ensure the application has been submitted before it can be reviewed.
        if (!['submitted', 'under_review'].includes(application.status)) {
            return res.status(403).json({
                message: `Application cannot be reviewed. Its current status is '${application.status}'.`
            });
        }

        // --- Update Review and Status Fields ---
        application.reviewerNotes = reviewerNotes;
        application.complianceScore = parseFloat(complianceScore);
        application.reviewStatus = reviewStatus;
        application.status = reviewStatus; // Update the main application status to 'approved' or 'rejected'
        application.reviewer = req.user.userId; // Assumes auth middleware provides req.user
        application.reviewDate = new Date();
        application.updatedBy = req.user.userId;

        // --- Calculate and Finalize Scores ---
        calculateAndUpdateScores(application);

        // --- Save and Respond ---
        const reviewedApplication = await application.save();

        res.status(200).json({
            message: 'Application review submitted and scores updated successfully.',
            success: true,
            applicationData: reviewedApplication
        });

    } catch (error) {
        console.error('Error adding review to application:', error);
        res.status(500).json({
            message: 'An error occurred while adding the review.',
            error: error.message
        });
    }
};

/**
 * Calculates the system score based on the application's fields and updates the total score.
 * This function modifies the application document in-place but does not save it.
 * @param {object} application - The Mongoose application document to score.
 */
const calculateAndUpdateScores = (application) => {
    let calculatedSystemScore = 0;

    // Iterate over each field in the application to calculate its score
    for (const field of application.fields) {
        // Only process fields that are marked as scoreable
        if (field.isScoreable) {
            let fieldScore = 0;
            const hasValue = field.value !== null && field.value !== undefined && field.value !== '';

            if (hasValue) {
                // If the field uses specific scores for options (e.g., radio buttons)
                if (field.optionScore && field.optionScore.length > 0) {
                    const matchedOption = field.optionScore.find(opt => opt.value === field.value);
                    if (matchedOption && typeof matchedOption.score === 'number') {
                        fieldScore = matchedOption.score;
                    }
                }
                // Otherwise, if the field is just filled (e.g., a file upload or text field), award its max score
                else {
                    fieldScore = field.maxScore || 0;
                }
            }
            // If a scoreable field is not filled, its score remains 0.

            // Assign the calculated score to the field itself for traceability
            field.score = fieldScore;
            calculatedSystemScore += fieldScore;
        } else {
             // If a field is not scoreable, ensure its score is null
             field.score = null;
        }
    }

    // Update the main scores on the application document
    application.systemScore = calculatedSystemScore;
    application.totalScore = (application.systemScore || 0) + (application.complianceScore || 0);
};

// ===== ADMIN APPLICATION MANAGEMENT FUNCTIONS =====

/**
 * Get all applications for a specific supplier company (Admin view)
 * @route   GET /api/admin/suppliers/:companyId/applications
 * @access  Private (Admin only)
 */
export const getSupplierApplications = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { type, status, page = 1, limit = 10, search } = req.query;

        // Validate companyId
        if (!mongoose.Types.ObjectId.isValid(companyId)) {
            return res.status(400).json({
                message: 'Invalid company ID',
                success: false
            });
        }

        // Build filter
        const filter = { spSupplierCompanyId: companyId };

        if (type && ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'].includes(type)) {
            filter.type = type;
        }

        if (status && ['draft', 'submitted', 'under_review', 'approved', 'rejected'].includes(status)) {
            filter.status = status;
        }

        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { ref: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get total count for pagination
        const totalCount = await SpApplication.countDocuments(filter);

        // Find applications with pagination
        const applications = await SpApplication.find(filter)
            .populate('spBuyerCompanyId', 'name logoUrl address')
            .populate('spSupplierCompanyId', 'name logoUrl address')
            .populate('submittedBy', 'firstName lastName email')
            .populate('reviewer', 'firstName lastName email')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        res.status(200).json({
            message: 'Applications retrieved successfully',
            success: true,
            data: {
                applications,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(totalCount / parseInt(limit)),
                    totalCount,
                    hasNext: skip + applications.length < totalCount,
                    hasPrev: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('Error retrieving supplier applications:', error);
        res.status(500).json({
            message: 'Error retrieving applications',
            success: false,
            error: error.message
        });
    }
};

/**
 * Get a specific application by ID (Admin view)
 * @route   GET /api/admin/suppliers/:companyId/applications/:applicationId
 * @access  Private (Admin only)
 */
export const getSupplierApplicationById = async (req, res) => {
    try {
        const { companyId, applicationId } = req.params;

        // Validate IDs
        if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({
                message: 'Invalid company or application ID',
                success: false
            });
        }

        // Find the application
        const application = await SpApplication.findOne({
            _id: applicationId,
            spSupplierCompanyId: companyId
        })
        .populate('spBuyerCompanyId', 'name logoUrl address')
        .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber contactPerson email phone')
        .populate('submittedBy', 'firstName lastName email')
        .populate('reviewer', 'firstName lastName email')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'title description type');

        if (!application) {
            return res.status(404).json({
                message: 'Application not found',
                success: false
            });
        }

        res.status(200).json({
            message: 'Application retrieved successfully',
            success: true,
            data: application
        });

    } catch (error) {
        console.error('Error retrieving supplier application:', error);
        res.status(500).json({
            message: 'Error retrieving application',
            success: false,
            error: error.message
        });
    }
};

/**
 * Admin review application - Add review comments and compliance score
 * @route   PUT /api/admin/suppliers/:companyId/applications/:applicationId/review
 * @access  Private (Admin only)
 */
export const adminReviewApplication = async (req, res) => {
    try {
        const { companyId, applicationId } = req.params;
        const { reviewerNotes, complianceScore, reviewStatus, fieldReviews } = req.body;

        // Validate IDs
        if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({
                message: 'Invalid company or application ID',
                success: false
            });
        }

        // Validate required fields
        if (!reviewStatus || !['approved', 'rejected', 'under_review'].includes(reviewStatus)) {
            return res.status(400).json({
                message: 'Invalid review status. Must be "approved", "rejected", or "under_review"',
                success: false
            });
        }

        if (complianceScore !== undefined && (isNaN(parseFloat(complianceScore)) || complianceScore < 0)) {
            return res.status(400).json({
                message: 'Compliance score must be a valid positive number',
                success: false
            });
        }

        // Find the application
        const application = await SpApplication.findOne({
            _id: applicationId,
            spSupplierCompanyId: companyId
        });

        if (!application) {
            return res.status(404).json({
                message: 'Application not found',
                success: false
            });
        }

        // Check if application can be reviewed (must be submitted)
        if (!['submitted', 'under_review'].includes(application.status)) {
            return res.status(403).json({
                message: `Application cannot be reviewed. Current status: ${application.status}`,
                success: false
            });
        }

        // Update application review fields
        application.reviewerNotes = reviewerNotes;
        application.complianceScore = complianceScore ? parseFloat(complianceScore) : 0;
        application.reviewStatus = reviewStatus;
        application.reviewer = req.user.userId;
        application.reviewDate = new Date();
        application.updatedBy = req.user.userId;

        // Update field-level review comments if provided
        if (fieldReviews && Array.isArray(fieldReviews)) {
            fieldReviews.forEach(fieldReview => {
                const field = application.fields.find(f => f.name === fieldReview.fieldName);
                if (field) {
                    field.reviewerComment = fieldReview.comment;
                    if (fieldReview.complianceScore !== undefined) {
                        field.complianceScore = parseFloat(fieldReview.complianceScore);
                    }
                }
            });
        }

        // Update application status based on review
        if (reviewStatus === 'approved' || reviewStatus === 'rejected') {
            application.status = reviewStatus;
        } else {
            application.status = 'under_review';
        }

        // Recalculate scores
        calculateAndUpdateScores(application);

        // Save the application
        const reviewedApplication = await application.save();

        res.status(200).json({
            message: 'Application review submitted successfully',
            success: true,
            data: reviewedApplication
        });

    } catch (error) {
        console.error('Error reviewing application:', error);
        res.status(500).json({
            message: 'Error submitting application review',
            success: false,
            error: error.message
        });
    }
};

/**
 * Get applications for a specific job category (Admin/Buyer view)
 * @route   GET /api/admin/categories/:categoryId/applications
 * @access  Private (Admin/Buyer)
 */
export const getCategoryApplications = async (req, res) => {
    try {
        const { categoryId } = req.params;
        const { status, page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

        // Validate categoryId
        if (!mongoose.Types.ObjectId.isValid(categoryId)) {
            return res.status(400).json({
                message: 'Invalid category ID',
                success: false
            });
        }

        // Build filter
        const filter = { spJobCategoryId: categoryId };

        if (status && ['draft', 'submitted', 'under_review', 'approved', 'rejected'].includes(status)) {
            filter.status = status;
        }

        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { ref: { $regex: search, $options: 'i' } }
            ];
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Get total count for pagination
        const totalCount = await SpApplication.countDocuments(filter);

        // Find applications with pagination
        const applications = await SpApplication.find(filter)
            .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber')
            .populate('submittedBy', 'firstName lastName email')
            .populate('reviewer', 'firstName lastName email')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit))
            .select('title ref status systemScore complianceScore totalScore submittedAt reviewDate reviewStatus spSupplierCompanyId submittedBy reviewer');

        res.status(200).json({
            message: 'Category applications retrieved successfully',
            success: true,
            data: {
                applications,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(totalCount / parseInt(limit)),
                    totalCount,
                    hasNext: skip + applications.length < totalCount,
                    hasPrev: parseInt(page) > 1
                }
            }
        });

    } catch (error) {
        console.error('Error retrieving category applications:', error);
        res.status(500).json({
            message: 'Error retrieving applications',
            success: false,
            error: error.message
        });
    }
};


export const getJobApplications = async (req, res) => {
    try {
        const { companyId, jobId } = req.params;
        const { status, page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

        // Validate IDs
        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({
                message: 'Invalid job ID',
                success: false
            });
        }

        // Build filter
        const filter = { spJobId: jobId };

        if (companyId) {
            if (!mongoose.Types.ObjectId.isValid(companyId)) {
                return res.status(400).json({
                    message: 'Invalid company ID',
                    success: false
                });
            }
            filter.spBuyerCompanyId = companyId;
        }
        // Get total count for pagination
        const totalCount = await SpApplication.countDocuments(filter);

        // Find applications with pagination
        const applications = await SpApplication.find(filter)
            .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber')
            .populate('submittedBy', 'firstName lastName email')
            .populate('reviewer', 'firstName lastName email')
            .select('title ref status systemScore complianceScore totalScore submittedAt reviewDate reviewStatus spSupplierCompanyId spJobCategoryId submittedBy reviewer');

        res.status(200).json({
            message: 'Job applications retrieved successfully',
            success: true,
            data: {
                applications
            }
        });

    } catch (error) {
        console.error('Error retrieving job applications:', error);
        res.status(500).json({
            message: 'Error retrieving applications',
            success: false,
            error: error.message
        });
    }
};

// ===== DOWNLOAD AND REPORT FUNCTIONS =====

/**
 * Download all applications for a category as ZIP file
 * @route   GET /api/admin/categories/:categoryId/applications/download/zip
 * @access  Private (Admin/Buyer)
 */
export const downloadCategoryApplicationsZip = async (req, res) => {
    try {
        const { categoryId } = req.params;
        const { includeDocuments = 'true' } = req.query;

        // Validate categoryId
        if (!mongoose.Types.ObjectId.isValid(categoryId)) {
            return res.status(400).json({
                message: 'Invalid category ID',
                success: false
            });
        }

        // Get category details
        const category = await SpJobCategory.findById(categoryId);
        if (!category) {
            return res.status(404).json({
                message: 'Category not found',
                success: false
            });
        }

        // Get all applications for this category
        const applications = await SpApplication.find({ spJobCategoryId: categoryId })
            .populate('spSupplierCompanyId', 'name registrationNumber')
            .populate('submittedBy', 'firstName lastName email');

        if (applications.length === 0) {
            return res.status(404).json({
                message: 'No applications found for this category',
                success: false
            });
        }

        // Create ZIP archive
        const archive = archiver('zip', {
            zlib: { level: 9 } // Maximum compression
        });

        // Set response headers
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${category.title}_applications.zip"`);

        // Pipe archive to response
        archive.pipe(res);

        // Add applications to ZIP
        for (const application of applications) {
            const supplierName = application.spSupplierCompanyId.name.replace(/[^a-zA-Z0-9]/g, '_');
            const folderName = `${supplierName}_${application.ref}`;

            // Add application JSON data
            const applicationData = {
                ...application.toObject(),
                supplierCompany: application.spSupplierCompanyId,
                submittedBy: application.submittedBy
            };

            archive.append(JSON.stringify(applicationData, null, 2), {
                name: `${folderName}/application_data.json`
            });

            // Add documents if requested
            if (includeDocuments === 'true') {
                for (const field of application.fields) {
                    if (field.documents && field.documents.length > 0) {
                        for (const document of field.documents) {
                            try {
                                const s3Response = await getObjectFromS3(document.filePath);
                                if (s3Response && s3Response.Body) {
                                    const chunks = [];
                                    for await (const chunk of s3Response.Body) {
                                        chunks.push(chunk);
                                    }
                                    const buffer = Buffer.concat(chunks);

                                    archive.append(buffer, {
                                        name: `${folderName}/documents/${field.name}/${document.fileName}`
                                    });
                                }
                            } catch (error) {
                                console.error(`Error downloading document ${document.fileName}:`, error);
                                // Continue with other documents
                            }
                        }
                    }
                }
            }
        }

        // Finalize the archive
        archive.finalize();

    } catch (error) {
        console.error('Error creating ZIP download:', error);
        res.status(500).json({
            message: 'Error creating ZIP download',
            success: false,
            error: error.message
        });
    }
};

/**
 * Download applications Excel report for a category
 * @route   GET /api/admin/categories/:categoryId/applications/download/excel
 * @access  Private (Admin/Buyer)
 */
export const downloadCategoryApplicationsExcel = async (req, res) => {
    try {
        const { categoryId } = req.params;
        const { includeScores = 'true', includeSystemScores = 'true', includeComplianceScores = 'true' } = req.query;

        // Validate categoryId
        if (!mongoose.Types.ObjectId.isValid(categoryId)) {
            return res.status(400).json({
                message: 'Invalid category ID',
                success: false
            });
        }

        // Get category details
        const category = await SpJobCategory.findById(categoryId);
        if (!category) {
            return res.status(404).json({
                message: 'Category not found',
                success: false
            });
        }

        // Get all applications for this category
        const applications = await SpApplication.find({ spJobCategoryId: categoryId })
            .populate('spSupplierCompanyId', 'name registrationNumber address contactPerson email phone')
            .populate('submittedBy', 'firstName lastName email')
            .populate('reviewer', 'firstName lastName email');

        if (applications.length === 0) {
            return res.status(404).json({
                message: 'No applications found for this category',
                success: false
            });
        }

        // Create Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Applications Report');

        // Define columns
        const columns = [
            { header: 'Supplier Company', key: 'supplierCompany', width: 25 },
            { header: 'Registration Number', key: 'registrationNumber', width: 20 },
            { header: 'Application Ref', key: 'applicationRef', width: 20 },
            { header: 'Status', key: 'status', width: 15 },
            { header: 'Submitted Date', key: 'submittedDate', width: 15 },
            { header: 'Submitted By', key: 'submittedBy', width: 20 }
        ];

        // Add score columns if requested
        if (includeScores === 'true') {
            if (includeSystemScores === 'true') {
                columns.push({ header: 'System Score', key: 'systemScore', width: 15 });
            }
            if (includeComplianceScores === 'true') {
                columns.push({ header: 'Compliance Score', key: 'complianceScore', width: 15 });
                columns.push({ header: 'Total Score', key: 'totalScore', width: 15 });
            }
            columns.push({ header: 'Review Status', key: 'reviewStatus', width: 15 });
            columns.push({ header: 'Reviewer', key: 'reviewer', width: 20 });
            columns.push({ header: 'Review Date', key: 'reviewDate', width: 15 });
        }

        // Add field columns for each section
        const fieldGroups = [...new Set(category.fields.map(f => f.group))];
        fieldGroups.forEach(group => {
            columns.push({
                header: `${group.charAt(0).toUpperCase() + group.slice(1)} Score`,
                key: `${group}Score`,
                width: 15
            });
        });

        worksheet.columns = columns;

        // Add header styling
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
        };

        // Add data rows
        applications.forEach(application => {
            const row = {
                supplierCompany: application.spSupplierCompanyId.name,
                registrationNumber: application.spSupplierCompanyId.registrationNumber,
                applicationRef: application.ref,
                status: application.status,
                submittedDate: application.submittedAt ? new Date(application.submittedAt).toLocaleDateString() : '',
                submittedBy: application.submittedBy ? `${application.submittedBy.firstName} ${application.submittedBy.lastName}` : ''
            };

            if (includeScores === 'true') {
                if (includeSystemScores === 'true') {
                    row.systemScore = application.systemScore || 0;
                }
                if (includeComplianceScores === 'true') {
                    row.complianceScore = application.complianceScore || 0;
                    row.totalScore = application.totalScore || 0;
                }
                row.reviewStatus = application.reviewStatus || 'pending';
                row.reviewer = application.reviewer ? `${application.reviewer.firstName} ${application.reviewer.lastName}` : '';
                row.reviewDate = application.reviewDate ? new Date(application.reviewDate).toLocaleDateString() : '';
            }

            // Calculate scores by field group
            fieldGroups.forEach(group => {
                const groupFields = application.fields.filter(f => f.group === group);
                const groupScore = groupFields.reduce((sum, field) => sum + (field.score || 0), 0);
                row[`${group}Score`] = groupScore;
            });

            worksheet.addRow(row);
        });

        // Set response headers
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${category.title}_applications_report.xlsx"`);

        // Write to response
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('Error creating Excel report:', error);
        res.status(500).json({
            message: 'Error creating Excel report',
            success: false,
            error: error.message
        });
    }
};

/**
 * Download documents from a specific section for all applicants in a category
 * @route   GET /api/admin/categories/:categoryId/applications/download/section/:sectionName
 * @access  Private (Admin/Buyer)
 */
export const downloadCategorySectionDocuments = async (req, res) => {
    try {
        const { categoryId, sectionName } = req.params;

        // Validate categoryId
        if (!mongoose.Types.ObjectId.isValid(categoryId)) {
            return res.status(400).json({
                message: 'Invalid category ID',
                success: false
            });
        }

        // Get category details
        const category = await SpJobCategory.findById(categoryId);
        if (!category) {
            return res.status(404).json({
                message: 'Category not found',
                success: false
            });
        }

        // Check if section exists in category
        const sectionFields = category.fields.filter(f => f.group === sectionName);
        if (sectionFields.length === 0) {
            return res.status(404).json({
                message: `Section '${sectionName}' not found in category`,
                success: false
            });
        }

        // Get all applications for this category
        const applications = await SpApplication.find({ spJobCategoryId: categoryId })
            .populate('spSupplierCompanyId', 'name registrationNumber');

        if (applications.length === 0) {
            return res.status(404).json({
                message: 'No applications found for this category',
                success: false
            });
        }

        // Check if section has file fields or data fields
        const hasFileFields = sectionFields.some(f => f.type === 'file');

        if (hasFileFields) {
            // Create ZIP for file downloads
            const archive = archiver('zip', {
                zlib: { level: 9 }
            });

            res.setHeader('Content-Type', 'application/zip');
            res.setHeader('Content-Disposition', `attachment; filename="${category.title}_${sectionName}_documents.zip"`);

            archive.pipe(res);

            // Add documents from each application
            for (const application of applications) {
                const supplierName = application.spSupplierCompanyId.name.replace(/[^a-zA-Z0-9]/g, '_');

                for (const field of application.fields) {
                    if (field.group === sectionName && field.documents && field.documents.length > 0) {
                        for (const document of field.documents) {
                            try {
                                const s3Response = await getObjectFromS3(document.filePath);
                                if (s3Response && s3Response.Body) {
                                    const chunks = [];
                                    for await (const chunk of s3Response.Body) {
                                        chunks.push(chunk);
                                    }
                                    const buffer = Buffer.concat(chunks);

                                    archive.append(buffer, {
                                        name: `${supplierName}/${field.name}/${document.fileName}`
                                    });
                                }
                            } catch (error) {
                                console.error(`Error downloading document ${document.fileName}:`, error);
                            }
                        }
                    }
                }
            }

            archive.finalize();
        } else {
            // Create Excel for data fields
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(`${sectionName} Data`);

            // Define columns
            const columns = [
                { header: 'Supplier Company', key: 'supplierCompany', width: 25 },
                { header: 'Application Ref', key: 'applicationRef', width: 20 }
            ];

            // Add field columns
            sectionFields.forEach(field => {
                columns.push({
                    header: field.label,
                    key: field.name,
                    width: 20
                });
            });

            worksheet.columns = columns;

            // Add header styling
            worksheet.getRow(1).font = { bold: true };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE0E0E0' }
            };

            // Add data rows
            applications.forEach(application => {
                const row = {
                    supplierCompany: application.spSupplierCompanyId.name,
                    applicationRef: application.ref
                };

                // Add field values
                sectionFields.forEach(sectionField => {
                    const appField = application.fields.find(f => f.name === sectionField.name);
                    row[sectionField.name] = appField ? appField.value : '';
                });

                worksheet.addRow(row);
            });

            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${category.title}_${sectionName}_data.xlsx"`);

            await workbook.xlsx.write(res);
            res.end();
        }

    } catch (error) {
        console.error('Error downloading section data:', error);
        res.status(500).json({
            message: 'Error downloading section data',
            success: false,
            error: error.message
        });
    }
};

/**
 * Download job-level applications report
 * @route   GET /api/admin/jobs/:jobId/applications/download/excel
 * @access  Private (Admin/Buyer)
 */
export const downloadJobApplicationsExcel = async (req, res) => {
    try {
        const { jobId } = req.params;
        const { reportType = 'summary' } = req.query; // summary, system, compliance, final, qualified

        // Validate jobId
        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({
                message: 'Invalid job ID',
                success: false
            });
        }

        // Get job details
        const job = await SpJob.findById(jobId);
        if (!job) {
            return res.status(404).json({
                message: 'Job not found',
                success: false
            });
        }

        // Get all categories for this job
        const categories = await SpJobCategory.find({ spJobId: jobId });
        if (categories.length === 0) {
            return res.status(404).json({
                message: 'No categories found for this job',
                success: false
            });
        }

        // Get all applications for all categories in this job
        const categoryIds = categories.map(c => c._id);
        const applications = await SpApplication.find({ spJobCategoryId: { $in: categoryIds } })
            .populate('spSupplierCompanyId', 'name registrationNumber')
            .populate('spJobCategoryId', 'title type')
            .populate('reviewer', 'firstName lastName');

        if (applications.length === 0) {
            return res.status(404).json({
                message: 'No applications found for this job',
                success: false
            });
        }

        // Create Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet(`Job ${reportType} Report`);

        // Define columns based on report type
        let columns = [
            { header: 'Supplier Company', key: 'supplierCompany', width: 25 },
            { header: 'Category', key: 'category', width: 20 },
            { header: 'Application Ref', key: 'applicationRef', width: 20 },
            { header: 'Status', key: 'status', width: 15 }
        ];

        switch (reportType) {
            case 'system':
                columns.push({ header: 'System Score', key: 'systemScore', width: 15 });
                break;
            case 'compliance':
                columns.push({ header: 'Compliance Score', key: 'complianceScore', width: 15 });
                columns.push({ header: 'Review Status', key: 'reviewStatus', width: 15 });
                columns.push({ header: 'Reviewer', key: 'reviewer', width: 20 });
                break;
            case 'final':
                columns.push({ header: 'System Score', key: 'systemScore', width: 15 });
                columns.push({ header: 'Compliance Score', key: 'complianceScore', width: 15 });
                columns.push({ header: 'Total Score', key: 'totalScore', width: 15 });
                columns.push({ header: 'Review Status', key: 'reviewStatus', width: 15 });
                break;
            case 'qualified':
                columns.push({ header: 'Total Score', key: 'totalScore', width: 15 });
                columns.push({ header: 'Pass Mark', key: 'passMark', width: 15 });
                columns.push({ header: 'Qualified', key: 'qualified', width: 15 });
                break;
            default: // summary
                columns.push({ header: 'System Score', key: 'systemScore', width: 15 });
                columns.push({ header: 'Compliance Score', key: 'complianceScore', width: 15 });
                columns.push({ header: 'Total Score', key: 'totalScore', width: 15 });
                columns.push({ header: 'Review Status', key: 'reviewStatus', width: 15 });
        }

        worksheet.columns = columns;

        // Add header styling
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
        };

        // Filter applications based on report type
        let filteredApplications = applications;
        if (reportType === 'qualified') {
            // Assuming pass mark is 70% of total possible score
            filteredApplications = applications.filter(app => {
                const passMark = (app.systemScore || 0) * 0.7; // This should be configurable
                return (app.totalScore || 0) >= passMark;
            });
        }

        // Add data rows
        filteredApplications.forEach(application => {
            const row = {
                supplierCompany: application.spSupplierCompanyId.name,
                category: application.spJobCategoryId.title,
                applicationRef: application.ref,
                status: application.status
            };

            switch (reportType) {
                case 'system':
                    row.systemScore = application.systemScore || 0;
                    break;
                case 'compliance':
                    row.complianceScore = application.complianceScore || 0;
                    row.reviewStatus = application.reviewStatus || 'pending';
                    row.reviewer = application.reviewer ? `${application.reviewer.firstName} ${application.reviewer.lastName}` : '';
                    break;
                case 'final':
                    row.systemScore = application.systemScore || 0;
                    row.complianceScore = application.complianceScore || 0;
                    row.totalScore = application.totalScore || 0;
                    row.reviewStatus = application.reviewStatus || 'pending';
                    break;
                case 'qualified':
                    const passMark = (application.systemScore || 0) * 0.7;
                    row.totalScore = application.totalScore || 0;
                    row.passMark = passMark;
                    row.qualified = (application.totalScore || 0) >= passMark ? 'Yes' : 'No';
                    break;
                default: // summary
                    row.systemScore = application.systemScore || 0;
                    row.complianceScore = application.complianceScore || 0;
                    row.totalScore = application.totalScore || 0;
                    row.reviewStatus = application.reviewStatus || 'pending';
            }

            worksheet.addRow(row);
        });

        // Set response headers
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${job.title}_${reportType}_report.xlsx"`);

        // Write to response
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('Error creating job Excel report:', error);
        res.status(500).json({
            message: 'Error creating job Excel report',
            success: false,
            error: error.message
        });
    }
};