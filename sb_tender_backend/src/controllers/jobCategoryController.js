import SpJobCategory from '../models/sp_job_category.js';
import defaultPrequalificationFields from '../models/default_prequalification_fields.js';
import defaultRegistrationFields from '../models/default_registration_fields.js';

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
export const getJobCategories = async (req, res) => {
  const { jobId } = req.params;
  console.log('Job ID:', jobId);

  try {
      const categories = await SpJobCategory.find({ spJobId: jobId })
          .populate({
              path: 'spCompanyId',
              select: 'name' // Only get the company name
          })
          .lean(); // Convert to plain JavaScript objects

      if (!categories || categories.length === 0) {
          console.error('No job categories found');
          return res.status(404).json({ error: 'No job categories found' });
      }

      // Transform the data to include companyName directly
      const categoriesWithCompany = categories.map(category => ({
          ...category,
          companyName: category.spCompanyId?.name || 'Unknown Company'
      }));

      res.status(200).json(categoriesWithCompany);
  } catch (error) {
      console.error('Error fetching job categories:', error);
      res.status(500).json({ error: error.message });
  }
};

export const getBuyerCategories = async (req, res) => {
  const { companyId } = req.params;
  const type  = req.query.type;
 
  console.log('companyId ID:', companyId);
  console.log('type:', type);

  try {
      const categories = await SpJobCategory.find({ spCompanyId: companyId, type })
          .populate({
              path: 'spCompanyId',
              select: 'name' // Only get the company name
          })
          .lean(); // Convert to plain JavaScript objects

      if (!categories || categories.length === 0) {
          console.error('No categories found');
          return res.status(404).json({ error: 'No job categories found' });
      }

      // Transform the data to include companyName directly
      const categoriesWithCompany = categories.map(category => ({
          ...category,
          companyName: category.spCompanyId?.name || 'Unknown Company'
      }));

      res.status(200).json(categoriesWithCompany);
  } catch (error) {
      console.error('Error fetching job categories:', error);
      res.status(500).json({ error: error.message });
  }
};

export const getJobCategoryById = async (req, res) => {
  const { jobId, categoryId } = req.params;
  console.log('Job ID:', jobId);
  console.log('Category ID:', categoryId);

  try {
      const category = await SpJobCategory.findOne({ _id: categoryId, spJobId: jobId })
          .populate({
              path: 'spCompanyId',
              select: 'name' // Only get the company name
          })
          .lean(); // Convert to plain JavaScript objects

      if (!category) {
          console.error('Category not found');
          return res.status(404).json({ error: 'Category not found' });
      }

      // Transform the data to include companyName directly
      const categoryWithCompany = {
          ...category,
          companyName: category.spCompanyId?.name || 'Unknown Company'
      };

      res.status(200).json(categoryWithCompany);
  } catch (error) {
      console.error('Error fetching job category:', error);
      res.status(500).json({ error: error.message });
  }
};

export const updateJobCategory = async (req, res) => {
  try {
    const { jobId, categoryId } = req.params; // Extract category ID and template ID
    // Get the actual updates - they're nested inside categoryTemplate
    const updateData = req.body.jobCategory || req.body;
    console.log('Buyer Template Update data structure:', Object.keys(updateData));
    const jobCategory = await SpJobCategory.findOne({ _id: categoryId, spJobId: jobId });
    if (!jobCategory) {
        console.error('Job Category not found for job category ID:', categoryId);
      return res.status(404).json({ message: 'Buyer Template not found' });
    } 
    console.log('Job category found', jobCategory.title);

    // Remove restricted fields from updates
    const { _id, spJobId, spCompanyId, createdAt, createdBy, __v, ...allowedUpdates } = updateData;
    console.log('Fields being updated:', Object.keys(allowedUpdates));
        if (allowedUpdates.fields) {
      console.log('Fields array length:', allowedUpdates.fields.length);
      jobCategory.fields = allowedUpdates.fields;
    }
    
    // Update other properties
    for (const [key, value] of Object.entries(allowedUpdates)) {
      if (key !== 'fields') {
        jobCategory[key] = value;
      }
    }
    jobCategory.updatedBy = req.user.userId;
        console.log('Job category before save - Fields count:', jobCategory.fields?.length);
        await jobCategory.save();
    console.log('Job category saved');
    res.status(200).json(jobCategory);
  } catch (err) {
    console.error('Job category Update Error:', err);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
};


export const deleteJobCategory = async (req, res) => {
  try {
    const category = await SpJobCategory.findByIdAndDelete(req.params.id);
    if (category) {
      console.log('Category template deleted:', category);
    } else {
      console.log('No category template found for this category');
    }
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
