import SpCategory from '../models/sp_category.js';
import defaultPrequalificationFields from '../models/default_prequalification_fields.js';
import defaultRegistrationFields from '../models/default_registration_fields.js';
import SpCategoryTemplate from '../models/sp_category_template.js';

const createCategoryTemplate = async function(category, userId) {
  console.log('Creating category template for category ID:', category._id);
  let fields = null;
  if(category.type === 'supplier_prequalification') {
    console.log('Category type is supplier_prequalification');
    fields = defaultPrequalificationFields;
  }
  if(category.type === 'supplier_registration') {
    console.log('Category type is supplier_registration');
    fields =  defaultRegistrationFields;
  }
  try {
    const categoryTemplate = new SpCategoryTemplate({
      name: category.name,
      spCategoryId: category._id,
      type: category.type,
      status: category.status,
      createdBy: userId,
      fields: fields,
    });
    await categoryTemplate.save();
    console.log('Category template created:', categoryTemplate._id);
    console.log('Category template created for category:', categoryTemplate.spCategoryId);

    return categoryTemplate; 
  } catch (error) {
    console.error('Error creating category:', error);
    return null;
  }
}


export const createCategory = async (req, res) => {
  try {
    const { name, description, type, status } = req.body;
    const category = new SpCategory({
      name,
      description,
      type,
      status,
      createdBy: req.user.userId,
    });
    await category.save();
    //create category template fields
    const categoryTemplate = await createCategoryTemplate(category, req.user.userId);

    if (categoryTemplate) {
      res.status(201).json(category);
    } else {
      res.status(201).json(category);
    }
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: error.message });
  }
};


export const getCategories = async (req, res) => {
  try {
    const categories = await SpCategory.find();
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const getCategoryById = async (req, res) => {
  try {
    const category = await SpCategory.findById(req.params.id);
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const updateCategory = async (req, res) => {
  console.log('Updating category server');
  console.log('Body server', req.body);
  try {
    const { id } = req.params;
    console.log('ID', id);
    const { name, description, type, status } = req.body;
    const category = await SpCategory.findByIdAndUpdate(id,
      { name, description, type, status, updatedBy: req.user.userId },
      { new: true }
    );
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json(category);
  } catch (error) {
    console.error('Error', error);
    res.status(500).json({ error: error.message });
  }
};

export const deleteCategory = async (req, res) => {
  try {
    const category = await SpCategory.findByIdAndDelete(req.params.id);
    const categoryTemplate = await SpCategoryTemplate.findOneAndDelete({spCategoryId: category._id});
    if (categoryTemplate) {
      console.log('Category template deleted:', categoryTemplate);
    } else {
      console.log('No category template found for this category');
    }
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
