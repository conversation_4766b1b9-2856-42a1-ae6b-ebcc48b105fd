import SpBuyerCategory from '../models/sp_buyer_category.js';
import defaultPrequalificationFields from '../models/default_prequalification_fields.js';
import defaultRegistrationFields from '../models/default_registration_fields.js';
import SpBuyerCategoryTemplate from '../models/sp_buyer_category_template.js';
import SpCategoryTemplate from '../models/sp_category_template.js';
import SpJob from '../models/sp_job.js';
import SpJobCategory from '../models/sp_job_category.js';
import SpCompany from '../models/sp_company.js'
import mongoose from 'mongoose';
function getFirstLetters(companyName) {
  return companyName.split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}
function getNumericEquivalent(companyName) {
  let numericValue = 0;
  for (let i = 0; i < companyName.length; i++) {
    const char = companyName.toUpperCase().charAt(i);
    if (char >= 'A' && char <= 'Z') {
      numericValue += char.charCodeAt(0) - 64; // A=1, B=2,...Z=26
    }
  }
  return numericValue;
}
function generateReference(companyName, refCount) {
  const firstLetters = getFirstLetters(companyName);
  const numericEquivalent = getNumericEquivalent(companyName);
  const year = new Date().getFullYear();
  return `${firstLetters}_${numericEquivalent}_${year}_${refCount}`;
}


// Example: "GTS" becomes 7(G) + 20(T) + 19(S) = 46

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * This function creates a new buyer and job
 */
export const createBuyerJob = async (req, res) => {
   console.log('Request body', req.body);
  try {
    const { companyId } = req.params;
    const { title, description, contract, starts, ends, location, categoryPrice, selectedJobCategories, type  } = req.body;
   
    console.log(title,description, contract, starts, ends, location, categoryPrice, companyId);
    const spJob = new SpJob({
      title,
      description,
      contract,
      starts,
      ends,
      location,
      type,
      status: 'draft',
      categoryPrice,
      spCompanyId: companyId,
      createdBy: req.user.userId,
    });
    await spJob.save();
    //create category template fields
    const jobCategories = await createJobCategories(companyId, spJob, selectedJobCategories, req.user.userId);

    if (jobCategories) {
        console.log('Job Categories created:', jobCategories._id);
      res.status(201).json(spJob);

    } else {
        console.log('No job categories created');
      res.status(201).json(spJob);
    }
  } catch (error) {
    console.error('Error creating job and categories:', error);
    res.status(500).json({ error: error.message });
  }
};

export const updateBuyerJob = async (req, res) => {
  console.log('Body server', req.body);
  console.log('Params server', req.params);
  try {
    const { jobId } = req.params;
    console.log('ID', jobId);
    const { title, description,  contract, starts, ends, location, categoryPrice,  } = req.body;
    const job = await SpJob.findByIdAndUpdate(jobId,
      { title, description, contract, starts, ends, location, categoryPrice},
      { new: true }
    );
    if (!job){
      console.log('Job not found');
      return res.status(404).json({ error: 'Job not found' });
    } 
    //update all categories with new job information
    let updatedCategories = updateJobCategories(job, req.user.userId);
  
    res.status(200).json(job);
  } catch (error) {
    console.error('Error', error);
    res.status(500).json({ error: error.message });
  }
};
const updateJobCategories = async (job, userId) => {
  const jobId = job._id;
  console.log('Job', job);
  console.log('User', userId);

  try {
    // Add await here to get the actual documents
    const jobCategories = await SpJobCategory.find({ spJobId: jobId }).exec();
    console.log('Found job categories', jobCategories.length);

    // Use for...of for array iteration
    for (const category of jobCategories) {
      // Create a new object with the updated values
      const updates = {
        title: category.title,
        description: category.description, //
        status: job.status,
        price: job.categoryPrice,
        starts: job.starts,
        ends: job.ends,
        location: job.location,
        spCompanyId: job.spCompanyId,
        type: job.type,
        updatedBy: userId
      };

      await SpJobCategory.findByIdAndUpdate(
        category._id,
        updates,
        { new: true }
      );
    }

    return true;
  } catch (error) {
    console.error('Could not update job categories', error);
    return false;
  }
};
const createJobCategories = async function(companyId, spJob, spBuyerCategoryIds, userId) {
  console.log('Creating category for job ID:', spJob._id);
  let fields = null;
// Use findOne() if expecting single document, or handle array from find()
let refCount = 1;
for (const spBuyerCategoryId of spBuyerCategoryIds) {
  const buyerCategory = await SpBuyerCategory.findOne({ _id: spBuyerCategoryId,
    spCompanyId: companyId,
  });
  const buyerCategoryTemplate = await SpBuyerCategoryTemplate.findOne({
    spBuyerCategoryId,
    spCompanyId: companyId,
  });
  
  const company = await SpCompany.findOne({_id: companyId});

  if(!buyerCategory || !buyerCategoryTemplate || !company) {
    console.log('No buyer category found for this category');
    return null;
  }
  
    const ref = generateReference(company.name, refCount);
    fields = buyerCategoryTemplate.fields;
    console.log('Buyer Category template found:', buyerCategoryTemplate._id);

  try {
    const jobCategory = new SpJobCategory({
      ref: ref,
      title: buyerCategory.description,
      description: spJob.title,
      status: spJob.status,
      price: spJob.categoryPrice,
      starts: spJob.starts,
      ends: spJob.ends,
      location: spJob.location,
      spBuyerCategoryId: buyerCategory._id,
      spBuyerCategoryTemplateId: buyerCategoryTemplate._id,
      spCompanyId: buyerCategory.spCompanyId,
      spJobId: spJob._id,
      type: buyerCategory.type,
      createdBy: userId,
      fields: fields,
    });
    await jobCategory.save();
    console.log('Job Category template created:', buyerCategoryTemplate._id);
  } catch (error) {
    console.error('Error creating job category template:', error);
  }
  refCount++;

}
return true;
}


export const getBuyerJobs = async (req, res) => {
  const { companyId } = req.params;
  const type  = req.query.type;
  console.log('Get buyer job Type:', type);
  try {
  // Validate companyId is a valid MongoDB ID if needed
  if (companyId && !mongoose.Types.ObjectId.isValid(companyId)) {
    return res.status(400).json({ error: 'Invalid company ID' });
  }

  const query = companyId ? { spCompanyId: companyId } : {};
  if (type) {
    query.type = type;
  }


  console.log('Company ID', companyId);

    let jobs = null;

      console.log('Company  ID:', query);

    // Find jobs and populate company information
    jobs = await SpJob.find(query)
      .populate({
        path: 'spCompanyId',
        select: 'name' // Only get the company name
      })
      .lean(); // Convert to plain JavaScript objects

    if (!jobs || jobs.length === 0) {
      console.log('No jobs found');
      return res.status(404).json({ error: 'No jobs found' });
    }

    // Transform the data to include companyName directly
    const jobsWithCompany = jobs.map(job => ({
      ...job,
      companyName: job.spCompanyId?.name || 'Unknown Company'
    }));

    console.log('Found jobs:', jobsWithCompany.length);
    res.status(200).json(jobsWithCompany);
  } catch (error) {
    console.error('Could not fetch jobs', error);
    res.status(500).json({ error: error.message });
  }
};
export const getBuyerJobById = async (req, res) => {
  const { companyId, jobId } = req.params;
  try {
    // Validate IDs
    if (!mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(400).json({ error: 'Invalid job ID' });
    }
    if (companyId && !mongoose.Types.ObjectId.isValid(companyId)) {
      return res.status(400).json({ error: 'Invalid company ID' });
    }

    // Build query - if companyId is provided, ensure job belongs to that company
    const query = { _id: jobId };
    if (companyId) {
      query.spCompanyId = companyId;
    }

    console.log('Fetching job with query:', query);

    // Find job and populate company information
    const job = await SpJob.findOne(query)
      .populate({
        path: 'spCompanyId',
        select: 'name' // Only get the company name
      })
      .lean(); // Convert to plain JavaScript object

    if (!job) {
      console.log('Job not found');
      return res.status(404).json({ error: 'Job not found' });
    }

    // Transform the data to include companyName directly
    const jobWithCompany = {
      ...job,
      companyName: job.spCompanyId?.name || 'Unknown Company'
    };

    console.log('Found job:', jobWithCompany._id);
    res.status(200).json(jobWithCompany);
  } catch (error) {
    console.error('Could not fetch job', error);
    res.status(500).json({ error: error.message });
  }
};

export const deleteBuyerJob = async (req, res) => {
  try {
    const { jobId } = req.params;

    const jobCategories = await SpJobCategories.find({spJobId: jobId});
   jobCategories.delete();
   const job = await SpJob.findByIdAndDelete(jobId);

    res.status(200).json({ message: 'Job and Categories deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

