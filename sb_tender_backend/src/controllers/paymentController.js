


import crypto from "crypto";
import SpPayment from "../models/sp_payment.js";
import SpOrder from "../models/sp_order.js";
import SpJobCategory from "../models/sp_job_category.js";
import SpApplication from "../models/sp_application.js";
import axios from "axios";
import mongoose from 'mongoose';
import User from '../models/sp_user.js';
// import NotificationHelper from '../helpers/notificationHelper.js'; // Uncomment when notification helper is available
//mpesa
async function getMpesaToken() {
    const secret = process.env.MPESA_CONSUMER_SECRET;
    const key = process.env.MPESA_CONSUMER_KEY;
    const credentials = btoa(`${key}:${secret}`); // base64 encode
    const url = process.env.MPESA_TOKEN_URL;
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                Authorization: `Basic ${credentials}`,
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const tokenResult = await response.json();
        const accessToken = tokenResult.access_token;
        console.log("Access Token:", accessToken);
        return accessToken;
    } catch (error) {
        console.error("Error fetching token:", error);
        throw error;
    }
}

async function mpesaPaymentRequest(mobile, amount, orderId) {
    console.log("New Mpesa payment request received");
    const timestamp = new Date().toISOString().replace(/[-:TZ]/g, "").slice(0, 14);
    console.log("Generated Timestamp:", timestamp);
    const passkey = process.env.MPESA_STK_PASSKEY;
    const shortCode = process.env.MPESA_SHORTCODE;
    const password = Buffer.from(shortCode + passkey + timestamp).toString("base64");
    console.log("Generated Password for Mpesa Request");

    const mpesaData = {
        BusinessShortCode: shortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: "CustomerPayBillOnline",
        Amount: parseInt(amount, 10),
        PartyA: mobile,
        PartyB: shortCode,
        PhoneNumber: mobile,
        CallBackURL: `${process.env.MPESA_STK_CALLBACK}`,
        AccountReference: `${orderId}`,
        TransactionDesc: "Category Payment",
    };

    console.log("Mpesa Request Data Prepared:", mpesaData);

    const token = await getMpesaToken();
    console.log("Mpesa Token Retrieved:", token);

    // Make the Mpesa request
    const response = await axios.post(process.env.MPESA_STK_URL, mpesaData, {
        headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        },
    });

    return response;
}
const formatPhoneNumber = (value) => {
    const numericOnly = value.replace(/\D/g, '');
    const lastNineDigits = numericOnly.slice(-9);
    
    if (lastNineDigits.length !== 9) {
      throw new Error('Phone number must contain at least 9 digits');
    }
    
    return `254${lastNineDigits}`;
  };

export const mpesaCheckout = async (req, res) => {
    try {
        console.log('Request body', req.body);
        const { amount, mobile, items, companyId } = req.body;
        console.log(amount, mobile, items, companyId );
        
        const formattedMobile = formatPhoneNumber(mobile);
        const userId = req.user.userId;

        // Validate input
        if (!items || items.length === 0) {
            return res.status(400).json({ message: "Cart items are required" });
        }

        if (!formattedMobile || formattedMobile.length < 12) {
            return res.status(400).json({ message: "Valid mobile number is required" });
        }

        // Verify each item exists in job categories and calculate total
        const orderItems = [];
        let calculatedTotal = 0;

        for (const item of items) {
            const category = await SpJobCategory.findById(item.id)
                .populate('spCompanyId') // Populate company info
                .populate('spJobId'); // Populate job info

            if (!category) {
                return res.status(404).json({
                    message: `Category not found: ${item.id}`
                });
            }

            // Verify price matches
            if (item.price !== category.price) {
                return res.status(400).json({
                    message: `Price mismatch for category: ${category.title}`
                });
            }

            orderItems.push({
                title: category.title,
                price: category.price,
                starts: category.starts,
                ends: category.ends,
                location: category.location,
                type: category.type,
                supplierCompanyId: companyId, // Assuming user is the supplier
                buyerCompanyId: category.spCompanyId,
                spJobCategoryId: category._id
            });

            calculatedTotal += category.price;
        }

        // Verify total amount matches calculated total
        if (amount !== calculatedTotal) {
            return res.status(400).json({
                message: `Amount mismatch. Expected: ${calculatedTotal}, Received: ${amount}`
            });
        }

        // Create the order first
        const order = new SpOrder({
            spCompanyId: companyId,
            orderItems,
            status: 'pending',
            totalAmount: calculatedTotal,
            createdBy: userId
        });

        await order.save();
        console.log('Order created');
        // Create payment record
        const payment = new SpPayment({
            amount: calculatedTotal,
            mobile: formattedMobile,
            status: 'pending',
            spOrderId: order._id,
            //reference: order._id,
            userId,
            paymentMethod: 'mpesa',
            reason: 'category'
        });

        await payment.save();

        console.log('Paymeent created');

        // Initiate M-Pesa STK push
        const stkResponse = await mpesaPaymentRequest(
            formattedMobile,
            calculatedTotal,
            `${order._id}`
        );

        console.log('STK response', stkResponse.data);


        if (!stkResponse.status === 200 || !stkResponse.statusText === 'OK' ) {
            console.error('Bad Request');
            throw new Error(stkResponse.data.message);
        }

        // Update payment with M-Pesa reference
        payment.reference = stkResponse.data.MerchantRequestID;
        payment.accessCode = stkResponse.data.CheckoutRequestID;
        await payment.save();
        console.log('Payment updated');

        res.json({
            success: true,
            orderId: order._id,
            paymentId: payment._id,
            message: "Payment initiated successfully"
        });

    } catch (error) {
        console.error("Checkout failed:", error.message);
        res.status(500).json({
            success: false,
            message: error.message || "Checkout processing failed"
        });
    }
};

export const mpesaCheckoutWebhook = async (req, res) => {
    try {
        console.log("Received webhook data:", JSON.stringify(req.body));
        const callbackData = req.body.Body.stkCallback;
        console.log("Callback data:", JSON.stringify(callbackData));

        const payment = await SpPayment.findOne({
            reference: callbackData.MerchantRequestID,
        });

        if (!payment) {
            console.log("Payment not found for MerchantRequestID:", callbackData.MerchantRequestID);
            return res.status(400).json({ message: "Payment not found" });
        }

        const pendingPaymentAmount = payment.amount;
        console.log('Pending amount' + pendingPaymentAmount);
        const resultCode = callbackData.ResultCode;

        let responseMessage = "Payment processed";
        let responseStatus = 200;

        if (payment.status === "success") {
            console.log("Duplicate payment detected for MerchantRequestID:", callbackData.MerchantRequestID);
            const paymentOrder = await SpOrder.findOne({
                _id: payment.spOrderId,
            });
    
            if (!paymentOrder) {
                console.log("Order not found for payment:", payment._id);
                return res.status(400).json({ message: "Payment updated but Order not found" });
            }
            paymentOrder.status = 'paid';
            await paymentOrder.save();
            payment.balance = 0;
            console.log("Order completed for payment:", payment._id);
            
            const result = await createApplicationsFromOrder(paymentOrder);
  
            if (!result.success) {
                responseMessage = "Payment processed but order update failed. Please contact support.";
                responseStatus = 400;
            }
        }

        if (resultCode === 0) {
            console.log("Payment success for MerchantRequestID:", callbackData.MerchantRequestID);
            payment.status = "success";
            const mpesaReceipt = callbackData.CallbackMetadata.Item.find((item) => item.Name === "MpesaReceiptNumber");
            const amount = callbackData.CallbackMetadata.Item.find((item) => item.Name === "Amount");
            const phoneNumber = callbackData.CallbackMetadata.Item.find((item) => item.Name === "PhoneNumber");

            if (mpesaReceipt) {
                payment.transID = mpesaReceipt.Value;
                console.log("MpesaReceiptNumber:", mpesaReceipt.Value);
            }

            if (amount) {
                payment.amount = amount.Value;
                console.log("Amount:", amount.Value);
            }

            if (phoneNumber) {
                payment.authorization = phoneNumber.Value;
                console.log("PhoneNumber:", phoneNumber.Value);
            }
        } else {
            console.log("Payment failed for MerchantRequestID:", callbackData.MerchantRequestID, " with ResultCode:", resultCode);
            payment.status = "failed";
        }

       
        payment.transactionType = "Mpesa";
        payment.respDesc = callbackData.ResultDesc;
        payment.currency = "KES";
        payment.channel = "mobile-money";
        payment.balance = payment.amount;
        await payment.save();
        console.log(payment);
        console.log("Saving payment data for MerchantRequestID:", callbackData.MerchantRequestID);

        if (payment.status === "success" && Number(pendingPaymentAmount) === Number(payment.amount)) {
            console.log("Payment matches pending order amount, updating the order for MerchantRequestID:", callbackData.MerchantRequestID);
            const order = await SpOrder.findOne({
                _id: payment.spOrderId,
            });
    
            if (!order) {
                console.log("Order not found for payment:", payment._id);
                return res.status(400).json({ message: "Payment updated but Order not found" });
            }
            order.status = 'paid';
            await order.save();
            payment.balance = 0;
            await payment.save();

            const result = await createApplicationsFromOrder(order);
  
            if (!result.success) {
                console.log("Payment processed but order update failed. Please contact support.");
                responseMessage = "Payment processed but order update failed. Please contact support.";
                responseStatus = 400;
            }    

            console.log("Order completed for payment:", payment._id);
        } else {
            console.log("No order updated for payment:", payment._id);
        }


        // await sendNotification(order); // Commented out as order variable is not defined in this scope

        // Only send the response once at the end
        return res.status(responseStatus).json({ message: responseMessage });

    } catch (error) {
        console.error("Error processing webhook for stkCallback:", error);
        return res.status(500).json({ error: "Error processing payment" });
    }
}

// const sendNotification = async (order) => {
//       try {
//         const user = await User.findById(order.createdBy).exec();
//         await NotificationHelper.sendOrderConfirmation(order, user, order.SpCompanyId);
//       } catch (notificationError) {
//         console.error('Failed to send order notification:', notificationError);
//       }
// }

async function paystackPaymentRequest(mobile, amount, email) {
    const paystackToken = process.env.PAYSTACK_SECRET_KEY;
    const paystackInitializeBaseUrl = process.env.PAYSTACK_INITIALIZE_URL;
    const paystackCallbackUrl = process.env.PAYSTACK_CALLBACK_URL;
    console.log(
        "Paystack Token Retrieved:",
        paystackToken ? "Token Found" : "No Token Found"
    );
    console.log("Paystack Base URL:", paystackInitializeBaseUrl);
    console.log("Paystack Callback URL:", paystackCallbackUrl);
    const response = await axios.post(
        paystackInitializeBaseUrl,
        {
            email,
            amount: (parseInt(amount, 10) * 100).toString(),
            callback_url: paystackCallbackUrl,
        },
        {
            headers: {
                Authorization: `Bearer ${paystackToken}`,
                "Content-Type": "application/json",
            },
        }
    );

    console.log("Response from Paystack:", response.data);
    return response;
}

export const cardCheckout = async (req, res) => {
    try {
        const { amount, mobile, email, planId, interval } = req.body;
        const teamUrl = req.params.teamUrl;
        const ownerId = req.user._id;
        console.log(`User ID from session: ${ownerId}`);
        const currentTeam = await Team.findOne({ url: teamUrl, ownerId });
        if (!currentTeam) {
            console.log("Team not found with the provided URL");
            return res.status(404).json({ message: "Team not found with the provided URL" });
        }
        console.log("Team found:", currentTeam._id);
        const isPaymentValid = await validatePaymentDetails(planId, amount, interval);
        if (!isPaymentValid) {
            console.log("Payment amounts seem off!");
            return res.status(400).json({ message: "Oops! Payment amounts seem off!" });
        }
        const plan = await SubscriptionPlan.findById(planId);
        if (!plan) {
            console.log("Plan not found");
            return false;
        }
        const finalAmount = calculateChargeableAmount(plan, interval);
        console.log("New card payment request received" + req.body);
        const response = await paystackPaymentRequest(mobile, finalAmount, email)
        console.log("Response from Paystack:", response.data);
        const { data } = response.data;
        console.log("Paystack response data extracted:", data);

        const subscription = await createSubscription(plan, currentTeam._id, req.user);

        // Create and save the payment record
        const payment = new Payment({
            teamId: currentTeam._id,
            subscriptionPlanId: planId,
            subscriptionId: subscription._id,
            email,
            mobile,
            amount: parseInt(finalAmount, 10),
            reference: data.reference,
            accessCode: data.access_code,
            authorizationUrl: data.authorization_url,
            reason: "subscription",
            status: "pending",
            interval,
            userId: ownerId
        });

        console.log("Preparing to save payment:", payment);
        await payment.save();
        console.log("Payment saved successfully:", payment._id);
        // Respond with the access code and payment ID
        res.json({ access_code: data.access_code, paymentId: payment._id });

    } catch (error) {
        console.error("Error initiating payment with Paystack:", error.message || error);
        res.status(500).json({ message: "Error initiating payment" });
    }
}

export const cardCheckoutWebhook = async (req, res) => {
    const event = req.body;
    const secret = process.env.PAYSTACK_SECRET_KEY;
    // Step 1: Verify Paystack's signature
    const hash = crypto.createHmac("sha512", secret)
        .update(JSON.stringify(event))
        .digest("hex");

    if (hash !== req.headers["x-paystack-signature"]) {
        console.error("Invalid Paystack signature received");
        // return res.status(400).json({ message: "Invalid signature" });
    }

    console.log("Paystack webhook event received:", event.event);

    // Step 2: Handle different event types
    try {
        const paymentEvent = event.event;

        if (paymentEvent === "charge.success") {
            console.log("Processing charge.success event");
            const reference = event.data.reference;
            const payment = await Payment.findOne({ reference });
            const pendingPaymentAmount = payment.amount;

            if (!payment) {
                console.error(`No payment found for reference: ${reference}`);
                return res.status(404).json({ message: "Payment not found" });
            }

            if (payment.status === "success") {
                console.log("Payment is already marked as success. Skipping processing.");
                return res.status(200).json({ message: "Payment already processed" });
            }

            console.log("Updating payment details for successful transaction");
            payment.status = "success";
            payment.amount = event.data.amount;
            payment.transactionType = "Paystack";
            payment.transID = event.data.id;
            payment.respDesc = event.data.gateway_response;
            payment.authorization = JSON.stringify(event.data.authorization);
            payment.paidAt = event.data.paid_at ? new Date(event.data.paid_at) : null;
            payment.paymentDate = event.data.transaction_date ? new Date(event.data.transaction_date) : null;
            payment.customerMessage = event.data.customer_message;
            payment.currency = event.data.currency;
            payment.channel = event.data.channel;
            payment.source = event.data.source;
            payment.fees = event.data.fees;
            payment.balance = event.data.amount;
            await payment.save();

            console.log("Payment updated successfully:", payment._id);

            // Step 3: Create subscription if necessary
            if (Number(pendingPaymentAmount) === Number(payment.amount) && payment.reason === "subscription") {
                console.log("Unpaid payment matches the webhook amount, creating a subscription");
                await createSubscription(payment);
                payment.balance = 0;
            } else if (Number(pendingPaymentAmount) === Number(payment.amount) && payment.reason === "extramessages") {
                console.log("Unpaid payment matches the webhook amount, creating a subscription");
                await addExtraMessagesToCurrentPlan(payment)
                payment.balance = 0;
            } else if (Number(pendingPaymentAmount) === Number(payment.amount) && payment.reason === "chatbots") {
                console.log("Unpaid payment matches the webhook amount, creating a subscription");
                await addChatbotsToCurrentPlan(payment)
                payment.balance = 0;
            } else if (Number(pendingPaymentAmount) === Number(payment.amount) && payment.reason === "domains") {
                console.log("Unpaid payment matches the webhook amount, creating a subscription");
                await addDomainsToCurrentPlan(payment)
                payment.balance = 0;
            } else if (Number(pendingPaymentAmount) === Number(payment.amount) && payment.reason === "branding") {
                console.log("Unpaid payment matches the webhook amount, creating a subscription");
                await addBrandingToCurrentPlan(payment)
                payment.balance = 0;
            } else {
                console.log("No subscription created as unpaid amount does not match payment amount");
            }
            await payment.save();

            return res.status(200).json({ message: "Payment successfully processed" });
        }

        if (paymentEvent === "charge.failed") {
            console.log("Processing charge.failed event");
            const reference = event.data.reference;
            const payment = await Payment.findOne({ reference });

            if (!payment) {
                console.error(`No payment found for reference: ${reference}`);
                return res.status404().json({ message: "Payment not found" });
            }

            payment.status = "failed";
            payment.respDesc = event.data.gateway_response;
            await payment.save();

            console.log("Payment marked as failed for reference:", reference);
            return res.status(200).json({ message: "Payment marked as failed" });
        }

        // Step 4: Respond to acknowledge receipt of the event
        return res.status(200).json({ message: "Event received and processed" });
    } catch (error) {
        console.error("Error processing Paystack webhook event:", error);
        return res.status(500).json({ message: "Error processing webhook event" });
    }
}


/**
 * Get order details including associated payment information
 */
export const getOrderDetails = async (req, res) => {
  try {
    const { orderId } = req.params;
    
    // Validate orderId
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required"
      });
    }

    // Find order and populate relevant details
    const order = await SpOrder.findById(orderId);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found"
      });
    }

    // Find associated payment
    const payment = await SpPayment.findOne({ spOrderId: orderId });
    //find associated 

    // Combine order with payment information
    const orderWithPayment = {
      ...order.toObject(),
      payment: payment ? payment.toObject() : null
    };

    return res.status(200).json({
      success: true,
      ...orderWithPayment
    });
  } catch (error) {
    console.error("Error fetching order details:", error.message);
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch order details"
    });
  }
};

/**
 * Verify payment status for an order
 */
export const verifyOrderPayment = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id; // Assuming user is authenticated

    // Validate orderId
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required"
      });
    }

    // Find order
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found"
      });
    }

    // Find associated payment
    const payment = await SpPayment.findOne({ spOrderId: orderId });
    if (!payment) {
      return res.status(404).json({
        success: false, 
        message: "Payment record not found for this order"
      });
    }

    // If payment is already marked as paid, return success
    if (payment.status === 'paid' || order.status === 'paid') {

      return res.status(200).json({
        success: true,
        message: "Payment has already been completed",
        verified: true,
        status: 'paid',
        orderId: order._id,
        paymentId: payment._id
      });
    }

    
    return res.status(200).json({
      success: true,
      message: "Payment not yet completed",
      verified: false,
      status: payment.status,
      orderId: order._id,
      paymentId: payment._id
    });
  } catch (error) {
    console.error("Error verifying payment:", error.message);
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to verify payment status"
    });
  }
};

/**
 * Retry payment for an order
 */
export const retryOrderPayment = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { mobile } = req.body;
    const userId = req.user.id; // Assuming user is authenticated

    // Validate required fields
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required"
      });
    }

    if (!mobile) {
      return res.status(400).json({
        success: false,
        message: "Mobile number is required"
      });
    }

    // Format mobile number (remove leading zero and add country code if needed)
    const formattedMobile = formatPhoneNumber(mobile);

    // Find order
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found"
      });
    }

    // Check if order is already paid
    if (order.status === 'paid') {
      return res.status(400).json({
        success: false,
        message: "Order is already paid"
      });
    }

    // Find associated payment
    let payment = await SpPayment.findOne({ spOrderId: orderId });
    
    if (!payment) {
      // Create new payment record if it doesn't exist
      payment = new SpPayment({
        amount: order.totalAmount,
        mobile: formattedMobile,
        status: 'pending',
        spOrderId: order._id,
        userId,
        paymentMethod: 'mpesa',
        reason: 'category'
      });
    } else {
      // Update existing payment record
      payment.mobile = formattedMobile;
      payment.status = 'pending';
      payment.updatedAt = new Date();
    }

    await payment.save();

    // Update order status
    order.status = 'pending';
    await order.save();

    // Initiate M-Pesa STK push
        const stkResponse = await mpesaPaymentRequest(
            formattedMobile,
            order.totalAmount,
            `${order._id}`
        );
      
        console.log('STK response', stkResponse.data);


        if (!stkResponse.status === 200 || !stkResponse.statusText === 'OK' ) {
            console.error('Bad Request');
            throw new Error(stkResponse.data.message);
        }

        // Update payment with M-Pesa reference
        payment.reference = stkResponse.data.MerchantRequestID;
        payment.accessCode = stkResponse.data.CheckoutRequestID;
        await payment.save();
        console.log('Payment updated');

        res.json({
            success: true,
            orderId: order._id,
            paymentId: payment._id,
            message: "Payment initiated successfully"
        });

    } catch (error) {
        console.error("Checkout failed:", error.message);
        res.status(500).json({
            success: false,
            message: error.message || "Checkout processing failed"
        });
    }


};


// Controller function to get all orders for a specific company
export const getAllOrders = async (req, res) => {
    try {
      const { companyId } = req.params;
      console.log('ComanyId', companyId);
      const { page = 1, limit = 50, sort = 'createdAt', order = 'desc', status } = req.query;
      
      // Build query
      const query = {};
      
      // Add status filter if provided
      if (status && status !== 'all') {
        query.status = status;
      }
  
      // Set up pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Determine sort direction
      const sortDirection = order === 'asc' ? 1 : -1;
      const sortOptions = {};
      sortOptions[sort] = sortDirection;
  
      // Find orders with pagination and sorting
      const orders = await SpOrder.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit));
  
      // Count total matching orders for pagination info
      const totalOrders = await SpOrder.countDocuments(query);
  
      // Get all order IDs to fetch associated payments
      const orderIds = orders.map(order => order._id);
      
      // Find all payments for these orders in a single query
      const payments = await SpPayment.find({ spOrderId: { $in: orderIds } });
      
      // Create a map of order ID to payment for faster lookup
      const paymentMap = payments.reduce((map, payment) => {
        map[payment.spOrderId.toString()] = payment;
        return map;
      }, {});
  
      // Combine orders with their payment information
      const ordersWithPayments = orders.map(order => {
        const orderObject = order.toObject();
        const paymentForOrder = paymentMap[order._id.toString()];
        
        return {
          ...orderObject,
          payment: paymentForOrder ? paymentForOrder.toObject() : null
        };
      });
  
      return res.status(200).json({
        success: true,
        data: ordersWithPayments,
        pagination: {
          total: totalOrders,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(totalOrders / parseInt(limit))
        }
      });
    } catch (error) {
      console.error("Error fetching orders:", error.message);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch orders"
      });
    }
  };

// Controller function to get all orders for a specific company
  export const getCompanyOrders = async (req, res) => {
    try {
      const { companyId } = req.params;
      console.log('ComanyId', companyId);
      const { page = 1, limit = 50, sort = 'createdAt', order = 'desc', status } = req.query;
      
      // Validate companyId
      if (!companyId) {
        return res.status(400).json({
          success: false,
          message: "Company ID is required"
        });
      }
      const spCompanyId = companyId;
  
      // Build query
      const query = { spCompanyId };
      
      // Add status filter if provided
      if (status && status !== 'all') {
        query.status = status;
      }
  
      // Set up pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Determine sort direction
      const sortDirection = order === 'asc' ? 1 : -1;
      const sortOptions = {};
      sortOptions[sort] = sortDirection;
  
      // Find orders with pagination and sorting
      const orders = await SpOrder.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit));
  
      // Count total matching orders for pagination info
      const totalOrders = await SpOrder.countDocuments(query);
  
      // Get all order IDs to fetch associated payments
      const orderIds = orders.map(order => order._id);
      
      // Find all payments for these orders in a single query
      const payments = await SpPayment.find({ spOrderId: { $in: orderIds } });
      
      // Create a map of order ID to payment for faster lookup
      const paymentMap = payments.reduce((map, payment) => {
        map[payment.spOrderId.toString()] = payment;
        return map;
      }, {});
  
      // Combine orders with their payment information
      const ordersWithPayments = orders.map(order => {
        const orderObject = order.toObject();
        const paymentForOrder = paymentMap[order._id.toString()];
        
        return {
          ...orderObject,
          payment: paymentForOrder ? paymentForOrder.toObject() : null
        };
      });
  
      return res.status(200).json({
        success: true,
        data: ordersWithPayments,
        pagination: {
          total: totalOrders,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(totalOrders / parseInt(limit))
        }
      });
    } catch (error) {
      console.error("Error fetching orders:", error.message);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch orders"
      });
    }
  };

  //update an order
  export const updateOrder = async (req, res) => {
    try {
      const { orderId } = req.params;
      const { status, notes } = req.body;
  
      // Validate required fields
      if (!orderId) {
        return res.status(400).json({
          success: false,
          message: "Order ID is required"
        });
      }
  
      if (!status) {
        return res.status(400).json({
          success: false,
          message: "Status is required"
        });
      }
  
      // Find order
      const order = await SpOrder.findById(orderId);
  
      if (!order) {
        return res.status(404).json({
          success: false,
          message: "Order not found"
        });
      }
  
      // Update order status
      order.status = status;
      order.notes = notes;
      await order.save();

      res.status(200).json({
        success: true,
        message: "Order updated successfully"
      });
    } catch (error) {
      console.error("Error updating order:", error.message);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to update order"
      });
    }
  };
  
  
  // Simplified version without pagination for frontend usage
  export const xgetCompanyOrders = async (req, res) => {
    try {
      const { companyId } = req.params;
      
      // Validate companyId
      if (!companyId) {
        return res.status(400).json({
          success: false,
          message: "Company ID is required"
        });
      }
  
      // Find orders sorted by creation date (newest first)
      const orders = await SpOrder.find({ companyId })
        .sort({ createdAt: -1 });
  
      // Get all order IDs
      const orderIds = orders.map(order => order._id);
      
      // Find payments for these orders
      const payments = await SpPayment.find({ spOrderId: { $in: orderIds } });
      
      // Create lookup map for payments
      const paymentMap = payments.reduce((map, payment) => {
        map[payment.spOrderId.toString()] = payment;
        return map;
      }, {});
  
      // Combine orders with their payment information
      const ordersWithPayments = orders.map(order => {
        const orderObject = order.toObject();
        const paymentForOrder = paymentMap[order._id.toString()];
        
        return {
          ...orderObject,
          payment: paymentForOrder ? paymentForOrder.toObject() : null
        };
      });
  
      return res.status(200).json(ordersWithPayments);
    } catch (error) {
      console.error("Error fetching company orders:", error.message);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch company orders"
      });
    }
  };
  
  // Helper function to get specific order details with payments
  // To use in other controller functions that need order details
  export const getOrderWithPaymentDetails = async (orderId) => {
    try {
      // Find order
      const order = await SpOrder.findById(orderId);
      
      if (!order) {
        return null;
      }
  
      // Find associated payment
      const payment = await SpPayment.findOne({ spOrderId: orderId });
  
      // Combine order with payment information
      return {
        ...order.toObject(),
        payment: payment ? payment.toObject() : null
      };
    } catch (error) {
      console.error("Error in getOrderWithPaymentDetails:", error.message);
      throw error;
    }
  };
  

  /**
   * Creates applications for each order item in an order
   * @param {string} userId - ID of the user creating the applications
   * @param {object} order - The order document containing order items
   * @returns {object} Result containing created applications and any errors
   */
  export const createApplicationsFromOrder = async (order) => {
    try {
      // Validate inputs
  
      if (!order || !order.orderItems || !Array.isArray(order.orderItems)) {
        console.error('Invalid order or order items missing');
        throw new Error('Invalid order or order items missing');
      }
      const userId = order.createdBy;

      console.info(`Starting application creation from order ${order._id} for user ${userId}`);
  
      const results = {
        success: [],
        skipped: [],
        errors: []
      };
  
      // Process each order item
      for (const [index, item] of order.orderItems.entries()) {
        try {
          console.debug(`Processing order item ${index + 1}/${order.orderItems.length} - category: ${item.spJobCategoryId}`);
  
          // Validate order item
          if (!mongoose.Types.ObjectId.isValid(item.spJobCategoryId)) {
            console.warn(`Skipping order item ${index} - invalid job category ID`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Invalid job category ID',
              details: item
            });
            continue;
          }
  
          // Check for existing application
          const existingApplication = await SpApplication.findOne({
            spSupplierCompanyId: order.spCompanyId,
            spBuyerCompanyId: item.buyerCompanyId,
            spJobCategoryId: item.spJobCategoryId
        });
  
          if (existingApplication) {
            console.warn(`Skipping order item ${index} - application already exists for this supplier and job category`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Application already exists for this supplier and job category',
              existingApplicationId: existingApplication._id,
              details: item
            });
            continue;
          }
  
          // Fetch the job category
          const category = await SpJobCategory.findById(item.spJobCategoryId)
            .populate('spJobId')
            .populate('spCompanyId');
  
          if (!category) {
            console.warn(`Skipping order item ${index} - job category not found`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Job category not found',
              details: item
            });
            continue;
          }
  
          // Prepare application fields from category fields
          const applicationFields = category.fields.map(field => ({
            name: field.name,
            value: null,
            score: 0,
            comment: '',
            reviewerComment: '',
            documents: [],
            group: field.group,
            subGroup: field.subGroup,
            isParent: field.isParent,
            title: field.title,
            order: field.order,
            placeholder: field.placeholder,
            description: field.description,
            helpText: field.helpText,
            tooltip: field.tooltip,
            isVisible: field.isVisible,
            isEditable: field.isEditable,
            type: field.type,
            label: field.label,
            options: field.options,
            optionScore: field.optionScore,
            isRequired: field.isRequired,
            isScoreable: field.isScoreable,
            maxScore: field.maxScore,
            validations: field.validations
          }));
  
          // Create application document
          const application = new SpApplication({
            title: category.title,
            ref: `APP-${Date.now()}-${index}`,
            description: category.description,
            spJobId: category.spJobId._id,
            spJobCategoryId: category._id,
            spOrder: order._id,
            spBuyerCompanyId: item.buyerCompanyId,
            spSupplierCompanyId: order.spCompanyId,
            starts: item.starts,
            ends: item.ends,
            location: item.location,
            type: item.type,
            fields: applicationFields,
            status: 'draft',
            submittedBy: userId,
            reviewerNotes: '',
            systemScore: 0,
            complianceScore: 0,
            totalScore: 0,
            createdBy: userId
          });
  
          // Save the application
          const savedApplication = await application.save();
          console.info(`Created application ${savedApplication._id} for order item ${index}`);
  
          results.success.push({
            orderItemIndex: index,
            applicationId: savedApplication._id,
            applicationRef: savedApplication.ref
          });
  
        } catch (itemError) {
          console.error(`Error processing order item ${index}: ${itemError.message}`, {
            stack: itemError.stack,
            orderItem: item
          });
  
          results.errors.push({
            orderItemIndex: index,
            error: itemError.message,
            details: item
          });
        }
      }
  
      console.info(`Completed processing order ${order._id}. Results:`, {
        created: results.success.length,
        skipped: results.skipped.length,
        errors: results.errors.length
      });
  
      return {
        success: true,
        message: `Processed ${order.orderItems.length} order items`,
        details: results
      };
  
    } catch (error) {
      console.error(`Failed to create applications from order: ${error.message}`, {
        stack: error.stack,
        userId: order?.createdBy,
        orderId: order?._id
      });
  
      return {
        success: false,
        error: error.message,
        details: error.stack
      };
    }
  };

/**
 * Get all payments with pagination and filtering for admin
 */
export const getAllPayments = async (req, res) => {
  try {
    const { page = 1, limit = 50, sort = 'createdAt', order = 'desc', status } = req.query;

    // Build query
    const query = {};

    // Add status filter if provided
    if (status && status !== 'all') {
      query.status = status;
    }

    // Set up pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Determine sort direction
    const sortDirection = order === 'asc' ? 1 : -1;
    const sortOptions = {};
    sortOptions[sort] = sortDirection;

    // Find payments with pagination and sorting
    const payments = await SpPayment.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Count total matching payments for pagination info
    const totalPayments = await SpPayment.countDocuments(query);

    // Get all order IDs to fetch associated order information
    const orderIds = payments.map(payment => payment.spOrderId).filter(Boolean);

    // Find all orders for these payments in a single query
    const orders = await SpOrder.find({ _id: { $in: orderIds } });

    // Create a map of order ID to order for faster lookup
    const orderMap = orders.reduce((map, order) => {
      map[order._id.toString()] = order;
      return map;
    }, {});

    // Combine payments with their order information
    const paymentsWithOrders = payments.map(payment => {
      const paymentObject = payment.toObject();
      const orderForPayment = orderMap[payment.spOrderId?.toString()];

      return {
        ...paymentObject,
        order: orderForPayment ? orderForPayment.toObject() : null
      };
    });

    return res.status(200).json({
      success: true,
      data: paymentsWithOrders,
      pagination: {
        total: totalPayments,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalPayments / parseInt(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching payments:", error.message);
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch payments"
    });
  }
};

/**
 * Update payment status and sync with corresponding order
 */
export const updatePayment = async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { status, notes } = req.body;

    // Validate required fields
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        message: "Transaction ID is required"
      });
    }

    if (!status) {
      return res.status(400).json({
        success: false,
        message: "Status is required"
      });
    }

    // Validate status values
    const validStatuses = ['pending', 'success', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    // Find payment
    const payment = await SpPayment.findById(transactionId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: "Payment not found"
      });
    }

    // Store old status for comparison
    const oldStatus = payment.status;

    // Update payment status
    payment.status = status;
    if (notes) {
      payment.notes = notes;
    }
    payment.updatedAt = new Date();

    // If status changed to success, update paidAt timestamp
    if (status === 'success' && oldStatus !== 'success') {
      payment.paidAt = new Date();
    }

    await payment.save();

    // Update corresponding order status based on payment status
    if (payment.spOrderId) {
      const order = await SpOrder.findById(payment.spOrderId);

      if (order) {
        let newOrderStatus = order.status;

        // Map payment status to order status
        switch (status) {
          case 'success':
            newOrderStatus = 'paid';
            break;
          case 'pending':
            newOrderStatus = 'pending';
            break;
          case 'failed':
          case 'cancelled':
            newOrderStatus = 'cancelled';
            break;
        }

        // Only update if status actually changed
        if (order.status !== newOrderStatus) {
          order.status = newOrderStatus;
          order.updatedAt = new Date();
          await order.save();

          console.log(`Updated order ${order._id} status from ${order.status} to ${newOrderStatus}`);
        }
      }
    }

    res.status(200).json({
      success: true,
      message: "Payment updated successfully",
      data: {
        paymentId: payment._id,
        oldStatus,
        newStatus: status,
        orderId: payment.spOrderId
      }
    });
  } catch (error) {
    console.error("Error updating payment:", error.message);
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to update payment"
    });
  }
};

/**
 * Create manual payment for existing order (admin only)
 */
export const createManualPayment = async (req, res) => {
  try {
    const {
      amount,
      mobile,
      orderId,
      supplierCompanyId,
      paymentMethod = 'manual',
      transactionType = 'Manual',
      channel = 'offline',
      currency = 'KES',
      reason = 'manual_payment',
      status = 'success',
      respDesc = 'Manual payment created by admin'
    } = req.body;

    // Validate required fields
    if (!amount || !orderId || !supplierCompanyId) {
      return res.status(400).json({
        success: false,
        message: "Amount, order ID, and supplier company ID are required"
      });
    }

    // Validate amount is a positive number
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Amount must be a positive number"
      });
    }

    // Validate order exists and is not already paid
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found"
      });
    }

    if (order.status === 'paid') {
      return res.status(400).json({
        success: false,
        message: "Order is already paid"
      });
    }

    // Verify supplier company matches order
    if (order.spCompanyId.toString() !== supplierCompanyId) {
      return res.status(400).json({
        success: false,
        message: "Supplier company ID does not match order"
      });
    }

    // Check if payment already exists for this order
    const existingPayment = await SpPayment.findOne({ spOrderId: orderId });
    if (existingPayment && existingPayment.status === 'success') {
      return res.status(400).json({
        success: false,
        message: "A successful payment already exists for this order"
      });
    }

    // Format mobile number if provided
    let formattedMobile = mobile;
    if (mobile) {
      try {
        formattedMobile = formatPhoneNumber(mobile);
      } catch (phoneError) {
        // If phone formatting fails, use original value
        console.warn('Phone number formatting failed:', phoneError.message);
      }
    }

    // Generate unique transaction ID
    const transactionId = `MAN-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Create or update payment record
    let payment;
    if (existingPayment) {
      // Update existing payment
      payment = existingPayment;
      payment.amount = numericAmount;
      payment.mobile = formattedMobile;
      payment.status = status;
      payment.paymentMethod = paymentMethod;
      payment.transactionType = transactionType;
      payment.channel = channel;
      payment.currency = currency;
      payment.reason = reason;
      payment.respDesc = respDesc;
      payment.transID = transactionId;
      payment.paidAt = new Date();
      payment.updatedAt = new Date();
    } else {
      // Create new payment
      payment = new SpPayment({
        amount: numericAmount,
        mobile: formattedMobile,
        status,
        spOrderId: orderId,
        userId: req.user?.userId || req.user?.id,
        paymentMethod,
        transactionType,
        channel,
        currency,
        reason,
        respDesc,
        transID: transactionId,
        paidAt: status === 'success' ? new Date() : null,
        balance: numericAmount
      });
    }

    await payment.save();

    // Update order status to paid if payment is successful
    if (status === 'success') {
      order.status = 'paid';
      order.updatedAt = new Date();
      await order.save();

      // Create applications from order if payment is successful
      try {
        const applicationResult = await createApplicationsFromOrder(order);
        if (!applicationResult.success) {
          console.warn('Applications creation failed for manual payment:', applicationResult.error);
        }
      } catch (appError) {
        console.error('Error creating applications for manual payment:', appError.message);
      }
    }

    res.status(201).json({
      success: true,
      message: "Manual payment created successfully",
      data: {
        paymentId: payment._id,
        transactionId: payment.transID,
        orderId: order._id,
        amount: payment.amount,
        status: payment.status,
        orderStatus: order.status
      }
    });

  } catch (error) {
    console.error("Error creating manual payment:", error.message);
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to create manual payment"
    });
  }
};