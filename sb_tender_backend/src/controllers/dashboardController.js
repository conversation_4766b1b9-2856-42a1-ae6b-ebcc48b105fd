

import SpActivityLog from "../models/sp_activity_log.js";
import spJobCategory from "../models/sp_job_category.js";
import spApplication from "../models/sp_application.js";
import SpUserRole from "../models/sp_user_role.js"
import SpNotification from "../models/sp_notification.js";
import mongoose from "mongoose";
import _ from "lodash";

export const getCompanyAcivities = async (req, res) => {
    try {
      const { page = 1, limit = 20, userId, action, resource } = req.query;
      const { companyId } = req.params;
  
      const filter = { companyId };
      if (userId) filter.userId = userId;
      if (action) filter.action = action;
      if (resource) filter.resource = resource;
      
      const activities = await SpActivityLog.find(filter)
        .populate('userId', 'name email')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();
      
      const total = await SpActivityLog.countDocuments(filter);
      
      res.json({
        activities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  export const getCompanyNotifications = async (req, res) => {
    try {
      const { page = 1, limit = 20, userId, action, resource } = req.query;
      const { companyId } = req.params;
  
      const filter = { companyId };
      if (userId) filter.userId = userId;
      if (action) filter.action = action;
      if (resource) filter.resource = resource;
      const notifications = await SpNotification.find(filter)
        .populate('userId', 'name email')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();
      
      const total = await SpNotification.countDocuments(filter);
      
      res.json({
        notifications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  export const getSystemAcivities = async (req, res) => {
     
    try {
      const { page = 1, limit = 20, userId, action, resource } = req.query;
     // const { companyId } = req.params;

      const activities = await SpActivityLog.find()
        .populate('userId', 'name email')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();
      
      const total = await SpActivityLog.countDocuments();
      
      res.json({
        activities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  const getMonthlyTrends = async (companyId) => {


    return companyId;
  }

/**
 * Fetches the total number of UNIQUE users associated with a given company.
 * Since a user can have multiple roles within the same company, this function
 * uses `distinct` to count each user only once.
 * @param {string} companyId - The ID of the company.
 * @returns {Promise<number>} - The total count of unique users for the company.
 */
const getTotalUsers = async (companyId) => {
  try {
    // Use distinct to get unique user IDs for the given company
    const uniqueUserIds = await SpUserRole.distinct('userId', { companyId: companyId });
    return uniqueUserIds.length; // The count of unique users is the length of the distinct array
  } catch (error) {
    console.error("Error fetching total users:", error);
    throw new Error("Failed to get total users count.");
  }
};

  export const getSupplierDashboardMetrics = async (req, res) => {
    try {
        const { companyId } = req.params;
        const now = new Date(); 
       // appliication states are ['draft', 'submitted', 'under_review', 'approved', 'rejected'],
       
    // Use Promise.all to fetch all counts concurrently for efficiency
    const [
      totalApplications,
      pendingApplications, // Applications in 'draft', 'submitted', or 'under_review' state
      completedApplications,  // Applications that are  expired
      closedApplications,  // Applications that are 'approved' or 'rejected'
      approvedApplications, // Applications that are 'approved'
      totalUsers,
    ] = await Promise.all([
      // 1. Total Applications: Count all applications for the company
      spApplication.countDocuments({ spSupplierCompanyId: companyId }),

      // 2. Pending Applications: Count applications that are not yet in a final state
      spApplication.countDocuments({
        spSupplierCompanyId: companyId,
        status: { $in: ['draft', 'submitted', 'under_review'] }
      }),

      // 3. Closed awards / regret Applications: Count applications that have reached a final decision
      spApplication.countDocuments({
        spSupplierCompanyId: companyId,
        status: { $in: ['approved', 'rejected'] }
      }),

      // 4. Closed Applications: Count applications where the 'ends' date is on or before now
      spApplication.countDocuments({
        spSupplierCompanyId: companyId,
        ends: { $lte: now } // Check if the end date is less than or equal to the current date
      }),

      // 5. Approved Applications: Count applications specifically in the 'approved' state
      spApplication.countDocuments({
        spSupplierCompanyId: companyId,
        status: 'approved'
      }),

      // 6. Total Users: Call the updated getTotalUsers function
      getTotalUsers(companyId)
    ]);

    // Send the calculated metrics in the response
    res.json({
      metrics: {
        totalApplications,
        pendingApplications,
        completedApplications,
        closedApplications,
        approvedApplications,
        totalUsers,
      },
    });
  } catch (error) {
    console.error("Error fetching buyer dashboard metrics:", error);
    // Send a 500 status code with the error message
    res.status(500).json({ error: error.message });
  }

  }


  export const getBuyerDashboardMetrics = async (req, res) => {
    try {
        const { companyId } = req.params;
        const now = new Date(); 
   
    // Use Promise.all to fetch all counts concurrently for efficiency
    const [
      totalCategories,
      openCategories,  //  Job Categories  that are  expired
      closedCategories,  // Job Categories that are 'approved' or 'rejected'
      savings, // Difference etween highes and lowest quotte
      totalUsers,
    ] = await Promise.all([
      // 1. Total Applications: Count all applications for the company
      spJobCategory.countDocuments({ 
        spCompanyId: new mongoose.Types.ObjectId(companyId)}),

      // 2. Pending Applications: Count applications that are not yet in a final state
      spJobCategory.countDocuments({
        spCompanyId: new mongoose.Types.ObjectId(companyId),
        status: { $in: ['draft', 'open'] }
      }),

      //Closed awards / regret Applications: Count applications that have reached a final decision
      spJobCategory.countDocuments({
        spCompanyId: new mongoose.Types.ObjectId(companyId),
        status: { $in: ['closed'] }
      }),

      // savings
      0,
      //Total Users: Call the updated getTotalUsers function
      getTotalUsers(companyId)
    ]);

    // Send the calculated metrics in the response
    res.json({
      metrics: {
        totalCategories,
        openCategories,
        closedCategories,
        savings,
        totalUsers,
      },
    });
  } catch (error) {
    console.error("Error fetching buyer dashboard metrics:", error);
    // Send a 500 status code with the error message
    res.status(500).json({ error: error.message });
  }

  }

  /*
  geet supplier bid activity data ie SpApplication and all existing jobCategories. just search for past 12 months
  and organize as follows:
        // Generate realistic activity chart data and return json object similar to this:
      [
        { name: "Jan", applications: 20, categories: 40 },
        { name: "Feb", applications: 15, categories: 30 },
        { name: "Mar", sales: 35, categories: 65 },
        { name: "Apr", sales: 25, categories: 45 },
         { name: "May", sales: 45, categories: 75 },
          { name: "Jun", sales: 30, categories: 55 },
          { name: "Jul", sales: 50, categories: 85 },
          { name: "Aug", sales: 35, categories: 60 },
          { name: "Sep", sales: 55, categories: 90 },
           { name: "Oct", sales: 40, categories: 70 },
           { name: "Nov", sales: 50, categories: 80 },
           { name: "Dec", sales: 60, categories: 90 },
      ]

  */
export const getSupplierBidActivityData = async (req, res) => {
    try {
      const { companyId } = req.params;
      const now = new Date(); 
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  
      // Fetch all applications and categories created in the last year
      const [applications, categories] = await Promise.all([
        spApplication.find({
          spSupplierCompanyId: companyId,
          createdAt: { $gte: oneYearAgo, $lte: now }
        }),
        spJobCategory.find({
          createdAt: { $gte: oneYearAgo, $lte: now }
        })
      ]);
  
      // Group by month
      const monthlyApplications = _.groupBy(applications, (app) => {
        const date = new Date(app.createdAt);
        return date.toLocaleString('default', { month: 'short' });
      });
  
      const monthlyCategories = _.groupBy(categories, (cat) => {
        const date = new Date(cat.createdAt);
        return date.toLocaleString('default', { month: 'short' });
      });
  
      // Prepare the final data
      const activityData = [];
      for (let i = 0; i < 12; i++) {
        const month = now.toLocaleString('default', { month: 'short' });
        activityData.unshift({
          name: month,
          applications: (monthlyApplications[month] || []).length,
          categories: (monthlyCategories[month] || []).length
        });
        now.setMonth(now.getMonth() - 1);
      }
  
      res.json({ activityData });
    } catch (error) {
      console.error("Error fetching supplier bid activity data:", error);
      res.status(500).json({ error: error.message });
    }
  }


  export const getBuyerCategoryActivityData = async (req, res) => {
    try {
      const { companyId } = req.params;
      const now = new Date(); 
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  
      // Fetch all applications and categories created in the last year
      const [applications, categories] = await Promise.all([
        spApplication.find({
          spBuyerCompanyId: companyId,
          createdAt: { $gte: oneYearAgo, $lte: now }
        }),
        spJobCategory.find({
          spCompanyId: companyId,
          createdAt: { $gte: oneYearAgo, $lte: now }
        })
      ]);
  
      // Group by month
      const monthlyApplications = _.groupBy(applications, (app) => {
        const date = new Date(app.createdAt);
        return date.toLocaleString('default', { month: 'short' });
      });
  
      const monthlyCategories = _.groupBy(categories, (cat) => {
        const date = new Date(cat.createdAt);
        return date.toLocaleString('default', { month: 'short' });
      });
  
      // Prepare the final data
      const activityData = [];
      for (let i = 0; i < 12; i++) {
        const month = now.toLocaleString('default', { month: 'short' });
        activityData.unshift({
          name: month,
          applications: (monthlyApplications[month] || []).length,
          categories: (monthlyCategories[month] || []).length
        });
        now.setMonth(now.getMonth() - 1);
      }
  
      res.json({ activityData });
    } catch (error) {
      console.error("Error fetching buyer category activity data:", error);
      res.status(500).json({ error: error.message });
    }
  }