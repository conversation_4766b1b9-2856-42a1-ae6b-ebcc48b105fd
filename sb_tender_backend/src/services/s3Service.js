import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import fs from 'fs/promises';

const s3Client = new S3Client({
  //endpoint: process.env.AWS_ENDPOINT,
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_KEY,
    secretAccessKey: process.env.AWS_SECRET,
  },
});


export const uploadToS3 = async (buffer, folder, fileName, companyId, acl = 'private') => {
  console.log("S3 ENV CONFIG:", {
    region: process.env.AWS_REGION,
    bucket: process.env.AWS_BUCKET,
    endpoint: process.env.AWS_ENDPOINT, // should be undefined or null
  });
  const params = {
    Bucket: process.env.AWS_BUCKET,
    Key: `${folder}/${companyId}/${fileName}`,
    Body: buffer,
    ACL: acl,
  };

  try {
    await s3Client.send(new PutObjectCommand(params));
    return `https://${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${folder}/${companyId}/${fileName}`;
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw new Error('Upload to S3 failed');
  }
};

export const deleteFromS3 = async (fileUrl) => {
  const s3Key = fileUrl.split('.com/')[1];
  const params = {
    Bucket: process.env.AWS_BUCKET,
    Key: s3Key,
  };

  try {
    await s3Client.send(new DeleteObjectCommand(params));
  } catch (error) {
    console.error('Error deleting from S3:', error);
    throw new Error('File deletion from S3 failed');
  }
};





export const getObjectFromS3 = async (fileUrl) => {
  console.log(fileUrl);
  const s3Key = fileUrl.split('.com/')[1]; 

  const params = {
    Bucket: process.env.AWS_BUCKET,
    Key: s3Key,
  };

  try {
    const s3Response = await s3Client.send(new GetObjectCommand(params));
    return s3Response;
   
  } catch (error) {
    console.error('S3 Download Error:', error);
    res.status(500).send('Failed to fetch file from S3');
  }

  
};
