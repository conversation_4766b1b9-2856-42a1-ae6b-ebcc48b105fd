import nodemailer from "nodemailer";
import SentEmail from "../models/SentEmail.js"
// Create the transporter configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SMTP_HOST,//"smtp.example.com",
  port: process.env.EMAIL_SMTP_PORT,//587,
  secure: false, // Upgrade later with STARTTLS
  auth: {
    user: process.env.EMAIL_SMTP_USER,
    pass: process.env.EMAIL_SMTP_PASS,
  },
  tls: {
    // Do not fail on invalid certs
    rejectUnauthorized: false,
  },
});

/**
 * Send an email with customizable options.
 * 
 * @param {Object} options - The email options.
 * @param {string} options.from - Sender's email address (mandatory).
 * @param {string|string[]} options.to - Recipient's email address(es) (mandatory).
 * @param {string|string[]} [options.cc] - CC recipient(s).
 * @param {string|string[]} [options.bcc] - BCC recipient(s).
 * @param {string} [options.subject] - Email subject.
 * @param {string} [options.text] - Plain text version of the email body.
 * @param {string} [options.html] - HTML version of the email body.
 * @param {Array} [options.attachments] - Array of attachments.
 * @param {string} [options.replyTo] - Email address for replies.
 * @param {string} [options.sender] - Sender's name or email address.
 * @param {string} [options.references] - Message reference IDs for threading.
 * @param {Object} [options.envelope] - Custom envelope options.
 * @param {Object} [options.dsn] - Delivery Status Notification options.
 * @returns {Promise} - Resolves if email is sent successfully, rejects otherwise.
 */
export const sendMail = async (options) => {
  try {
    // Validate mandatory fields
    if (!options.from || !options.to) {
      throw new Error("Both 'from' and 'to' fields are mandatory.");
    }

    // Send the email
    const info = await transporter.sendMail({
      from: options.from,
      to: options.to,
      cc: options.cc,
      bcc: options.bcc,
      subject: options.subject,
      text: options.text,
      //html: options.html,
      attachments: options.attachments,
      replyTo: options.replyTo,
      sender: options.sender,
      references: options.references,
      envelope: options.envelope,
      dsn: options.dsn,
    });
    //save all messages sent
    await SentEmail.create({
      sender: options.from,
      recipient: options.to,
      subject: options.subject,
      body: options.text || '',
      hasAttachment: !!options.attachments?.length,
      attachments: options.attachments || [],
      cc: options.cc || [],
      bcc: options.bcc || [],
      status: 'sent'
    });

    console.log("Email sent successfully:", info.messageId);
    return info;
  } catch (error) {
    await SentEmail.create({
      sender: options.from,
      recipient: options.to,
      subject: options.subject,
      body: options.text || '',
      hasAttachment: !!options.attachments?.length,
      attachments: options.attachments || [],
      cc: options.cc || [],
      bcc: options.bcc || [],
      status: 'failed',
      errorMessage: error.message
    });

    console.error("Error sending email:", error);
    throw error;
  }
};

/**
 * Verify the email transporter configuration.
 * 
 * @returns {Promise} - Resolves if transporter is ready, rejects otherwise.
 */
export const verifySendMail = async () => {
  try {
    await transporter.verify();
    console.log("Email transporter is ready to send messages.");
  } catch (error) {
    console.error("Error verifying email transporter:", error);
    throw error;
  }
};
