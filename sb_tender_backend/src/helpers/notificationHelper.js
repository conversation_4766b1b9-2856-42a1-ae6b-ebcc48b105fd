import notificationService from '../services/notificationService.js';
import User from '../models/sp_user.js';

class NotificationHelper {
  // Quick send methods for common use cases
  
  // Send welcome email
  static async sendWelcomeEmail(user, companyId = null) {
    return await notificationService.sendNotification({
      companyId,
      userId: user._id,
      channel: 'email',
      type: 'email',
      title: 'Welcome to Our Platform!',
      message: `Hello ${user.name}, welcome to our platform. We're excited to have you on board!`,
      richContent: {
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Welcome to Our Platform!</h2>
            <p>Hello ${user.name},</p>
            <p>Welcome to our platform. We're excited to have you on board!</p>
            <p>Get started by exploring our features and don't hesitate to reach out if you need help.</p>
            <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">
              <p style="margin: 0;"><strong>Next Steps:</strong></p>
              <ul>
                <li>Complete your profile</li>
                <li>Explore the dashboard</li>
                <li>Join your first project</li>
              </ul>
            </div>
            <p>Best regards,<br>The Team</p>
          </div>
        `
      },
      recipient: {
        email: user.email,
        userId: user._id
      },
      metadata: {
        category: 'onboarding',
        tags: ['welcome', 'user-registration']
      },
      priority: 'high'
    });
  }

  // Send password reset email
  static async sendPasswordResetEmail(user, resetToken, companyId = null) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    return await notificationService.sendNotification({
      companyId,
      userId: user._id,
      channel: 'email',
      type: 'email',
      title: 'Password Reset Request',
      message: `Hello ${user.name}, you requested a password reset. Click the link to reset your password: ${resetUrl}`,
      richContent: {
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Password Reset Request</h2>
            <p>Hello ${user.name},</p>
            <p>You requested a password reset for your account. Click the button below to reset your password:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
            </div>
            <p>If you didn't request this password reset, please ignore this email.</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>Best regards,<br>The Team</p>
          </div>
        `
      },
      recipient: {
        email: user.email,
        userId: user._id
      },
      metadata: {
        category: 'security',
        tags: ['password-reset', 'security'],
        reference: 'password-reset',
        referenceId: user._id
      },
      priority: 'high',
      settings: {
        expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
      }
    });
  }

  // Send order confirmation
  static async sendOrderConfirmation(order, user, companyId = null) {
    return await notificationService.sendNotification({
      companyId,
      userId: user._id,
      channel: 'email',
      type: 'email',
      title: `Order Confirmation - #${order.orderNumber}`,
      message: `Your order #${order.orderNumber} has been confirmed. Total: $${order.totalAmount}`,
      richContent: {
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Order Confirmation</h2>
            <p>Hello ${user.name},</p>
            <p>Thank you for your order! Your order has been confirmed and is being processed.</p>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3>Order Details</h3>
              <p><strong>Order Number:</strong> #${order._id}</p>
              <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
              <p><strong>Total Amount:</strong> $${order.totalAmount}</p>
            </div>
            <p>You will receive another email when your order ships.</p>
            <p>Best regards,<br>The Team</p>
          </div>
        `
      },
      recipient: {
        email: user.email,
        userId: user._id
      },
      metadata: {
        category: 'order',
        tags: ['order-confirmation', 'purchase'],
        reference: 'order',
        referenceId: order._id,
        customData: {
          orderNumber: order._id,
          total: order.totalAmount
        }
      },
      priority: 'medium'
    });
  }

  // Send system alert
  static async sendSystemAlert(message, alertType = 'info', companyId = null, userId = null) {
    return await notificationService.sendNotification({
      companyId,
      userId,
      channel: 'app',
      type: 'app',
      title: 'System Alert',
      message,
      recipient: {
        userId
      },
      metadata: {
        category: 'system',
        tags: ['system-alert', alertType],
        customData: {
          alertType
        }
      },
      priority: alertType === 'error' ? 'urgent' : 'medium'
    });
  }

  // Send SMS verification code
  static async sendSMSVerification(phone, code, userId = null, companyId = null) {
    return await notificationService.sendNotification({
      companyId,
      userId,
      channel: 'sms',
      type: 'sms',
      title: 'Verification Code',
      message: `Your verification code is: ${code}. This code will expire in 10 minutes.`,
      recipient: {
        phone,
        userId
      },
      metadata: {
        category: 'verification',
        tags: ['sms-verification', 'security'],
        customData: { code }
      },
      priority: 'high',
      settings: {
        expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
      }
    });
  }

  // Send push notification
  static async sendPushNotification(deviceToken, title, message, data = {}, userId = null, companyId = null) {
    return await notificationService.sendNotification({
      companyId,
      userId,
      channel: 'push',
      type: 'push',
      title,
      message,
      recipient: {
        deviceToken,
        userId
      },
      metadata: {
        category: 'push',
        tags: ['push-notification'],
        customData: data
      },
      priority: 'medium'
    });
  }

  // Send notification to multiple users
  static async sendBulkUserNotification(userIds, notificationData, companyId = null) {
    const notifications = userIds.map(userId => ({
      ...notificationData,
      companyId,
      userId,
      recipient: {
        ...notificationData.recipient,
        userId
      }
    }));

    return await notificationService.sendBulkNotifications(notifications);
  }

  // Send scheduled reminder
  static async scheduleReminder(scheduleDate, title, message, userId, companyId = null, channel = 'app') {
    const delay = new Date(scheduleDate).getTime() - Date.now();
    
    if (delay <= 0) {
      throw new Error('Reminder date must be in the future');
    }

    return await notificationService.sendNotification({
      companyId,
      userId,
      channel,
      type: channel,
      title,
      message,
      recipient: {
        userId
      },
      metadata: {
        category: 'reminder',
        tags: ['scheduled', 'reminder']
      },
      delivery: {
        scheduledAt: scheduleDate
      }
    }, {
      useQueue: true,
      delay
    });
  }

  // Send company-wide announcement
  static async sendCompanyAnnouncement(companyId, title, message, options = {}) {
    return await notificationService.sendNotification({
      companyId,
      channel: options.channel || 'app',
      type: options.channel || 'app',
      title,
      message,
      richContent: options.richContent,
      metadata: {
        category: 'announcement',
        tags: ['company-wide', 'announcement'],
        customData: options.customData
      },
      priority: options.priority || 'medium'
    }, {
      useQueue: options.useQueue || false
    });
  }

  // Helper to create notification templates
  static createEmailTemplate(templateName, data) {
    const templates = {
      welcome: (data) => ({
        subject: `Welcome to ${data.companyName || 'Our Platform'}!`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Welcome ${data.userName}!</h2>
            <p>We're excited to have you join ${data.companyName || 'our platform'}.</p>
            ${data.content || ''}
            <p>Best regards,<br>The Team</p>
          </div>
        `
      }),
      notification: (data) => ({
        subject: data.subject,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>${data.title}</h2>
            <p>${data.message}</p>
            ${data.content || ''}
            <p>Best regards,<br>The Team</p>
          </div>
        `
      })
    };

    const template = templates[templateName];
    return template ? template(data) : null;
  }


  /**
   * Sends a new tender alert to all users with matching preferences.
   * This function is called when a new tender is published (e.g., via an API endpoint).
   *
   * @param {Object} tenderData - The complete tender information received from the frontend/source.
   * Expected structure:
   * {
   * ref: String, // RS/23/2025-2027
   * location: String, // e.g., Nairobi (must match user preference string exactly or case-insensitively)
   * category: String, // e.g., SERVICES-Information technology (must match user preference string)
   * description: String,
   * website: String,
   * link: String, // Full public link
   * organization: String, // PE: Naromoru Water & Sanitation Company
   * closes: String, // e.g., "Jun 25, 2025" - parse to Date
   * published: String, // e.g., "Jun 20, 2025" - parse to Date
   * ocid: String,
   * tenderNo: String,
   * procurementMethod: String,
   * submissionMethod: String,
   * openingVenue: String,
   * tenderFee: String,
   * closingTime: String,
   * portalPublishDate: String, // e.g., "2025 Jun 11 10:51:33" - parse to Date
   * daysToClose: Number,
   * financialYear: String,
   * attachments: Array<{ name: string, url: string }>
   * }
   */
  static async sendNewTenderAlert(tenderData) {
    console.log(`Received request to alert for new tender: ${tenderData.ref} (${tenderData.category}, ${tenderData.location})`);

    try {
      // Validate essential tender data
      if (!tenderData.ref || !tenderData.location || !tenderData.category || !tenderData.description || !tenderData.link) {
        throw new Error('Missing essential tender data for alert generation.');
      }

      // Standardize location/category strings for matching
      const tenderLocation = tenderData.location.trim().toLowerCase();
      const tenderCategory = tenderData.category.trim().toLowerCase();

      // Parse dates from string to Date objects for consistent storage
      const parsedTenderData = { ...tenderData };
      if (tenderData.closes) parsedTenderData.closes = new Date(tenderData.closes);
      if (tenderData.published) parsedTenderData.published = new Date(tenderData.published);
      if (tenderData.portalPublishDate) parsedTenderData.portalPublishDate = new Date(tenderData.portalPublishDate);


      // Find all users who have active tender alert preferences
      const usersWithPreferences = await User.find({
        'notificationPreferences.tenderAlerts': { $exists: true, $not: { $size: 0 } },
        // Optionally add a check for general alert enablement if you have one
        // 'notificationPreferences.enableTenderAlerts': true, 
      })
      .select('_id name email mobile notificationPreferences') // Select only necessary fields
      .lean();

      if (usersWithPreferences.length === 0) {
        console.log('No users with tender alert preferences found.');
        return { success: true, message: 'No users with tender preferences to alert.' };
      }

      let alertsQueuedCount = 0;

      for (const user of usersWithPreferences) {
        const userId = user._id.toString();
        const userEmail = user.email;
        const userMobile = user.mobile;
        const userName = user.name;
        const userPreferences = user.notificationPreferences.tenderAlerts || []; 
        const enableSms = user.notificationPreferences.enableSmsAlerts ?? true;
        const enableEmail = user.notificationPreferences.enableEmailAlerts ?? true;

        // --- Duplicate Check using SpNotification metadata ---
        // This query checks if an 'tender_alert' notification for this tenderRef and userId
        // has already been sent (status: 'sent' or 'delivered').
        const existingAlert = await SpNotification.findOne({
            userId: user._id,
            'metadata.tenderInformation.ref': tenderData.ref, // Match by tender reference
            type: { $in: ['tender_alert_sms', 'tender_alert_email'] }, // Match by type
            status: { $in: ['sent', 'delivered'] } // Only if it was actually sent/delivered
        }).lean();

        if (existingAlert) {
            console.log(`Skipping tender ${tenderData.ref} for user ${userId}: Alert already sent via SpNotification.`);
            continue;
        }

        // --- Preference Matching Logic ---
        const isMatch = userPreferences.some(pref => {
            const prefLocation = pref.location.trim().toLowerCase();
            const prefCategory = pref.category.trim().toLowerCase();

            // Location Match: exact match or "countrywide" preference
            const locationMatches = (tenderLocation === prefLocation) || 
                                   (prefLocation === 'countrywide' || prefLocation === 'nationwide');

            // Category Match: exact match or "all" category preference
            const categoryMatches = (tenderCategory === prefCategory) ||
                                    (prefCategory === 'all' || prefCategory.includes('all')); // Simple 'all' check

            return locationMatches && categoryMatches;
        });

        if (isMatch) {
            console.log(`Tender ${tenderData.ref} matches preferences for user ${userId}. Queuing alert.`);

            // Construct message details
            const organization = tenderData.organization || 'N/A';
            const location = tenderData.location.toUpperCase(); // Original case for display
            const category = tenderData.category.toUpperCase(); // Original case for display
            const description = tenderData.description ? tenderData.description.toUpperCase() : 'N/A';
            const tenderTitle = tenderData.title || tenderData.description || 'New Tender';
            const publicLink = tenderData.link; // The direct link to the tender

            const smsMessage = `${category},${location}\nPE: ${organization}\nREF: ${tenderData.ref}\nDESC: ${description}\n\nView: ${publicLink}`;
            const emailHtml = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333;">New Tender Alert: ${tenderTitle}</h2>
                    <p><strong>Procuring Entity:</strong> ${organization}</p>
                    <p><strong>Reference:</strong> ${tenderData.ref}</p>
                    <p><strong>Category:</strong> ${category}</p>
                    <p><strong>Location:</strong> ${location}</p>
                    <p><strong>Description:</strong> ${description}</p>
                    <p><strong>Published Date:</strong> ${tenderData.published ? new Date(tenderData.published).toDateString() : 'N/A'}</p>
                    <p><strong>Closing Date:</strong> ${parsedTenderData.closes ? parsedTenderData.closes.toDateString() : 'N/A'}</p>
                    <p>Click the link below to view the full tender details:</p>
                    <p><a href="${publicLink}" style="background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">View Tender</a></p>
                    ${tenderData.website ? `<p><strong>Organization Website:</strong> <a href="${tenderData.website}">${tenderData.website}</a></p>` : ''}
                    <p>Best regards,<br>The TenderAsili Team</p>
                </div>
            `;
            
            // Populate metadata.tenderInformation
            const tenderMetadata = {
                ...parsedTenderData, // All fields from parsed tenderData
                // Ensure correct types if converting from string
                closes: parsedTenderData.closes,
                published: parsedTenderData.published,
                portalPublishDate: parsedTenderData.portalPublishDate,
            };

            // Send SMS if mobile exists
            if (userMobile) {
                try {
                    await notificationService.sendNotification({
                        userId: userId,
                        channel: 'sms',
                        type: 'tender_alert_sms', // Specific type for easy querying
                        title: 'Tender Alert',
                        message: smsMessage,
                        recipient: { phone: userMobile, userId: userId },
                        metadata: {
                            category: 'tender_alert',
                            tags: ['tender', 'new', 'sms'],
                            tenderInformation: tenderMetadata // Store all tender info here
                        },
                        priority: 'high'
                    });
                    alertsQueuedCount++;
                    console.log(`SMS alert queued for user ${userId} for tender ${tenderData.ref}`);
                } catch (smsError) {
                    console.error(`Error queuing SMS for user ${userId}, tender ${tenderData.ref}:`, smsError);
                }
              }
          

            // Send Email if email exists
            if (userEmail) {
                try {
                    await notificationService.sendNotification({
                        userId: userId,
                        channel: 'email',
                        type: 'tender_alert_email', // Specific type for easy querying
                        title: `New Tender Alert: ${tenderTitle}`,
                        message: `A new tender matching your preferences: ${tenderTitle}`, // Plain text for summary
                        richContent: {
                            html: emailHtml
                        },
                        recipient: { email: userEmail, userId: userId },
                        metadata: {
                            category: 'tender_alert',
                            tags: ['tender', 'new', 'email'],
                            tenderInformation: tenderMetadata // Store all tender info here
                        },
                        priority: 'high'
                    });
                    alertsQueuedCount++;
                    console.log(`Email alert queued for user ${userId} for tender ${tenderData.ref}`);
                } catch (emailError) {
                    console.error(`Error queuing Email for user ${userId}, tender ${tenderData.ref}:`, emailError);
                }
            }
        }
      }

      console.log(`Tender alert processing finished for tender ${tenderData.ref}. Total alerts queued: ${alertsQueuedCount}`);
      return { success: true, message: `Tender ${tenderData.ref} processed. ${alertsQueuedCount} alerts queued.` };

    } catch (error) {
      console.error(`Error processing new tender alert for ${tenderData.ref}:`, error);
      throw error; // Re-throw for API endpoint to handle
    }
  }
   // Note: sendEmail and sendSMS methods are not directly in NotificationHelper if
  // you are using NotificationService.sendNotification to queue them.
  // The actual sending happens in the BullMQ worker's processQueuedNotification function.



  // --- Utility methods (as in your existing helper) ---
  static validateNotificationData(data) {
    if (!data.channel || !data.recipient || !data.title || !data.message) {
      return { isValid: false, errors: ['Missing required notification data'] };
    }
    return { isValid: true, errors: [] };
  }

 
/**
   * Utility method to get notification preferences for a specific user.
   * Fetches preferences directly from the User model.
   */
static async getUserNotificationPreferences(userId) {
  try {
    const user = await User.findById(userId)
      .select('notificationPreferences') // Only fetch the preferences field
      .lean(); // Return plain JavaScript object for performance

    if (!user) {
      console.warn(`User ${userId} not found for preferences lookup.`);
      // Return a default, "all enabled" or "all disabled" preference if user not found
      // For safety, might default to disabled to prevent accidental spam
      return {
        emailAlerts: { enabled: false, categories: {} },
        smsAlerts: { enabled: false, categories: {} },
        appAlerts: { enabled: false, categories: {} },
        pushAlerts: { enabled: false, categories: {} },
        tenderAlerts: [] // Empty for tender specific
      };
    }
    return user.notificationPreferences;
  } catch (error) {
    console.error(`Error fetching preferences for user ${userId}:`, error);
    // Fallback to safe defaults on error
    return {
      emailAlerts: { enabled: false, categories: {} },
      smsAlerts: { enabled: false, categories: {} },
      appAlerts: { enabled: false, categories: {} },
      pushAlerts: { enabled: false, categories: {} },
      tenderAlerts: []
    };
  }
}


/**
 * Check if a notification should be sent to a user based on their preferences.
 * @param {string} userId - The ID of the user.
 * @param {'email'|'sms'|'app'|'push'} channel - The communication channel.
 * @param {string} type - The notification type (e.g., 'welcome', 'orders', 'tender'). This maps to categories.
 * @returns {boolean} - True if notification should be sent, false otherwise.
 */
static async shouldSendNotification(userId, channel, type) {
  const preferences = await this.getUserNotificationPreferences(userId);
  const channelPrefKey = `${channel}Alerts`; // e.g., 'emailAlerts', 'smsAlerts'

  // Check if the channel preferences object exists and is enabled
  if (!preferences[channelPrefKey] || !preferences[channelPrefKey].enabled) {
    return false;
  }

  // Check if the specific notification type (category) is enabled for this channel
  // We assume `type` directly maps to a category name (e.g., 'orders', 'tender', 'marketing', 'system', 'reminders',  )
  const categories = preferences[channelPrefKey].categories;
  if (categories && (typeof categories[type] === 'boolean')) {
    return categories[type]; // Return the specific setting for that type
  }
  
  // Default to true if category not explicitly defined (or category object itself is missing)
  // You might want a stricter default (e.g., false) based on your app's privacy policy.
  return true;
}

  // Direct email sending method for password reset and other system emails
  static async sendEmail(emailData) {
    try {
      console.log('📧 [NOTIFICATION_HELPER] Sending direct email to:', emailData.to);

      // Use the notification service to send email directly
      const notificationData = {
        type: 'system',
        channel: 'email',
        priority: 'high',
        title: emailData.subject,
        message: emailData.html || emailData.text,
        recipient: {
          email: emailData.to
        },
        metadata: {
          directEmail: true,
          htmlContent: emailData.html
        }
      };

      // Import notification service dynamically to avoid circular imports
      const { default: notificationService } = await import('../services/notificationService.js');

      const result = await notificationService.sendNotification(notificationData);
      console.log('✅ [NOTIFICATION_HELPER] Email sent successfully');
      return result;

    } catch (error) {
      console.error('❌ [NOTIFICATION_HELPER] Failed to send email:', error);
      throw error;
    }
  }
}

export default NotificationHelper;