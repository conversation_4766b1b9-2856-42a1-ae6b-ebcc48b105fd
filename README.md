



# TECHNICAL ARCHITECTURE

sketch of the Tenderasili Procurement Platform Infrastructure Setup:

                              +---------------------+
                              |    Users/Clients    |
                              +---------+-----------+
                                        |
                                        v
                             +----------+-----------+
                             |   Serverless App     |
                             |     (Vercel)         |
                             +----------+-----------+
                                        |
             +--------------------------+---------------------------+
             |                          |                           |
             v                          v                           v
+-----------------------+   +------------------------+   +------------------------+
|   Redis Database      |   |   Main Database        |   |   Object Storage       |
|   (Aiven - In-Memory) |   |   (Aiven - Postgres)   |   |   (S3 - Documents)     |
+-----------------------+   +------------------------+   +------------------------+

             |                          |                           |
             |                          |                           |
             +-----------------------------------------------------+
                                        |
                                        v
                            +------------------------+
                            |   Email Provider       |
                            |   (Local, to upgrade)  |
                            +------------------------+

                            +------------------------+
                            |   SMS Integration      |
                            |   (Sozuri API)         |
                            +------------------------+

                            +------------------------+
                            |   Payment Integration  |
                            |   (Mesa & Paystack)    |
                            +------------------------+
Notes:
Vercel is the main entry point running the frontend/backend.

It connects to Redis for in-memory caching (e.g. sessions).

Main data (companies, users, forms) is in Aiven's PostgreSQL.

Documents (uploads) go to S3.

Email is currently local (can be upgraded to SendGrid, Resend, etc.).

Sozuri handles SMS messaging.

Mesa handles mobile payments; Paystack handles card payments.