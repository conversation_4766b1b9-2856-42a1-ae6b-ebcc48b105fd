when coding in next pages

use isloading state to indicate component loading status
backeend logic...
// Controller function to get all orders for a specific company
export const getCompanyOrders = async (req, res) => {
    try {
      const { companyId } = req.params;
      console.log('ComanyId', companyId);
      const { page = 1, limit = 50, sort = 'createdAt', order = 'desc', status } = req.query;
      
      // Validate companyId
      if (!companyId) {
        return res.status(400).json({
          success: false,
          message: "Company ID is required"
        });
      }
      const spCompanyId = companyId;
  
      // Build query
      const query = { spCompanyId };
      
      // Add status filter if provided
      if (status && status !== 'all') {
        query.status = status;
      }
  
      // Set up pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Determine sort direction
      const sortDirection = order === 'asc' ? 1 : -1;
      const sortOptions = {};
      sortOptions[sort] = sortDirection;
  
      // Find orders with pagination and sorting
      const orders = await SpOrder.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit));
  
      // Count total matching orders for pagination info
      const totalOrders = await SpOrder.countDocuments(query);
  
      // Get all order IDs to fetch associated payments
      const orderIds = orders.map(order => order._id);
      
      // Find all payments for these orders in a single query
      const payments = await SpPayment.find({ spOrderId: { $in: orderIds } });
      
      // Create a map of order ID to payment for faster lookup
      const paymentMap = payments.reduce((map, payment) => {
        map[payment.spOrderId.toString()] = payment;
        return map;
      }, {});
  
      // Combine orders with their payment information
      const ordersWithPayments = orders.map(order => {
        const orderObject = order.toObject();
        const paymentForOrder = paymentMap[order._id.toString()];
        
        return {
          ...orderObject,
          payment: paymentForOrder ? paymentForOrder.toObject() : null
        };
      });
  
      return res.status(200).json({
        success: true,
        data: ordersWithPayments,
        pagination: {
          total: totalOrders,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(totalOrders / parseInt(limit))
        }
      });
    } catch (error) {
      console.error("Error fetching orders:", error.message);
      return res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch orders"
      });
    }
  };






## create page frontend /app / admin > orders
shows all spoorders (supplier orders made against buyer jobcategories). should have a summary cards single row at the top and a search and filterable paginated table of all orders. with ability to update status and add notes of an order via a modal. this can happen when a paymeent is maybe done offline failing tto update. give the page a clean modern look.
orderItems correspond to spJobCategory so theese should be displayed in a column like 'order categories'. 
import mongoose from 'mongoose';

const orderSchema = new mongoose.Schema({
    spCompanyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpCompany',
        required: true
    },
    status: {
        type: String,
        enum: [ 'checkout', 'pending', 'paid', 'cancelled', 'suspend'],
        default: 'checkout'
    },
    totalAmount: {
        type: mongoose.Schema.Types.Decimal128,
        default: 0
    },
    orderItems: [{
        title: { type: String, required: true, trim: true },     
        price: {
            type: mongoose.Schema.Types.Decimal128,
            required: true
        },
        starts: {
            type: Date,
            required: true
        },
        ends: {
            type: Date,
            required: true
        },
        location: {
            type: String,
            required: true
        },
        type: {
            type: String,
            enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
            required: true
        },
        supplierCompanyId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpCompany',
            required: true
        },
        buyerCompanyId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpCompany',
            required: true
        },
        spJobCategoryId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpJobCaegory',
            required: true
        },

    }],
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

const SpOrder = mongoose.model('SpOrder', orderSchema);
export default SpOrder;

backend api/admin/orders get, put



## Admin > transactions 
shows all payments. search filter pagination. summary cards single row on top. paginateed table with ability to update status or create a new payment via a modal(this requires the order to be first created by thee supplier and existing and not in paid status). this can happen when a paymeent is maybe done offline. a manual payment(requires entering the amount, supplier id and order id) will create a payment record and update the order status to paid. if a paymeent is set to pending, the corresponding order is set to pending. 

/ SpPayment({
            amount: calculatedTotal,
            mobile: formattedMobile,
            status: 'pending',
            spOrderId: order._id,
            //reference: order._id,
            userId,
            paymentMethod: 'mpesa',
            reason: 'category'
          payment.transactionType = "Mpesa";
        payment.respDesc = callbackData.ResultDesc;
        payment.currency = "KES";
        payment.channel = "mobile-money";
        payment.balance = payment.amount;

backend api/admin/transactions get, post route (needs odrerId and supplier companyId), put


## allow admin to do review/compliance or due diligence

** check update application how it works and uses scores ***

  admin/buyer > job > category > applications > applicationId on a scrollable page   with a button to toggle review(make review inputs editable)
  this should load the entire application as a long form allowing the reviewer to review and add compliance score to any field. 
  fields with documents should have a view button that downloads or opens a modal to view the document
  group fields into sections and subsections
  each section field has a score and compliance score that is a sum of all fields in that section
  each subsection field has a score and compliance score that is a sum of all fields in that subsection
  each field has a score and compliance score
  the compliance score is the score given by the reviewer together with a comment
  the system score is the score given by the system based on the validation rules


each application fields field has 
    comment: String,                         // optional comment
  reviewerComment: String,                // optional reviewer comment
  score: Number, //NOT CHANGEABLE
    maxScore: {
    type: Number,
    default: 0
  },
  complianceScore: {  //THIS IS CHANGEABLE BY THE PERSON DOING REVIEW
    type: Number,
    default: 0
  },


an application has states AND CAN ONLY BE REVIEWED when in submitted state orunder_review state
 enum: ['draft', 'submitted', 'under_review', 'approved', 'rejected'],

the reviewer can also add reviewerNotes to the application at the end of the review.
 reviewer, reviewDate, reviewStatus, systemScore, complianceScore, totalScore are updated when application is reeviewed:




# ARCHIVE: add document archive
buyer/archive/jobs   buyer/archive/jobs/categories buyer/archive/jobs/categories buyer/archive/jobs/categories/applications  buyer/archive/jobs/categories/applications/applicationId page where you can download all documents for that applicationr plus ddLetter, paymentReceipt, contractDocument, technicalResponse, responseLetter


supplier/archive/jobs   supplier/archive/jobs/categories supplier/archive/jobs/categories supplier/archive/jobs/categories/applications  supplier/archive/jobs/categories/applications/applicationId page where you can download all documents for that application plus ddLetter, paymentReceipt, contractDocument, technicalResponse, responseLetter


Admin archive
 -admin/archive/jobs > admin/archive/jobs/categories > admin/archive/jobs/categories/cateegoryId/applications > admin/archive/jobs/categories/categoryId/applications/applicationId  a page with all field documents uploaded by supplier plus ddLetter, paymentReceipt, contractDocument, technicalResponse, responseLetter




  # NOTIFICATIONS
  how they are working

  ### job related nottifications
  - when job is created, notify all suppliers in the category
  - when job is updated, notify all suppliers in the category
  - when job is deleted, notify all suppliers in the category
  - when job is closed, notify all suppliers in the category
  - when job is reopened, notify all suppliers in the category
  - when job is assigned to a supplier, notify the supplier

  ### public tender notifications
  - when a new tender is created, notify all suppliers with matching preferences

### Admin/ Notifications

ability to see all notifications, create a notification of any type to any user or group of users groups.
page with a single row of notifications stats summarycards at the top showing importantmetrics about notifications.
shows all notifications. add a search and filterable paginated table of all notifications latest first. with ability to resend a failed notification. or all notifications having the same bulkId. nootifications sent to same recipients will have the same bulkId. when users are selected, the phone numbers and emails and names are resolved from the user data.
allow message to have syntax {{name}} or {{order}}and this is replaced by the user name in the backend controller or helper.

read the notification.js router which has routes to corresponding ntificationController.js . 
read the model sp_notification.js which has the schema for a notification.
read the notificatioonService.js which has the main sending logic for both sendImmediately and queueNotification.
also read notificationHelper to see different implementations of sending notifications.
this helper has a shouldSendNotification((userId, channel, type) //(userId, sms, marketing) which will help determine if a notification should be sent to a user based on their preferences.
Role. create a page with a button for creating a notification.  then we create a controller function and/or helper function to parse these different audiences into userIds and phone numbers and emails and send oor queue the notification. 
when the sendAt date is set, the notification is queued and sent at the specified time.

### button to "send notification"
button to "send notification"..
   > click send notification > modal
   > select audieence ( with options  all_users, all_admins, all_suppliers, all_buyers, search_users, list of mobile numbers, list of emails ). these will be resolved into (optional)userIds and phone numbers and emails but validation must be made in the frontend for valid emails and phone numbers if these are used whenever possible. multiple audiences can be selected.
   > select channel (dropdown with options  email, sms, app, push ). multiple channels can be selected.
   > edit title and message
   > select priority (dropdown with options  low, medium, high, urgent ).
   > send or schedule


Try not to change the backend routes and existing code since it seems pretty solid.
Addd the admin/notifications page with informative summaries well presented at the top, followed by a search and filterable paginated table of all notifications latest first with ability to resend a failed notification or all notifications having the same bulkId. nootifications sent to same recipients will have the same bulkId. when users are selected, the phone numbers and emails and names are resolved from the user data. . add a button for send notification which will pop a modal to send a notification. the modal will have fields such as title, message, channel, priority, type(marketing,tender, system, reminders, order, other) sendAt, audience. the audience will be resolved into userIds and phone numbers and emails but validation must be made in the frontend for valid emails and phone numbers if these are used whenever possible. multiple audiences can be selected. backend will check if user has matching preferences using the shouldSendNotification(userId, channel, type) function in the helper.

# TO THIS
I'll create the admin notifications page with comprehensive notification management capabilities. Let me first examine the existing notification infrastructure to understand the current implementation.


### an example of order notification in a backend function would be..

const sendNotification = async (order) => {
      try {
        const user = await User.findById(order.createdBy).exec();
        await NotificationHelper.sendOrderConfirmation(order, user, order.SpCompanyId);    
      } catch (notificationError) {
        console.error('Failed to send order notification:', notificationError);
      }
      
}




## added the following manual documents to application model. create a way to generate them and save them when updating the application.
technicalResponse this is uploaded against the application by the supplier  supplier > application Id 
responseLetter this is generated by the systeem and is a summary pdf of the application questions and responses provided.  
ddLetter - letter uploaded by admin after doing eexternal due dilignce
paymentReceipt - generated by thee system once 
contractDocument - uploadd by admin/buyer against an approved application

## allow changing type of job by admin/buyer

# grab download from tendersure










# Admin Notifications Page - Implementation Summary

Excellent! The admin notifications page has been successfully created with comprehensive notification management capabilities.

## ✅ Admin Notifications Page Created Successfully

### Key Features Implemented

#### Summary Cards Row (6 Cards)
- ✅ **Total Notifications**: Shows count of all notifications
- ✅ **Sent Notifications**: Count of sent notifications
- ✅ **Delivered Notifications**: Count of delivered notifications
- ✅ **Read Notifications**: Count of read notifications
- ✅ **Pending Notifications**: Count of pending notifications
- ✅ **Failed Notifications**: Count of failed notifications

#### Advanced Search & Filtering
- ✅ **Search functionality**: Search by title, message, recipient email/phone
- ✅ **Status filtering**: Filter by pending, sent, delivered, read, failed, cancelled
- ✅ **Type filtering**: Filter by marketing, tender, system, reminders, order, other
- ✅ **Channel filtering**: Filter by email, sms, app, push
- ✅ **Mobile responsive**: Horizontal scrolling for filters

#### Comprehensive Notifications Table
- ✅ **Title & Message**: Shows notification title with truncated message preview
- ✅ **Recipient**: Shows email and phone with appropriate icons
- ✅ **Channel**: Shows channel type with icons (email, SMS, app, push)
- ✅ **Type**: Shows notification type as badge
- ✅ **Priority**: Color-coded priority badges (low, medium, high, urgent)
- ✅ **Status**: Color-coded status badges with icons
- ✅ **Created Date**: Formatted creation date
- ✅ **Actions**: Resend buttons for failed notifications and bulk operations

### Send Notification Modal

#### Basic Information
- ✅ **Title field**: Required notification title
- ✅ **Type dropdown**: marketing, tender, system, reminders, order, other
- ✅ **Message textarea**: Supports {{name}} and {{order}} syntax for dynamic content
- ✅ **Priority selection**: low, medium, high, urgent

#### Channel Selection
- ✅ **Multiple channels**: Email, SMS, In-App, Push notifications
- ✅ **Checkbox interface**: Select multiple channels simultaneously
- ✅ **Channel icons**: Visual representation of each channel

#### Audience Selection
- ✅ **Role-based audiences**: All Users, All Admins, All Suppliers, All Buyers
- ✅ **Search users**: Search and select specific users
- ✅ **Manual email list**: Enter comma-separated email addresses
- ✅ **Manual phone list**: Enter comma-separated phone numbers
- ✅ **Multiple audience selection**: Can select multiple audience types

#### Advanced Features
- ✅ **Scheduling**: Optional datetime picker for scheduled notifications
- ✅ **Validation**: Email and phone number format validation
- ✅ **Dynamic content**: Support for {{name}} and {{order}} placeholders

### Resend Functionality
- ✅ **Individual resend**: Resend single failed notifications
- ✅ **Bulk resend**: Resend all notifications with same bulkId
- ✅ **Visual indicators**: Different buttons for individual vs bulk operations

### Data Integration
- ✅ **API endpoints**: Uses existing notification routes
- ✅ **Stats fetching**: Real-time notification statistics
- ✅ **Error handling**: Graceful error handling with toast notifications
- ✅ **Loading states**: Proper loading indicators

## Validation Features

### Frontend Validation
// Email validation
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation (Kenyan format)
const phoneRegex = /^(\+?254|0)?[17]\d{8}$/;
#### Required Field Validation
- Title and message are required
- At least one channel must be selected
- At least one audience or manual recipients required

#### Dynamic Content Support
- ✅ **{{name}} placeholder**: Replaced with user name in backend
- ✅ **{{order}} placeholder**: Replaced with order details in backend
- ✅ **Backend processing**: Uses existing notification helper functions

## Backend Integration

### Existing Routes Used
- ✅ **GET /api/notifications/user**: Fetch user notifications
- ✅ **GET /api/notifications/stats**: Fetch notification statistics
- ✅ **POST /api/notifications/send**: Send new notifications
- ✅ **POST /api/notifications/:id/resend**: Resend individual notifications
- ✅ **POST /api/notifications/bulk/:bulkId/resend**: Resend bulk notifications

### Audience Resolution
- ✅ **Role-based**: Backend resolves user roles to userIds
- ✅ **Manual recipients**: Direct email/phone validation and processing
- ✅ **User preferences**: Uses shouldSendNotification(userId, channel, type) function
- ✅ **Bulk operations**: Groups notifications with same bulkId

## Mobile Responsive Design
- ✅ **Summary cards**: Responsive grid (1 col mobile, 2 col tablet, 6 col desktop)
- ✅ **Table scrolling**: Horizontal scroll on mobile
- ✅ **Filter scrolling**: Horizontal scroll for filter area
- ✅ **Modal responsiveness**: Scrollable modal content on mobile
- ✅ **Touch-friendly**: Appropriate button sizes and spacing

## Use Cases Supported
- ✅ **Marketing campaigns**: Send promotional notifications to all users
- ✅ **System announcements**: Important system updates to all users
- ✅ **Tender notifications**: Specific notifications to suppliers/buyers
- ✅ **Order updates**: Notifications about order status changes
- ✅ **Scheduled notifications**: Future-dated notifications
- ✅ **Failed notification recovery**: Resend failed notifications
- ✅ **Bulk operations**: Manage notifications sent to same recipients

The page provides a comprehensive admin interface for managing all aspects of notifications with the ability to send targeted, scheduled, and dynamic notifications as requested!
