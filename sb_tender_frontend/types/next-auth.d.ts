import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      firstName: string;
      lastName: string;
      type: 'admin' | 'buyer' | 'supplier';
      image?: string | null;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    firstName: string;
    lastName: string;
    type: 'admin' | 'buyer' | 'supplier';
    image?: string | null;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    type: 'admin' | 'buyer' | 'supplier';
    firstName: string;
    lastName: string;
  }
}
