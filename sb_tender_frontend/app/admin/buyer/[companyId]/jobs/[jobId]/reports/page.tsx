"use client";

import { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Download,
  FileSpreadsheet,
  BarChart3,
  Users,
  Building,
  TrendingUp,
  CheckCircle,
  Clock,
  XCircle,
  Search,
  Eye
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface JobSummary {
  _id: string;
  title: string;
  description: string;
  status: string;
  categoryPrice: number;
  companyName: string;
  contract: string;
  createdAt: string;
  createdBy: string;
  ends: string;
  location: string;
  spCompanyId: {
    _id: string;
    name: string;
  };
  starts: string;
  updatedAt: string;
  __v: number;
}

interface CategoryWithApplications {
  _id: string;
  title: string;
  description: string;
  type: string;
  status: string;
  price: number;
  starts: string;
  ends: string;
  location: string;
  spJobId: string;
  spCompanyId: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  // Calculated fields for applications
  totalApplications: number;
  submittedApplications: number;
  averageScore: number;
  highestScore: number;
  topSupplier?: string;
}

export default function BuyerJobReportsPage() {
  const params = useParams();
  const router = useRouter();
  const { companyId, jobId } = params;

  const [jobSummary, setJobSummary] = useState<JobSummary | null>(null);
  const [categories, setCategories] = useState<CategoryWithApplications[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);

  // Filters for categories table
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // Pagination for categories table
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Prevent multiple simultaneous calls
  const [isFetching, setIsFetching] = useState(false);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    console.log('=== useEffect triggered ===');
    console.log('companyId:', companyId);
    console.log('jobId:', jobId);
    console.log('isFetching:', isFetching);

    if (companyId && jobId && !isFetching) {
      console.log('Starting fetch operations...');
      setIsFetching(true);

      Promise.all([
        fetchJobSummary(),
        fetchJobCategories()
      ]).finally(() => {
        setIsFetching(false);
        console.log('=== All fetch operations completed ===');
      });
    } else {
      console.log('Skipping fetch - missing params or already fetching');
    }
  }, [companyId, jobId]);


  // Memoize the fetch functions to prevent recreation on every render
  const fetchJobSummary = useCallback(async () => {
    try {
      console.log('=== STARTING fetchJobSummary ===');
      setLoading(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        console.log('No auth token found in fetchJobSummary');
        toast.error('Authentication token not found');
        return;
      }

      // Try to fetch job details, but fall back to mock data if it fails
      console.log('Fetching job details for jobId:', jobId);
      let jobData = null;
      try {
        const response = await fetch(
          `${BACKEND_API_URL}/api/admin/buyer/${companyId}/jobs/${jobId}`,
          {
            headers: {
              'Authorization': `Bearer ${cookieToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Job API response status:', response.status);
        if (response.ok) {
          const data = await response.json();
          // Handle both direct job data and wrapped response
          jobData = data.data || data;
          console.log('Successfully fetched job data:', jobData);
          setJobSummary(jobData);
        } else {
          console.log('Job API call failed with status:', response.status);
        }
      } catch (apiError) {
        console.log('Job API error, using mock data:', apiError);
      }

      console.log('=== fetchJobSummary COMPLETED ===');
    } catch (error) {
      console.error('Error fetching job summary:', error);
      toast.error('Failed to fetch job summary');
    } finally {
      setLoading(false);
      console.log('=== fetchJobSummary FINALLY block ===');
    }
  }, [companyId, jobId, BACKEND_API_URL]);

  const fetchJobCategories = useCallback(async () => {
    try {
      console.log('=== STARTING fetchJobCategories ===');
      setCategoriesLoading(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        console.log('No auth token found in fetchJobCategories');
        toast.error('Authentication token not found');
        return;
      }

      console.log('Fetching job categories for job:', jobId);
      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/buyer/${companyId}/jobs/${jobId}/categories`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Categories API response status:', response.status);
      if (response.ok) {
        const categoriesData = await response.json();
        console.log('Successfully fetched categories:', categoriesData);

        // Fetch all applications for this job in one API call (more efficient than per-category calls)
        // Each application has spJobId so we can filter by spJobCategoryId on frontend
        // Applications already have totalScore, systemScore, complianceScore calculated
        console.log(`Fetching all applications for job: ${jobId}`);

        let allApplications = [];
        try {
          const applicationsResponse = await fetch(
            `${BACKEND_API_URL}/api/admin/buyer/${companyId}/jobs/${jobId}/applications`,
            {
              headers: {
                'Authorization': `Bearer ${cookieToken}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (applicationsResponse.ok) {
            const rawData = await applicationsResponse.json();

            allApplications = rawData.data.applications;
            console.log(`Successfully fetched ${allApplications.length} applications for job`);
          } else {
            console.log(`Failed to fetch applications for job ${jobId}, status: ${applicationsResponse.status}`);
          }
        } catch (error) {
          console.error(`Error fetching applications for job ${jobId}:`, error);
        }

        // Transform categories data to include application statistics
        // Filter applications by category and calculate stats from pre-calculated scores
        const categoriesWithStats = categoriesData.map((category: any) => {
          // fetch applications for each category via api/buyer/:companyId/categories/:categoryId/applications.
          // then we build the categorieswithstats using the applications data
          //the rest of the stats can be set by filtering or manipulating the applications data
          //we have job data, job categories, and applications

          // Filter applications for this specific category
          const categoryApplications = allApplications?.filter((app: any) =>
            app.spJobCategoryId === category._id || app.spJobCategoryId?._id === category._id
          );

          console.log(`Category ${category.title}: ${categoryApplications.length} applications`);

          // Calculate statistics from filtered applications
          const totalApplications = categoryApplications.length;

          // Filter applications by status for submitted count
          const submittedApplications = categoryApplications.filter((app: any) =>
            ['submitted', 'under_review', 'approved', 'rejected'].includes(app.status)
          ).length;

          // Use pre-calculated scores from applications (no need to recalculate from fields)
          const applicationsWithScores = categoryApplications.filter((app: any) =>
            app.totalScore !== undefined && app.totalScore > 0
          );

          let averageScore = 0;
          let highestScore = 0;
          let topSupplier = null;

          if (applicationsWithScores.length > 0) {
            // Use pre-calculated totalScore (systemScore + complianceScore)
            const totalScores = applicationsWithScores.map((app: any) => app.totalScore);
            averageScore = totalScores.reduce((sum: number, score: number) => sum + score, 0) / totalScores.length;

            // Find highest score
            highestScore = Math.max(...totalScores);

            // Find top supplier (application with highest total score)
            const topApplication = applicationsWithScores.find((app: any) =>
              app.totalScore === highestScore
            );

            if (topApplication && topApplication.spSupplierCompanyId) {
              // Handle both populated and non-populated supplier company data
              topSupplier = topApplication.spSupplierCompanyId.name ||
                           topApplication.spSupplierCompanyId.companyName ||
                           topApplication.spSupplierCompanyId;
            }
          }

          return {
            ...category,
            totalApplications,
            submittedApplications,
            averageScore: Math.round(averageScore * 10) / 10, // Round to 1 decimal
            highestScore,
            topSupplier
          };
        });

        setCategories(categoriesWithStats);
        toast.success('Categories loaded successfully');
        console.log('=== fetchJobCategories COMPLETED ===');
      } else {
        console.log('Categories API call failed with status:', response.status);
        toast.error('Failed to fetch job categories');

        // Fall back to mock data
        const mockCategories: CategoryWithApplications[] = [
          {
            _id: '1',
            title: 'Technical Capability',
            description: 'Assessment of technical skills and experience',
            type: 'supplier_prequalification',
            status: 'open',
            price: 1000,
            starts: '2024-01-01',
            ends: '2024-12-31',
            location: 'Remote',
            spJobId: jobId as string,
            spCompanyId: companyId as string,
            createdBy: 'admin',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            totalApplications: 8,
            submittedApplications: 6,
            averageScore: 78.5,
            highestScore: 95,
            topSupplier: 'TechCorp Solutions Ltd'
          }
        ];
        setCategories(mockCategories);
      }
    } catch (error) {
      console.error('Error fetching job categories:', error);
      toast.error('Failed to fetch job categories');

      // Fall back to mock data on error
      setCategories([]);
    } finally {
      setCategoriesLoading(false);
      console.log('=== fetchJobCategories FINALLY block ===');
    }
  }, [companyId, jobId, BACKEND_API_URL]); // Add dependencies

  // FIXED: Simplified useEffect with proper dependencies
  useEffect(() => {
    console.log('=== useEffect triggered ===');
    console.log('companyId:', companyId);
    console.log('jobId:', jobId);

    if (companyId && jobId) {
      console.log('Starting fetch operations...');
      
      // Fetch both in parallel
      Promise.all([
        fetchJobSummary(),
        fetchJobCategories()
      ]).then(() => {
        console.log('=== All fetch operations completed ===');
      });
    } else {
      console.log('Skipping fetch - missing params');
    }
  }, [companyId, jobId, fetchJobSummary, fetchJobCategories]); // Include the memoized functions


  
  const downloadJobReport = async (reportType: string) => {
    try {
      setDownloading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const queryParams = new URLSearchParams({
        reportType
      });

      const response = await fetch(
        `${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${jobId}/applications/download/excel?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `job_${reportType}_report.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Report download started');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Failed to download report');
    } finally {
      setDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft', icon: Clock },
      open: { color: 'bg-green-100 text-green-800', label: 'Open', icon: CheckCircle },
      closed: { color: 'bg-red-100 text-red-800', label: 'Closed', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  // Pagination logic for categories
  const getFilteredCategories = () => {
    return categories.filter(category => {
      const matchesSearch = category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          category.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || category.status === statusFilter;
      const matchesType = typeFilter === 'all' || category.type === typeFilter;
      return matchesSearch && matchesStatus && matchesType;
    });
  };

  const getPaginatedCategories = () => {
    const filteredCategories = getFilteredCategories();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCategories.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const filteredCategories = getFilteredCategories();
    return Math.ceil(filteredCategories.length / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset pagination when filters change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading job reports...</div>
      </div>
    );
  }

  if (!jobSummary) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Job not found</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Job Report: {jobSummary.title}</h1>
            <p className="text-gray-600">Job Reports & Analytics</p>
          </div>
        </div>
        <div className="flex gap-2">
          {getStatusBadge(jobSummary.status)}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={downloading}>
                <Download className="h-4 w-4 mr-2" />
                Download Reports
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => downloadJobReport('summary')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Summary Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadJobReport('system')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                System Scores Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadJobReport('compliance')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Compliance Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadJobReport('final')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Final Scores Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadJobReport('qualified')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Qualified Suppliers Report
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-gray-500">Total Categories</p>
              <p className="text-2xl font-bold">{categories.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2">
            <Users className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-gray-500">Total Applications</p>
              <p className="text-2xl font-bold">{categories.reduce((sum, cat) => sum + cat.totalApplications, 0)}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2">
            <Building className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-gray-500">Company</p>
              <p className="text-2xl font-bold">{jobSummary.spCompanyId.name}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-8 w-8 text-orange-500" />
            <div>
              <p className="text-sm text-gray-500">Avg Total Score</p>
              <p className="text-2xl font-bold">{categories.length > 0 ? (categories.reduce((sum, cat) => sum + cat.averageScore, 0) / categories.length).toFixed(1) : '0.0'}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Application Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Job Details</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Contract</span>
              <span className="font-medium">{jobSummary.contract}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Location</span>
              <span className="font-medium">{jobSummary.location}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Status</span>
              <span className="font-medium capitalize">{jobSummary.status}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Category Price</span>
              <span className="font-medium">KES {jobSummary.categoryPrice}</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Timeline</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Start Date</span>
              <span className="font-medium">{new Date(jobSummary.starts).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">End Date</span>
              <span className="font-medium">{new Date(jobSummary.ends).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Created</span>
              <span className="font-medium">{new Date(jobSummary.createdAt).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Last Updated</span>
              <span className="font-medium">{new Date(jobSummary.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Job Categories & Applications Table */}
      <Card>
        <CardHeader>
          <CardTitle>Job Categories & Participants</CardTitle>
          <CardDescription>Detailed view of all categories in this job with application statistics</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4 items-center mb-6">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-64"
              />
            </div>

            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={handleTypeFilterChange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="supplier_registration">Registration</SelectItem>
                <SelectItem value="supplier_prequalification">Prequalification</SelectItem>
                <SelectItem value="rfq">RFQ</SelectItem>
                <SelectItem value="tender">Tender</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Categories Table */}
          {categoriesLoading ? (
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30%]">Category</TableHead>
                    <TableHead className="w-[12%]">Type</TableHead>
                    <TableHead className="w-[10%]">Status</TableHead>
                    <TableHead className="w-[12%]">Applications</TableHead>
                    <TableHead className="w-[10%]">Avg Score</TableHead>
                    <TableHead className="w-[16%]">Top Supplier</TableHead>
                    <TableHead className="w-[10%]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(3)].map((_, index) => (
                    <TableRow key={index}>
                      <TableCell className="w-[30%] max-w-0">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-3 w-3/4" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[12%] max-w-0"><Skeleton className="h-4 w-full" /></TableCell>
                      <TableCell className="w-[10%] max-w-0"><Skeleton className="h-6 w-full" /></TableCell>
                      <TableCell className="w-[12%] max-w-0">
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-3 w-3/4" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[10%] max-w-0">
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-3 w-3/4" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[16%] max-w-0">
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-3 w-1/2" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[10%] max-w-0"><Skeleton className="h-8 w-full" /></TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30%]">Category</TableHead>
                    <TableHead className="w-[12%]">Type</TableHead>
                    <TableHead className="w-[10%]">Status</TableHead>
                    <TableHead className="w-[12%]">Applications</TableHead>
                    <TableHead className="w-[10%]">Avg Score</TableHead>
                    <TableHead className="w-[16%]">Top Supplier</TableHead>
                    <TableHead className="w-[10%]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {categories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No categories found for this job
                    </TableCell>
                  </TableRow>
                ) : (
                  getPaginatedCategories().map((category) => (
                      <TableRow key={category._id}>
                        <TableCell className="w-[30%] max-w-0">
                          <div className="space-y-1">
                            <div className="font-medium text-sm break-words">{category.title}</div>
                            <div className="text-xs text-muted-foreground break-words overflow-hidden" style={{display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical'}}>{category.description}</div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[12%] max-w-0">
                          <div className="text-sm capitalize break-words">
                            {category.type.replace('_', ' ')}
                          </div>
                        </TableCell>
                        <TableCell className="w-[10%] max-w-0">
                          {getStatusBadge(category.status)}
                        </TableCell>
                        <TableCell className="w-[12%] max-w-0">
                          <div className="text-sm text-center">
                            <div className="font-medium">{category.submittedApplications}/{category.totalApplications}</div>
                            <div className="text-xs text-muted-foreground">submitted</div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[10%] max-w-0">
                          <div className="text-sm text-center">
                            <div className="font-medium">{category.averageScore.toFixed(1)}</div>
                            <div className="text-xs text-muted-foreground">avg score</div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[16%] max-w-0">
                          <div className="text-sm">
                            {category.topSupplier ? (
                              <>
                                <div className="font-medium text-xs break-words overflow-hidden" style={{display: '-webkit-box', WebkitLineClamp: 1, WebkitBoxOrient: 'vertical'}}>{category.topSupplier}</div>
                                <div className="text-xs text-muted-foreground">{category.highestScore} pts</div>
                              </>
                            ) : (
                              <span className="text-xs text-muted-foreground">No applications</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="w-[10%] max-w-0">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/admin/buyer/${companyId}/jobs/${jobId}/categories/${category._id}/reports`)}
                            className="text-xs px-2 py-1 h-8"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
            </div>
          )}

          {/* Pagination */}
          {!categoriesLoading && getFilteredCategories().length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(getTotalPages(), currentPage + 1))}
                      className={currentPage === getTotalPages() ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="text-sm text-muted-foreground text-center mt-2">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredCategories().length)} to {Math.min(currentPage * itemsPerPage, getFilteredCategories().length)} of {getFilteredCategories().length} categories
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Applications by Category */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Applications by Category</h3>
        <div className="space-y-3">
          {categories.map((category) => (
            <div key={category._id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-medium">{category.title}</span>
              <div className="flex items-center gap-2">
                <span>{category.totalApplications} applications</span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => router.push(`/buyer/${companyId}/jobs/${jobId}/categories/${category._id}/reports`)}
                >
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Top Suppliers */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Top Suppliers by Category</h3>
        <div className="space-y-3">
          {categories.filter(cat => cat.topSupplier).map((category) => (
            <div key={category._id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <div>
                <span className="font-medium">{category.topSupplier}</span>
                <p className="text-sm text-gray-500">{category.title}</p>
              </div>
              <div className="text-right">
                <p className="font-bold text-green-600">{category.highestScore} pts</p>
                <p className="text-sm text-gray-500">Highest Score</p>
              </div>
            </div>
          ))}
          {categories.filter(cat => cat.topSupplier).length === 0 && (
            <p className="text-gray-500 text-center py-4">No suppliers with scores yet</p>
          )}
        </div>
      </Card>


    </div>
  );
}
