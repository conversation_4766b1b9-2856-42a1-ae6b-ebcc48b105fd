"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  FileSpreadsheet, 
  FileArchive, 
  MoreVertical,
  BarChart3
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface Application {
  _id: string;
  title: string;
  ref: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  systemScore: number;
  complianceScore: number;
  totalScore: number;
  submittedAt: string;
  reviewDate?: string;
  reviewStatus: 'pending' | 'approved' | 'rejected';
  spSupplierCompanyId: {
    _id: string;
    name: string;
    registrationNumber: string;
  };
  submittedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewer?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function AdminCategoryApplicationsPage() {
  const params = useParams();
  const router = useRouter();
  const { jobId, categoryId } = params;

  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false
  });

  // Filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchApplications();
  }, [categoryId, pagination.currentPage, statusFilter, reviewStatusFilter, searchQuery, sortBy, sortOrder]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const queryParams = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: '10',
        sortBy,
        sortOrder,
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(reviewStatusFilter !== 'all' && { reviewStatus: reviewStatusFilter }),
        ...(searchQuery && { search: searchQuery })
      });

      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch applications');
      }

      const data = await response.json();
      
      if (data.success) {
        setApplications(data.data.applications);
        setPagination(data.data.pagination);
      } else {
        toast.error(data.message || 'Failed to fetch applications');
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Failed to fetch applications');
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (reportType: string) => {
    try {
      setDownloading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      let endpoint = '';
      let filename = '';

      switch (reportType) {
        case 'excel':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/excel`;
          filename = 'applications_report.xlsx';
          break;
        case 'zip':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/zip?includeDocuments=true`;
          filename = 'applications.zip';
          break;
        case 'zip-no-docs':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/zip?includeDocuments=false`;
          filename = 'applications_data.zip';
          break;
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Download started');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Failed to download report');
    } finally {
      setDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      submitted: { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' },
      pending: { color: 'bg-orange-100 text-orange-800', label: 'Pending' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const handleViewApplication = (application: Application) => {
    router.push(`/admin/jobs/${jobId}/categories/${categoryId}/applications/${application._id}`);
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Category Applications</h1>
            <p className="text-muted-foreground">Manage applications for this category</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button 
            variant="outline"
            onClick={() => router.push(`/admin/jobs/${jobId}/categories/${categoryId}/reports`)}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            View Reports
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={downloading}>
                <Download className="h-4 w-4 mr-2" />
                Downloads
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => downloadReport('excel')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Excel Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP with Documents
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip-no-docs')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP Data Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={reviewStatusFilter} onValueChange={setReviewStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Review Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reviews</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">Newest First</SelectItem>
                <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                <SelectItem value="totalScore-desc">Highest Score</SelectItem>
                <SelectItem value="totalScore-asc">Lowest Score</SelectItem>
                <SelectItem value="submittedAt-desc">Recently Submitted</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Applications Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Supplier</TableHead>
              <TableHead>Application Ref</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Scores</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Review Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading applications...
                </TableCell>
              </TableRow>
            ) : applications.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No applications found
                </TableCell>
              </TableRow>
            ) : (
              applications.map((application) => (
                <TableRow key={application._id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{application.spSupplierCompanyId.name}</div>
                      <div className="text-sm text-muted-foreground">{application.spSupplierCompanyId.registrationNumber}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{application.ref}</TableCell>
                  <TableCell>
                    {getStatusBadge(application.status)}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span>System:</span>
                        <span className="font-medium">{application.systemScore || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Compliance:</span>
                        <span className="font-medium">{application.complianceScore || 0}</span>
                      </div>
                      <div className="flex justify-between border-t pt-1">
                        <span className="font-medium">Total:</span>
                        <span className="font-bold">{application.totalScore || 0}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {application.submittedAt ? 
                      new Date(application.submittedAt).toLocaleDateString() : 
                      'Not submitted'
                    }
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getStatusBadge(application.reviewStatus)}
                      {application.reviewer && (
                        <div className="text-xs text-muted-foreground">
                          by {application.reviewer.firstName} {application.reviewer.lastName}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleViewApplication(application)}>
                          <Eye className="h-4 w-4 mr-2" />
                          Review Application
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-between items-center p-4 border-t">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.currentPage - 1) * 10) + 1} to {Math.min(pagination.currentPage * 10, pagination.totalCount)} of {pagination.totalCount} applications
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={!pagination.hasPrev}
                onClick={() => handlePageChange(pagination.currentPage - 1)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={!pagination.hasNext}
                onClick={() => handlePageChange(pagination.currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
