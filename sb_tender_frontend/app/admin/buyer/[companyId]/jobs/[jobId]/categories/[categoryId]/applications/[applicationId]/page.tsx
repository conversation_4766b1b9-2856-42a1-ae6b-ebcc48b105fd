"use client";

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ArrowLeft, 
  Save, 
  Download, 
  FileText, 
  User, 
  Building, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface ApplicationField {
  name: string;
  label: string;
  type: string;
  value: any;
  score?: number;
  maxScore?: number;
  isScoreable: boolean;
  group: string;
  subGroup?: string;
  reviewerComment?: string;
  complianceScore?: number;
  documents?: Array<{
    fileName: string;
    fileType: string;
    fileSize: number;
    filePath: string;
    uploadedAt: string;
  }>;
}

interface Application {
  _id: string;
  title: string;
  ref: string;
  description: string;
  status: string;
  type: string;
  systemScore: number;
  complianceScore: number;
  totalScore: number;
  submittedAt: string;
  reviewStatus: string;
  reviewerNotes?: string;
  reviewDate?: string;
  fields: ApplicationField[];
  spSupplierCompanyId: {
    _id: string;
    name: string;
    registrationNumber: string;
    address: string;
    contactPerson: string;
    email: string;
    phone: string;
  };
  spBuyerCompanyId: {
    _id: string;
    name: string;
    address: string;
  };
  submittedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewer?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function AdminApplicationReviewPage() {
  const params = useParams();
  const router = useRouter();
  const { jobId, categoryId, applicationId } = params;

  const [application, setApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Review form state
  const [reviewerNotes, setReviewerNotes] = useState('');
  const [complianceScore, setComplianceScore] = useState<number>(0);
  const [reviewStatus, setReviewStatus] = useState<string>('under_review');
  const [fieldReviews, setFieldReviews] = useState<Record<string, { comment: string; complianceScore: number }>>({});

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchApplication();
  }, [applicationId]);

  const fetchApplication = async () => {
    try {
      setLoading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      // We need to get the supplier company ID first, or modify the backend to support this route
      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/applications/${applicationId}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch application');
      }

      const data = await response.json();
      
      if (data.success) {
        setApplication(data.data);
        setReviewerNotes(data.data.reviewerNotes || '');
        setComplianceScore(data.data.complianceScore || 0);
        setReviewStatus(data.data.reviewStatus || 'under_review');
        
        // Initialize field reviews
        const fieldReviewsInit: Record<string, { comment: string; complianceScore: number }> = {};
        data.data.fields.forEach((field: ApplicationField) => {
          fieldReviewsInit[field.name] = {
            comment: field.reviewerComment || '',
            complianceScore: field.complianceScore || 0
          };
        });
        setFieldReviews(fieldReviewsInit);
      } else {
        toast.error(data.message || 'Failed to fetch application');
      }
    } catch (error) {
      console.error('Error fetching application:', error);
      toast.error('Failed to fetch application');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    try {
      setSaving(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const reviewData = {
        reviewerNotes,
        complianceScore,
        reviewStatus,
        fieldReviews: Object.entries(fieldReviews).map(([fieldName, review]) => ({
          fieldName,
          comment: review.comment,
          complianceScore: review.complianceScore
        }))
      };

      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/suppliers/${application?.spSupplierCompanyId._id}/applications/${applicationId}/review`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(reviewData)
        }
      );

      if (!response.ok) {
        throw new Error('Failed to submit review');
      }

      const data = await response.json();
      
      if (data.success) {
        toast.success('Review submitted successfully');
        fetchApplication(); // Refresh the application data
      } else {
        toast.error(data.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setSaving(false);
    }
  };

  const handleFieldReviewChange = (fieldName: string, key: 'comment' | 'complianceScore', value: string | number) => {
    setFieldReviews(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        [key]: value
      }
    }));
  };

  const downloadDocument = async (fieldName: string, fileName: string) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/suppliers/${application?.spSupplierCompanyId._id}/applications/${applicationId}/fields/${fieldName}/documents/${fileName}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to download document');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft', icon: Clock },
      submitted: { color: 'bg-blue-100 text-blue-800', label: 'Submitted', icon: CheckCircle },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review', icon: AlertCircle },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected', icon: XCircle },
      pending: { color: 'bg-orange-100 text-orange-800', label: 'Pending', icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const IconComponent = config.icon;
    
    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading application...</div>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Application not found</div>
      </div>
    );
  }

  // Group fields by section
  const fieldsByGroup = application.fields.reduce((acc, field) => {
    if (!acc[field.group]) {
      acc[field.group] = [];
    }
    acc[field.group].push(field);
    return acc;
  }, {} as Record<string, ApplicationField[]>);

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{application.title}</h1>
            <p className="text-muted-foreground">Application Review - {application.ref}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {getStatusBadge(application.status)}
          <Button onClick={handleSubmitReview} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Review'}
          </Button>
        </div>
      </div>

      {/* Application Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Building className="h-5 w-5" />
              Supplier Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Company</p>
              <p className="font-medium">{application.spSupplierCompanyId.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Registration</p>
              <p>{application.spSupplierCompanyId.registrationNumber}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Contact Person</p>
              <p>{application.spSupplierCompanyId.contactPerson}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Email</p>
              <p className="text-sm">{application.spSupplierCompanyId.email}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Application Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Type</p>
              <p className="capitalize">{application.type.replace('_', ' ')}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Submitted</p>
              <p>{new Date(application.submittedAt).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Submitted By</p>
              <p>{application.submittedBy.firstName} {application.submittedBy.lastName}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5" />
              Scoring Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">System Score</span>
              <span className="font-bold text-blue-600">{application.systemScore || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">Compliance Score</span>
              <span className="font-bold text-green-600">{application.complianceScore || 0}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="font-medium">Total Score</span>
              <span className="font-bold text-lg">{application.totalScore || 0}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Review Form and Application Content */}
      <Tabs defaultValue="review" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="review">Review & Scoring</TabsTrigger>
          <TabsTrigger value="application">Application Content</TabsTrigger>
        </TabsList>

        <TabsContent value="review" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Overall Review</CardTitle>
              <CardDescription>Provide your assessment and compliance scoring</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Review Status</label>
                  <Select value={reviewStatus} onValueChange={setReviewStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="under_review">Under Review</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Compliance Score</label>
                  <Input
                    type="number"
                    value={complianceScore}
                    onChange={(e) => setComplianceScore(Number(e.target.value))}
                    placeholder="Enter compliance score"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Review Notes</label>
                <Textarea
                  value={reviewerNotes}
                  onChange={(e) => setReviewerNotes(e.target.value)}
                  placeholder="Enter your review comments..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="application" className="space-y-6">
          {Object.entries(fieldsByGroup).map(([groupName, fields]) => (
            <Card key={groupName}>
              <CardHeader>
                <CardTitle className="capitalize">
                  {groupName.replace('_', ' ')} Section
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {fields.map((field) => (
                  <div key={field.name} className="border-b pb-4 last:border-b-0">
                    <div className="flex justify-between items-start mb-3">
                      <label className="text-sm font-medium">{field.label}</label>
                      {field.isScoreable && (
                        <Badge variant="outline">
                          Score: {field.score || 0}/{field.maxScore || 0}
                        </Badge>
                      )}
                    </div>
                    
                    {/* Field Value */}
                    <div className="mb-3">
                      {field.type === 'file' ? (
                        <div>
                          {field.documents && field.documents.length > 0 ? (
                            field.documents.map((doc, index) => (
                              <div key={index} className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                                <FileText className="h-4 w-4" />
                                <span className="flex-1 text-sm">{doc.fileName}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => downloadDocument(field.name, doc.fileName)}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              </div>
                            ))
                          ) : (
                            <span className="text-muted-foreground text-sm">No file uploaded</span>
                          )}
                        </div>
                      ) : (
                        <div className="p-3 bg-muted rounded-lg text-sm">
                          {field.value || 'No value provided'}
                        </div>
                      )}
                    </div>

                    {/* Field Review */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-muted-foreground mb-1">Review Comment</label>
                        <Textarea
                          value={fieldReviews[field.name]?.comment || ''}
                          onChange={(e) => handleFieldReviewChange(field.name, 'comment', e.target.value)}
                          placeholder="Add review comment..."
                          rows={2}
                        />
                      </div>
                      {field.isScoreable && (
                        <div>
                          <label className="block text-xs font-medium text-muted-foreground mb-1">Compliance Score</label>
                          <Input
                            type="number"
                            value={fieldReviews[field.name]?.complianceScore || 0}
                            onChange={(e) => handleFieldReviewChange(field.name, 'complianceScore', Number(e.target.value))}
                            placeholder="0"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
