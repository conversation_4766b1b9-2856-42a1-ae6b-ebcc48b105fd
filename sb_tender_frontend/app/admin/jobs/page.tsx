"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Combine, Pen, Trash2, } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"

export type Job = {
    _id: string
    title: string
    description: string
    status: "draft" | "open" | "closed"
    categoryPrice: number
    contract: string
    location: string
    starts: string
    ends: string
    createdBy: string
    spCompanyId: {
      _id: string;
      name: string;
    };
    createdAt: string
    updatedAt: string
  }
  
  
  

export default function JobsPage() {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})

  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);

  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchJobs();
    }
  }, [token]);


  const fetchJobs = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get('/api/admin/jobs', {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log('Admin jobs response:', data);
      // Extract jobs from the response data structure
      if (data.success && data.data && data.data.jobs) {
        setJobs(data.data.jobs);
      } else {
        console.error('Unexpected response structure:', data);
        setJobs([]);
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };


  const [editingJob, setEditingJob] = useState<Job | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Initialize react-hook-form
  const { register, handleSubmit, reset, setValue } = useForm<Job>();

  // Function to open the dialog and set the job data
  const handleEditClick = (job: Job) => {
    setEditingJob(job);
    setIsEditDialogOpen(true);
    // Set form default values
    setValue('title', job.title);
    setValue('description', job.description);
    // Format dates for datetime-local input
    setValue('starts', new Date(job.starts).toISOString().slice(0, 16));
    setValue('ends', new Date(job.ends).toISOString().slice(0, 16));
    setValue('categoryPrice', job.categoryPrice);
    setValue('location', job.location);
    setValue('status', job.status);
    // Do not set _id or spCompanyId as they are not edited via the form
  };

  // Function to handle form submission for editing
  const onEditSubmit = async (formData: Job) => {
    if (!editingJob || !token) {
      toast.error("Cannot save changes. No job selected or token missing.");
      return;
    }

    try {
        const updatedJobData = {
            ...formData,
            starts: new Date(formData.starts).toISOString(),
            ends: new Date(formData.ends).toISOString(),
            categoryPrice: parseFloat(formData.categoryPrice.toString()),
            spCompanyId: editingJob.spCompanyId,
            createdBy: editingJob.createdBy,
             _id: editingJob._id,
             createdAt: editingJob.createdAt,
             updatedAt: new Date().toISOString(), // Update updatedAt on client side or rely on backend
        };

      const { data } = await axios.put(`/api/admin/jobs/${editingJob._id}`, updatedJobData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log("Job updated successfully:", data);
      toast.success("Job updated successfully!");
      setIsEditDialogOpen(false); 
      reset();
      fetchJobs(); 
    } catch (error) {
      console.error("Failed to update job:", error);
      toast.error('Failed to update job');
    }
  };




const columns: ColumnDef<Job>[] = [
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => <div>{row.getValue("title")}</div>,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue("status")}</div>
      ),
      size: 80,
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => <div>{row.getValue("type")}</div>,
    },
    {
      accessorKey: "location",
      header: "Location",
      cell: ({ row }) => <div className="max-w-[120px] truncate">{row.getValue("location")}</div>,
    },
    {
      accessorKey: "categoryPrice",
      header: () => <div className="text-right">Price</div>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("categoryPrice"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "KES",
        }).format(amount)

        return <div className="text-right font-medium max-w-[100px] truncate">{formatted}</div>
      }
    },
    {
      accessorKey: "starts",
      header: "Open Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("starts"))
        const formatted = date.toLocaleDateString()
        return <div className="max-w-[100px] truncate">{formatted}</div>
      },

    },
    {
      accessorKey: "ends",
      header: "Close Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("ends"))
        const formatted = date.toLocaleDateString()
        return <div className="max-w-[100px] truncate">{formatted}</div>
      },
    },
    {
        id: "actions",
        enableHiding: false,
        cell: ({ row }) => {
          const job = row.original;

          return (
            <div className="flex gap-">
             
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/admin/jobs/${job._id}/categories`}
              >
                  Categories
              </Button>
              

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.href = `/admin/buyer/${job.spCompanyId._id}/jobs/${job._id}/reports`}
                >
                   Report
                </Button>
              
             
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleEditClick(job)}
              >
                Edit
              </Button>
            </div>
          );
        },
      }
  ]


    const table = useReactTable({
        data: jobs,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        state: {
          sorting,
          columnFilters,
          columnVisibility,
        },
      })

  

  return (
    <div className="p-6 space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">All Jobs</h1>
          <p className="text-muted-foreground">Manage and monitor job postings</p>
        </div>
      </div>

      {/* Jobs Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Jobs</CardTitle>
          <CardDescription>Manage and monitor job postings</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <Input
              placeholder="Filter titles..."
              value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn("title")?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-auto">
                  Columns <ChevronDown />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Table */}
          <div className="border rounded-lg overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              // Skeleton rows while loading
              Array.from({ length: 6 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell>
                    <Skeleton className="h-4 w-[200px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[80px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[120px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-[80px]" />
                      <Skeleton className="h-8 w-[60px]" />
                      <Skeleton className="h-8 w-[60px]" />
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      <div className="text-wrap">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-end space-x-2 py-4">
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit {editingJob?.title}</DialogTitle>
              <DialogDescription>
                Update the job details below.
              </DialogDescription>
            </DialogHeader>
            {/* Wrap form content in a form tag and use handleSubmit */}
            <form onSubmit={handleSubmit(onEditSubmit)}>
                <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="title" className="text-right">
                          Title
                          </Label>
                          <Input
                          id="title"
                          type="text"
                          className="col-span-3"
                          {...register('title', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="description" className="text-right">
                          Description
                          </Label>
                          <Textarea
                          id="description"
                          className="col-span-3"
                          {...register('description', { required: true })} // Register textarea
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="starts" className="text-right">
                          Starts
                          </Label>
                          <Input
                          id="starts"
                          type="datetime-local"
                          className="col-span-3"
                          {...register('starts', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="ends" className="text-right">
                          Closes
                          </Label>
                          <Input
                          id="ends"
                          type="datetime-local"
                          className="col-span-3"
                          {...register('ends', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="categoryPrice" className="text-right">
                          Category price
                          </Label>
                          <Input
                          id="categoryPrice"
                          type="number"
                          className="col-span-3"
                           {...register('categoryPrice', { required: true, valueAsNumber: true })} // Register input, treat as number
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="location" className="text-right">
                          Location
                          </Label>
                          <Input
                          id="location"
                          type="text"
                          className="col-span-3"
                          {...register('location', { required: true })} // Register input
                          />
                      </div>

                      {/* Company ID - Display only, not editable */}
                       <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="company" className="text-right">
                          Company
                          </Label>
                          <Input
                          id="company"
                          type="text"
                          value={editingJob?.spCompanyId?.name || ''} // Display company name
                          className="col-span-3"
                          disabled // Keep disabled
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="status" className="text-right">
                          Status
                          </Label>
                           {/* Use Controller or manage state manually for shadcn-ui Select with react-hook-form */}
                           {/* A simple way is to manage the value manually and use setValue on change */}
                          <Select
                            value={editingJob?.status} // Control value from state or form state
                            onValueChange={(value) => {
                                setEditingJob(prev => prev ? ({ ...prev, status: value as Job['status'] }) : null);
                                setValue('status', value as Job['status']); // Update form state
                            }}
                          >
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="open">Open</SelectItem> {/* Use 'published' */}
                                <SelectItem value="closed">Closed</SelectItem>
                            </SelectContent>
                            </Select>
                             {/* Also register a hidden input or use Controller if needed for stricter form control */}
                             <input type="hidden" {...register('status', { required: true })} />
                      </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit">Save changes</Button>
                  </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
    </div>
  )
}