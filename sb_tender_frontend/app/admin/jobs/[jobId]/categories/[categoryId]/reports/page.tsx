"use client";

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ArrowLeft, 
  Download, 
  FileSpreadsheet, 
  FileArchive, 
  BarChart3, 
  Users, 
  TrendingUp, 
  Award,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  MoreVertical,
  Eye
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface CategoryReportData {
  category: {
    _id: string;
    title: string;
    description: string;
    type: string;
    status: string;
  };
  job: {
    _id: string;
    title: string;
    status: string;
  };
  statistics: {
    totalApplications: number;
    submittedApplications: number;
    draftApplications: number;
    underReviewApplications: number;
    approvedApplications: number;
    rejectedApplications: number;
    averageSystemScore: number;
    averageComplianceScore: number;
    averageTotalScore: number;
    highestScore: number;
    lowestScore: number;
    uniqueSuppliers: number;
  };
  topPerformers: Array<{
    supplierId: string;
    supplierName: string;
    totalScore: number;
    systemScore: number;
    complianceScore: number;
    status: string;
  }>;
}

interface Application {
  _id: string;
  ref: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  systemScore: number;
  complianceScore: number;
  totalScore: number;
  submittedAt: string;
  reviewStatus: 'pending' | 'approved' | 'rejected';
  reviewerNotes?: string;
  spSupplierCompanyId: {
    _id: string;
    name: string;
    registrationNumber: string;
  };
  submittedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewer?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function AdminCategoryReportsPage() {
  const params = useParams();
  const router = useRouter();
  const { jobId, categoryId } = params;

  const [reportData, setReportData] = useState<CategoryReportData | null>(null);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);

  // Filters for applications table
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState('all');

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchReportData();
  }, [jobId, categoryId]);

  const fetchReportData = async () => {
    try {
      setLoading(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      console.log('Fetching real data for job:', jobId, 'category:', categoryId);

      // Fetch real data from the API
      const response = await fetch(
        `/api/admin/jobs/${jobId}/categories/${categoryId}/applications?limit=100`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.status}`);
      }

      const apiData = await response.json();
      console.log('API Response:', apiData);

      if (!apiData.success) {
        throw new Error(apiData.message || 'Failed to fetch report data');
      }

      const { job, category, statistics, topPerformers, applications } = apiData.data;

      // Transform API data to match our interface
      const transformedData: CategoryReportData = {
        category: {
          _id: category._id,
          title: category.title,
          description: category.description || '',
          type: category.type,
          status: category.status
        },
        job: {
          _id: job._id,
          title: job.title,
          status: job.status
        },
        statistics: {
          totalApplications: statistics.totalApplications || 0,
          submittedApplications: statistics.submittedApplications || 0,
          draftApplications: statistics.draftApplications || 0,
          underReviewApplications: statistics.underReviewApplications || 0,
          approvedApplications: statistics.approvedApplications || 0,
          rejectedApplications: statistics.rejectedApplications || 0,
          averageSystemScore: statistics.averageSystemScore || 0,
          averageComplianceScore: statistics.averageComplianceScore || 0,
          averageTotalScore: statistics.averageTotalScore || 0,
          highestScore: statistics.highestScore || 0,
          lowestScore: statistics.lowestScore || 0,
          uniqueSuppliers: statistics.uniqueSuppliers || 0
        },
        topPerformers: topPerformers || []
      };

      console.log('Transformed data:', transformedData);
      setReportData(transformedData);

      // Transform applications data
      const transformedApplications: Application[] = applications.map((app: any) => {
        // Map review status to valid values
        let reviewStatus: 'pending' | 'approved' | 'rejected' = 'pending';
        if (app.reviewStatus === 'approved' || app.status === 'approved') {
          reviewStatus = 'approved';
        } else if (app.reviewStatus === 'rejected' || app.status === 'rejected') {
          reviewStatus = 'rejected';
        }

        return {
          _id: app._id,
          ref: app.ref || `APP-${app._id.slice(-6)}`,
          status: app.status,
          systemScore: app.systemScore || 0,
          complianceScore: app.complianceScore || 0,
          totalScore: app.totalScore || 0,
          submittedAt: app.submittedAt || app.createdAt,
          reviewStatus,
          reviewerNotes: app.reviewerNotes,
          spSupplierCompanyId: {
            _id: app.spSupplierCompanyId._id,
            name: app.spSupplierCompanyId.name,
            registrationNumber: app.spSupplierCompanyId.registrationNumber || 'N/A'
          },
          submittedBy: app.submittedBy || app.createdBy || {
            _id: 'unknown',
            firstName: 'Unknown',
            lastName: 'User',
            email: '<EMAIL>'
          },
          reviewer: app.reviewer || app.updatedBy
        };
      });

      console.log('Transformed applications:', transformedApplications);
      setApplications(transformedApplications);

      toast.success('Report data loaded successfully');
    } catch (error: any) {
      console.error('Error fetching report data:', error);
      toast.error(error.message || 'Failed to fetch report data');

      // Set empty data on error
      setReportData({
        category: {
          _id: categoryId as string,
          title: 'Category Not Found',
          description: '',
          type: 'unknown',
          status: 'draft'
        },
        job: {
          _id: jobId as string,
          title: 'Job Not Found',
          status: 'draft'
        },
        statistics: {
          totalApplications: 0,
          submittedApplications: 0,
          draftApplications: 0,
          underReviewApplications: 0,
          approvedApplications: 0,
          rejectedApplications: 0,
          averageSystemScore: 0,
          averageComplianceScore: 0,
          averageTotalScore: 0,
          highestScore: 0,
          lowestScore: 0,
          uniqueSuppliers: 0
        },
        topPerformers: []
      });
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };
 
  const downloadReport = async (reportType: string) => {
    try {
      setDownloading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const queryParams = new URLSearchParams({
        includeScores: 'true',
        includeSystemScores: 'true',
        includeComplianceScores: 'true'
      });

      let endpoint = '';
      let filename = '';

      switch (reportType) {
        case 'excel':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/excel?${queryParams}`;
          filename = 'category_applications_report.xlsx';
          break;
        case 'zip':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/zip?includeDocuments=true`;
          filename = 'category_applications.zip';
          break;
        case 'zip-no-docs':
          endpoint = `${BACKEND_API_URL}/api/admin/categories/${categoryId}/applications/download/zip?includeDocuments=false`;
          filename = 'category_applications_data.zip';
          break;
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Download started');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Failed to download report');
    } finally {
      setDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft', icon: Clock },
      open: { color: 'bg-green-100 text-green-800', label: 'Open', icon: CheckCircle },
      closed: { color: 'bg-red-100 text-red-800', label: 'Closed', icon: XCircle },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review', icon: AlertCircle },
      submitted: { color: 'bg-blue-100 text-blue-800', label: 'Submitted', icon: CheckCircle },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected', icon: XCircle },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-20 h-10 bg-muted rounded animate-pulse"></div>
            <div>
              <div className="w-64 h-8 bg-muted rounded animate-pulse mb-2"></div>
              <div className="w-48 h-4 bg-muted rounded animate-pulse"></div>
            </div>
          </div>
          <div className="w-32 h-10 bg-muted rounded animate-pulse"></div>
        </div>

        <div className="w-full h-20 bg-muted rounded animate-pulse"></div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="w-full h-24 bg-muted rounded animate-pulse"></div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="w-full h-64 bg-muted rounded animate-pulse"></div>
          <div className="w-full h-64 bg-muted rounded animate-pulse"></div>
        </div>

        <div className="w-full h-48 bg-muted rounded animate-pulse"></div>

        <div className="text-center py-4 text-muted-foreground">
          Loading category reports and applications...
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Category not found</div>
      </div>
    );
  }

  const { category, job, statistics, topPerformers } = reportData;

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{category.title}</h1>
            <p className="text-muted-foreground">Category Reports & Analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {getStatusBadge(category.status)}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={downloading}>
                <Download className="h-4 w-4 mr-2" />
                Download Reports
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => downloadReport('excel')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Excel Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP with Documents
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip-no-docs')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP Data Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Job Context */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Job Context</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{job.title}</p>
              <p className="text-sm text-muted-foreground">Job ID: {job._id}</p>
            </div>
            {getStatusBadge(job.status)}
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.totalApplications}</p>
                <p className="text-sm text-muted-foreground">Total Applications</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.submittedApplications}</p>
                <p className="text-sm text-muted-foreground">Submitted</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-8 w-8 text-orange-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.averageTotalScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg Total Score</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Award className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.approvedApplications}</p>
                <p className="text-sm text-muted-foreground">Approved</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Application Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Application Status</CardTitle>
            <CardDescription>Distribution of application statuses</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { label: 'Submitted', count: statistics.submittedApplications, color: 'bg-blue-500' },
              { label: 'Under Review', count: statistics.underReviewApplications, color: 'bg-yellow-500' },
              { label: 'Approved', count: statistics.approvedApplications, color: 'bg-green-500' },
              { label: 'Rejected', count: statistics.rejectedApplications, color: 'bg-red-500' },
              { label: 'Draft', count: statistics.draftApplications, color: 'bg-gray-500' }
            ].map((item) => (
              <div key={item.label} className="flex items-center justify-between">
                <span className="text-sm font-medium">{item.label}</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm">{item.count}</span>
                  <div className="w-20 bg-muted rounded-full h-2">
                    <div 
                      className={`${item.color} h-2 rounded-full`}
                      style={{ width: `${(item.count / statistics.totalApplications) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Score Statistics</CardTitle>
            <CardDescription>Performance metrics overview</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{statistics.averageSystemScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg System Score</p>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold text-green-600">{statistics.averageComplianceScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg Compliance Score</p>
              </div>
            </div>
            <Separator />
            <div className="flex justify-between text-sm">
              <span>Score Range:</span>
              <span className="font-medium">{statistics.lowestScore} - {statistics.highestScore}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Suppliers</CardTitle>
          <CardDescription>Highest scoring applications in this category</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {topPerformers.map((performer, index) => (
              <div key={performer.supplierId} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{performer.supplierName}</p>
                    <p className="text-sm text-muted-foreground">
                      System: {performer.systemScore} | Compliance: {performer.complianceScore}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-lg font-bold">{performer.totalScore}</p>
                    <p className="text-sm text-muted-foreground">Total Score</p>
                  </div>
                  {getStatusBadge(performer.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Navigate to detailed views</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              onClick={() => router.push(`/admin/jobs/${jobId}/categories/${categoryId}/applications`)}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              View All Applications
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push(`/admin/jobs/${jobId}/reports`)}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Job Reports
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Applications Table */}
      <Card>
        <CardHeader>
          <CardTitle>Category Applications</CardTitle>
          <CardDescription>Detailed view of all applications for this category</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4 items-center mb-6">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={reviewStatusFilter} onValueChange={setReviewStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Review Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reviews</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Applications Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Application Ref</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Scores</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Review Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <Users className="h-12 w-12 text-muted-foreground opacity-50" />
                      <p className="text-muted-foreground">No applications found for this category</p>
                      <p className="text-sm text-muted-foreground">
                        Applications will appear here once suppliers submit them
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                applications
                  .filter(application => {
                    const matchesSearch = application.ref.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                        application.spSupplierCompanyId.name.toLowerCase().includes(searchQuery.toLowerCase());
                    const matchesStatus = statusFilter === 'all' || application.status === statusFilter;
                    const matchesReviewStatus = reviewStatusFilter === 'all' || application.reviewStatus === reviewStatusFilter;
                    return matchesSearch && matchesStatus && matchesReviewStatus;
                  })
                  .map((application) => (
                  <TableRow key={application._id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{application.spSupplierCompanyId.name}</div>
                        <div className="text-sm text-muted-foreground">{application.spSupplierCompanyId.registrationNumber}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{application.ref}</TableCell>
                    <TableCell>
                      {getStatusBadge(application.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>System:</span>
                          <span className="font-medium">{application.systemScore}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Compliance:</span>
                          <span className="font-medium">{application.complianceScore}</span>
                        </div>
                        <div className="flex justify-between border-t pt-1">
                          <span className="font-medium">Total:</span>
                          <span className="font-bold">{application.totalScore}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(application.submittedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {getStatusBadge(application.reviewStatus)}
                        {application.reviewer && (
                          <div className="text-xs text-muted-foreground">
                            by {application.reviewer.firstName} {application.reviewer.lastName}
                          </div>
                        )}
                        {application.reviewerNotes && (
                          <div className="text-xs text-muted-foreground max-w-xs truncate" title={application.reviewerNotes}>
                            "{application.reviewerNotes}"
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/admin/jobs/${jobId}/categories/${categoryId}/applications/${application._id}`)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Review
                      </Button>
                    </TableCell>
                  </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
