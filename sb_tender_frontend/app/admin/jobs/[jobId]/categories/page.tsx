'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import {
  Comb<PERSON>,
  Pen,
  Trash2,
  Users,
  BarChart3,
  Search,
  Plus,
  Edit,
  Settings,
  Target,
  CheckSquare,
  Building2,
  Calendar,
  User,
  X
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useParams } from 'next/navigation';
import Link from 'next/link';

const CategoriesPage = () => {
  const [categories, setCategories] = useState<any[]>([]);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Unified modal state
  const [isUnifiedModalVisible, setIsUnifiedModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('basic'); // 'basic' or 'fields'
  const [currentCategory, setCurrentCategory] = useState<any>(null);

  // Template/fields state
  const [jobCategory, setCategoryTemplate] = useState<any>(null);
  const [isTemplateFieldsModalVisible, setIsTemplateFieldsModalVisible] = useState(false);
  const [selectedField, setSelectedField] = useState<any>(null);
  const [editedField, setEditedField] = useState<any>(null);
  const [tempFields, setTempFields] = useState<any[]>([]); // Temporary fields for editing
  const [isAddFieldModalVisible, setIsAddFieldModalVisible] = useState(false);
  const [editingFieldIndex, setEditingFieldIndex] = useState<number | null>(null);
  const [newFieldData, setNewFieldData] = useState({
    name: '',
    label: '',
    description: '',
    type: 'text',
    placeholder: '',
    tooltip: '',
    helpText: '',
    isRequired: false,
    isEditable: true,
    isVisible: true,
    group: '',
    subGroup: '',
    order: 0,
    maxScore: 0,
    maxComplianceScore: 0
  });

const params = useParams();
const [jobId, setJobId] = useState<string | null>(null);
const [systemCategories, setSystemCategories] = useState([]);
const [selectedJobCategories, setSelectedJobCategories] = useState([]);

useEffect(() => {
  if (params?.jobId && typeof params.jobId === 'string') {
    setJobId(params.jobId);
  }
}, [params]);

  const form = useForm({
    defaultValues: {
      title: '',
      description: '',
      type: '',
      status: 'active',
      passMark: 0
    }
  });

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);

  useEffect(() => {
    if (editingCategory) {
      form.reset({
        title: editingCategory.title,
        description: editingCategory.description,
        type: editingCategory.type,
        status: editingCategory.status,
        passMark: editingCategory.passMark || 0
      });
    } else {
      form.reset({
        title: '',
        description: '',
        type: '',
        status: 'active',
        passMark: 0
      });
    }
  }, [editingCategory, form]);

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`/api/admin/jobs/${jobId}/categories`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log(data);
      setCategories(data.data.categories);
    } catch (error) {
      toast.error('Failed to fetch categories');
    }
  };
  
  const handleSearch = (e: any) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category: any) =>
    category.title.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddCategory = () => {
    setEditingCategory(null);
    setIsModalVisible(true);
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setIsModalVisible(true);
  };

  // New unified modal handler - no unnecessary API call
  const handleEditCategoryUnified = (category: any) => {
    console.log('Opening unified modal for category:', category);
    setCurrentCategory(category);
    setEditingCategory(category);
    setActiveTab('basic');

    // Use the category data we already have - no need for API call
    setCategoryTemplate(category);
    setTempFields(category.fields || []);

    // Set form values for basic details tab
    form.reset({
      title: category.title,
      description: category.description,
      type: category.type,
      status: category.status,
      passMark: category.passMark || 0
    });

    setIsUnifiedModalVisible(true);
  };

  // Add field handler
  const handleAddField = () => {
    setNewFieldData({
      name: '',
      label: '',
      description: '',
      type: 'text',
      placeholder: '',
      tooltip: '',
      helpText: '',
      isRequired: false,
      isEditable: true,
      isVisible: true,
      group: '',
      subGroup: '',
      order: tempFields.length + 1,
      maxScore: 0,
      maxComplianceScore: 0
    });
    setIsAddFieldModalVisible(true);
  };

  // Save new field or update existing field
  const handleSaveNewField = () => {
    const fieldData = { ...newFieldData };

    if (editingFieldIndex !== null) {
      // Update existing field
      const updatedFields = [...tempFields];
      updatedFields[editingFieldIndex] = fieldData;
      setTempFields(updatedFields);
      toast.success('Field updated successfully');
    } else {
      // Add new field
      setTempFields([...tempFields, fieldData]);
      toast.success('Field added successfully');
    }

    setIsAddFieldModalVisible(false);
    setEditingFieldIndex(null);
  };

  // Edit field handler
  const handleEditField = (field: any, index: number) => {
    setNewFieldData(field);
    setEditingFieldIndex(index);
    setIsAddFieldModalVisible(true);
  };

  // Delete field handler
  const handleDeleteField = (index: number) => {
    const updatedFields = tempFields.filter((_, i) => i !== index);
    setTempFields(updatedFields);
    toast.success('Field deleted successfully');
  };

  // Unified save function for both basic details and fields
  const handleSaveCategory = async (basicDetailsData?: any) => {
    if (!currentCategory) return;

    setIsSaving(true);
    try {
      // Combine basic details (if provided) with current fields
      const updateData = {
        ...currentCategory,
        ...(basicDetailsData || {}),
        fields: tempFields
      };

      console.log('Saving complete category data:', updateData);

      const response = await axios.put(
        `/api/admin/jobs/${jobId}/categories/${currentCategory._id}`,
        { jobCategory: updateData },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data) {
        // Update the category in the main list
        setCategories(categories.map(cat =>
          cat._id === currentCategory._id ? response.data : cat
        ));

        toast.success('Category saved successfully');
        setIsUnifiedModalVisible(false);
        fetchCategories(); // Refresh the list
      }
    } catch (error) {
      console.error('Failed to save category:', error);
      toast.error('Failed to save category');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteCategory = async (id: string) => {
    try {
      await axios.delete(`/api/admin/jobs/${jobId}/categories/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      toast.success('Company deleted successfully');
      fetchCategories();
    } catch (error) {
      toast.error('Failed to delete category');
    }
  };

  const onSubmit = async (values: any) => {
    console.log("Form values:", values);
    console.log("category update token", token);
    setIsSaving(true);

    try {
      if (editingCategory && jobId) {
        console.log(`Editing job ${jobId} category:`, editingCategory);
        // Use the jobId from params instead of nested object to avoid state issues
        await axios.put(`/api/admin/jobs/${jobId}/categories/${editingCategory._id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
        toast.success('Category updated successfully');

        // Close the appropriate modal
        if (isUnifiedModalVisible) {
          setIsUnifiedModalVisible(false);
        } else {
          setIsModalVisible(false);
        }

        // Reset form and refresh data
        form.reset();
        fetchCategories();
      } else {
        await axios.post(`/api/admin/jobs/${jobId}/categories`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
        toast.success('Category created successfully');
        setIsModalVisible(false);
        form.reset();
        fetchCategories();
      }
    } catch (error) {
      console.error('Save category error:', error);
      toast.error('Failed to save category');
    } finally {
      setIsSaving(false);
    }
  };

  const openTemplateFieldsModal = async (category: any) => {
    try {
      setCategoryTemplate(category);
      setIsTemplateFieldsModalVisible(true);
    } catch (error) {
      toast.error('Failed to fetch category criteria');
    }
  };

  const openFieldEditModal = (field: any) => setEditedField(field);
  const closeFieldEditModal = () => setEditedField(null);

  const handleFieldChange = (key: string, value: any) => {
    console.log(`Before update - ${key}:`, editedField[key]);
    console.log(`Setting ${key} to:`, value);
    
    // Create a completely new object to ensure React detects the change
    const updatedField = { ...editedField };
    updatedField[key] = value;
    
    console.log("Updated field object:", updatedField);
    setEditedField(updatedField);
    
    setTimeout(() => {
      console.log(`After update - ${key}:`, editedField[key]);
    }, 0);
  };

  const handleTemplateUpdate = async (updatedCategory: any) => {
    console.log('Updating (template)fields for category:', updatedCategory);
    try {
      const { data } = await axios.put(
        `/api/admin/jobs/${jobId}/categories/${updatedCategory._id}`,
        { jobCategory: updatedCategory },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setCategoryTemplate(data);
      toast.success('Template updated successfully');
    } catch (error) {
      console.error('Failed to update template:', error);
      toast.error('Failed to update template');
    }
  };
  
  const saveEditedField = async () => {
    try {
      // Check if the field already exists
      console.log('Edited field:', editedField);
      const fieldExists = jobCategory.fields.some((field: any) => field.name === editedField.name);
      // Update or add the field
      const updatedFields = fieldExists
        ? jobCategory.fields.map((field: any) =>
            field.name === editedField.name ? editedField : field
          )
        : [...jobCategory.fields, editedField]; // Add new field if it doesn't exist
      // Create the updated template
      const updatedCategory = { ...jobCategory, fields: updatedFields };
      console.log(updatedCategory);
      // Update the state
      setCategoryTemplate(updatedCategory);
      // Persist the changes
      await handleTemplateUpdate(updatedCategory);
      // Clear the edited field
      setEditedField(null);
    } catch (err) {
      console.error('Failed to update field');
    }
  };

  const deleteField = async (fieldName: string) => {
    try {
      const updatedFields = jobCategory.fields.filter((field: any) => field.name !== fieldName);

      // Update the state and persist the changes
      setCategoryTemplate((prev: any) => ({ ...prev, fields: updatedFields }));
      const updatedCategory = { ...jobCategory, fields: updatedFields };
      await handleTemplateUpdate(updatedCategory);
       } catch (err) {
      toast.error('Failed to delete field');
    }
  };

  const addField = async () => {
    const newField = {
      name: '',
      label: '',
      type: 'text',
      helpText: '',
      placeholder: '',
      tooltip: '',
      description: '',
      required: false,
      editable: true,
      visible: true,
      maxScore: null,
      options: [],
      subgroup: '',
      group: '',
      order: 0,
    };
    const updatedFields = [...jobCategory.fields, newField];

    try { 
      setEditedField(newField);
    } catch (err) {
      toast.error('Failed to add field');
    }
  };


  return (
    <div className="w-full max-w-full overflow-x-hidden">
      <div className="p-3 sm:p-6 bg-muted/30 rounded-lg shadow-md border border-border/50">
        <Toaster />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
          <h1 className="text-xl sm:text-2xl font-bold text-foreground">Job Categories</h1>
          <Link
            href="/admin/dashboard"
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Dashboard
          </Link>
        </div>

        {/* Search and Actions Bar */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4 p-3 sm:p-4 bg-background/50 rounded-lg border border-border">
          <div className="flex-1 max-w-sm relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Search categories..."
              value={search}
              onChange={handleSearch}
              className="pl-10 bg-background"
            />
          </div>
        </div>

        {/* Categories Table */}
        <Card className="bg-card border-border shadow-lg">
          <CardHeader className="bg-muted/50 border-b border-border">
            <CardTitle className="text-card-foreground">Job Categories</CardTitle>
          </CardHeader>
          <CardContent className="p-0 bg-card">
            <div className="border border-border rounded-lg overflow-x-auto min-w-full">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow className="border-b border-border">
                    <TableHead className="w-12">
                      <CheckSquare className="h-4 w-4" />
                    </TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Opens</TableHead>
                    <TableHead>Closes</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-32">
                      <Building2 className="h-4 w-4 inline mr-1" />
                      Company
                    </TableHead>
                    <TableHead className="w-32">
                      <Calendar className="h-4 w-4 inline mr-1" />
                      Created
                    </TableHead>
                    <TableHead className="w-32">
                      <User className="h-4 w-4 inline mr-1" />
                      Created By
                    </TableHead>
                    <TableHead className="w-40">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCategories.map((category: any) => (
                    <TableRow key={category._id} className="border-b border-border/50">
                      <TableCell>
                        <input type="checkbox" className="rounded" />
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="text-wrap" title={category.title}>
                          {category.title}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="text-wrap max-w-[200px]" title={category.description}>
                          {category.description}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {category.starts ? new Date(category.starts).toLocaleDateString() : 'N/A'}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {category.ends ? new Date(category.ends).toLocaleDateString() : 'N/A'}
                      </TableCell>
                      <TableCell className="text-sm">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          category.type === 'goods' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          category.type === 'services' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          category.type === 'works' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                        }`}>
                          {category.type}
                        </span>
                      </TableCell>
                      <TableCell className="text-sm">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          category.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {category.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="truncate" title={category.spCompanyId?.name}>
                          {category.spCompanyId?.name || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {category.createdAt ? new Date(category.createdAt).toLocaleDateString() : 'N/A'}
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="truncate" title={`${category.createdBy?.firstName} ${category.createdBy?.lastName}`}>
                          {category.createdBy ? `${category.createdBy.firstName} ${category.createdBy.lastName}` : 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditCategoryUnified(category)}
                            className="flex items-center gap-1 min-w-0"
                          >
                            <Edit className="h-3 w-3" />
                            <span className="hidden sm:inline">Edit</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = `/admin/jobs/${jobId}/categories/${category._id}/applications`}
                            className="flex items-center gap-1 min-w-0"
                          >
                            <Users className="h-3 w-3" />
                            <span className="hidden sm:inline">Participants</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = `/admin/jobs/${jobId}/categories/${category._id}/reports`}
                            className="flex items-center gap-1 min-w-0"
                          >
                            <BarChart3 className="h-3 w-3" />
                            <span className="hidden sm:inline">Reports</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteCategory(category._id)}
                            className="flex items-center gap-1 text-destructive hover:text-destructive min-w-0"
                          >
                            <Trash2 className="h-3 w-3" />
                            <span className="hidden sm:inline">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Edit Category Modal */}
        <Dialog open={isModalVisible} onOpenChange={setIsModalVisible}>
          <DialogContent className="w-[95vw] max-w-[500px] max-h-[90vh] overflow-y-auto bg-card border-border">
            <DialogHeader className="border-b border-border pb-4">
              <DialogTitle className="text-card-foreground flex items-center gap-2">
                <Edit className="h-5 w-5" />
                {editingCategory ? 'Edit Category' : 'Add Category'}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                {editingCategory ? 'Update the category details below.' : 'Fill in the details for the new category.'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid gap-4 py-4 bg-card">
                <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="sm:text-right text-card-foreground">
                    Title
                  </Label>
                  <Input
                    id="title"
                    {...form.register('title', { required: true })}
                    className="sm:col-span-3 bg-background"
                    placeholder="Category title"
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="sm:text-right text-card-foreground">
                    Description
                  </Label>
                  <Input
                    id="description"
                    {...form.register('description')}
                    className="sm:col-span-3 bg-background"
                    placeholder="Category description"
                  />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="sm:text-right text-card-foreground">
                    Type
                  </Label>
                  <select
                    id="type"
                    {...form.register('type', { required: true })}
                    className="sm:col-span-3 w-full p-2 border border-border rounded bg-background text-foreground"
                  >
                    <option value="">Select type</option>
                    <option value="supplier_registration">Supplier Registration</option>
                    <option value="supplier_prequalification">Supplier Prequalification</option>
                    <option value="rfq">RFQ</option>
                    <option value="tender">Tender</option>
                  </select>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="sm:text-right text-card-foreground">
                    Status
                  </Label>
                  <select
                    id="status"
                    {...form.register('status', { required: true })}
                    className="sm:col-span-3 w-full p-2 border border-border rounded bg-background text-foreground"
                  >
                    <option value="draft">Draft</option>
                    <option value="open">Open</option>
                    <option value="closed">Closed</option>
                  </select>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                  <Label htmlFor="passMark" className="sm:text-right text-card-foreground flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Pass Mark
                  </Label>
                  <Input
                    id="passMark"
                    type="number"
                    {...form.register('passMark', { required: true, valueAsNumber: true })}
                    className="sm:col-span-3 bg-background"
                    placeholder="Enter pass mark percentage"
                    min="0"
                    max="100"
                  />
                </div>
              </div>
              <DialogFooter className="border-t border-border pt-4 flex flex-col sm:flex-row gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalVisible(false)}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4" />
                      Save Category
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Unified Category Edit Modal */}
        <Dialog open={isUnifiedModalVisible} onOpenChange={setIsUnifiedModalVisible}>
          <DialogContent className="w-[98vw] max-w-[1400px] h-[95vh] overflow-hidden bg-card border-border flex flex-col">
            <DialogHeader className="border-b border-border pb-4">
              <DialogTitle className="text-card-foreground flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Edit Category: {currentCategory?.title}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Manage category details and criteria fields in one place.
              </DialogDescription>
            </DialogHeader>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-muted p-1 rounded-lg mb-6">
              <button
                onClick={() => setActiveTab('basic')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'basic'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <Edit className="h-4 w-4" />
                  Basic Details
                </div>
              </button>
              <button
                onClick={() => setActiveTab('fields')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'fields'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <Settings className="h-4 w-4" />
                  Criteria Fields
                </div>
              </button>
            </div>

            {/* Tab Content */}
            {activeTab === 'basic' && (
              <form onSubmit={form.handleSubmit((data) => handleSaveCategory(data))} className="space-y-4">
                <div className="grid gap-4 py-4 bg-card">
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                    <Label htmlFor="title" className="sm:text-right text-card-foreground">
                      Title
                    </Label>
                    <Input
                      id="title"
                      {...form.register('title', { required: true })}
                      className="sm:col-span-3 bg-background"
                      placeholder="Category title"
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                    <Label htmlFor="description" className="sm:text-right text-card-foreground">
                      Description
                    </Label>
                    <Input
                      id="description"
                      {...form.register('description')}
                      className="sm:col-span-3 bg-background"
                      placeholder="Category description"
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                    <Label htmlFor="type" className="sm:text-right text-card-foreground">
                      Type
                    </Label>
                    <select
                      id="type"
                      {...form.register('type', { required: true })}
                      className="sm:col-span-3 w-full p-2 border border-border rounded bg-background text-foreground"
                    >
                      <option value="">Select type</option>
                      <option value="supplier_registration">Supplier Registration</option>
                      <option value="supplier_prequalification">Supplier Prequalification</option>
                      <option value="rfq">RFQ</option>
                      <option value="tender">Tender</option>
                    </select>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                    <Label htmlFor="status" className="sm:text-right text-card-foreground">
                      Status
                    </Label>
                    <select
                      id="status"
                      {...form.register('status', { required: true })}
                      className="sm:col-span-3 w-full p-2 border border-border rounded bg-background text-foreground"
                    >
                      <option value="draft">Draft</option>
                      <option value="open">Open</option>
                      <option value="closed">Closed</option>
                    </select>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4">
                    <Label htmlFor="passMark" className="sm:text-right text-card-foreground flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Pass Mark
                    </Label>
                    <Input
                      id="passMark"
                      type="number"
                      {...form.register('passMark', { required: true, valueAsNumber: true })}
                      className="sm:col-span-3 bg-background"
                      placeholder="Enter pass mark percentage"
                      min="0"
                      max="100"
                    />
                  </div>
                </div>
                <DialogFooter className="border-t border-border pt-4 flex flex-col sm:flex-row gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsUnifiedModalVisible(false)}
                    disabled={isSaving}
                    className="flex items-center gap-2"
                  >
                    <X className="h-4 w-4" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center gap-2"
                  >
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Edit className="h-4 w-4" />
                        Save Details
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            )}

            {activeTab === 'fields' && (
              <div className="flex flex-col h-full space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-card-foreground">
                    Criteria Fields ({tempFields.length})
                  </h3>
                  <Button
                    onClick={handleAddField}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Field
                  </Button>
                </div>

                <div className="flex-1 overflow-y-auto space-y-3 pr-2">
                  {tempFields.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No criteria fields defined yet.</p>
                      <p className="text-sm">Click "Add Field" to create your first field.</p>
                    </div>
                  ) : (
                    tempFields.map((field: any, index: number) => (
                      <Card key={index} className="border border-border">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-card-foreground">
                                {field.label || 'Unnamed field'}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {field.name} — {field.type}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {field.description}
                              </p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {field.isRequired && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    Required
                                  </span>
                                )}
                                {!field.isEditable && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    Read Only
                                  </span>
                                )}
                                {!field.isVisible && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                    Hidden
                                  </span>
                                )}
                                {field.maxScore > 0 && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    Score: {field.maxScore}
                                  </span>
                                )}
                                {field.maxComplianceScore > 0 && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Compliance: {field.maxComplianceScore}
                                  </span>
                                )}
                                {field.group && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                    {field.group}
                                  </span>
                                )}
                                {field.subGroup && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                    {field.subGroup}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-2 ml-4">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditField(field, index)}
                                className="flex items-center gap-1"
                              >
                                <Edit className="h-3 w-3" />
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteField(index)}
                                className="flex items-center gap-1 text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-3 w-3" />
                                Delete
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>

                <DialogFooter className="border-t border-border pt-4 mt-auto">
                  <Button
                    variant="outline"
                    onClick={() => setIsUnifiedModalVisible(false)}
                    className="flex items-center gap-2"
                  >
                    <X className="h-4 w-4" />
                    Close
                  </Button>
                  <Button
                    onClick={() => handleSaveCategory()}
                    className="flex items-center gap-2"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Settings className="h-4 w-4" />
                        Save Fields
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Add/Edit Field Modal */}
        <Dialog open={isAddFieldModalVisible} onOpenChange={setIsAddFieldModalVisible}>
          <DialogContent className="w-[95vw] max-w-[800px] max-h-[90vh] overflow-y-auto bg-card border-border">
            <DialogHeader className="border-b border-border pb-4">
              <DialogTitle className="text-card-foreground flex items-center gap-2">
                <Plus className="h-5 w-5" />
                {editingFieldIndex !== null ? 'Edit Field' : 'Add New Field'}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Configure all field properties and validation rules.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6 py-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-card-foreground">Basic Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fieldLabel" className="text-card-foreground">Label *</Label>
                    <Input
                      id="fieldLabel"
                      value={newFieldData.label}
                      onChange={(e) => setNewFieldData({...newFieldData, label: e.target.value})}
                      className="bg-background"
                      placeholder="Display Label"
                    />
                  </div>
                  <div>
                    <Label htmlFor="fieldName" className="text-card-foreground">Name *</Label>
                    <Input
                      id="fieldName"
                      value={newFieldData.name}
                      onChange={(e) => setNewFieldData({...newFieldData, name: e.target.value})}
                      className="bg-background"
                      placeholder="field_name"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="fieldDescription" className="text-card-foreground">Description</Label>
                  <Input
                    id="fieldDescription"
                    value={newFieldData.description}
                    onChange={(e) => setNewFieldData({...newFieldData, description: e.target.value})}
                    className="bg-background"
                    placeholder="Field description"
                  />
                </div>
              </div>

              {/* Field Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-card-foreground">Field Configuration</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fieldType" className="text-card-foreground">Type *</Label>
                    <select
                      id="fieldType"
                      value={newFieldData.type}
                      onChange={(e) => setNewFieldData({...newFieldData, type: e.target.value})}
                      className="w-full p-2 border border-border rounded bg-background text-foreground"
                    >
                      <option value="text">Text</option>
                      <option value="textarea">Textarea</option>
                      <option value="number">Number</option>
                      <option value="date">Date</option>
                      <option value="file">File</option>
                      <option value="checkbox">Checkbox</option>
                      <option value="radio">Radio</option>
                      <option value="select">Select</option>
                      <option value="section">Section</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="fieldOrder" className="text-card-foreground">Order</Label>
                    <Input
                      id="fieldOrder"
                      type="number"
                      value={newFieldData.order}
                      onChange={(e) => setNewFieldData({...newFieldData, order: parseInt(e.target.value) || 0})}
                      className="bg-background"
                      placeholder="0"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fieldPlaceholder" className="text-card-foreground">Placeholder</Label>
                    <Input
                      id="fieldPlaceholder"
                      value={newFieldData.placeholder}
                      onChange={(e) => setNewFieldData({...newFieldData, placeholder: e.target.value})}
                      className="bg-background"
                      placeholder="Enter placeholder text"
                    />
                  </div>
                  <div>
                    <Label htmlFor="fieldTooltip" className="text-card-foreground">Tooltip</Label>
                    <Input
                      id="fieldTooltip"
                      value={newFieldData.tooltip}
                      onChange={(e) => setNewFieldData({...newFieldData, tooltip: e.target.value})}
                      className="bg-background"
                      placeholder="Tooltip text"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="fieldHelpText" className="text-card-foreground">Help Text</Label>
                  <Input
                    id="fieldHelpText"
                    value={newFieldData.helpText}
                    onChange={(e) => setNewFieldData({...newFieldData, helpText: e.target.value})}
                    className="bg-background"
                    placeholder="Help text for users"
                  />
                </div>
              </div>

              {/* Grouping */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-card-foreground">Grouping</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fieldGroup" className="text-card-foreground">Group</Label>
                    <select
                      id="fieldGroup"
                      value={newFieldData.group}
                      onChange={(e) => setNewFieldData({...newFieldData, group: e.target.value})}
                      className="w-full p-2 border border-border rounded bg-background text-foreground"
                    >
                      <option value="">Select group</option>
                      <option value="company">Company</option>
                      <option value="declaration">Declaration</option>
                      <option value="statutory">Statutory</option>
                      <option value="shareholders">Shareholders</option>
                      <option value="integrity">Integrity</option>
                      <option value="esg">ESG</option>
                      <option value="sla">SLA</option>
                      <option value="hr">HR</option>
                      <option value="experience">Experience</option>
                      <option value="reference">Reference</option>
                      <option value="submission">Submission</option>
                      <option value="financial">Financial</option>
                      <option value="technical">Technical</option>
                      <option value="system">System</option>
                      <option value="compliance">Compliance</option>
                      <option value="attachments">Attachments</option>
                      <option value="outcome">Outcome</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="fieldSubGroup" className="text-card-foreground">Sub Group</Label>
                    <select
                      id="fieldSubGroup"
                      value={newFieldData.subGroup}
                      onChange={(e) => setNewFieldData({...newFieldData, subGroup: e.target.value})}
                      className="w-full p-2 border border-border rounded bg-background text-foreground"
                    >
                      <option value="">Select sub group</option>
                      <option value="info">Info</option>
                      <option value="contact">Contact</option>
                      <option value="bank">Bank</option>
                      <option value="declaration">Declaration</option>
                      <option value="statutory">Statutory</option>
                      <option value="director1">Director 1</option>
                      <option value="director2">Director 2</option>
                      <option value="director3">Director 3</option>
                      <option value="shareholder1">Shareholder 1</option>
                      <option value="shareholder2">Shareholder 2</option>
                      <option value="shareholder3">Shareholder 3</option>
                      <option value="risk">Risk</option>
                      <option value="litigation">Litigation</option>
                      <option value="env">Environmental</option>
                      <option value="social">Social</option>
                      <option value="governance">Governance</option>
                      <option value="reference1">Reference 1</option>
                      <option value="reference2">Reference 2</option>
                      <option value="reference3">Reference 3</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Field Properties */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-card-foreground">Field Properties</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isRequired"
                      checked={newFieldData.isRequired}
                      onChange={(e) => setNewFieldData({...newFieldData, isRequired: e.target.checked})}
                      className="rounded"
                    />
                    <Label htmlFor="isRequired" className="text-card-foreground">Required</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isEditable"
                      checked={newFieldData.isEditable}
                      onChange={(e) => setNewFieldData({...newFieldData, isEditable: e.target.checked})}
                      className="rounded"
                    />
                    <Label htmlFor="isEditable" className="text-card-foreground">Editable</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isVisible"
                      checked={newFieldData.isVisible}
                      onChange={(e) => setNewFieldData({...newFieldData, isVisible: e.target.checked})}
                      className="rounded"
                    />
                    <Label htmlFor="isVisible" className="text-card-foreground">Visible</Label>
                  </div>
                </div>
              </div>

              {/* Scoring */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-card-foreground">Scoring</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxScore" className="text-card-foreground">Max Score</Label>
                    <Input
                      id="maxScore"
                      type="number"
                      value={newFieldData.maxScore}
                      onChange={(e) => setNewFieldData({...newFieldData, maxScore: parseInt(e.target.value) || 0})}
                      className="bg-background"
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxComplianceScore" className="text-card-foreground">Max Compliance Score</Label>
                    <Input
                      id="maxComplianceScore"
                      type="number"
                      value={newFieldData.maxComplianceScore}
                      onChange={(e) => setNewFieldData({...newFieldData, maxComplianceScore: parseInt(e.target.value) || 0})}
                      className="bg-background"
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter className="border-t border-border pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setIsAddFieldModalVisible(false);
                  setEditingFieldIndex(null);
                }}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
              <Button
                onClick={handleSaveNewField}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {editingFieldIndex !== null ? 'Update Field' : 'Add Field'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

{isTemplateFieldsModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
          <div className="bg-white w-full max-w-3xl p-6 rounded shadow-xl overflow-auto max-h-[90vh]">
            <h3 className="text-xl font-semibold mb-4">Job Category Criteria Fields</h3>
            <ul className="space-y-3">
              {jobCategory?.fields?.map((field, index) => (
                <li key={index} className="border p-3 rounded flex justify-between items-center">
                  <div>
                    <p className="font-medium">{field.label || 'Unnamed field'}</p>
                    <p className="text-sm text-gray-500">{field.name} — {field.type}</p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="px-3 py-1 bg-indigo-500 text-white rounded hover:bg-indigo-600"
                      onClick={() => openFieldEditModal(field)}
                    >
                      Edit
                    </button>
                    <button
                      className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                      onClick={() => deleteField(field.name)}
                    >
                      Delete
                    </button>
                  </div>
                </li>
              ))}
            </ul>
            <div className="flex justify-between mt-4">
              <button
                onClick={addField}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                + Add Field
              </button>
              <button
                onClick={() => setIsTemplateFieldsModalVisible(false)}
                className="px-4 py-2 border rounded hover:bg-gray-100"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {editedField && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
          <div className="bg-white w-full max-w-3xl p-6 rounded shadow-xl overflow-auto max-h-[90vh]">
            <h3 className="text-xl font-semibold mb-4">Modify criterion field</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Label</label>
                <input type="text" value={editedField.label || ''} onChange={(e) => handleFieldChange('label', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input type="text" value={editedField.description || ''} onChange={(e) => handleFieldChange('description', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input type="text" value={editedField.name || ''} onChange={(e) => handleFieldChange('name', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Type</label>
                <select value={editedField.type || 'text'} onChange={(e) => handleFieldChange('type', e.target.value)} className="w-full p-2 border rounded">
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="select">Select</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Placeholder</label>
                <input type="text" value={editedField.placeholder || ''} onChange={(e) => handleFieldChange('placeholder', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Tooltip</label>
                <input type="text" value={editedField.tooltip || ''} onChange={(e) => handleFieldChange('tooltip', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Help Text</label>
                <input type="text" value={editedField.helpText || ''} onChange={(e) => handleFieldChange('helpText', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Required</label>
                <input type="checkbox" checked={editedField.isRequired === true} onChange={(e) => handleFieldChange('isRequired', e.target.checked)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Editable</label>
                <input type="checkbox" checked={editedField.isEditable || false} onChange={(e) => handleFieldChange('isEditable', e.target.checked)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Visible</label>
                <input type="checkbox" checked={editedField.isVisible || false} onChange={(e) => handleFieldChange('isVisible', e.target.checked)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Group</label>
                <input type="text" value={editedField.group || ''} onChange={(e) => handleFieldChange('group', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Subgroup</label>
                <input type="text" value={editedField.subGroup || ''} onChange={(e) => handleFieldChange('subGroup', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Order</label>
                <input type="number" value={editedField.order || 0} onChange={(e) => handleFieldChange('order', parseInt(e.target.value))} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Max Sccore</label>
                <input type="number" value={editedField.maxScore || 0} onChange={(e) => handleFieldChange('maxScore', parseInt(e.target.value))} className="w-full p-2 border rounded" />
              </div>
              <div className="col-span-2 flex justify-end mt-4 gap-2">
                <button onClick={closeFieldEditModal} className="px-4 py-2 border rounded hover:bg-gray-100">Cancel</button>
                <button onClick={saveEditedField} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Save</button>
              </div>
            </div>
          </div>
        </div>
      )}

      </div>
    </div>
  );
};

export default CategoriesPage;
