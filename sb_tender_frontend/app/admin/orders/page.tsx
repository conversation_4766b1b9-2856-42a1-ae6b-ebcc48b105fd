"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { 
  ShoppingCart, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  Search,
  Edit,
  Eye,
  Package
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface OrderItem {
  _id: string;
  title: string;
  price: {
    $numberDecimal: string;
  } | number;
  starts: string;
  ends: string;
  location: string;
  type: 'supplier_registration' | 'supplier_prequalification' | 'rfq' | 'tender';
  supplierCompanyId: string;
  buyerCompanyId: string;
  spJobCategoryId: string;
}

interface PaymentInfo {
  _id: string;
  transactionId?: string;
  transID?: string;
  mobile?: string;
  status: string;
  amount: {
    $numberDecimal: string;
  };
  currency: string;
  paidAt?: string;
  reference?: string;
  respDesc?: string;
}

interface Order {
  _id: string;
  spCompanyId: string;
  status: 'checkout' | 'pending' | 'paid' | 'cancelled' | 'suspend';
  totalAmount: {
    $numberDecimal: string;
  } | number;
  orderItems: OrderItem[];
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  payment?: PaymentInfo;
}

interface OrderSummary {
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  paidOrders: number;
  cancelledOrders: number;
}

export default function AdminOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [summary, setSummary] = useState<OrderSummary>({
    totalOrders: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    paidOrders: 0,
    cancelledOrders: 0
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Filters and pagination
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Modal state
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [orderNotes, setOrderNotes] = useState('');

  // Helper function to convert decimal values
  const getDecimalValue = (value: { $numberDecimal: string } | number): number => {
    if (typeof value === 'number') return value;
    return parseFloat(value.$numberDecimal) || 0;
  };

  // Helper function to get company name (fallback for non-populated data)
  const getCompanyName = (companyId: string): string => {
    // In a real implementation, you might want to fetch company names
    // For now, return a placeholder or the ID
    return `Company ${companyId.slice(-6)}`;
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      console.log('Fetching orders...');
      const response = await fetch(
        `/api/admin/orders`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        const ordersData = data.data || [];
        console.log('Successfully fetched orders:', ordersData);
        
        setOrders(ordersData);
        calculateSummary(ordersData);
        toast.success('Orders loaded successfully');
      } else {
        console.log('Orders API call failed with status:', response.status);
        toast.error('Failed to fetch orders');
        
        // Fallback to empty array for development
        const mockOrders: Order[] = [];
        setOrders(mockOrders);
        calculateSummary(mockOrders);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to fetch orders');
      
      // Fallback to empty array
      const mockOrders: Order[] = [];
      setOrders(mockOrders);
      calculateSummary(mockOrders);
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = (ordersData: Order[]) => {
    const summary: OrderSummary = {
      totalOrders: ordersData.length,
      totalRevenue: ordersData
        .filter(order => order.status === 'paid')
        .reduce((sum, order) => sum + getDecimalValue(order.totalAmount), 0),
      pendingOrders: ordersData.filter(order => order.status === 'pending').length,
      paidOrders: ordersData.filter(order => order.status === 'paid').length,
      cancelledOrders: ordersData.filter(order => order.status === 'cancelled').length
    };
    setSummary(summary);
  };



  const updateOrderStatus = async () => {
    if (!selectedOrder) return;

    try {
      setUpdating(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/orders/${selectedOrder._id}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: newStatus,
            notes: orderNotes
          })
        }
      );

      if (response.ok) {
        toast.success('Order updated successfully');
        setModalOpen(false);
        fetchOrders(); // Refresh the orders list
      } else {
        toast.error('Failed to update order');
      }
    } catch (error) {
      console.error('Error updating order:', error);
      toast.error('Failed to update order');
    } finally {
      setUpdating(false);
    }
  };

  const openUpdateModal = (order: Order) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setOrderNotes(order.notes || '');
    setModalOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      checkout: { color: 'bg-gray-100 text-gray-800', label: 'Checkout', icon: Clock },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: AlertCircle },
      paid: { color: 'bg-green-100 text-green-800', label: 'Paid', icon: CheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled', icon: XCircle },
      suspend: { color: 'bg-orange-100 text-orange-800', label: 'Suspended', icon: AlertCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.checkout;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getFilteredOrders = () => {
    return orders.filter(order => {
      const matchesSearch = order._id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          getCompanyName(order.spCompanyId).toLowerCase().includes(searchQuery.toLowerCase()) ||
                          order.orderItems.some(item =>
                            item.title.toLowerCase().includes(searchQuery.toLowerCase())
                          );
      const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  };

  const getPaginatedOrders = () => {
    const filteredOrders = getFilteredOrders();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredOrders.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const filteredOrders = getFilteredOrders();
    return Math.ceil(filteredOrders.length / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading orders...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Orders(invoices) Management</h1>
          <p className="text-muted-foreground">Manage supplier orders and payments</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{summary.totalOrders}</p>
                <p className="text-sm text-muted-foreground">Total Orders</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">KES {summary.totalRevenue.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{summary.pendingOrders}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{summary.paidOrders}</p>
                <p className="text-sm text-muted-foreground">Paid</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <XCircle className="h-8 w-8 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{summary.cancelledOrders}</p>
                <p className="text-sm text-muted-foreground">Cancelled</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Orders</CardTitle>
          <CardDescription>Manage supplier orders and update payment status</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters - Mobile Responsive */}
          <div className="overflow-x-auto pb-2 mb-6">
            <div className="flex gap-4 items-center min-w-fit">
              <div className="flex items-center gap-2 min-w-fit">
                <Search className="h-4 w-4 flex-shrink-0" />
                <Input
                  placeholder="Search orders, companies, categories..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-80 min-w-[200px]"
                />
              </div>

              <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="checkout">Checkout</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="suspend">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Table */}
          <div className="border rounded-lg overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Supplier Company</TableHead>
                  <TableHead className="w-48">Order Categories</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getPaginatedOrders().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No orders found
                    </TableCell>
                  </TableRow>
                ) : (
                  getPaginatedOrders().map((order) => (
                    <TableRow key={order._id}>
                      <TableCell className="font-mono text-sm">
                        {order._id.slice(-8)}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{getCompanyName(order.spCompanyId)}</div>
                          <div className="text-sm text-muted-foreground">
                            ID: {order.spCompanyId.slice(-8)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-48 max-w-48 break-words">
                      <div className="space-y-1 ">
                        {order.orderItems.slice(0, 2).map((item, index) => (
                          <div key={index} className="text-sm">
                            <div className="font-medium break-words hyphens-auto overflow-wrap-anywhere overflow-hidden">
                              {item.title}
                            </div>
                            <div className="text-muted-foreground text-xs">
                              KES {getDecimalValue(item.price).toLocaleString()}
                            </div>
                          </div>
                        ))}
                        {order.orderItems.length > 2 && (
                          <div className="text-xs text-muted-foreground">
                            +{order.orderItems.length - 2} more categories
                          </div>
                        )}
                      </div>
                    </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          KES {getDecimalValue(order.totalAmount).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(order.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openUpdateModal(order)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {getFilteredOrders().length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(getTotalPages(), currentPage + 1))}
                      className={currentPage === getTotalPages() ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="text-sm text-muted-foreground text-center mt-2">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredOrders().length)} to {Math.min(currentPage * itemsPerPage, getFilteredOrders().length)} of {getFilteredOrders().length} orders
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Update Order Modal */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
            <DialogDescription>
              Update the status and add notes for order {selectedOrder?._id.slice(-8)}
            </DialogDescription>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-4">
              {/* Order Details */}
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Order Details</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Company:</strong> {getCompanyName(selectedOrder.spCompanyId)}</div>
                  <div><strong>Total Amount:</strong> KES {getDecimalValue(selectedOrder.totalAmount).toLocaleString()}</div>
                  <div><strong>Categories:</strong> {selectedOrder.orderItems.length} items</div>
                  <div><strong>Current Status:</strong> {getStatusBadge(selectedOrder.status)}</div>
                  {selectedOrder.payment && (
                    <div><strong>Payment:</strong> {selectedOrder.payment.transID || 'N/A'} ({selectedOrder.payment.status})</div>
                  )}
                </div>
              </div>

              {/* Status Update */}
              <div className="space-y-2">
                <label className="text-sm font-medium">New Status</label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="checkout">Checkout</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="suspend">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  placeholder="Add notes about this order update..."
                  value={orderNotes}
                  onChange={(e) => setOrderNotes(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateOrderStatus} disabled={updating}>
              {updating ? 'Updating...' : 'Update Order'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
