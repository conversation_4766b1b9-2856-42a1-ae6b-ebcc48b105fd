"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Bell,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Plus,
  RefreshCw,
  Mail,
  MessageSquare,
  Smartphone,
  Users,
  Calendar,
  Paperclip
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface Notification {
  _id: string;
  type: 'marketing' | 'tender' | 'system' | 'reminders' | 'order' | 'other';
  channel: 'email' | 'sms' | 'app' | 'push';
  bulkId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed' | 'cancelled';
  title: string;
  message: string;
  recipient: {
    email?: string;
    phone?: string;
    userId?: string;
  };
  delivery: {
    attempts: number;
    maxAttempts: number;
    scheduledAt?: string;
    sentAt?: string;
    deliveredAt?: string;
    readAt?: string;
    failedAt?: string;
    errorMessage?: string;
  };
  attachments?: {
    filename: string;
    path: string;
    contentType: string;
    size: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface NotificationStats {
  totalNotifications: number;
  sentNotifications: number;
  deliveredNotifications: number;
  failedNotifications: number;
  pendingNotifications: number;
  readNotifications: number;
}

interface AudienceOption {
  id: string;
  label: string;
  type: 'role' | 'search' | 'manual';
  description: string;
}

export default function AdminNotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({
    totalNotifications: 0,
    sentNotifications: 0,
    deliveredNotifications: 0,
    failedNotifications: 0,
    pendingNotifications: 0,
    readNotifications: 0
  });
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  // Filters and pagination
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  
  // Form state for creating notifications
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'system' as const,
    channels: [] as string[],
    priority: 'medium' as const,
    audiences: [] as string[],
    manualEmails: '',
    manualPhones: '',
    scheduledAt: '',
    searchUsers: ''
  });
  const [attachments, setAttachments] = useState<File[]>([]);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  const audienceOptions: AudienceOption[] = [
    { id: 'all_users', label: 'All Users', type: 'role', description: 'Send to all registered users' },
    { id: 'all_admins', label: 'All Admins', type: 'role', description: 'Send to all admin users' },
    { id: 'all_suppliers', label: 'All Suppliers', type: 'role', description: 'Send to all supplier companies' },
    { id: 'all_buyers', label: 'All Buyers', type: 'role', description: 'Send to all buyer companies' },
    { id: 'search_users', label: 'Search Users', type: 'search', description: 'Search and select specific users' },
    { id: 'manual_emails', label: 'Email List', type: 'manual', description: 'Enter email addresses manually' },
    { id: 'manual_phones', label: 'Phone List', type: 'manual', description: 'Enter phone numbers manually' }
  ];

  const channelOptions = [
    { id: 'email', label: 'Email', icon: Mail },
    { id: 'sms', label: 'SMS', icon: MessageSquare },
    { id: 'app', label: 'In-App', icon: Bell, disabled: true },
    { id: 'push', label: 'Push', icon: Smartphone }
  ];

  useEffect(() => {
    fetchNotifications();
    fetchStats();
  }, []);

  const fetchNotifications = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      }

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      console.log('Fetching notifications...');
      const response = await fetch(
        //`${BACKEND_API_URL}/api/notifications/admin/all`,
          `/api/admin/notifications`,

        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        const notificationsData = data.data?.notifications || data.data || [];
        console.log('Successfully fetched notifications:', notificationsData);

        setNotifications(notificationsData);
        if (showLoading) {
          toast.success('Notifications loaded successfully');
        }
      } else {
        console.log('Notifications API call failed with status:', response.status);
        if (showLoading) {
          toast.error('Failed to fetch notifications');
        }

        // Fallback to empty array
        setNotifications([]);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      if (showLoading) {
        toast.error('Failed to fetch notifications');
      }
      setNotifications([]);
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  const fetchStats = async (showToast = false) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) return;

      const response = await fetch(
        `/api/admin/notifications`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        const statsData = data.data || data;
        setStats({
          totalNotifications: statsData.total || 0,
          sentNotifications: (statsData.byStatus?.sent || 0), // If you have 'sent'
          deliveredNotifications: (statsData.byStatus?.delivered || 0),
          failedNotifications: (statsData.byStatus?.failed || 0),
          pendingNotifications: (statsData.byStatus?.pending || 0),
          readNotifications: (statsData.byStatus?.read || 0)
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      if (showToast) {
        toast.error('Failed to fetch stats');
      }
    }
  };

  const sendNotification = async () => {
    try {
      setSending(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      // Validate form data
      if (!formData.title.trim() || !formData.message.trim()) {
        toast.error('Title and message are required');
        return;
      }

      if (formData.channels.length === 0) {
        toast.error('At least one channel must be selected');
        return;
      }

      if (formData.audiences.length === 0 && !formData.manualEmails.trim() && !formData.manualPhones.trim()) {
        toast.error('At least one audience must be selected or manual recipients provided');
        return;
      }

      // Validate manual emails
      if (formData.manualEmails.trim()) {
        const emails = formData.manualEmails.split(',').map(e => e.trim()).filter(e => e);
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const invalidEmails = emails.filter(email => !emailRegex.test(email));
        if (invalidEmails.length > 0) {
          toast.error(`Invalid email addresses: ${invalidEmails.join(', ')}`);
          return;
        }
      }

      // Validate manual phone numbers
      if (formData.manualPhones.trim()) {
        const phones = formData.manualPhones.split(',').map(p => p.trim()).filter(p => p);
        const phoneRegex = /^(\+?254|0)?[17]\d{8}$/;
        const invalidPhones = phones.filter(phone => !phoneRegex.test(phone));
        if (invalidPhones.length > 0) {
          toast.error(`Invalid phone numbers: ${invalidPhones.join(', ')}`);
          return;
        }
      }

      // Create FormData for file uploads
      const formDataPayload = new FormData();
      formDataPayload.append('title', formData.title);
      formDataPayload.append('message', formData.message);
      formDataPayload.append('type', formData.type);
      formDataPayload.append('priority', formData.priority);
      formDataPayload.append('channels', JSON.stringify(formData.channels));
      formDataPayload.append('audiences', JSON.stringify(formData.audiences));
      formDataPayload.append('manualEmails', JSON.stringify(formData.manualEmails.trim() ? formData.manualEmails.split(',').map(e => e.trim()).filter(e => e) : []));
      formDataPayload.append('manualPhones', JSON.stringify(formData.manualPhones.trim() ? formData.manualPhones.split(',').map(p => p.trim()).filter(p => p) : []));

      if (formData.scheduledAt) {
        formDataPayload.append('scheduledAt', formData.scheduledAt);
      }

      if (formData.searchUsers.trim()) {
        formDataPayload.append('searchUsers', formData.searchUsers.trim());
      }

      // Add attachments
      attachments.forEach((file, index) => {
        formDataPayload.append('attachments', file);
      });
      console.log('formDataPayload', formDataPayload);

      const response = await fetch(
        `/api/admin/notifications`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${cookieToken}`
            // Don't set Content-Type for FormData, let browser set it with boundary
          },
          body: formDataPayload
        }
      );

      if (response.ok) {
        const result = await response.json();
        toast.success(`Notification ${formData.scheduledAt ? 'scheduled' : 'sent'} successfully`);
        setModalOpen(false);
        resetForm();
        // Refresh data without showing loading state to preserve toast visibility
        setTimeout(() => {
          fetchNotifications(false); // Don't show loading state
          fetchStats(false); // Don't show toast for stats
        }, 1500); // Longer delay to let user see the success toast
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to send notification');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      toast.error('Failed to send notification');
    } finally {
      setSending(false);
    }
  };

  const resendNotification = async (notification: Notification) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/notifications/${notification._id}/resend`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        toast.success('Notification resent successfully');
        // Refresh data without showing loading state to preserve toast visibility
        setTimeout(() => {
          fetchNotifications(false); // Don't show loading state
          fetchStats(false); // Don't show toast for stats
        }, 1500); // Longer delay to let user see the success toast
      } else {
        toast.error('Failed to resend notification');
      }
    } catch (error) {
      console.error('Error resending notification:', error);
      toast.error('Failed to resend notification');
    }
  };

  const resendBulkNotifications = async (bulkId: string) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/notifications/bulk/${bulkId}/resend`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        toast.success('Bulk notifications resent successfully');
        setTimeout(() => {
          fetchNotifications(false);
          fetchStats(false);
        }, 1500);
      } else {
        toast.error('Failed to resend bulk notifications');
      }
    } catch (error) {
      console.error('Error resending bulk notifications:', error);
      toast.error('Failed to resend bulk notifications');
    }
  };

  const downloadAttachments = async (notificationId: string) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/notifications/${notificationId}/attachments/download`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          }
        }
      );

      if (response.ok) {
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          // Multiple attachments - show list
          const data = await response.json();
          console.log('Multiple attachments available:', data.data.attachments);
          toast.success(`${data.data.attachments.length} attachments available for download`);
          // You could open a modal here to show the list of attachments
        } else {
          // Single attachment - download directly
          const blob = await response.blob();
          const contentDisposition = response.headers.get('content-disposition');
          const filename = contentDisposition
            ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
            : 'attachment';

          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);

          toast.success('Attachment downloaded successfully');
        }
      } else {
        toast.error('Failed to download attachments');
      }
    } catch (error) {
      console.error('Error downloading attachments:', error);
      toast.error('Failed to download attachments');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      message: '',
      type: 'system',
      channels: [],
      priority: 'medium',
      audiences: [],
      manualEmails: '',
      manualPhones: '',
      scheduledAt: '',
      searchUsers: ''
    });
    setAttachments([]);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: Clock },
      sent: { color: 'bg-blue-100 text-blue-800', label: 'Sent', icon: Send },
      delivered: { color: 'bg-green-100 text-green-800', label: 'Delivered', icon: CheckCircle },
      read: { color: 'bg-green-100 text-green-800', label: 'Read', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', label: 'Failed', icon: XCircle },
      cancelled: { color: 'bg-gray-100 text-gray-800', label: 'Cancelled', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getChannelIcon = (channel: string) => {
    const icons = {
      email: Mail,
      sms: MessageSquare,
      app: Bell,
      push: Smartphone
    };
    const IconComponent = icons[channel as keyof typeof icons] || Bell;
    return <IconComponent className="h-4 w-4" />;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getFilteredNotifications = () => {
    return notifications.filter(notification => {
      const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.recipient.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.recipient.phone?.includes(searchQuery);

      const matchesStatus = statusFilter === 'all' || notification.status === statusFilter;
      const matchesType = typeFilter === 'all' || notification.type === typeFilter;
      const matchesChannel = channelFilter === 'all' || notification.channel === channelFilter;

      return matchesSearch && matchesStatus && matchesType && matchesChannel;
    });
  };

  const getPaginatedNotifications = () => {
    const filteredNotifications = getFilteredNotifications();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredNotifications.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const filteredNotifications = getFilteredNotifications();
    return Math.ceil(filteredNotifications.length / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'status':
        setStatusFilter(value);
        break;
      case 'type':
        setTypeFilter(value);
        break;
      case 'channel':
        setChannelFilter(value);
        break;
    }
    setCurrentPage(1);
  };

  const openCreateModal = () => {
    resetForm();
    setModalOpen(true);
  };

  const handleChannelToggle = (channelId: string) => {
    setFormData(prev => ({
      ...prev,
      channels: prev.channels.includes(channelId)
        ? prev.channels.filter(c => c !== channelId)
        : [...prev.channels, channelId]
    }));
  };

  const handleAudienceToggle = (audienceId: string) => {
    setFormData(prev => ({
      ...prev,
      audiences: prev.audiences.includes(audienceId)
        ? prev.audiences.filter(a => a !== audienceId)
        : [...prev.audiences, audienceId]
    }));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading notifications...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notifications Management</h1>
          <p className="text-muted-foreground">Send and manage notifications to users</p>
        </div>
        <Button onClick={openCreateModal}>
          <Plus className="h-4 w-4 mr-2" />
          Send Notification
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Bell className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{stats.totalNotifications}</p>
                <p className="text-sm text-muted-foreground">Total</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Send className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{stats.sentNotifications}</p>
                <p className="text-sm text-muted-foreground">Sent</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{stats.deliveredNotifications}</p>
                <p className="text-sm text-muted-foreground">Delivered</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{stats.readNotifications}</p>
                <p className="text-sm text-muted-foreground">Read</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{stats.pendingNotifications}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <XCircle className="h-8 w-8 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{stats.failedNotifications}</p>
                <p className="text-sm text-muted-foreground">Failed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notifications Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Notifications</CardTitle>
          <CardDescription>Manage and resend notifications</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="overflow-x-auto pb-2 mb-6">
            <div className="flex gap-4 items-center min-w-fit">
              <div className="flex items-center gap-2 min-w-fit">
                <Search className="h-4 w-4 flex-shrink-0" />
                <Input
                  placeholder="Search notifications, recipients..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-80 min-w-[200px]"
                />
              </div>

              <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="read">Read</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value) => handleFilterChange('type', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="tender">Tender</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="reminders">Reminders</SelectItem>
                  <SelectItem value="order">Order</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>

              <Select value={channelFilter} onValueChange={(value) => handleFilterChange('channel', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Channel" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Channels</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="app">In-App</SelectItem>
                  <SelectItem value="push">Push</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Table */}
          <div className="border rounded-lg overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Recipient</TableHead>
                  <TableHead>Channel</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Attachments</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getPaginatedNotifications().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                      No notifications found
                    </TableCell>
                  </TableRow>
                ) : (
                  getPaginatedNotifications().map((notification) => (
                    <TableRow key={notification._id}>
                      <TableCell className="break-words">
                        <div>
                          <div className="font-medium text-wrap">{notification.title}</div>
                          <div className="text-sm text-muted-foreground text-wrap">
                            {notification.message.length > 50
                              ? `${notification.message.substring(0, 50)}...`
                              : notification.message}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {notification.recipient.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {notification.recipient.email}
                            </div>
                          )}
                          {notification.recipient.phone && (
                            <div className="flex items-center gap-1">
                              <Smartphone className="h-3 w-3" />
                              {notification.recipient.phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getChannelIcon(notification.channel)}
                          <span className="capitalize">{notification.channel}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {notification.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(notification.priority)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(notification.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(notification.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        {notification.attachments && notification.attachments.length > 0 ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadAttachments(notification._id)}
                            title={`${notification.attachments.length} attachment(s)`}
                          >
                            <Paperclip className="h-4 w-4" />
                            <span className="ml-1">{notification.attachments.length}</span>
                          </Button>
                        ) : (
                          <span className="text-muted-foreground text-sm">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {(notification.status === 'failed') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => resendNotification(notification)}
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                          {notification.bulkId && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => resendBulkNotifications(notification.bulkId!)}
                              title="Resend all notifications with same bulk ID"
                            >
                              <Users className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {getFilteredNotifications().length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(getTotalPages(), currentPage + 1))}
                      className={currentPage === getTotalPages() ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="text-sm text-muted-foreground text-center mt-2">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredNotifications().length)} to {Math.min(currentPage * itemsPerPage, getFilteredNotifications().length)} of {getFilteredNotifications().length} notifications
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Send Notification Modal */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Send Notification</DialogTitle>
            <DialogDescription>
              Create and send notifications to users. Use {`{{name}}`} or {`{{order}}`} for dynamic content.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  placeholder="Notification title"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Type *</Label>
                <Select value={formData.type} onValueChange={(value: any) => setFormData({...formData, type: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="tender">Tender</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="reminders">Reminders</SelectItem>
                    <SelectItem value="order">Order</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Message *</Label>
              <Textarea
                id="message"
                placeholder="Notification message. Use {{name}} for user name, {{order}} for order details..."
                value={formData.message}
                onChange={(e) => setFormData({...formData, message: e.target.value})}
                rows={4}
              />
            </div>

            {/* Channels */}
            <div className="space-y-2">
              <Label>Channels *</Label>
              <div className="grid grid-cols-2 gap-4">
                {channelOptions.map((channel) => {
                  const IconComponent = channel.icon;
                  return (
                    <div key={channel.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={channel.id}
                        checked={formData.channels.includes(channel.id)}
                        onCheckedChange={() => handleChannelToggle(channel.id)}
                      />
                      <Label htmlFor={channel.id} className="flex items-center gap-2 cursor-pointer">
                        <IconComponent className="h-4 w-4" />
                        {channel.label}
                      </Label>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Priority and Scheduling */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value: any) => setFormData({...formData, priority: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="scheduledAt">Schedule (Optional)</Label>
                <Input
                  id="scheduledAt"
                  type="datetime-local"
                  value={formData.scheduledAt}
                  onChange={(e) => setFormData({...formData, scheduledAt: e.target.value})}
                />
              </div>
            </div>

            {/* Attachments */}
            <div className="space-y-2">
              <Label htmlFor="attachments">Attachments (Optional)</Label>
              <Input
                id="attachments"
                type="file"
                multiple
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  setAttachments(files);
                }}
                className="cursor-pointer"
              />
              {attachments.length > 0 && (
                <div className="text-sm text-muted-foreground">
                  {attachments.length} file(s) selected: {attachments.map(f => f.name).join(', ')}
                </div>
              )}
            </div>

            {/* Audience Selection */}
            <div className="space-y-2">
              <Label>Audience *</Label>
              <div className="grid grid-cols-1 gap-2">
                {audienceOptions.map((audience) => (
                  <div key={audience.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={audience.id}
                      checked={formData.audiences.includes(audience.id)}
                      onCheckedChange={() => handleAudienceToggle(audience.id)}
                    />
                    <Label htmlFor={audience.id} className="cursor-pointer">
                      <div>
                        <div className="font-medium">{audience.label}</div>
                        <div className="text-sm text-muted-foreground">{audience.description}</div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Manual Recipients */}
            {formData.audiences.includes('manual_emails') && (
              <div className="space-y-2">
                <Label htmlFor="manualEmails">Email Addresses</Label>
                <Textarea
                  id="manualEmails"
                  placeholder="Enter email addresses separated by commas"
                  value={formData.manualEmails}
                  onChange={(e) => setFormData({...formData, manualEmails: e.target.value})}
                  rows={3}
                />
              </div>
            )}

            {formData.audiences.includes('manual_phones') && (
              <div className="space-y-2">
                <Label htmlFor="manualPhones">Phone Numbers</Label>
                <Textarea
                  id="manualPhones"
                  placeholder="Enter phone numbers separated by commas (e.g., 254712345678, 0712345678)"
                  value={formData.manualPhones}
                  onChange={(e) => setFormData({...formData, manualPhones: e.target.value})}
                  rows={3}
                />
              </div>
            )}

            {formData.audiences.includes('search_users') && (
              <div className="space-y-2">
                <Label htmlFor="searchUsers">Search Users</Label>
                <Input
                  id="searchUsers"
                  placeholder="Search users by name, email, or company"
                  value={formData.searchUsers}
                  onChange={(e) => setFormData({...formData, searchUsers: e.target.value})}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={sendNotification} disabled={sending}>
              {sending ? 'Sending...' : (formData.scheduledAt ? 'Schedule Notification' : 'Send Notification')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
