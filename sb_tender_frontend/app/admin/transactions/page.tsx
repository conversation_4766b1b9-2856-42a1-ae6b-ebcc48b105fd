"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { 
  CreditCard, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  Search,
  Edit,
  Plus,
  Smartphone
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface Payment {
  _id: string;
  transactionId?: string;
  transID?: string;
  userId: string;
  spOrderId: string;
  amount: {
    $numberDecimal: string;
  } | number;
  mobile?: string;
  status: 'pending' | 'success' | 'failed' | 'cancelled';
  paymentMethod?: string;
  transactionType?: string;
  channel?: string;
  currency: string;
  respDesc?: string;
  reference?: string;
  reason?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  balance?: {
    $numberDecimal: string;
  } | number;
  // Order details (populated)
  order?: {
    _id: string;
    spCompanyId: string;
    status: string;
    totalAmount: {
      $numberDecimal: string;
    } | number;
  };
}

interface TransactionSummary {
  totalTransactions: number;
  totalAmount: number;
  successfulPayments: number;
  pendingPayments: number;
  failedPayments: number;
}

export default function AdminTransactionsPage() {
  const [transactions, setTransactions] = useState<Payment[]>([]);
  const [summary, setSummary] = useState<TransactionSummary>({
    totalTransactions: 0,
    totalAmount: 0,
    successfulPayments: 0,
    pendingPayments: 0,
    failedPayments: 0
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Filters and pagination
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'update' | 'create'>('update');
  const [selectedTransaction, setSelectedTransaction] = useState<Payment | null>(null);
  
  // Form state for creating/updating payments
  const [formData, setFormData] = useState({
    status: '',
    amount: '',
    mobile: '',
    orderId: '',
    supplierCompanyId: '',
    notes: ''
  });


  // Helper function to convert decimal values
  const getDecimalValue = (value: { $numberDecimal: string } | number): number => {
    if (typeof value === 'number') return value;
    return parseFloat(value.$numberDecimal) || 0;
  };

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      console.log('Fetching transactions...');
      const response = await fetch(
        `/api/admin/transactions`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        const transactionsData = data.data.transactions || [];
        console.log('Successfully fetched transactions:', transactionsData);
        
        setTransactions(transactionsData);
        calculateSummary(transactionsData);
        toast.success('Transactions loaded successfully');
      } else {
        console.log('Transactions API call failed with status:', response.status);
        toast.error('Failed to fetch transactions');
        
        // Fallback to empty array
        const mockTransactions: Payment[] = [];
        setTransactions(mockTransactions);
        calculateSummary(mockTransactions);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to fetch transactions');
      
      // Fallback to empty array
      const mockTransactions: Payment[] = [];
      setTransactions(mockTransactions);
      calculateSummary(mockTransactions);
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = (transactionsData: Payment[]) => {
    const summary: TransactionSummary = {
      totalTransactions: transactionsData.length,
      totalAmount: transactionsData
        .filter(transaction => transaction.status === 'success')
        .reduce((sum, transaction) => sum + getDecimalValue(transaction.amount), 0),
      successfulPayments: transactionsData.filter(transaction => transaction.status === 'success').length,
      pendingPayments: transactionsData.filter(transaction => transaction.status === 'pending').length,
      failedPayments: transactionsData.filter(transaction => transaction.status === 'failed').length
    };
    setSummary(summary);
  };

  const updateTransactionStatus = async () => {
    if (!selectedTransaction) return;

    try {
      setUpdating(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/transactions/${selectedTransaction._id}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: formData.status,
            notes: formData.notes
          })
        }
      );

      if (response.ok) {
        toast.success('Transaction updated successfully');
        setModalOpen(false);
        fetchTransactions(); // Refresh the transactions list
      } else {
        toast.error('Failed to update transaction');
      }
    } catch (error) {
      console.error('Error updating transaction:', error);
      toast.error('Failed to update transaction');
    } finally {
      setUpdating(false);
    }
  };

  const createManualPayment = async () => {
    try {
      setUpdating(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `/api/admin/transactions`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            amount: parseFloat(formData.amount),
            mobile: formData.mobile,
            orderId: formData.orderId,
            supplierCompanyId: formData.supplierCompanyId,
            paymentMethod: 'manual',
            transactionType: 'Manual',
            channel: 'offline',
            currency: 'KES',
            reason: 'manual_payment',
            status: 'success',
            respDesc: 'Manual payment created by admin'
          })
        }
      );

      if (response.ok) {
        toast.success('Manual payment created successfully');
        setModalOpen(false);
        fetchTransactions(); // Refresh the transactions list
        resetForm();
      } else {
        toast.error('Failed to create manual payment');
      }
    } catch (error) {
      console.error('Error creating manual payment:', error);
      toast.error('Failed to create manual payment');
    } finally {
      setUpdating(false);
    }
  };

  const resetForm = () => {
    setFormData({
      status: '',
      amount: '',
      mobile: '',
      orderId: '',
      supplierCompanyId: '',
      notes: ''
    });
  };

  const openUpdateModal = (transaction: Payment) => {
    setSelectedTransaction(transaction);
    setModalType('update');
    setFormData({
      status: transaction.status,
      amount: getDecimalValue(transaction.amount).toString(),
      mobile: transaction.mobile || '',
      orderId: transaction.spOrderId._id,
      supplierCompanyId: transaction.spOrderId?.spCompanyId?._id || '',
      notes: ''
    });
    setModalOpen(true);
  };

  const openCreateModal = () => {
    setSelectedTransaction(null);
    setModalType('create');
    resetForm();
    setModalOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: Clock },
      success: { color: 'bg-green-100 text-green-800', label: 'Success', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', label: 'Failed', icon: XCircle },
      cancelled: { color: 'bg-gray-100 text-gray-800', label: 'Cancelled', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getFilteredTransactions = () => {
    return transactions.filter(transaction => {
      const matchesSearch = transaction._id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          transaction.transID?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          transaction.mobile?.includes(searchQuery) ||
                          transaction.spOrderId.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  };

  const getPaginatedTransactions = () => {
    const filteredTransactions = getFilteredTransactions();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredTransactions.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const filteredTransactions = getFilteredTransactions();
    return Math.ceil(filteredTransactions.length / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  const handleSubmit = () => {
    if (modalType === 'create') {
      createManualPayment();
    } else {
      updateTransactionStatus();
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading transactions...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Transactions Management</h1>
          <p className="text-muted-foreground">Manage payments and create manual transactions</p>
        </div>
        <Button onClick={openCreateModal}>
          <Plus className="h-4 w-4 mr-2" />
          Create Manual Payment
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CreditCard className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{summary.totalTransactions}</p>
                <p className="text-sm text-muted-foreground">Total Transactions</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">KES {summary.totalAmount.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">Total Amount</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{summary.successfulPayments}</p>
                <p className="text-sm text-muted-foreground">Successful</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{summary.pendingPayments}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <XCircle className="h-8 w-8 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{summary.failedPayments}</p>
                <p className="text-sm text-muted-foreground">Failed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Transactions</CardTitle>
          <CardDescription>Manage payment transactions and update status</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="overflow-x-auto pb-2 mb-6">
            <div className="flex gap-4 items-center min-w-fit">
              <div className="flex items-center gap-2 min-w-fit">
                <Search className="h-4 w-4 flex-shrink-0" />
                <Input
                  placeholder="Search transactions, order ID, mobile..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-80 min-w-[200px]"
                />
              </div>

              <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Table */}
          <div className="border rounded-lg overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Mobile</TableHead>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getPaginatedTransactions().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  getPaginatedTransactions().map((transaction) => (
                    <TableRow key={transaction._id}>
                      <TableCell className="font-mono text-sm">
                        {transaction.transID || transaction._id.slice(-8)}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {transaction.spOrderId._id.slice(-8)}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          KES {getDecimalValue(transaction.amount).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {transaction.mobile && <Smartphone className="h-4 w-4" />}
                          {transaction.mobile || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell className="break-words">
                        <div className="text-sm">
                          <div>{transaction.transactionType || transaction.paymentMethod || 'N/A'}</div>
                          <div className="text-muted-foreground text-xs">{transaction.channel}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(transaction.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(transaction.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openUpdateModal(transaction)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {getFilteredTransactions().length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(getTotalPages(), currentPage + 1))}
                      className={currentPage === getTotalPages() ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="text-sm text-muted-foreground text-center mt-2">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredTransactions().length)} to {Math.min(currentPage * itemsPerPage, getFilteredTransactions().length)} of {getFilteredTransactions().length} transactions
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Update Transaction Modal */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {modalType === 'create' ? 'Create Manual Payment' : 'Update Transaction Status'}
            </DialogTitle>
            <DialogDescription>
              {modalType === 'create'
                ? 'Create a manual payment for an existing order. This will update the order status to paid.'
                : `Update the status for transaction ${selectedTransaction?._id.slice(-8)}`
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {modalType === 'create' ? (
              <>
                {/* Create Manual Payment Form */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="orderId">Order ID *</Label>
                    <Input
                      id="orderId"
                      placeholder="Enter order ID"
                      value={formData.orderId}
                      onChange={(e) => setFormData({...formData, orderId: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="supplierCompanyId">Supplier Company ID *</Label>
                    <Input
                      id="supplierCompanyId"
                      placeholder="Enter supplier company ID"
                      value={formData.supplierCompanyId}
                      onChange={(e) => setFormData({...formData, supplierCompanyId: e.target.value})}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (KES) *</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={formData.amount}
                      onChange={(e) => setFormData({...formData, amount: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="mobile">Mobile Number</Label>
                    <Input
                      id="mobile"
                      placeholder="254XXXXXXXXX"
                      value={formData.mobile}
                      onChange={(e) => setFormData({...formData, mobile: e.target.value})}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add notes about this manual payment..."
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    rows={3}
                  />
                </div>
              </>
            ) : (
              <>
                {/* Update Transaction Status Form */}
                {selectedTransaction && (
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Transaction Details</h4>
                    <div className="space-y-1 text-sm">
                      <div><strong>Transaction ID:</strong> {selectedTransaction.transID || selectedTransaction._id.slice(-8)}</div>
                      <div><strong>Order ID:</strong> {selectedTransaction.spOrderId._id.slice(-8)}</div>
                      <div><strong>Amount:</strong> KES {getDecimalValue(selectedTransaction.amount).toLocaleString()}</div>
                      <div><strong>Mobile:</strong> {selectedTransaction.mobile || 'N/A'}</div>
                      <div><strong>Current Status:</strong> {getStatusBadge(selectedTransaction.status)}</div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="status">New Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add notes about this status update..."
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    rows={3}
                  />
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={updating}>
              {updating ? 'Processing...' : (modalType === 'create' ? 'Create Payment' : 'Update Status')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
