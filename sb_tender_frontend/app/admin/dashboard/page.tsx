"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Building,
  FileText,
  AlertCircle,
  Activity,
  DollarSign,
  BarChart as ChartIcon,
  Settings,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Bell,
  CreditCard,
  Eye,
  MoreHorizontal,
  Calendar,
  Target,
  CheckCircle,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import Link from "next/link";
import toast from "react-hot-toast";

interface DashboardStats {
  totalUsers: number;
  totalCompanies: number;
  totalJobs: number;
  totalApplications: number;
  weeklyGrowth: number;
  monthlyRevenue: number;
  revenueGrowth: number;
}

interface ActivityData {
  name: string;
  sales: number;
  views: number;
}

interface RecentActivity {
  _id: string;
  description: string;
  timestamp: string;
  action: string;
  userId?: {
    name: string;
  };
}

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const getAuthToken = () => {
    return document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const token = getAuthToken();

      if (!token) {
        throw new Error('Authentication token not found');
      }

      console.log('Fetching dashboard data...');
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('Dashboard data received:', data);

      if (data.success) {
        setDashboardData(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch dashboard data');
      }

    } catch (err: any) {
      console.error('Dashboard fetch error:', err);
      setError(err.message);
      toast.error(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6 bg-background min-h-screen">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div>
            <div className="w-48 h-8 bg-muted rounded animate-pulse mb-2"></div>
            <div className="w-64 h-4 bg-muted rounded animate-pulse"></div>
          </div>
          <div className="w-32 h-10 bg-muted rounded animate-pulse"></div>
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-muted rounded animate-pulse"></div>
                <div className="w-16 h-6 bg-muted rounded animate-pulse"></div>
              </div>
              <div className="w-20 h-8 bg-muted rounded animate-pulse mb-2"></div>
              <div className="w-24 h-4 bg-muted rounded animate-pulse"></div>
            </div>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="w-40 h-6 bg-muted rounded animate-pulse mb-4"></div>
            <div className="w-full h-64 bg-muted rounded animate-pulse"></div>
          </div>
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="w-32 h-6 bg-muted rounded animate-pulse mb-4"></div>
            <div className="w-full h-64 bg-muted rounded animate-pulse"></div>
          </div>
        </div>

        {/* Tables Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-card border border-border rounded-lg p-6">
              <div className="w-32 h-6 bg-muted rounded animate-pulse mb-4"></div>
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map((j) => (
                  <div key={j} className="flex items-center justify-between">
                    <div className="w-32 h-4 bg-muted rounded animate-pulse"></div>
                    <div className="w-16 h-4 bg-muted rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-background min-h-screen">
        <div className="text-center py-8">
          <div className="text-destructive mb-4">Error loading dashboard: {error}</div>
          <Button onClick={fetchDashboardData}>Retry</Button>
        </div>
      </div>
    );
  }

  // Get data with fallbacks
  const statistics = dashboardData?.statistics || {};
  const chartData = dashboardData?.chartData || [];
  const recentApplications = dashboardData?.recentApplications || [];
  const systemAlerts = dashboardData?.systemAlerts || [];
  const recentPayments = dashboardData?.recentPayments || [];

  return (
    <div className="min-h-screen bg-background p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground mt-1">Welcome back! Here's what's happening.</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Last 30 days
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* Total Revenue Card - Large */}
        <div className="col-span-12 lg:col-span-5">
          <Card className="bg-card border border-border shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-muted-foreground">Total Revenue</p>
                  <div className="flex items-center space-x-2">
                    <h2 className="text-3xl font-bold text-card-foreground">
                      KES {(statistics.totalRevenue || 0).toLocaleString()}
                    </h2>
                    <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {statistics.revenueGrowth || 0}%
                    </div>
                  </div>
                </div>
              </div>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="applications"
                      stroke="#10b981"
                      strokeWidth={2}
                      fill="url(#salesGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats Cards - 4 small cards */}
        <div className="col-span-12 lg:col-span-7">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-card border border-border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-blue-50 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-3">
                    <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold text-card-foreground">{statistics.openCategories || 0}</p>
                  <p className="text-sm text-muted-foreground">Open Categories</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-green-50 dark:bg-green-900 rounded-lg flex items-center justify-center mb-3">
                    <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-2xl font-bold text-card-foreground">{statistics.activeBids || 0}</p>
                  <p className="text-sm text-muted-foreground">Active Bids</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-orange-50 dark:bg-orange-900 rounded-lg flex items-center justify-center mb-3">
                    <Activity className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <p className="text-2xl font-bold text-card-foreground">{statistics.activeUsers || 0}</p>
                  <p className="text-sm text-muted-foreground">Active Users</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border border-border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-purple-50 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-3">
                    <CheckCircle className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold text-card-foreground">{statistics.awards || 0}</p>
                  <p className="text-sm text-muted-foreground">Awards</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Total Users Card */}
        <div className="col-span-12 lg:col-span-3">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-900 mb-2">2.4K</p>
                <div className="h-24 mb-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData.slice(0, 6)}>
                      <Bar dataKey="applications" fill="#10b981" radius={[2, 2, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex items-center justify-center text-sm text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  12.5% from last month
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Companies Card */}
        <div className="col-span-12 lg:col-span-3">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Active Users</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-900 mb-2">156</p>
                <div className="relative w-24 h-24 mx-auto mb-4">
                  <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeDasharray="65, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold text-blue-600">65%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">Logged in last 30 days</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications Submitted Chart */}
        <div className="col-span-12 lg:col-span-6">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Bids Submitted</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData} barCategoryGap="20%">
                    <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar dataKey="applications" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Applications" />
                    <Bar dataKey="categories" fill="#8b5cf6" radius={[4, 4, 0, 0]} name="Categories" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Third Row - Performance & Goals */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Platform Performance */}
        <div className="col-span-12 lg:col-span-6">
          <Card className="bg-white border-0 shadow-sm h-full">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Platform Performance</h3>
                  <p className="text-sm text-gray-600 mt-1">System uptime and response times</p>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">System Uptime</span>
                    <span className="text-gray-900 font-medium">99.8%</span>
                  </div>
                  <Progress value={99.8} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Response Time</span>
                    <span className="text-gray-900 font-medium">142ms</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">API Success Rate</span>
                    <span className="text-gray-900 font-medium">98.5%</span>
                  </div>
                  <Progress value={98.5} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Targets & Quarterly Growth */}
        <div className="col-span-12 lg:col-span-6">
          <div className="grid grid-cols-2 gap-4 h-full">
            {/* Monthly Targets */}
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-600 mb-4">Buyer Satisfaction [soon]</h4>
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="3"
                      strokeDasharray="72, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-gray-900">+12%</p>
                  <p className="text-sm text-green-600">vs last quarter</p>
                </div>
              </CardContent>
            </Card>

            {/* Quarterly Growth */}
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-600 mb-4">Supplier satisfaction [soon]</h4>
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#10b981"
                      strokeWidth="3"
                      strokeDasharray="84, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-gray-900">+24.9%</p>
                  <p className="text-sm text-green-600">vs last quarter</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Fourth Row - Management Sections */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Recent Bids */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-card border border-border shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-card-foreground">Recent Bids</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                {recentApplications.length > 0 ? recentApplications.map((application: any) => (
                  <div key={application._id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        application.status === 'approved' ? 'bg-green-100 dark:bg-green-900' :
                        application.status === 'submitted' ? 'bg-blue-100 dark:bg-blue-900' :
                        application.status === 'under_review' ? 'bg-yellow-100 dark:bg-yellow-900' :
                        'bg-gray-100 dark:bg-gray-900'
                      }`}>
                        <FileText className={`h-4 w-4 ${
                          application.status === 'approved' ? 'text-green-600 dark:text-green-400' :
                          application.status === 'submitted' ? 'text-blue-600 dark:text-blue-400' :
                          application.status === 'under_review' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium text-card-foreground text-sm">{application.supplierName}</p>
                        <p className="text-xs text-muted-foreground">{application.categoryTitle}</p>
                        <p className="text-xs text-muted-foreground">
                          Score: {application.totalScore} • {new Date(application.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground opacity-50 mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No recent applications</p>
                  </div>
                )}

                <Link href="/admin/jobs">
                  <Button variant="outline" className="w-full mt-4">
                    View All Applications
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Alerts */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-card border border-border shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-card-foreground">System Alerts</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                {systemAlerts.length > 0 ? systemAlerts.map((alert: any) => (
                  <div key={alert._id} className={`flex items-center space-x-3 p-3 rounded-lg ${
                    alert.type === 'error' ? 'bg-red-50 dark:bg-red-900/20' :
                    alert.type === 'warning' ? 'bg-orange-50 dark:bg-orange-900/20' :
                    alert.type === 'success' ? 'bg-green-50 dark:bg-green-900/20' :
                    'bg-blue-50 dark:bg-blue-900/20'
                  }`}>
                    <AlertCircle className={`h-5 w-5 ${
                      alert.type === 'error' ? 'text-red-600 dark:text-red-400' :
                      alert.type === 'warning' ? 'text-orange-600 dark:text-orange-400' :
                      alert.type === 'success' ? 'text-green-600 dark:text-green-400' :
                      'text-blue-600 dark:text-blue-400'
                    }`} />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-card-foreground">{alert.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {alert.userName} • {new Date(alert.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-muted-foreground opacity-50 mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No recent alerts</p>
                  </div>
                )}

                <Link href="/admin/notifications">
                  <Button variant="outline" className="w-full mt-4">
                    View All Alerts
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-card border border-border shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-card-foreground">Recent Transactions</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                {recentPayments.length > 0 ? recentPayments.map((payment: any) => (
                  <div key={payment._id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        payment.status === 'successful' ? 'bg-green-100 dark:bg-green-900' :
                        payment.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900' :
                        payment.status === 'failed' ? 'bg-red-100 dark:bg-red-900' :
                        'bg-blue-100 dark:bg-blue-900'
                      }`}>
                        <CreditCard className={`h-4 w-4 ${
                          payment.status === 'successful' ? 'text-green-600 dark:text-green-400' :
                          payment.status === 'pending' ? 'text-yellow-600 dark:text-yellow-400' :
                          payment.status === 'failed' ? 'text-red-600 dark:text-red-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium text-card-foreground text-sm">
                          {payment.paymentMethod || 'Payment'} - {payment.companyName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(payment.createdAt).toLocaleDateString()} • {payment.status}
                        </p>
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${
                      payment.status === 'successful' ? 'text-green-600 dark:text-green-400' :
                      payment.status === 'pending' ? 'text-yellow-600 dark:text-yellow-400' :
                      payment.status === 'failed' ? 'text-red-600 dark:text-red-400' :
                      'text-blue-600 dark:text-blue-400'
                    }`}>
                      {payment.status === 'successful' ? '+' : ''}KES {(payment.amount || 0).toLocaleString()}
                    </span>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <CreditCard className="h-12 w-12 text-muted-foreground opacity-50 mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No recent transactions</p>
                  </div>
                )}

                <Link href="/admin/transactions">
                  <Button variant="outline" className="w-full mt-4">
                    View All Transactions
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}