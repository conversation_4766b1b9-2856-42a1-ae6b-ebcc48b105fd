"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Building,
  FileText,
  AlertCircle,
  Activity,
  DollarSign,
  BarChart as ChartIcon,
  Settings,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Bell,
  CreditCard,
  Eye,
  MoreHorizontal,
  Calendar,
  Target,
  Briefcase,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import Link from "next/link";
import toast from "react-hot-toast";

interface DashboardStats {
  totalUsers: number;
  totalCompanies: number;
  totalJobs: number;
  totalApplications: number;
  weeklyGrowth: number;
  monthlyRevenue: number;
  revenueGrowth: number;
}

interface ActivityData {
  name: string;
  sales: number;
  views: number;
}

interface RecentActivity {
  _id: string;
  description: string;
  timestamp: string;
  action: string;
  userId?: {
    name: string;
  };
}

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalCompanies: 0,
    totalJobs: 0,
    totalApplications: 0,
    weeklyGrowth: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
  });
  const [activityData, setActivityData] = useState<ActivityData[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [recentJobs, setRecentJobs] = useState([]);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const getAuthToken = () => {
    return document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        toast.error('Authentication required');
        return;
      }

      // Fetch all data in parallel
      const [companiesRes, jobsRes, activitiesRes] = await Promise.all([
        fetch('/api/companies', {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch('/api/jobs', {
          headers: { Authorization: `Bearer ${token}` }
        }),
        fetch(`${BACKEND_API_URL}/api/admin/activities`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
      ]);

      const [companies, jobs, activities] = await Promise.all([
        companiesRes.json(),
        jobsRes.json(),
        activitiesRes.json(),
      ]);

      // Use dummy data for visual demonstration.
      setStats({
        totalUsers: 2400,
        totalCompanies: 156,
        totalJobs: 85,
        totalApplications: 342,
        weeklyGrowth: 8.6,
        monthlyRevenue: 9568,
        revenueGrowth: 8.6,
      });

      // Set dummy recent jobs
      setRecentJobs([
        { _id: '1', title: 'Road Construction Project', status: 'Active', createdAt: new Date() },
        { _id: '2', title: 'IT Equipment Procurement', status: 'Pending', createdAt: new Date() },
        { _id: '3', title: 'Medical Supplies Tender', status: 'Review', createdAt: new Date() },
        { _id: '4', title: 'Office Furniture Supply', status: 'Active', createdAt: new Date() },
        { _id: '5', title: 'Security Services Contract', status: 'Closed', createdAt: new Date() },
      ]);

      // Set dummy recent activities
      setRecentActivities([
        { _id: '1', description: 'New supplier registration', timestamp: new Date().toISOString(), action: 'create' },
        { _id: '2', description: 'Payment processed', timestamp: new Date().toISOString(), action: 'update' },
        { _id: '3', description: 'Tender published', timestamp: new Date().toISOString(), action: 'create' },
        { _id: '4', description: 'Application submitted', timestamp: new Date().toISOString(), action: 'create' },
        { _id: '5', description: 'Contract awarded', timestamp: new Date().toISOString(), action: 'update' },
      ]);

      // Generate realistic activity chart data
      setActivityData([
        { name: "Jan", sales: 20, views: 40 },
        { name: "Feb", sales: 15, views: 30 },
        { name: "Mar", sales: 35, views: 65 },
        { name: "Apr", sales: 25, views: 45 },
        { name: "May", sales: 45, views: 75 },
        { name: "Jun", sales: 30, views: 55 },
        { name: "Jul", sales: 50, views: 85 },
        { name: "Aug", sales: 35, views: 60 },
        { name: "Sep", sales: 55, views: 90 },
      ]);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50/30 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/30 p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's what's happening.</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Last 30 days
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* Weekly Sales Card - Large */}
        <div className="col-span-12 lg:col-span-5">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                  <div className="flex items-center space-x-2">
                    <h2 className="text-3xl font-bold text-gray-900">KES 2,568,000</h2>
                    <div className="flex items-center text-green-600 text-sm">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      8.6%
                    </div>
                  </div>
                </div>
              </div>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={activityData}>
                    <defs>
                      <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="sales"
                      stroke="#10b981"
                      strokeWidth={2}
                      fill="url(#salesGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats Cards - 4 small cards */}
        <div className="col-span-12 lg:col-span-7">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-3">
                    <Briefcase className="h-6 w-6 text-blue-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">85</p>
                  <p className="text-sm text-gray-600">Open Categories</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center mb-3">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">KES 16M</p>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-orange-50 rounded-lg flex items-center justify-center mb-3">
                    <AlertCircle className="h-6 w-6 text-orange-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">23</p>
                  <p className="text-sm text-gray-600">Pending Approvals</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center mb-3">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">47</p>
                  <p className="text-sm text-gray-600">New Registrations</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Total Users Card */}
        <div className="col-span-12 lg:col-span-3">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-900 mb-2">2.4K</p>
                <div className="h-24 mb-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={activityData.slice(0, 6)}>
                      <Bar dataKey="sales" fill="#ef4444" radius={[2, 2, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex items-center justify-center text-sm text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  12.5% from last month
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Companies Card */}
        <div className="col-span-12 lg:col-span-3">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Active Companies</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-900 mb-2">156</p>
                <div className="relative w-24 h-24 mx-auto mb-4">
                  <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeDasharray="65, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold text-blue-600">65%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">Verified and active companies</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications Submitted Chart */}
        <div className="col-span-12 lg:col-span-6">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Applications Submitted</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={activityData} barCategoryGap="20%">
                    <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar dataKey="sales" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Applications" />
                    <Bar dataKey="views" fill="#8b5cf6" radius={[4, 4, 0, 0]} name="Reviews" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Third Row - Performance & Goals */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Platform Performance */}
        <div className="col-span-12 lg:col-span-6">
          <Card className="bg-white border-0 shadow-sm h-full">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Platform Performance</h3>
                  <p className="text-sm text-gray-600 mt-1">System uptime and response times</p>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">System Uptime</span>
                    <span className="text-gray-900 font-medium">99.8%</span>
                  </div>
                  <Progress value={99.8} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Response Time</span>
                    <span className="text-gray-900 font-medium">142ms</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">API Success Rate</span>
                    <span className="text-gray-900 font-medium">98.5%</span>
                  </div>
                  <Progress value={98.5} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Targets & Quarterly Growth */}
        <div className="col-span-12 lg:col-span-6">
          <div className="grid grid-cols-2 gap-4 h-full">
            {/* Monthly Targets */}
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-600 mb-4">Monthly Targets</h4>
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="3"
                      strokeDasharray="72, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-gray-900">KES 12M</p>
                  <p className="text-sm text-green-600">72% of target</p>
                </div>
              </CardContent>
            </Card>

            {/* Quarterly Growth */}
            <Card className="bg-white border-0 shadow-sm">
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-600 mb-4">Quarterly Growth</h4>
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#10b981"
                      strokeWidth="3"
                      strokeDasharray="84, 100"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-gray-900">+24.9%</p>
                  <p className="text-sm text-green-600">vs last quarter</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Fourth Row - Management Sections */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Recent Tenders */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Tenders</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Road Construction Tender</p>
                      <p className="text-xs text-gray-500">Published today</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <FileText className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">IT Equipment Supply</p>
                      <p className="text-xs text-gray-500">2 days ago</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <FileText className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Medical Supplies</p>
                      <p className="text-xs text-gray-500">1 week ago</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>

                <Link href="/admin/jobs">
                  <Button variant="outline" className="w-full mt-4">
                    View All Tenders
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Alerts */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">System Alerts</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Failed Payment</p>
                    <p className="text-xs text-gray-500">ABC Corp - 5 min ago</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-orange-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Pending Approval</p>
                    <p className="text-xs text-gray-500">XYZ Ltd - 15 min ago</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">New Registration</p>
                    <p className="text-xs text-gray-500">Tech Solutions - 1 hour ago</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4">
                  View All Alerts
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Payment Received</p>
                      <p className="text-xs text-gray-500">Today, 2:30 PM</p>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-green-600">+$2,450</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Contract Payment</p>
                      <p className="text-xs text-gray-500">Yesterday, 4:15 PM</p>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-green-600">+$5,200</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Tender Fee</p>
                      <p className="text-xs text-gray-500">2 days ago</p>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-green-600">+$850</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-orange-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">Subscription</p>
                      <p className="text-xs text-gray-500">3 days ago</p>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-green-600">+$1,200</span>
                </div>

                <Button variant="outline" className="w-full mt-4">
                  View All Transactions
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}