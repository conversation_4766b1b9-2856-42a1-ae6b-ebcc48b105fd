'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { <PERSON><PERSON> } from "@/components/ui/button"
import * as XLSX from 'xlsx';
import { useParams } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select } from '@/components/ui/select';
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  <PERSON>ting<PERSON>,
  Upload,
  X,
  Calendar,
  User,
  Building2,
  CheckSquare
} from 'lucide-react'

// Type definitions
interface Category {
  _id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  spCompanyId?: {
    _id: string;
    name: string;
    type: string;
  };
  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

interface Field {
  name: string;
  label: string;
  type: string;
  helpText: string;
  placeholder: string;
  tooltip: string;
  description: string;
  required: boolean;
  editable: boolean;
  visible: boolean;
  maxScore: number | null;
  options: string[];
  subgroup: string;
  group: string;
  order: number;
  isRequired?: boolean;
  isEditable?: boolean;
  isVisible?: boolean;
  subGroup?: string;
}

interface CategoryTemplate {
  _id: string;
  fields: Field[];
  spBuyerCategoryId: string;
}

interface ExcelCategory {
  code: string;
  description: string;
  systemCategory: string;
  type: string;
}

const CategoriesPage = () => {
  const form = useForm({
    defaultValues: {
      name: '',
      description: '',
      type: '',
      status: 'active'
    }
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedJobCategories, setSelectedJobCategories] = useState<string[]>([]);
  const [newJobFormData, setNewJobFormData] = useState({
    title: "",
    description: "",
    contract: "",
    starts: "",
    ends: "",
    location: "",
    categoryPrice: "",
    passMark: "",
    status: "draft",
    type: ""
  });
//template
const [categoryTemplate, setCategoryTemplate] = useState<CategoryTemplate | null>(null);
const [isTemplateFieldsModalVisible, setIsTemplateFieldsModalVisible] = useState(false);
const [editedField, setEditedField] = useState<Field | null>(null);
//xls
const [excelCategories, setExcelCategories] = useState<ExcelCategory[]>([]);
const [isExcelUploadVisible, setIsExcelUploadVisible] = useState(false);
const params = useParams();
const [companyId, setCompanyId] = useState<string | null>(null);
const [systemCategories, setSystemCategories] = useState<any[]>([]);

useEffect(() => {
  if (params?.companyId && typeof params.companyId === 'string') {
    setCompanyId(params.companyId);
  }
}, [params]);



  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);

  useEffect(() => {
    if (editingCategory) {
      form.reset({
        name: editingCategory.name,
        description: editingCategory.description,
        type: editingCategory.type,
        status: editingCategory.status
      });
    } else {
      form.reset({
        name: '',
        description: '',
        type: '',
        status: 'active'
      });
    }
  }, [editingCategory, form]);

  useEffect(() => {
    const fetchSystemCategories = async () => {
      try {
        const { data } = await axios.get(`/api/admin/categories`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        console.log('System categories', data);
        setSystemCategories(data);
      } catch (error) {
        toast.error('Failed to fetch system categories');
      }
    };
  
    if (token) {
      fetchSystemCategories();
    }
  }, [token]);

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`/api/admin/companies/${companyId}/categories`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      setCategories(data.data?.categories);
    } catch (error) {
      toast.error('Failed to fetch categories');
    }
  };
  
  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddCategory =  () => {
    setEditingCategory(null);
    setIsModalVisible(true);
  };


  useEffect(() => {
    console.log('selectedJobCategories changed:', selectedJobCategories);
  }, [selectedJobCategories]);


  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setIsModalVisible(true);
  };

  const handleDeleteCategory = async (categoryId) => {
    try {
      await axios.delete(`/api/admin/companies/${companyId}/categories/${categoryId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      toast.success('Company deleted successfully');
      fetchCategories();
    } catch (error) {
      toast.error('Failed to delete category');
    }
  };

  const onSubmit = async (values) => {
    console.log("Form values:", values);
    console.log("category update token", token);
    setIsSaving(true);
    try {
      if (editingCategory) {
        await axios.put(`/api/admin/companies/${companyId}/categories/${editingCategory._id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      } else {
        await axios.post(`/api/admin/companies/${companyId}/categories`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      toast.success('Category saved successfully');
      //reset the form
      fetchCategories();
    } catch (error) {
      toast.error('Failed to save category');
    } finally {
      setTimeout(()=>
      setIsSaving(false), 2000);
      setIsModalVisible(false);
    }
  };

  const openTemplateFieldsModal = async (category) => {
    try {
      const { data } = await axios.get(`/api/admin/companies/${companyId}/categories/${category._id}/template`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setCategoryTemplate(data);
      setIsTemplateFieldsModalVisible(true);
    } catch (error) {
      toast.error('Failed to fetch category criteria');
    }
  };


  const openFieldEditModal = (field) => setEditedField(field);
  const closeFieldEditModal = () => setEditedField(null);

  const handleFieldChange = (key, value) => {
    setEditedField((prev) => ({ ...prev, [key]: value }));
  };

  const handleTemplateUpdate = async (updatedTemplate) => {
    try {
      const { data } = await axios.put(
        `/api/admin/companies/${companyId}/categories/${updatedTemplate.spBuyerCategoryId}/templates/${updatedTemplate._id}`,
        { categoryTemplate: updatedTemplate },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setCategoryTemplate(data);
      toast.success('Template updated successfully');
    } catch (error) {
      toast.error('Failed to update template');
    }
  };
  
  const saveEditedField = async () => {
    try {
      console.log('Edited field:', editedField);
      const fieldExists = categoryTemplate.fields.some((field) => field.name === editedField.name);
  
      // Update or add the field
      const updatedFields = fieldExists
        ? categoryTemplate.fields.map((field) =>
            field.name === editedField.name ? editedField : field
          )
        : [...categoryTemplate.fields, editedField]; // Add new field if it doesn't exist
  
      const updatedTemplate = { ...categoryTemplate, fields: updatedFields };
      console.log(updatedTemplate);
      setCategoryTemplate(updatedTemplate);
        await handleTemplateUpdate(updatedTemplate);
  
      setEditedField(null);
    } catch (err) {
      console.error('Failed to update field');
    }
  };

  const deleteField = async (fieldName) => {
    try {
      // Filter out the field matching the fieldName
      const updatedFields = categoryTemplate.fields.filter((field) => field.name !== fieldName);
  
      // Update the state and persist the changes
      setCategoryTemplate((prev) => ({ ...prev, fields: updatedFields }));
      const updatedTemplate = { ...categoryTemplate, fields: updatedFields };
      await handleTemplateUpdate(updatedTemplate);
  
      toast.success('Field deleted');
    } catch (err) {
      toast.error('Failed to delete field');
    }
  };

  const addField = async () => {
    const newField = {
      name: '',
      label: '',
      type: 'text',
      helpText: '',
      placeholder: '',
      tooltip: '',
      description: '',
      required: false,
      editable: true,
      visible: true,
      maxScore: null,
      options: [],
      subgroup: '',
      group: '',
      order: 0,
    };
    const updatedFields = [...categoryTemplate.fields, newField];
  //
    try { 
      setEditedField(newField);
    } catch (err) {
      toast.error('Failed to add field');
    }
  };

const handleExcelUpload = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (event) => {
    try {
      const wb = XLSX.read(event.target.result, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws);
      
      console.log("Raw Excel data:", data); // For debugging
      
      // Extract using the correct column names
      const parsedCategories = data
        .filter(row => row['CATEGORY CODE'] && row['CATEGORY DESCRIPTION']) // Only include rows with both values
        .map(row => ({
          code: row['CATEGORY CODE'] || '',
          description: row['CATEGORY DESCRIPTION'] || '',
          systemCategory: '', // To be selected by user
          type: 'supplier_registration' // Default value
        }));
      
      console.log("Parsed categories:", parsedCategories); // For debugging
      
      if (parsedCategories.length === 0) {
        toast.error('No valid categories found. Please check your Excel format.');
        return;
      }
      
      setExcelCategories(parsedCategories);
      setIsExcelUploadVisible(true);
    } catch (error) {
      toast.error('Failed to parse Excel file');
      console.error(error);
    }
  };
  
  reader.readAsBinaryString(file);
};
  
  // Function to create categories from Excel data
  const handleCreateCategoriesFromExcel = async () => {
    // Validate if all excel categories have a systemCategory selected
    const isValid = excelCategories.every(cat => cat.systemCategory);
    
    if (!isValid) {
      toast.error('Please select a system category for all rows');
      return;
    }
    
    setIsSaving(true);
    try {
      for (const category of excelCategories) {
        await axios.post(`/api/companies/${companyId}/categories`, {
          name: category.code,
          description: category.description,
          type: category.type,
          spCategoryId: category.systemCategory,
          status: 'active'
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      
      toast.success('Categories created successfully');
      fetchCategories();
      setExcelCategories([]);
      setIsExcelUploadVisible(false);
    } catch (error) {
      toast.error('Failed to create categories');
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Function to update a specific Excel category row
  const updateExcelCategory = (index, field, value) => {
    const updatedCategories = [...excelCategories];
    updatedCategories[index][field] = value;
    setExcelCategories(updatedCategories);
  };



  const handleNewJobSubmit = async () => {
    if(selectedJobCategories.length === 0 || !newJobFormData.type) {
      toast.error('Please select a few categories and type first then click create a job.');
      return;
    }
    console.log(newJobFormData);
    console.log(selectedJobCategories);
    try {
      const response = await fetch(`/api/admin/companies/${companyId}/jobs`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...newJobFormData,
          selectedJobCategories,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create job");
      }

      const data = await response.json();
      //redirect to company jobs
      toast.success('Buyer job created successfully.')
    } catch (error) {
      console.error("Error creating job:", error);
      toast.error('Error creatting buyer Job.')
    }
  };

  const handleNewJobChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewJobFormData(prev => ({ ...prev, [id]: value }));
  };

  return (
    <div className="p-6 bg-muted/30 rounded-lg shadow-md border border-border/50">
      <Toaster />

      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-foreground">Company Categories</h1>
      </div>

      {/* Search and Actions Bar */}
      <div className="flex justify-between items-center mb-6 gap-4 p-4 bg-background/50 rounded-lg border border-border">
        <div className="flex-1 max-w-sm relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Search categories..."
            value={search}
            onChange={handleSearch}
            className="pl-10 bg-background"
          />
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddCategory} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Category
          </Button>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add New Job
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px] bg-card border-border">
              <DialogHeader className="border-b border-border pb-4">
                <DialogTitle className="text-card-foreground flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Create New Job
                </DialogTitle>
                <DialogDescription className="text-muted-foreground">
                  Fill in the details for the new buyer job.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4 bg-card">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right text-card-foreground">
                    Title
                  </Label>
                  <Input
                    id="title"
                    value={newJobFormData.title}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right text-card-foreground">
                    Description
                  </Label>
                  <Input
                    id="description"
                    value={newJobFormData.description}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="contract" className="text-right text-card-foreground">
                    Contract
                  </Label>
                  <Input
                    id="contract"
                    value={newJobFormData.contract}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="starts" className="text-right text-card-foreground">
                    Starts
                  </Label>
                  <Input
                    id="starts"
                    type="date"
                    value={newJobFormData.starts}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="ends" className="text-right text-card-foreground">
                    Ends
                  </Label>
                  <Input
                    id="ends"
                    type="date"
                    value={newJobFormData.ends}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="location" className="text-right text-card-foreground">
                    Location
                  </Label>
                  <Input
                    id="location"
                    value={newJobFormData.location}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="categoryPrice" className="text-right text-card-foreground">
                    Category Price
                  </Label>
                  <Input
                    id="categoryPrice"
                    type="number"
                    value={newJobFormData.categoryPrice}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="passMark" className="text-right text-card-foreground">
                    Pass Mark
                  </Label>
                  <Input
                    id="passMark"
                    type="number"
                    value={newJobFormData.passMark}
                    onChange={handleNewJobChange}
                    className="col-span-3 bg-background"
                    placeholder="Enter pass mark"
                  />
                </div>
                {/* Job type select */}
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right text-card-foreground">
                    Job Type
                  </Label>
                  <Select
                    value={newJobFormData.type}
                    onValueChange={(value) => {
                      setNewJobFormData(prev => ({
                        ...prev,
                        type: value
                      }));
                    }}
                  >
                    <SelectTrigger className="col-span-3 bg-background" id="type">
                      <SelectValue placeholder="Select job type" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      <SelectItem value="supplier_registration">Supplier Registration</SelectItem>
                      <SelectItem value="supplier_prequalification">Supplier Prequalification</SelectItem>
                      <SelectItem value="rfq">RFQ</SelectItem>
                      <SelectItem value="tender">Tender</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter className="border-t border-border pt-4">
                <Button type="button" onClick={handleNewJobSubmit} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Create Job
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button
            variant="outline"
            onClick={() => document.getElementById('excelUpload')?.click()}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            Upload Excel
          </Button>
          <input
            type="file"
            id="excelUpload"
            accept=".xlsx, .xls"
            onChange={handleExcelUpload}
            className="hidden"
          />
        </div>
      </div>

      {/* Categories Table */}
      <Card className="bg-card border-border shadow-lg">
        <CardHeader className="bg-muted/50 border-b border-border">
          <CardTitle className="text-card-foreground">Categories</CardTitle>
        </CardHeader>
        <CardContent className="p-0 bg-card">
          <div className="border border-border rounded-lg overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border">
                  <TableHead className="w-12">
                    <CheckSquare className="h-4 w-4" />
                  </TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-32">
                    <Building2 className="h-4 w-4 inline mr-1" />
                    Company
                  </TableHead>
                  <TableHead className="w-32">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    Created
                  </TableHead>
                  <TableHead className="w-32">
                    <User className="h-4 w-4 inline mr-1" />
                    Created By
                  </TableHead>
                  <TableHead className="w-48">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCategories.map((category: any) => (
                  <TableRow key={category._id} className="border-b border-border hover:bg-muted/50">
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={selectedJobCategories.includes(category._id)}
                        onChange={() => {
                          if (selectedJobCategories.includes(category._id)) {
                            setSelectedJobCategories(selectedJobCategories.filter((id: string) => id !== category._id));
                          } else {
                            setSelectedJobCategories([...selectedJobCategories, category._id]);
                          }
                        }}
                        className="rounded border-border"
                      />
                    </TableCell>
                    <TableCell className="font-medium">{category.name}</TableCell>
                    <TableCell className="max-w-xs truncate" title={category.description}>
                      {category.description}
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {category.type?.replace('_', ' ')}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        category.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {category.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="truncate" title={category.spCompanyId?.name}>
                        {category.spCompanyId?.name || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {category.createdAt ? new Date(category.createdAt).toLocaleDateString() : 'N/A'}
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="truncate" title={`${category.createdBy?.firstName} ${category.createdBy?.lastName}`}>
                        {category.createdBy ? `${category.createdBy.firstName} ${category.createdBy.lastName}` : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditCategory(category)}
                          className="flex items-center gap-1"
                        >
                          <Edit className="h-3 w-3" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openTemplateFieldsModal(category)}
                          className="flex items-center gap-1"
                        >
                          <Settings className="h-3 w-3" />
                          Criteria
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteCategory(category._id)}
                          className="flex items-center gap-1 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Excel Upload Modal */}
      {isExcelUploadVisible && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card border border-border p-6 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-card-foreground">Create Categories from Excel</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExcelUploadVisible(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {excelCategories.length === 0 ? (
              <p className="text-muted-foreground">No valid categories found in the Excel file.</p>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-border">
                      <TableHead>Code</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>System Category</TableHead>
                      <TableHead>Type</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {excelCategories.map((category: any, index: number) => (
                      <TableRow key={index} className="border-b border-border">
                        <TableCell>{category.code}</TableCell>
                        <TableCell className="max-w-xs truncate">{category.description}</TableCell>
                        <TableCell>
                          <select
                            value={category.systemCategory}
                            onChange={(e) => updateExcelCategory(index, 'systemCategory', e.target.value)}
                            className="w-full p-2 border border-border rounded bg-background text-foreground"
                            required
                          >
                            <option value="">Select system category</option>
                            {systemCategories.map((cat: any) => (
                              <option key={cat._id} value={cat._id}>{cat.name}</option>
                            ))}
                          </select>
                        </TableCell>
                        <TableCell>
                          <select
                            value={category.type}
                            onChange={(e) => updateExcelCategory(index, 'type', e.target.value)}
                            className="w-full p-2 border border-border rounded bg-background text-foreground"
                            required
                          >
                            <option value="supplier_registration">Supplier Registration</option>
                            <option value="supplier_prequalification">Supplier Prequalification</option>
                            <option value="rfq">RFQ</option>
                            <option value="tender">Tender</option>
                          </select>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
            
            <div className="flex justify-end space-x-2 pt-4 mt-4 border-t border-border">
              <Button
                variant="outline"
                onClick={() => setIsExcelUploadVisible(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateCategoriesFromExcel}
                disabled={isSaving || excelCategories.length === 0}
                className="flex items-center gap-2"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4" />
                    Create Categories
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Category Edit Modal */}
      {isModalVisible && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card border border-border p-6 rounded-lg shadow-xl w-full max-w-xl">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-card-foreground">
                {editingCategory ? 'Edit Category' : 'Add Category'}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsModalVisible(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Name</Label>
                <Input
                  type="text"
                  {...form.register('name')}
                  placeholder="Category name"
                  className="bg-background border-border"
                  required
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Description</Label>
                <textarea
                  {...form.register('description')}
                  placeholder="Category description"
                  className="w-full p-2 border border-border rounded bg-background text-foreground"
                  required
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Type</Label>
                <select
                  {...form.register('type')}
                  className="w-full p-2 border border-border rounded bg-background text-foreground"
                  required
                >
                  <option value="">Select type</option>
                  <option value="supplier_registration">Supplier Registration</option>
                  <option value="supplier_prequalification">Supplier Prequalification</option>
                  <option value="rfq">RFQ</option>
                  <option value="tender">Tender</option>
                </select>
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Status</Label>
                <select
                  {...form.register('status')}
                  className="w-full p-2 border border-border rounded bg-background text-foreground"
                  required
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex justify-end space-x-2 pt-4 border-t border-border">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalVisible(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Criteria Fields Modal */}
      {isTemplateFieldsModalVisible && (
        <div className="fixed inset-0 bg-black/60 z-50 flex justify-center items-center">
          <div className="bg-card border border-border w-full max-w-3xl p-6 rounded-lg shadow-xl overflow-auto max-h-[90vh]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-card-foreground">Criteria Fields</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsTemplateFieldsModalVisible(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <ul className="space-y-3">
              {categoryTemplate?.fields?.map((field: any, index: number) => (
                <li key={index} className="border border-border p-3 rounded bg-background flex justify-between items-center">
                  <div>
                    <p className="font-medium text-foreground">{field.label || 'Unnamed field'}</p>
                    <p className="text-sm text-muted-foreground">{field.name} — {field.type}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openFieldEditModal(field)}
                      className="flex items-center gap-1"
                    >
                      <Edit className="h-3 w-3" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteField(field.name)}
                      className="flex items-center gap-1 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-3 w-3" />
                      Delete
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
            <div className="flex justify-between mt-4 pt-4 border-t border-border">
              <Button
                onClick={addField}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Field
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsTemplateFieldsModalVisible(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Field Edit Modal */}
      {editedField && (
        <div className="fixed inset-0 bg-black/60 z-50 flex justify-center items-center">
          <div className="bg-card border border-border w-full max-w-3xl p-6 rounded-lg shadow-xl overflow-auto max-h-[90vh]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-card-foreground">Modify Criterion Field</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeFieldEditModal}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Label</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.label || ''}
                  onChange={(e) => handleFieldChange('label', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Description</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.description || ''}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Name</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.name || ''}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Type</Label>
                <select
                  value={(editedField as any)?.type || 'text'}
                  onChange={(e) => handleFieldChange('type', e.target.value)}
                  className="w-full p-2 border border-border rounded bg-background text-foreground"
                >
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="select">Select</option>
                </select>
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Placeholder</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.placeholder || ''}
                  onChange={(e) => handleFieldChange('placeholder', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Tooltip</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.tooltip || ''}
                  onChange={(e) => handleFieldChange('tooltip', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Help Text</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.helpText || ''}
                  onChange={(e) => handleFieldChange('helpText', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Required</Label>
                <input
                  type="checkbox"
                  checked={(editedField as any)?.isRequired === true}
                  onChange={(e) => handleFieldChange('isRequired', e.target.checked)}
                  className="rounded border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Editable</Label>
                <input
                  type="checkbox"
                  checked={(editedField as any)?.isEditable || false}
                  onChange={(e) => handleFieldChange('isEditable', e.target.checked)}
                  className="rounded border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Visible</Label>
                <input
                  type="checkbox"
                  checked={(editedField as any)?.isVisible || false}
                  onChange={(e) => handleFieldChange('isVisible', e.target.checked)}
                  className="rounded border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Group</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.group || ''}
                  onChange={(e) => handleFieldChange('group', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Subgroup</Label>
                <Input
                  type="text"
                  value={(editedField as any)?.subGroup || ''}
                  onChange={(e) => handleFieldChange('subGroup', e.target.value)}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Order</Label>
                <Input
                  type="number"
                  value={(editedField as any)?.order || 0}
                  onChange={(e) => handleFieldChange('order', parseInt(e.target.value))}
                  className="bg-background border-border"
                />
              </div>
              <div>
                <Label className="block text-sm font-medium mb-1 text-card-foreground">Max Score</Label>
                <Input
                  type="number"
                  value={(editedField as any)?.maxScore || 0}
                  onChange={(e) => handleFieldChange('maxScore', parseInt(e.target.value))}
                  className="bg-background border-border"
                />
              </div>
              <div className="col-span-2 flex justify-end mt-4 pt-4 border-t border-border gap-2">
                <Button variant="outline" onClick={closeFieldEditModal}>Cancel</Button>
                <Button onClick={saveEditedField} className="flex items-center gap-2">
                  <Edit className="h-4 w-4" />
                  Save
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}





    </div>
  );
};

export default CategoriesPage;
