'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Search, Trash } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';

const CategoryTemplatesPage = () => {
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [fields, setFields] = useState([]);
  
  const form = useForm({
    defaultValues: {
      name: '',
      categoryId: '',
      fields: []
    }
  });

  useEffect(() => {
    fetchTemplates();
    fetchCategories();
  }, []);

  useEffect(() => {
    if (editingTemplate) {
      form.reset({
        name: editingTemplate.name,
        categoryId: editingTemplate.categoryId,
        fields: editingTemplate.fields
      });
      setFields(editingTemplate.fields);
    } else {
      form.reset({
        name: '',
        categoryId: '',
        fields: []
      });
      setFields([]);
    }

    const token = document.cookie
    .split('; ')
    .find((row) => row.startsWith('token='))
    ?.split('=')[1];

  }, [editingTemplate, form]);


  const token = document.cookie
    .split('; ')
    .find((row) => row.startsWith('token='))
    ?.split('=')[1];


  const fetchTemplates = async () => {
    try {
      const { data } = await axios.get('/api/admin/buyer/categories/templates', {
        headers: {
          Authorization: `Bearer ${token}`,
        }});
      setTemplates(data);
    } catch (error) {
      console.error("Failed to fetch templates:", error);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get('/api/admin/categories', {
        headers: {
          Authorization: `Bearer ${token}`,
        }});
      setCategories(data);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    }
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const handleCategoryFilter = (value) => {
    setCategoryFilter(value);
  };

  const filteredTemplates = templates.filter((template) =>
    template.name.toLowerCase().includes(search.toLowerCase()) &&
    (categoryFilter === 'all' || template.categoryId === categoryFilter)
  );

  const handleAddTemplate = () => {
    setEditingTemplate(null);
    setIsModalVisible(true);
  };

  const handleEditTemplate = (template) => {
    setEditingTemplate(template);
    setIsModalVisible(true);
  };

  const handleDeleteTemplate = async (id) => {
    try {
      await axios.delete(`/api/admin/buyer/categories/templates`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        data: { id }
      });
      fetchTemplates();
    } catch (error) {
      console.error("Failed to delete template:", error);
    }
  };

  const addField = () => {
    const newField = {
      name: '',
      group: 'company',
      isParent: false,
      title: '',
      order: fields.length + 1,
      placeholder: '',
      description: '',
      helpText: '',
      tooltip: '',
      isVisible: true,
      isEditable: true,
      type: 'text',
      label: '',
      options: [],
      isRequired: false,
      isScoreable: false,
      maxScore: 0,
      validations: {
        min: null,
        max: null,
        pattern: '',
        fileTypes: [],
        maxFileSize: null
      }
    };
    setFields([...fields, newField]);
  };

  const removeField = (index) => {
    const updatedFields = [...fields];
    updatedFields.splice(index, 1);
    // Update order for remaining fields
    updatedFields.forEach((field, idx) => {
      field.order = idx + 1;
    });
    setFields(updatedFields);
  };

  const updateField = (index, field) => {
    const updatedFields = [...fields];
    updatedFields[index] = field;
    setFields(updatedFields);
  };

  const addOption = (fieldIndex) => {
    const updatedFields = [...fields];
    if (!updatedFields[fieldIndex].options) {
      updatedFields[fieldIndex].options = [];
    }
    updatedFields[fieldIndex].options.push({ label: '', value: '' });
    setFields(updatedFields);
  };

  const removeOption = (fieldIndex, optionIndex) => {
    const updatedFields = [...fields];
    updatedFields[fieldIndex].options.splice(optionIndex, 1);
    setFields(updatedFields);
  };

  const updateOption = (fieldIndex, optionIndex, key, value) => {
    const updatedFields = [...fields];
    updatedFields[fieldIndex].options[optionIndex][key] = value;
    setFields(updatedFields);
  };

  const onSubmit = async (values) => {
    try {
      const payload = {
        ...values,
        fields: fields
      };
      
      if (editingTemplate) {
        await axios.put(`/api/admin/buyer/categories/templates`, {
          ...payload,
          id: editingTemplate._id
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      } else {
        await axios.post('/api/admin/buyer/categories/templates', payload, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      setIsModalVisible(false);
      fetchTemplates();
    } catch (error) {
      console.error("Failed to save template:", error);
    }
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat._id === categoryId);
    return category ? category.name : 'Unknown';
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex gap-4 items-center">
          <div className="relative w-64">
            <Input
              type="text"
              placeholder="Search templates"
              value={search}
              onChange={handleSearch}
              className="pl-8"
            />
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          </div>
          
          <Select value={categoryFilter} onValueChange={handleCategoryFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category._id} value={category._id.toString()}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Button onClick={handleAddTemplate}>
          <Plus className="mr-2 h-4 w-4" /> Add Template
        </Button>
      </div>
      
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Fields Count</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTemplates.map((template) => (
              <TableRow key={template._id}>
                <TableCell>{template.name}</TableCell>
                <TableCell>{getCategoryName(template.categoryId)}</TableCell>
                <TableCell>{template.fields?.length || 0}</TableCell>
                <TableCell>{new Date(template.createdAt).toLocaleDateString()}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleEditTemplate(template)}>
                      Edit
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDeleteTemplate(template._id)}>
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredTemplates.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  No templates found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>

      <Modal
        open={isModalVisible}
        onOpenChange={setIsModalVisible}
        title={editingTemplate ? 'Edit Category Template' : 'Add Category Template'}
        size="xl"
      >
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Template Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Template name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category._id} value={category._id.toString()}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Fields</h3>
                <Button type="button" variant="outline" onClick={addField}>
                  Add Field
                </Button>
              </div>
              
              {fields.map((field, index) => (
                <Card key={index} className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium">Field #{index + 1}</h4>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm"
                      onClick={() => removeField(index)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor={`field-${index}-name`}>Name</Label>
                      <Input
                        id={`field-${index}-name`}
                        value={field.name}
                        onChange={(e) => updateField(index, { ...field, name: e.target.value })}
                        placeholder="Field name"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-label`}>Label</Label>
                      <Input
                        id={`field-${index}-label`}
                        value={field.label}
                        onChange={(e) => updateField(index, { ...field, label: e.target.value })}
                        placeholder="Field label"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-group`}>Group</Label>
                      <Select
                        value={field.group}
                        onValueChange={(value) => updateField(index, { ...field, group: value })}
                      >
                        <SelectTrigger id={`field-${index}-group`}>
                          <SelectValue placeholder="Select group" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="company">Company</SelectItem>
                          <SelectItem value="contact">Contact</SelectItem>
                          <SelectItem value="bank">Bank</SelectItem>
                          <SelectItem value="declaration">Declaration</SelectItem>
                          <SelectItem value="statutory">Statutory</SelectItem>
                          <SelectItem value="director">Director</SelectItem>
                          <SelectItem value="risk">Risk</SelectItem>
                          <SelectItem value="esg">ESG</SelectItem>
                          <SelectItem value="sla">SLA</SelectItem>
                          <SelectItem value="exp">Experience</SelectItem>
                          <SelectItem value="reference">Reference</SelectItem>
                          <SelectItem value="fin">Financial</SelectItem>
                          <SelectItem value="tech">Technical</SelectItem>
                          <SelectItem value="hr">HR</SelectItem>
                          <SelectItem value="submission">Submission</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                          <SelectItem value="compliance">Compliance</SelectItem>
                          <SelectItem value="attachments">Attachments</SelectItem>
                          <SelectItem value="outcome">Outcome</SelectItem>
                          <SelectItem value="contacts">Contacts</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-type`}>Field Type</Label>
                      <Select
                        value={field.type}
                        onValueChange={(value) => updateField(index, { ...field, type: value })}
                      >
                        <SelectTrigger id={`field-${index}-type`}>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="textarea">Textarea</SelectItem>
                          <SelectItem value="number">Number</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="file">File</SelectItem>
                          <SelectItem value="checkbox">Checkbox</SelectItem>
                          <SelectItem value="radio">Radio</SelectItem>
                          <SelectItem value="select">Select</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-order`}>Order</Label>
                      <Input
                        id={`field-${index}-order`}
                        type="number"
                        value={field.order}
                        onChange={(e) => updateField(index, { ...field, order: parseInt(e.target.value) })}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-title`}>Title (Optional)</Label>
                      <Input
                        id={`field-${index}-title`}
                        value={field.title || ''}
                        onChange={(e) => updateField(index, { ...field, title: e.target.value })}
                        placeholder="Field title"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`field-${index}-placeholder`}>Placeholder</Label>
                      <Input
                        id={`field-${index}-placeholder`}
                        value={field.placeholder}
                        onChange={(e) => updateField(index, { ...field, placeholder: e.target.value })}
                        placeholder="Field placeholder"
                      />
                    </div>
                    
                    <div className="col-span-3">
                      <Label htmlFor={`field-${index}-description`}>Description</Label>
                      <Textarea
                        id={`field-${index}-description`}
                        value={field.description}
                        onChange={(e) => updateField(index, { ...field, description: e.target.value })}
                        placeholder="Field description"
                        rows={2}
                      />
                    </div>
                    
                    <div className="col-span-3">
                      <Label htmlFor={`field-${index}-helpText`}>Help Text</Label>
                      <Textarea
                        id={`field-${index}-helpText`}
                        value={field.helpText}
                        onChange={(e) => updateField(index, { ...field, helpText: e.target.value })}
                        placeholder="Field help text"
                        rows={2}
                      />
                    </div>
                    
                    <div className="col-span-3">
                      <Label htmlFor={`field-${index}-tooltip`}>Tooltip</Label>
                      <Textarea
                        id={`field-${index}-tooltip`}
                        value={field.tooltip}
                        onChange={(e) => updateField(index, { ...field, tooltip: e.target.value })}
                        placeholder="Field tooltip"
                        rows={2}
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${index}-isParent`}
                        checked={field.isParent}
                        onCheckedChange={(checked) => updateField(index, { ...field, isParent: checked })}
                      />
                      <Label htmlFor={`field-${index}-isParent`}>Is Parent</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${index}-isVisible`}
                        checked={field.isVisible}
                        onCheckedChange={(checked) => updateField(index, { ...field, isVisible: checked })}
                      />
                      <Label htmlFor={`field-${index}-isVisible`}>Is Visible</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${index}-isEditable`}
                        checked={field.isEditable}
                        onCheckedChange={(checked) => updateField(index, { ...field, isEditable: checked })}
                      />
                      <Label htmlFor={`field-${index}-isEditable`}>Is Editable</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${index}-isRequired`}
                        checked={field.isRequired}
                        onCheckedChange={(checked) => updateField(index, { ...field, isRequired: checked })}
                      />
                      <Label htmlFor={`field-${index}-isRequired`}>Is Required</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${index}-isScoreable`}
                        checked={field.isScoreable}
                        onCheckedChange={(checked) => updateField(index, { ...field, isScoreable: checked })}
                      />
                      <Label htmlFor={`field-${index}-isScoreable`}>Is Scoreable</Label>
                    </div>
                    
                    {field.isScoreable && (
                      <div>
                        <Label htmlFor={`field-${index}-maxScore`}>Max Score</Label>
                        <Input
                          id={`field-${index}-maxScore`}
                          type="number"
                          value={field.maxScore}
                          onChange={(e) => updateField(index, { ...field, maxScore: parseInt(e.target.value) })}
                        />
                      </div>
                    )}
                    
                    {(field.type === 'select' || field.type === 'radio' || field.type === 'checkbox') && (
                      <div className="col-span-3 mt-4">
                        <div className="flex items-center justify-between mb-2">
                          <Label>Options</Label>
                          <Button type="button" variant="outline" size="sm" onClick={() => addOption(index)}>
                            Add Option
                          </Button>
                        </div>
                        {field.options?.map((option, optionIndex) => (
                          <div key={optionIndex} className="grid grid-cols-2 gap-2 mb-2">
                            <Input
                              placeholder="Label"
                              value={option.label}
                              onChange={(e) => updateOption(index, optionIndex, 'label', e.target.value)}
                            />
                            <div className="flex gap-2">
                              <Input
                                placeholder="Value"
                                value={option.value}
                                onChange={(e) => updateOption(index, optionIndex, 'value', e.target.value)}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeOption(index, optionIndex)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {(field.type === 'number' || field.type === 'date') && (
                      <div className="col-span-3 grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <Label htmlFor={`field-${index}-min`}>Min Value</Label>
                          <Input
                            id={`field-${index}-min`}
                            type="number"
                            value={field.validations?.min || ''}
                            onChange={(e) => {
                              const validations = { ...field.validations, min: e.target.value ? Number(e.target.value) : null };
                              updateField(index, { ...field, validations });
                            }}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`field-${index}-max`}>Max Value</Label>
                          <Input
                            id={`field-${index}-max`}
                            type="number"
                            value={field.validations?.max || ''}
                            onChange={(e) => {
                              const validations = { ...field.validations, max: e.target.value ? Number(e.target.value) : null };
                              updateField(index, { ...field, validations });
                            }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {field.type === 'text' && (
                      <div className="col-span-3 mt-2">
                        <Label htmlFor={`field-${index}-pattern`}>Validation Pattern (regex)</Label>
                        <Input
                          id={`field-${index}-pattern`}
                          value={field.validations?.pattern || ''}
                          onChange={(e) => {
                            const validations = { ...field.validations, pattern: e.target.value };
                            updateField(index, { ...field, validations });
                          }}
                          placeholder="e.g. ^[a-zA-Z0-9]+$"
                        />
                      </div>
                    )}
                    
                    {field.type === 'file' && (
                      <div className="col-span-3 grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <Label htmlFor={`field-${index}-fileTypes`}>Allowed File Types (comma separated)</Label>
                          <Input
                            id={`field-${index}-fileTypes`}
                            value={(field.validations?.fileTypes || []).join(',')}
                            onChange={(e) => {
                              const fileTypes = e.target.value.split(',').map(type => type.trim()).filter(Boolean);
                              const validations = { ...field.validations, fileTypes };
                              updateField(index, { ...field, validations });
                            }}
                            placeholder="e.g. .pdf,.docx,.jpg"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`field-${index}-maxFileSize`}>Max File Size (in KB)</Label>
                          <Input
                            id={`field-${index}-maxFileSize`}
                            type="number"
                            value={field.validations?.maxFileSize || ''}
                            onChange={(e) => {
                              const validations = { ...field.validations, maxFileSize: e.target.value ? Number(e.target.value) : null };
                              updateField(index, { ...field, validations });
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
              
              {fields.length === 0 && (
                <div className="text-center py-8 border rounded-md">
                  <p className="text-gray-500">No fields added yet. Click "Add Field" to get started.</p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsModalVisible(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Save Template</Button>
            </div>
          </form>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryTemplatesPage;