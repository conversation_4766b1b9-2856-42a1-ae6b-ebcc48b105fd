'use client';

import { useState, useEffect } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import {
  Combine,
  Copy,
  Pen,
  PlusIcon,
  Trash2,
  Users2,
  X,
  Download,
  Search,
  Building2,
  Calendar,
  User,
  Settings,
  Link,
  Edit
} from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";


interface Company {
  _id: string;
  name: string;
  type: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  registrationNumber: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export default function Companies() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [search, setSearch] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [type, setType] = useState<string | null >(null);
 const [token, setToken] = useState<string | null>(null);
 useEffect(() => {
  fetchCompanies();
}, [token]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    console.log('Type', type);
    setType(type);
  }
}, []);


  useEffect(() => {
    const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (token) {
      setToken(token);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);


  const fetchCompanies = async () => {
    const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (!token) {
      console.error("Token not found in cookies");
      return;
    }
    const url = `/api/admin/companies?type=${type}`;
    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`, // Add the token to the Authorization header
        },
      });

      const data = await response.json();
      console.log(data);
      const companiesData = data.data || [];
      console.log('Companies data:', companiesData);

      // Ensure the response is an array
      if (Array.isArray(companiesData)) {
        setCompanies( companiesData);
      } else {
        console.error('API response is not an array:', companiesData);
        setCompanies([]); // Fallback to an empty array
      }
    } catch (error) {
      console.error('Failed to fetch companies:', error);
      toast.error('Failed to fetch companies');
      setCompanies([]); // Fallback to an empty array
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const filteredCompanies = Array.isArray(companies)
    ? companies.filter((company) =>
        company.name.toLowerCase().includes(search.toLowerCase())
      )
    : []; // Ensure `filteredCompanies` is always an array

  const handleAddCompany = () => {
    setIsModalOpen(true);
  };

  const handleEditCompany = (company: any) => {
    setSelectedCompany(company);
    setIsModalOpen(true);
  };

  const handleDeleteCompany = async (id: string) => {
    try {
      await fetch(`/api/admin/companies/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`, // Add the token to the Authorization header
        },
      });

      toast.success('Company deleted successfully');
      fetchCompanies();
    } catch (error) {
      toast.error('Failed to delete company');
    }
  };
  


  const handleCopyLink = async (companyId: string) => {
    const link = `${window.location.origin}/buyer-setup/${companyId}`;
    
    try {
      // Modern clipboard API
      await navigator.clipboard.writeText(link);
      showFeedback();
    } catch (err) {
      console.log('Modern clipboard failed, trying fallback');
      try {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = link;
        textArea.style.position = 'fixed';  // Prevent scrolling
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          showFeedback();
        } else {
          throw new Error('Fallback copy failed');
        }
      } catch (fallbackErr) {
        console.error('All copy methods failed:', fallbackErr);
        // Optionally show error to user
      }
    }
  };
  
  const showFeedback = () => {
    setIsCopied(true);
    toast.success('Company admin access link copied successfully');
    setTimeout(() => setIsCopied(false), 3000);
  };

  return (
    <div className="p-6 bg-muted/30 rounded-lg shadow-md border border-border/50">
      <Toaster />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-foreground">{type?.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ')} Companies</h1>
        
        <Button onClick={handleAddCompany}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add New Company
        </Button>

      </div>
      <div className="flex justify-between items-center mb-6 gap-4 p-4 bg-background/50 rounded-lg border border-border">
        <div className="flex-1 max-w-sm relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Search companies..."
            value={search}
            onChange={handleSearch}
            className="pl-10 bg-background"
          />
        </div>
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-input bg-background rounded-md text-sm text-foreground">
            <option value="">Filter by Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      <Card className="bg-card border-border shadow-lg">
        <CardHeader className="bg-muted/50 border-b border-border">
          <CardTitle className="text-card-foreground">Companies</CardTitle>
        </CardHeader>
        <CardContent className="p-0 bg-card">
          <div className="border border-border rounded-lg overflow-x-auto">
              <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead>
                <Building2 className="h-4 w-4 inline mr-1" />
                Company Name
              </TableHead>
              <TableHead>
                <User className="h-4 w-4 inline mr-1" />
                Contact Name
              </TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-64">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCompanies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  No companies found
                </TableCell>
              </TableRow>
            ) : (
              filteredCompanies.map((company) => (
                <TableRow key={company._id} className="border-b border-border hover:bg-muted/50">
                  <TableCell>
                    <div>
                      <div className="font-medium">{company.name}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          {company.type?.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{company.contactPerson}</TableCell>
                  <TableCell>{company.phone}</TableCell>
                  <TableCell className="break-words">{company.email}</TableCell>
                  <TableCell>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      company.status === 'active'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : company.status === 'inactive'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      {company.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {console.log('clicked'); handleEditCompany(company);}}
                        className="flex items-center gap-1"
                      >
                        <Edit className="h-3 w-3" />
                        Edit
                      </Button>
                      {company.type === 'buyer' && (
                      <Button
                        onClick={() => {
                          window.location.href = `/admin/companies/${company._id}/categories`;
                        }}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Settings className="h-3 w-3" />
                        Categories
                      </Button>
                       )}

                      <Button
                        onClick={() => {
                          window.location.href = `/admin/companies/${company._id}/users`;
                        }}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Users2 className="h-3 w-3" />
                        Users
                      </Button>
                      {company.type === 'buyer' && (

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyLink(company._id)}
                        className="flex items-center gap-1"
                      >
                        <Link className="h-3 w-3" />
                        Copy Link
                      </Button>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCompany(company._id)}
                        className="flex items-center gap-1 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
        </CardContent>
      </Card>
      {isModalOpen && (
        <CompanyModal
          company={selectedCompany}
          onClose={() => setIsModalOpen(false)}
          onSave={fetchCompanies}
        />
      )}
    </div>
  );
}



function CompanyModal({ company, onClose, onSave }: any) {
  const [isLoading, setIsLoading] = useState(false);
  const [downloadingStates, setDownloadingStates] = useState<Record<string, boolean>>({});

  const [formData, setFormData] = useState({
    name: company?.name || '',
    contactPerson: company?.contactPerson || '',
    phone: company?.phone || '',
    email: company?.email || '',
    registrationNumber: company?.registrationNumber || '',
    address: company?.address || '',
    documents: {
      contract: null,
      kraPinCertificate: null,
      certificateOfIncorporation: null,
      tradingLicense: null,
      companyCR12: null,
      taxComplianceCertificate: null,
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setFormData((prev) => ({
        ...prev,
        documents: { ...prev.documents, [name]: files[0] },
      }));
    }
  };

  const handleDownload = async (e: React.MouseEvent<HTMLSpanElement>) => {
    e.preventDefault();
  
    const target = e.currentTarget as HTMLElement;
    const companyId = target.dataset.companyId;
    const documentType = target.dataset.documentType;
    const fileName = target.dataset.fileName;

    // Create unique key for this download
    const downloadKey = `${companyId}-${documentType}`;
    // Set downloading state to true
    setDownloadingStates(prev => ({
      ...prev,
      [downloadKey]: true
    }));

    setIsLoading(true);
  
    if (!companyId || !documentType || !fileName) {
      toast.error("Missing document metadata");
      return;
    }
  
    const url = `/api/admin/companies/documents/download?companyId=${companyId}&documentType=${documentType}&fileName=${fileName}`;
  
    try {
      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];
  
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      if (!response.ok) throw new Error('Failed to download document');
  
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
  
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = fileName!;
      document.body.appendChild(a);
      a.click();
      a.remove();
  
      window.URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error(err);
      toast.error("Failed to download document");
    } finally {
      // Reset downloading state
      setDownloadingStates(prev => ({
        ...prev,
        [downloadKey]: false
      }));
      setIsLoading(false);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      const method = company ? 'PUT' : 'POST';
      const url = company ? `/api/admin/companies/${company._id}` : '/api/admin/companies';
      
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name);
      formDataToSend.append('contactPerson', formData.contactPerson);
      formDataToSend.append('phone', formData.phone);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('address', formData.address);
      formDataToSend.append('registrationNumber', formData.registrationNumber);

      Object.entries(formData.documents).forEach(([key, file]) => {
        if (file) {
          formDataToSend.append(key, file);
        }
      });

      await fetch(url, {
        method: method,
        body: formDataToSend,
        headers: {
          Authorization: `Bearer ${document.cookie
            .split('; ')
            .find((row) => row.startsWith('token='))
            ?.split('=')[1]}`,
        },
      });

      toast.success("Company updated successfully");
      onSave();
      onClose();
    } catch (error) {
      toast.error("Failed to save company");
    } finally {
      setIsLoading(false);
    }
  };

  const kraPinDocument = company?.documents?.find(
    (doc) => doc.type === 'kraPinCertificate'
  );
  const certificateOfIncorporationDocument = company?.documents?.find(
    (doc) => doc.type === 'certificateOfIncorporation'
  );
  const tradingLicenseDocument = company?.documents?.find(
    (doc) => doc.type === 'tradingLicense'
  );
  const companyCR12Document = company?.documents?.find(
    (doc) => doc.type === 'companyCR12'
  );
  const taxComplianceCertificateDocument = company?.documents?.find(
    (doc) => doc.type === 'taxComplianceCertificate'
  );
  const contractDocument = company?.documents?.find(
    (doc) => doc.type === 'contract'
  );

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl">
        <CardHeader className="relative">
          <CardTitle>{company ? 'Edit Company' : 'Add Company'}</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 h-8 w-8 rounded-sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} encType="multipart/form-data">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Company Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact Name</Label>
                <Input
                  id="contactPerson"
                  name="contactPerson"
                  value={formData.contactPerson}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="registrationNumber">Registration Number</Label>
                <Input
                  id="registrationNumber"
                  name="registrationNumber"
                  value={formData.registrationNumber}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  required
                />
              </div>

              {/* Document Upload Sections */}
              <div className="space-y-2">
                <Label>Contract</Label>
                {contractDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{contractDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={contractDocument.type}
                      data-file-name={contractDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${contractDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${contractDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="contract"
                  onChange={handleFileChange}
                  required={!contractDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>KRA PIN Certificate</Label>
                {kraPinDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{kraPinDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={kraPinDocument.type}
                      data-file-name={kraPinDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${kraPinDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${kraPinDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="kraPinCertificate"
                  onChange={handleFileChange}
                  required={!kraPinDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Certificate of Incorporation</Label>
                {certificateOfIncorporationDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{certificateOfIncorporationDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={certificateOfIncorporationDocument.type}
                      data-file-name={certificateOfIncorporationDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${certificateOfIncorporationDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${certificateOfIncorporationDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="certificateOfIncorporation"
                  onChange={handleFileChange}
                  required={!certificateOfIncorporationDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Trading License</Label>
                {tradingLicenseDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{tradingLicenseDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={tradingLicenseDocument.type}
                      data-file-name={tradingLicenseDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${tradingLicenseDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${tradingLicenseDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="tradingLicense"
                  onChange={handleFileChange}
                  required={!tradingLicenseDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Company CR12</Label>
                {companyCR12Document && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{companyCR12Document.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={companyCR12Document.type}
                      data-file-name={companyCR12Document.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${companyCR12Document.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${companyCR12Document.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="companyCR12"
                  onChange={handleFileChange}
                  required={!companyCR12Document}
                />
              </div>

              <div className="space-y-2">
                <Label>Tax Compliance Certificate</Label>
                {taxComplianceCertificateDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{taxComplianceCertificateDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={taxComplianceCertificateDocument.type}
                      data-file-name={taxComplianceCertificateDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${taxComplianceCertificateDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${taxComplianceCertificateDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="taxComplianceCertificate"
                  onChange={handleFileChange}
                  required={!taxComplianceCertificateDocument}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit"
                disabled={isLoading}>
                {isLoading ? 'Loading...' : 'Save'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}