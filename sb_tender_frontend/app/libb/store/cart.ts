import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import axios from 'axios';
import { toast } from 'sonner';

export interface CartItem {
  id: string;
  type: 'registration' | 'prequalification';
  category: string;
  buyerCompany: string;
  price: number;
}

interface CartStore {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  clearCart: () => void;
  checkout: () => Promise<void>;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      addItem: (item) => {
        const items = get().items;
        const exists = items.find((i) => i.id === item.id);
        
        if (exists) {
          toast.error('Item already in cart');
          return;
        }
        
        set({ items: [...items, item] });
        toast.success('Added to cart');
      },
      removeItem: (id) => {
        set({ items: get().items.filter((item) => item.id !== id) });
        toast.success('Removed from cart');
      },
      clearCart: () => {
        set({ items: [] });
      },
      checkout: async () => {
        try {
          const items = get().items;
          const total = items.reduce((sum, item) => sum + item.price, 0);
          
          // Call M-Pesa API to initiate payment
          const response = await axios.post('/api/mpesa/stkPush', {
            amount: total,
            items: items,
          });
          
          if (response.data.success) {
            toast.success('Payment initiated. Please check your phone.');
          } else {
            throw new Error('Payment failed');
          }
        } catch (error) {
          toast.error('Payment failed. Please try again.');
          throw error;
        }
      },
    }),
    {
      name: 'cart-storage',
    }
  )
);