'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, Building2 } from "lucide-react";
import { useAuthStore } from '@/lib/store/useAuthStore';

export default function BuyerDashboardFallback() {
  const router = useRouter();
  const { user, companies, activeCompanyId, setActiveCompanyId } = useAuthStore();

  useEffect(() => {
    // If user has companies, redirect to the first company's dashboard
    if (companies && companies.length > 0) {
      const firstCompany = companies[0];
      if (firstCompany?.companyId) {
        setActiveCompanyId(firstCompany.companyId);
        router.push(`/buyer/${firstCompany.companyId}/dashboard`);
        return;
      }
    }

    // If user has an active company ID, redirect to it
    if (activeCompanyId) {
      router.push(`/buyer/${activeCompanyId}/dashboard`);
      return;
    }
  }, [companies, activeCompanyId, router, setActiveCompanyId]);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-center items-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle>Company Not Found</CardTitle>
            <CardDescription>
              We couldn't find your company information. This might be because:
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                Your account hasn't been assigned to a company yet
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                Your company registration is still pending
              </li>
              <li className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                There was an error loading your company data
              </li>
            </ul>
            
            <div className="pt-4 space-y-3">
              <Button 
                onClick={() => window.location.reload()} 
                className="w-full"
              >
                <Building2 className="h-4 w-4 mr-2" />
                Retry Loading
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => router.push('/auth/login')}
                className="w-full"
              >
                Back to Login
              </Button>
            </div>

            <div className="pt-4 text-center">
              <p className="text-xs text-gray-500">
                Need help? Contact support at{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
