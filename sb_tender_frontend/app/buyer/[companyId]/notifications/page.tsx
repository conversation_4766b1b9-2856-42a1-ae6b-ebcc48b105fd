"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Bell,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Mail,
  MessageSquare,
  Smartphone,
  ArrowLeft,
  Paperclip,
  Download
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

interface Notification {
  _id: string;
  type: 'marketing' | 'tender' | 'system' | 'reminders' | 'order' | 'other';
  channel: 'email' | 'sms' | 'app' | 'push';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed' | 'cancelled';
  title: string;
  message: string;
  recipient: {
    email?: string;
    phone?: string;
    userId?: string;
  };
  delivery: {
    attempts: number;
    maxAttempts: number;
    scheduledAt?: string;
    sentAt?: string;
    deliveredAt?: string;
    readAt?: string;
    failedAt?: string;
    errorMessage?: string;
  };
  attachments?: {
    filename: string;
    path: string;
    contentType: string;
    size: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

// Skeleton Loader Component
const NotificationsSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="flex items-center justify-between">
      <div>
        <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
      <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
    </div>

    {/* Filters skeleton */}
    <Card>
      <CardContent className="p-6">
        <div className="flex gap-4 items-center mb-6">
          <div className="h-10 w-80 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
        </div>

        {/* Table skeleton */}
        <div className="border rounded-lg">
          <div className="p-4">
            {/* Header row */}
            <div className="flex space-x-4 mb-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
            {/* Data rows */}
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex space-x-4 mb-3 py-2">
                {Array.from({ length: 6 }).map((_, j) => (
                  <div key={j} className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

export default function SupplierNotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const router = useRouter();
  const { companyId } = params;

  // Filters and pagination
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    if (companyId) {
      fetchNotifications();
    }
  }, [companyId]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/supplier/${companyId}/notifications`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        const notificationsData = data.data?.notifications || data.data || data.applicationsData || [];
        setNotifications(notificationsData);
      } else {
        toast.error('Failed to fetch notifications');
        setNotifications([]);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Failed to fetch notifications');
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  const downloadAttachments = async (notificationId: string) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/notifications/${notificationId}/attachments/download`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          }
        }
      );

      if (response.ok) {
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          toast.success(`${data.data.attachments.length} attachments available for download`);
        } else {
          const blob = await response.blob();
          const contentDisposition = response.headers.get('content-disposition');
          const filename = contentDisposition
            ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
            : 'attachment';

          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);

          toast.success('Attachment downloaded successfully');
        }
      } else {
        toast.error('Failed to download attachments');
      }
    } catch (error) {
      console.error('Error downloading attachments:', error);
      toast.error('Failed to download attachments');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: Clock },
      sent: { color: 'bg-blue-100 text-blue-800', label: 'Sent', icon: Mail },
      delivered: { color: 'bg-green-100 text-green-800', label: 'Delivered', icon: CheckCircle },
      read: { color: 'bg-green-100 text-green-800', label: 'Read', icon: CheckCircle },
      failed: { color: 'bg-red-100 text-red-800', label: 'Failed', icon: XCircle },
      cancelled: { color: 'bg-gray-100 text-gray-800', label: 'Cancelled', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getChannelIcon = (channel: string) => {
    const icons = {
      email: Mail,
      sms: MessageSquare,
      app: Bell,
      push: Smartphone
    };
    const IconComponent = icons[channel as keyof typeof icons] || Bell;
    return <IconComponent className="h-4 w-4" />;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
      medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
      high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
      urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getFilteredNotifications = () => {
    return notifications.filter(notification => {
      const matchesSearch = notification.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.message?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.recipient?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          notification.recipient?.phone?.includes(searchQuery);

      const matchesStatus = statusFilter === 'all' || notification.status === statusFilter;
      const matchesType = typeFilter === 'all' || notification.type === typeFilter;
      const matchesChannel = channelFilter === 'all' || notification.channel === channelFilter;

      return matchesSearch && matchesStatus && matchesType && matchesChannel;
    });
  };

  const getPaginatedNotifications = () => {
    const filteredNotifications = getFilteredNotifications();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredNotifications.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const filteredNotifications = getFilteredNotifications();
    return Math.ceil(filteredNotifications.length / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'status':
        setStatusFilter(value);
        break;
      case 'type':
        setTypeFilter(value);
        break;
      case 'channel':
        setChannelFilter(value);
        break;
    }
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="p-6">
        <NotificationsSkeleton />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground">View your notifications and messages</p>
          </div>
        </div>
      </div>

      {/* Notifications Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Notifications</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="overflow-x-auto pb-2 mb-6">
            <div className="flex gap-4 items-center min-w-fit">
              <div className="flex items-center gap-2 min-w-fit">
                <Search className="h-4 w-4 flex-shrink-0" />
                <Input
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-80 min-w-[200px]"
                />
              </div>

              <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="read">Read</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value) => handleFilterChange('type', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="tender">Tender</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="reminders">Reminders</SelectItem>
                  <SelectItem value="order">Order</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>

              <Select value={channelFilter} onValueChange={(value) => handleFilterChange('channel', value)}>
                <SelectTrigger className="w-40 min-w-[120px]">
                  <SelectValue placeholder="Channel" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Channels</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="app">In-App</SelectItem>
                  <SelectItem value="push">Push</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Table */}
          <div className="border rounded-lg overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[200px]">Title</TableHead>
                  <TableHead className="min-w-[300px]">Message</TableHead>
                  <TableHead className="min-w-[120px]">Channel</TableHead>
                  <TableHead className="min-w-[100px]">Type</TableHead>
                  <TableHead className="min-w-[100px]">Priority</TableHead>
                  <TableHead className="min-w-[120px]">Status</TableHead>
                  <TableHead className="min-w-[120px]">Date</TableHead>
                  <TableHead className="min-w-[120px]">Attachments</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getPaginatedNotifications().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No notifications found
                    </TableCell>
                  </TableRow>
                ) : (
                  getPaginatedNotifications().map((notification) => (
                    <TableRow key={notification._id}>
                      <TableCell className="break-words">
                        <div className="font-medium text-wrap max-w-[200px]">
                          {notification.title}
                        </div>
                      </TableCell>
                      <TableCell className="break-words">
                        <div className="text-sm text-wrap max-w-[300px] leading-relaxed">
                          {notification.message}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getChannelIcon(notification.channel)}
                          <span className="capitalize">{notification.channel}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {notification.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(notification.priority)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(notification.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{new Date(notification.createdAt).toLocaleDateString()}</div>
                          <div className="text-muted-foreground">
                            {new Date(notification.createdAt).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {notification.attachments && notification.attachments.length > 0 ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadAttachments(notification._id)}
                            title={`Download ${notification.attachments.length} attachment(s)`}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            <span>{notification.attachments.length}</span>
                          </Button>
                        ) : (
                          <span className="text-muted-foreground text-sm">-</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {getFilteredNotifications().length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {Array.from({ length: getTotalPages() }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(getTotalPages(), currentPage + 1))}
                      className={currentPage === getTotalPages() ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>

              <div className="text-sm text-muted-foreground text-center mt-2">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, getFilteredNotifications().length)} to {Math.min(currentPage * itemsPerPage, getFilteredNotifications().length)} of {getFilteredNotifications().length} notifications
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
