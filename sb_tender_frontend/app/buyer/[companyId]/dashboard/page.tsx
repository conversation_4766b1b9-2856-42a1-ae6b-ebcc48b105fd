"use client";

import { Card } from "@/components/ui/card";
import {
  Users,
  Building,
  FileText,
  AlertCircle,
  Activity,
  DollarSign,
  BarChart as ChartIcon,
  Settings,
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import DashboardMetrics from "@/components/buyer-metrics";
import BuyerJobs from "@/components/buyer-jobs";
import { useParams, useRouter } from 'next/navigation';
import ActivityFeed from "@/components/buyer-activity-feed";

const activityData = [
  {
    name: "Jan",
    buyers: 45,
    buyers: 12,
    tenders: 28,
    applications: 120,
  },
  {
    name: "Feb",
    buyers: 52,
    buyers: 15,
    tenders: 35,
    applications: 145,
  },
  {
    name: "Mar",
    buyers: 58,
    buyers: 18,
    tenders: 42,
    applications: 168,
  },
];

const revenueData = [
  { name: "Week 1", revenue: 12500 },
  { name: "Week 2", revenue: 15800 },
  { name: "Week 3", revenue: 14200 },
  { name: "Week 4", revenue: 18900 },
];

const userDistribution = [
  { name: "Active Buyers", value: 450 },
  { name: "Active Buyers", value: 120 },
  { name: "Pending Approval", value: 45 },
  { name: "Suspended", value: 15 },
];

const COLORS = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
];

const alerts = [
  {
    type: "warning",
    message: "High server load detected",
    time: "5 minutes ago",
  },
  {
    type: "error",
    message: "Failed payment attempt - ABC Corp",
    time: "15 minutes ago",
  },
  {
    type: "info",
    message: "New buyer registration - XYZ Ltd",
    time: "1 hour ago",
  },
  {
    type: "success",
    message: "System backup completed",
    time: "2 hours ago",
  },
];

export default function AdminDashboard() {
  const params = useParams();
  const router = useRouter();
const { companyId } = params;

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Buyer Dashboard</h1>

        <div className="flex space-x-4">
          {/*<button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2">
            <AlertCircle className="h-5 w-5" />
            <span>System Alerts</span>
          </button>
          <button className="bg-gray-100 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Settings</span>
          </button>*/}
        </div>

      </div>
      <DashboardMetrics companyId={companyId} />

      <div className="flex justify-between items-center">
        <BuyerJobs />
        </div>
        <div className="space-y-6">

      <Card className="p-6">
        <ActivityFeed companyId={companyId}/>
        </Card>
      </div>

      
    </div>
  );
}