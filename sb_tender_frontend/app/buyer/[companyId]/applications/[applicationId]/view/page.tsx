"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ArrowLeft, Download, FileText, User, Building, Eye } from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface ApplicationField {
  name: string;
  label: string;
  type: string;
  value: any;
  score?: number;
  maxScore?: number;
  isScoreable: boolean;
  group: string;
  subGroup?: string;
  reviewerComment?: string;
  complianceScore?: number;
  documents?: Array<{
    fileName: string;
    fileType: string;
    fileSize: number;
    filePath: string;
    uploadedAt: string;
  }>;
}

interface Application {
  _id: string;
  title: string;
  ref: string;
  description: string;
  status: string;
  type: string;
  systemScore: number;
  complianceScore: number;
  totalScore: number;
  submittedAt: string;
  reviewStatus: string;
  reviewerNotes?: string;
  reviewDate?: string;
  fields: ApplicationField[];
  spSupplierCompanyId: {
    _id: string;
    name: string;
    registrationNumber: string;
    address: string;
    contactPerson: string;
    email: string;
    phone: string;
  };
  spBuyerCompanyId: {
    _id: string;
    name: string;
    address: string;
  };
  submittedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewer?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function BuyerApplicationViewPage() {
  const params = useParams();
  const router = useRouter();
  const { companyId, applicationId } = params;

  const [application, setApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(true);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchApplication();
  }, [companyId, applicationId]);

  const fetchApplication = async () => {
    try {
      setLoading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      // For buyers, we need to get the application through the admin route since we don't have the supplier company ID
      // We'll need to modify this to work with the buyer's context
      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/suppliers/${application?.spSupplierCompanyId?._id || 'unknown'}/applications/${applicationId}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch application');
      }

      const data = await response.json();
      
      if (data.success) {
        setApplication(data.data);
      } else {
        toast.error(data.message || 'Failed to fetch application');
      }
    } catch (error) {
      console.error('Error fetching application:', error);
      toast.error('Failed to fetch application');
    } finally {
      setLoading(false);
    }
  };

  const downloadDocument = async (fieldName: string, fileName: string) => {
    try {
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/buyer/${companyId}/applications/${applicationId}/fields/${fieldName}/documents/${fileName}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to download document');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      submitted: { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading application...</div>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Application not found</div>
      </div>
    );
  }

  // Group fields by section
  const fieldsByGroup = application.fields.reduce((acc, field) => {
    if (!acc[field.group]) {
      acc[field.group] = [];
    }
    acc[field.group].push(field);
    return acc;
  }, {} as Record<string, ApplicationField[]>);

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{application.title}</h1>
            <p className="text-gray-600">Application View - {application.ref}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {getStatusBadge(application.status)}
          <Badge variant="outline">Read Only</Badge>
        </div>
      </div>

      {/* Application Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Building className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Supplier Information</h3>
          </div>
          <div className="space-y-2">
            <div><strong>Company:</strong> {application.spSupplierCompanyId.name}</div>
            <div><strong>Registration:</strong> {application.spSupplierCompanyId.registrationNumber}</div>
            <div><strong>Contact:</strong> {application.spSupplierCompanyId.contactPerson}</div>
            <div><strong>Email:</strong> {application.spSupplierCompanyId.email}</div>
            <div><strong>Phone:</strong> {application.spSupplierCompanyId.phone}</div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Application Details</h3>
          </div>
          <div className="space-y-2">
            <div><strong>Type:</strong> {application.type.replace('_', ' ')}</div>
            <div><strong>Submitted:</strong> {new Date(application.submittedAt).toLocaleDateString()}</div>
            <div><strong>System Score:</strong> {application.systemScore || 0}</div>
            <div><strong>Compliance Score:</strong> {application.complianceScore || 0}</div>
            <div><strong>Total Score:</strong> {application.totalScore || 0}</div>
            <div><strong>Review Status:</strong> {getStatusBadge(application.reviewStatus)}</div>
          </div>
        </Card>
      </div>

      {/* Review Information */}
      {application.reviewerNotes && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Review Information</h3>
          </div>
          <div className="space-y-2">
            {application.reviewer && (
              <div><strong>Reviewer:</strong> {application.reviewer.firstName} {application.reviewer.lastName}</div>
            )}
            {application.reviewDate && (
              <div><strong>Review Date:</strong> {new Date(application.reviewDate).toLocaleDateString()}</div>
            )}
            <div><strong>Review Notes:</strong></div>
            <div className="p-3 bg-gray-50 rounded-md">
              {application.reviewerNotes}
            </div>
          </div>
        </Card>
      )}

      {/* Application Content */}
      <Tabs defaultValue="application" className="w-full">
        <TabsList>
          <TabsTrigger value="application">Application Content</TabsTrigger>
        </TabsList>

        <TabsContent value="application" className="space-y-6">
          {Object.entries(fieldsByGroup).map(([groupName, fields]) => (
            <Card key={groupName} className="p-6">
              <h3 className="text-lg font-semibold mb-4 capitalize">
                {groupName.replace('_', ' ')} Section
              </h3>
              <div className="space-y-4">
                {fields.map((field) => (
                  <div key={field.name} className="border-b pb-4 last:border-b-0">
                    <div className="flex justify-between items-start mb-2">
                      <label className="block text-sm font-medium">{field.label}</label>
                      <div className="flex gap-2">
                        {field.isScoreable && (
                          <Badge variant="outline">
                            Score: {field.score || 0}/{field.maxScore || 0}
                          </Badge>
                        )}
                        {field.complianceScore !== undefined && (
                          <Badge variant="outline">
                            Compliance: {field.complianceScore}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {/* Field Value */}
                    <div className="mb-2">
                      {field.type === 'file' ? (
                        <div>
                          {field.documents && field.documents.length > 0 ? (
                            field.documents.map((doc, index) => (
                              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                                <FileText className="h-4 w-4" />
                                <span className="flex-1">{doc.fileName}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => downloadDocument(field.name, doc.fileName)}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              </div>
                            ))
                          ) : (
                            <span className="text-gray-500">No file uploaded</span>
                          )}
                        </div>
                      ) : (
                        <div className="p-2 bg-gray-50 rounded">
                          {field.value || 'No value provided'}
                        </div>
                      )}
                    </div>

                    {/* Review Comments (Read-only) */}
                    {field.reviewerComment && (
                      <div className="mt-2">
                        <label className="block text-xs text-gray-600 mb-1">Review Comment</label>
                        <div className="p-2 bg-blue-50 rounded text-sm">
                          {field.reviewerComment}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
