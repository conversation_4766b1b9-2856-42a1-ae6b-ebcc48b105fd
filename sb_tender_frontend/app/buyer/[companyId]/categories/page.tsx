'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Button } from "@/components/ui/button"
import * as XLSX from 'xlsx';
import { useParams } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

const CategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [token, setToken] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
//template
const [categoryTemplate, setCategoryTemplate] = useState(null);
const [isTemplateFieldsModalVisible, setIsTemplateFieldsModalVisible] = useState(false);
const [selectedField, setSelectedField] = useState(null);
const [editedField, setEditedField] = useState(null);
//xls
const [excelCategories, setExcelCategories] = useState([]);
const [isExcelUploadVisible, setIsExcelUploadVisible] = useState(false);
const [isExcelSaving, setIsExcelSaving] = useState(false);
const params = useParams();
const [companyId, setCompanyId] = useState<string | null>(null);
const [systemCategories, setSystemCategories] = useState([]);
const [selectedJobCategories, setSelectedJobCategories] = useState([]);

useEffect(() => {
  if (params?.companyId && typeof params.companyId === 'string') {
    setCompanyId(params.companyId);
  }
}, [params]);

  const form = useForm({
    defaultValues: {
      name: '',
      description: '',
      type: '',
      status: 'active'
    }
  });

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);

  useEffect(() => {
    if (editingCategory) {
      form.reset({
        name: editingCategory.name,
        description: editingCategory.description,
        type: editingCategory.type,
        status: editingCategory.status
      });
    } else {
      form.reset({
        name: '',
        description: '',
        type: '',
        status: 'active'
      });
    }
  }, [editingCategory, form]);

  useEffect(() => {
    const fetchSystemCategories = async () => {
      try {
        const { data } = await axios.get('/api/categories', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        console.log('System categories', data);
        setSystemCategories(data);
      } catch (error) {
        toast.error('Failed to fetch system categories');
      }
    };
  
    if (token) {
      fetchSystemCategories();
    }
  }, [token]);

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`/api/companies/${companyId}/categories`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      setCategories(data);
    } catch (error) {
      toast.error('Failed to fetch categories');
    }
  };
  
  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddCategory =  () => {
    setEditingCategory(null);
    setIsModalVisible(true);
  };


  useEffect(() => {
    console.log('selectedJobCategories changed:', selectedJobCategories);
  }, [selectedJobCategories]);


  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setIsModalVisible(true);
  };

  const handleDeleteCategory = async (id) => {
    try {
      await axios.delete(`/api/companies/${companyId}/categories?categoryId=${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      toast.success('Company deleted successfully');
      fetchCategories();
    } catch (error) {
      toast.error('Failed to delete category');
    }
  };

  const onSubmit = async (values) => {
    console.log("Form values:", values);
    console.log("category update token", token);
    setIsSaving(true);
    try {
      if (editingCategory) {
        await axios.put(`/api/companies/${companyId}/categories?categoryId=${editingCategory._id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      } else {
        await axios.post(`/api/companies/${companyId}/categories`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      toast.success('Category saved successfully');
      //reset the form
      fetchCategories();
    } catch (error) {
      toast.error('Failed to save category');
    } finally {
      setTimeout(()=>
      setIsSaving(false), 2000);
      setIsModalVisible(false);
    }
  };



  const openTemplateFieldsModal = async (category) => {
    try {
      const { data } = await axios.get(`/api/companies/${companyId}/template?categoryId=${category._id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setCategoryTemplate(data);
      setIsTemplateFieldsModalVisible(true);
    } catch (error) {
      toast.error('Failed to fetch category criteria');
    }
  };


  const openFieldEditModal = (field) => setEditedField(field);
  const closeFieldEditModal = () => setEditedField(null);

  const handleFieldChange = (key, value) => {
    setEditedField((prev) => ({ ...prev, [key]: value }));
  };

  const handleTemplateUpdate = async (updatedTemplate) => {
    try {
      const { data } = await axios.put(
        `/api/companies/${companyId}/template?buyerCategoryId=${updatedTemplate.spBuyerCategoryId}&templateId=${updatedTemplate._id}`,
        { categoryTemplate: updatedTemplate },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setCategoryTemplate(data);
      toast.success('Template updated successfully');
    } catch (error) {
      toast.error('Failed to update template');
    }
  };
  
  const saveEditedField = async () => {
    try {
      console.log('Edited field:', editedField);
      const fieldExists = categoryTemplate.fields.some((field) => field.name === editedField.name);
  
      // Update or add the field
      const updatedFields = fieldExists
        ? categoryTemplate.fields.map((field) =>
            field.name === editedField.name ? editedField : field
          )
        : [...categoryTemplate.fields, editedField]; // Add new field if it doesn't exist
  
      const updatedTemplate = { ...categoryTemplate, fields: updatedFields };
      console.log(updatedTemplate);
      setCategoryTemplate(updatedTemplate);
        await handleTemplateUpdate(updatedTemplate);
  
      setEditedField(null);
    } catch (err) {
      console.error('Failed to update field');
    }
  };

  const deleteField = async (fieldName) => {
    try {
      // Filter out the field matching the fieldName
      const updatedFields = categoryTemplate.fields.filter((field) => field.name !== fieldName);
  
      // Update the state and persist the changes
      setCategoryTemplate((prev) => ({ ...prev, fields: updatedFields }));
      const updatedTemplate = { ...categoryTemplate, fields: updatedFields };
      await handleTemplateUpdate(updatedTemplate);
  
      toast.success('Field deleted');
    } catch (err) {
      toast.error('Failed to delete field');
    }
  };

  const addField = async () => {
    const newField = {
      name: '',
      label: '',
      type: 'text',
      helpText: '',
      placeholder: '',
      tooltip: '',
      description: '',
      required: false,
      editable: true,
      visible: true,
      maxScore: null,
      options: [],
      subgroup: '',
      group: '',
      order: 0,
    };
    const updatedFields = [...categoryTemplate.fields, newField];
  //
    try { 
      setEditedField(newField);
    } catch (err) {
      toast.error('Failed to add field');
    }
  };

const handleExcelUpload = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (event) => {
    try {
      const wb = XLSX.read(event.target.result, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws);
      
      console.log("Raw Excel data:", data); // For debugging
      
      // Extract using the correct column names
      const parsedCategories = data
        .filter(row => row['CATEGORY CODE'] && row['CATEGORY DESCRIPTION']) // Only include rows with both values
        .map(row => ({
          code: row['CATEGORY CODE'] || '',
          description: row['CATEGORY DESCRIPTION'] || '',
          systemCategory: '', // To be selected by user
          type: 'supplier_registration' // Default value
        }));
      
      console.log("Parsed categories:", parsedCategories); // For debugging
      
      if (parsedCategories.length === 0) {
        toast.error('No valid categories found. Please check your Excel format.');
        return;
      }
      
      setExcelCategories(parsedCategories);
      setIsExcelUploadVisible(true);
    } catch (error) {
      toast.error('Failed to parse Excel file');
      console.error(error);
    }
  };
  
  reader.readAsBinaryString(file);
};
  
  // Function to create categories from Excel data
  const handleCreateCategoriesFromExcel = async () => {
    // Validate if all excel categories have a systemCategory selected
    const isValid = excelCategories.every(cat => cat.systemCategory);
    
    if (!isValid) {
      toast.error('Please select a system category for all rows');
      return;
    }
    
    setIsSaving(true);
    try {
      for (const category of excelCategories) {
        await axios.post(`/api/companies/${companyId}/categories`, {
          name: category.code,
          description: category.description,
          type: category.type,
          spCategoryId: category.systemCategory,
          status: 'active'
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      
      toast.success('Categories created successfully');
      fetchCategories();
      setExcelCategories([]);
      setIsExcelUploadVisible(false);
    } catch (error) {
      toast.error('Failed to create categories');
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Function to update a specific Excel category row
  const updateExcelCategory = (index, field, value) => {
    const updatedCategories = [...excelCategories];
    updatedCategories[index][field] = value;
    setExcelCategories(updatedCategories);
  };

  const [newJobFormData, setNewJobFormData] = useState({
    title: "",
    description: "",
    contract: "",
    starts: "",
    ends: "",
    location: "",
    categoryPrice: "",
    status: "draft",
  });

  const handleNewJobSubmit = async () => {
    if(selectedJobCategories.length === 0) {
      toast.error('Please select a few categories first then click create a job.');
      return;
    }
    console.log(newJobFormData);
    console.log(selectedJobCategories);
    try {
      const response = await fetch(`/api/companies/${companyId}/jobs`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...newJobFormData,
          selectedJobCategories,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create job");
      }

      const data = await response.json();
      //redirect to company jobs
      toast.success('Buyer job created successfully.')
    } catch (error) {
      console.error("Error creating job:", error);
      toast.error('Error creatting buyer Job.')
    }
  };

  const handleNewJobChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewJobFormData(prev => ({ ...prev, [id]: value }));
  };

  return (
    <div className="p-4">
            <Toaster />
      <h1 className="text-2xl font-bold text-gray-800">My Company's 'Master Categories'</h1>

      <div className="flex items-center justify-between mb-6">
        <div className="relative w-64">
          <input
            type="text"
            placeholder="Search categories"
            value={search}
            onChange={handleSearch}
            className="w-full p-2 border rounded"
          />
        </div>
        <button
          onClick={handleAddCategory}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          + Add Category
        </button>

        <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">Add a New Buyer Job</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Job</DialogTitle>
          <DialogDescription>
            Fill in the details for the new buyer job.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Title
            </Label>
            <Input
              id="title"
              value={newJobFormData.title}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Input
              id="description"
              value={newJobFormData.description}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contract" className="text-right">
              Contract
            </Label>
            <Input
              id="contract"
              value={newJobFormData.contract}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="starts" className="text-right">
              Starts
            </Label>
            <Input
              id="starts"
              type="date"
              value={newJobFormData.starts}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="ends" className="text-right">
              Ends
            </Label>
            <Input
              id="ends"
              type="date"
              value={newJobFormData.ends}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="location" className="text-right">
              Location
            </Label>
            <Input
              id="location"
              value={newJobFormData.location}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="categoryPrice" className="text-right">
              Category Price
            </Label>
            <Input
              id="categoryPrice"
              type="number"
              value={newJobFormData.categoryPrice}
              onChange={handleNewJobChange}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" onClick={handleNewJobSubmit}>
            Create Job
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

        <button
          onClick={() => document.getElementById('excelUpload').click()}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 ml-2"
        >
          Upload Excel Caegories
        </button>
        <input
          type="file"
          id="excelUpload"
          accept=".xlsx, .xls"
          onChange={handleExcelUpload}
          className="hidden"
        />

      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border">
          <thead>
            <tr>
              <th>Select</th>
              <th className="px-4 py-2 border">Name</th>
              <th className="px-4 py-2 border">Description</th>
              <th className="px-4 py-2 border">Type</th>
              <th className="px-4 py-2 border">Status</th>
              <th className="px-4 py-2 border">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredCategories.map((category) => (
              <tr key={category._id}>
                <td className="px-4 py-2 border">
                  <input
                    type="checkbox"
                    checked={selectedJobCategories.includes(category._id)}
                    onChange={() => {
                      if (selectedJobCategories.includes(category._id)) {
                        setSelectedJobCategories(selectedJobCategories.filter(id => id !== category._id));
                      } else {
                        setSelectedJobCategories([...selectedJobCategories, category._id]);
                      }
                    }}
                  /></td>
                <td className="px-4 py-2 border">{category.name}</td>
                <td className="px-4 py-2 border">{category.description}</td>
                <td className="px-4 py-2 border">{category.type}</td>
                <td className="px-4 py-2 border">{category.status}</td>
                <td className="px-4 py-2 border">
                  <div className="flex space-x-2">
                    <button
                      className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                      onClick={() => handleEditCategory(category)}
                    >
                      Edit
                    </button>
                    <button
                      className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                      onClick={() => openTemplateFieldsModal(category)}
                    >
                      Criteria
                    </button>
                    <button
                      className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                      onClick={() => handleDeleteCategory(category._id)}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {isExcelUploadVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg w-full max-w-6xl max-h-[90vh] overflow-auto">
            <h2 className="text-xl font-semibold mb-4">Create Categories from Excel</h2>
            
            {excelCategories.length === 0 ? (
              <p>No valid categories found in the Excel file.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 border">Code</th>
                      <th className="px-4 py-2 max-w-4 border">Description</th>
                      <th className="px-4 py-2 border">System Category</th>
                      <th className="px-4 py-2 border">Type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {excelCategories.map((category, index) => (
                      <tr key={index}>
                        <td className="px-4 py-2 border">{category.code}</td>
                        <td className="px-4 py-2 border">{category.description}</td>
                        <td className="px-4 py-2 border">
                          <select 
                            value={category.systemCategory}
                            onChange={(e) => updateExcelCategory(index, 'systemCategory', e.target.value)}
                            className="w-full p-2 border rounded"
                            required
                          >
                            <option value="">Select system category</option>
                            {systemCategories.map((cat) => (
                              <option key={cat._id} value={cat._id}>{cat.name}</option>
                            ))}
                          </select>
                        </td>
                        <td className="px-4 py-2 border">
                          <select 
                            value={category.type}
                            onChange={(e) => updateExcelCategory(index, 'type', e.target.value)}
                            className="w-full p-2 border rounded"
                            required
                          >
                            <option value="supplier_registration">Supplier Registration</option>
                            <option value="supplier_prequalification">Supplier Prequalification</option>
                            <option value="rfq">RFQ</option>
                            <option value="tender">Tender</option>
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            <div className="flex justify-end space-x-2 pt-4 mt-4">
              <button
                type="button"
                className="px-4 py-2 border rounded hover:bg-gray-100"
                onClick={() => setIsExcelUploadVisible(false)}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateCategoriesFromExcel}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                disabled={isSaving || excelCategories.length === 0}
              >
                {isSaving ? (
                  <>
                    <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                    </svg>
                    Creating...
                  </>
                ) : 'Create Categories'}
              </button>
            </div>
          </div>
        </div>
      )}

      {isModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg w-full max-w-xl">
            <h2 className="text-xl font-semibold mb-4">{editingCategory ? 'Edit Category' : 'Add Category'}</h2>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input type="text" {...form.register('name')} placeholder="Category name" className="w-full p-2 border rounded" required/>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea {...form.register('description')} placeholder="Category description" className="w-full p-2 border rounded" required/>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Type</label>
                <select {...form.register('type')} className="w-full p-2 border rounded" required>
                  <option value="">Select type</option>
                  <option value="supplier_registration">Supplier Registration</option>
                  <option value="supplier_prequalification">Supplier Prequalification</option>
                  <option value="rfq">RFQ</option>
                  <option value="tender">Tender</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <select {...form.register('status')} className="w-full p-2 border rounded" required>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <button
                  type="button"
                  className="px-4 py-2 border rounded hover:bg-gray-100"
                  onClick={() => setIsModalVisible(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : 'Save'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

{isTemplateFieldsModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
          <div className="bg-white w-full max-w-3xl p-6 rounded shadow-xl overflow-auto max-h-[90vh]">
            <h3 className="text-xl font-semibold mb-4">Criteria Fields</h3>
            <ul className="space-y-3">
              {categoryTemplate?.fields?.map((field, index) => (
                <li key={index} className="border p-3 rounded flex justify-between items-center">
                  <div>
                    <p className="font-medium">{field.label || 'Unnamed field'}</p>
                    <p className="text-sm text-gray-500">{field.name} — {field.type}</p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="px-3 py-1 bg-indigo-500 text-white rounded hover:bg-indigo-600"
                      onClick={() => openFieldEditModal(field)}
                    >
                      Edit
                    </button>
                    <button
                      className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                      onClick={() => deleteField(field.name)}
                    >
                      Delete
                    </button>
                  </div>
                </li>
              ))}
            </ul>
            <div className="flex justify-between mt-4">
              <button
                onClick={addField}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                + Add Field
              </button>
              <button
                onClick={() => setIsTemplateFieldsModalVisible(false)}
                className="px-4 py-2 border rounded hover:bg-gray-100"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {editedField && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
          <div className="bg-white w-full max-w-3xl p-6 rounded shadow-xl overflow-auto max-h-[90vh]">
            <h3 className="text-xl font-semibold mb-4">Modify criterion field</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Label</label>
                <input type="text" value={editedField.label || ''} onChange={(e) => handleFieldChange('label', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input type="text" value={editedField.description || ''} onChange={(e) => handleFieldChange('description', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input type="text" value={editedField.name || ''} onChange={(e) => handleFieldChange('name', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Type</label>
                <select value={editedField.type || 'text'} onChange={(e) => handleFieldChange('type', e.target.value)} className="w-full p-2 border rounded">
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="select">Select</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Placeholder</label>
                <input type="text" value={editedField.placeholder || ''} onChange={(e) => handleFieldChange('placeholder', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Tooltip</label>
                <input type="text" value={editedField.tooltip || ''} onChange={(e) => handleFieldChange('tooltip', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Help Text</label>
                <input type="text" value={editedField.helpText || ''} onChange={(e) => handleFieldChange('helpText', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Required</label>
                <input
                  type="checkbox"
                  checked={editedField.isRequired === true} // Explicitly check for true
                  onChange={(e) => handleFieldChange('isRequired', e.target.checked)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Editable</label>
                <input type="checkbox" checked={editedField.isEditable || false} onChange={(e) => handleFieldChange('isEditable', e.target.checked)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Visible</label>
                <input type="checkbox" checked={editedField.isVisible || false} onChange={(e) => handleFieldChange('isVisible', e.target.checked)} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Group</label>
                <input type="text" value={editedField.group || ''} onChange={(e) => handleFieldChange('group', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Subgroup</label>
                <input type="text" value={editedField.subGroup || ''} onChange={(e) => handleFieldChange('subGroup', e.target.value)} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Order</label>
                <input type="number" value={editedField.order || 0} onChange={(e) => handleFieldChange('order', parseInt(e.target.value))} className="w-full p-2 border rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Max Sccore</label>
                <input type="number" value={editedField.maxScore || 0} onChange={(e) => handleFieldChange('maxScore', parseInt(e.target.value))} className="w-full p-2 border rounded" />
              </div>
              <div className="col-span-2 flex justify-end mt-4 gap-2">
                <button onClick={closeFieldEditModal} className="px-4 py-2 border rounded hover:bg-gray-100">Cancel</button>
                <button onClick={saveEditedField} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Save</button>
              </div>
            </div>
          </div>
        </div>
      )}





    </div>
  );
};

export default CategoriesPage;
