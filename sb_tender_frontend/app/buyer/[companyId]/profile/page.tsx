'use client';

import { useState, useEffect } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import { Combine,  Copy, Pen, Trash2, Users2, Building, Mail, Phone, MapPin, FileText, Image } from 'lucide-react';
import { Button } from "@/components/ui/button"
import { useParams } from 'next/navigation';
import { X, Download } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Companies() {
  const [companies, setCompanies] = useState([]);
  const [search, setSearch] = useState('');
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [type, setType] = useState<string | null >(null);
  const [downloadingStates, setDownloadingStates] = useState<Record<string, boolean>>({});
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const [logoLoading, setLogoLoading] = useState(false);
  const params = useParams();

  const { companyId } = params;

 const [token, setToken] = useState<string | null>(null);
 useEffect(() => {
  fetchCompanies();
}, [token]);

useEffect(() => {
  const company = companies[0];
  if (company) {
    downloadLogoForPreview(company);
  }
}, [companies]);

// Cleanup blob URLs on unmount
useEffect(() => {
  return () => {
    if (logoPreviewUrl) {
      URL.revokeObjectURL(logoPreviewUrl);
    }
  };
}, [logoPreviewUrl]);

useEffect(() => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    console.log('Type', type);
    setType(type);
  }
}, []);


  useEffect(() => {
    const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (token) {
      setToken(token);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);


  const fetchCompanies = async () => {
    const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (!token) {
      console.error("Token not found in cookies");
      return;
    }
    const url = type ? `/api/companies?type=${type}` : `/api/companies`;
    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`, // Add the token to the Authorization header
        },
      });

      const data = await response.json();
      console.log(data);

      // Ensure the response is an array
      if (Array.isArray(data)) {
        const filteredCompanies = data.filter(company => company._id === companyId);
        console.log('FIltered',filteredCompanies );
        setCompanies(filteredCompanies);
      } else {
        console.error('API response is not an array:', data);
        setCompanies([]); // Fallback to an empty array
      }
    } catch (error) {
      console.error('Failed to fetch companies:', error);
      toast.error('Failed to fetch companies');
      setCompanies([]); // Fallback to an empty array
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const filteredCompanies = Array.isArray(companies)
    ? companies.filter((company) =>
        company.name.toLowerCase().includes(search.toLowerCase())
      )
    : []; // Ensure `filteredCompanies` is always an array

  const handleAddCompany = () => {
    setIsModalOpen(true);
  };

  const handleEditCompany = (company: any) => {
    setSelectedCompany(company);
    setIsModalOpen(true);
  };

  const handleDeleteCompany = async (id: string) => {
    try {
      await fetch(`/api/companies?id=${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`, // Add the token to the Authorization header
        },
      });

      toast.success('Company deleted successfully');
      fetchCompanies();
    } catch (error) {
      toast.error('Failed to delete company');
    }
  };
  

  const handleCopyLink = async (companyId: string) => {
    const link = `${window.location.origin}/buyer-setup/${companyId}`;
    
    try {
      // Modern clipboard API
      await navigator.clipboard.writeText(link);
      showFeedback();
    } catch (err) {
      console.log('Modern clipboard failed, trying fallback');
      try {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = link;
        textArea.style.position = 'fixed';  // Prevent scrolling
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          showFeedback();
        } else {
          throw new Error('Fallback copy failed');
        }
      } catch (fallbackErr) {
        console.error('All copy methods failed:', fallbackErr);
        // Optionally show error to user
      }
    }
  };
  
  const showFeedback = () => {
    setIsCopied(true);
    toast.success('Company admin access link copied successfully');
    setTimeout(() => setIsCopied(false), 3000);
  };

  const downloadLogoForPreview = async (company: any) => {
    const logoDocument = company?.documents?.find((doc: any) => doc.type === 'logoUrl');

    if (!logoDocument) {
      setLogoPreviewUrl(null);
      return;
    }

    try {
      setLogoLoading(true);
      const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        console.error('No token found');
        return;
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/admin/companies/documents/download?companyId=${company._id}&documentType=logoUrl&fileName=${logoDocument.fileName}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const blob = await response.blob();
        const imageUrl = URL.createObjectURL(blob);
        setLogoPreviewUrl(imageUrl);
      } else {
        console.error('Failed to download logo');
        setLogoPreviewUrl(null);
      }
    } catch (error) {
      console.error('Error downloading logo:', error);
      setLogoPreviewUrl(null);
    } finally {
      setLogoLoading(false);
    }
  };

  const handleDownload = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const target = e.currentTarget as HTMLElement;
    const companyId = target.dataset.companyId;
    const documentType = target.dataset.documentType;
    const fileName = target.dataset.fileName;

    // Create unique key for this download
    const downloadKey = `${companyId}-${documentType}`;
    // Set downloading state to true
    setDownloadingStates(prev => ({
      ...prev,
      [downloadKey]: true
    }));

    if (!companyId || !documentType || !fileName) {
      toast.error("Missing document metadata");
      setDownloadingStates(prev => ({
        ...prev,
        [downloadKey]: false
      }));
      return;
    }

    const url = `/api/companies/download?companyId=${companyId}&documentType=${documentType}&fileName=${fileName}`;

    try {
      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) throw new Error('Failed to download document');

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = fileName!;
      document.body.appendChild(a);
      a.click();
      a.remove();

      window.URL.revokeObjectURL(blobUrl);
      toast.success('Document downloaded successfully');
    } catch (err) {
      console.error(err);
      toast.error("Failed to download document");
    } finally {
      // Reset downloading state
      setDownloadingStates(prev => ({
        ...prev,
        [downloadKey]: false
      }));
    }
  };

  const company: any = filteredCompanies[0]; // Get the first (and typically only) company

  return (
    <div className="min-h-screen p-6">
      <Toaster />

      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Company Profile</h1>
          <p className="text-muted-foreground mt-1">Manage your company information and documents</p>
        </div>
      </div>

      {company ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card className="border shadow-sm">
              <CardContent className="p-6">
                <div className="text-center">
                  {/* Logo Section */}
                  <div className="w-24 h-24 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    {logoLoading ? (
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    ) : logoPreviewUrl ? (
                      <img
                        src={logoPreviewUrl}
                        alt={`${company.name} logo`}
                        className="w-full h-full object-cover rounded-full"
                      />
                    ) : (
                      <Building className="h-12 w-12 text-muted-foreground" />
                    )}
                  </div>

                  <h2 className="text-xl font-bold mb-2">{company.name}</h2>
                  <Badge variant="outline" className="mb-4 capitalize">
                    {company.type}
                  </Badge>

                  <div className="space-y-3 text-left">
                    <div className="flex items-center space-x-3">
                      <Users2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{company.contactPerson}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{company.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{company.phone}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{company.address}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Reg: {company.registrationNumber}</span>
                    </div>
                  </div>

                  <Button
                    className="w-full mt-6"
                    onClick={() => handleEditCompany(company)}
                  >
                    <Pen className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>

                  <Button 
                    className="w-full mt-6"
                    onClick={() => {
                      window.location.href = `/buyer/${company._id}/categories`;
                    }} variant="outline">
                    <Combine  className="h-4 w-4 mr-2" />Manage Company Categories
                    </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Documents Section */}
          <div className="lg:col-span-2">
            <Card className="border shadow-sm">
              <CardHeader>
                <CardTitle>Company Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { type: 'logoUrl', label: 'Company Logo', icon: Image },
                    { type: 'contract', label: 'Contract', icon: FileText },
                    { type: 'kraPinCertificate', label: 'KRA PIN Certificate', icon: FileText },
                    { type: 'certificateOfIncorporation', label: 'Certificate of Incorporation', icon: FileText },
                    { type: 'tradingLicense', label: 'Trading License', icon: FileText },
                    { type: 'companyCR12', label: 'Company CR12', icon: FileText },
                    { type: 'taxComplianceCertificate', label: 'Tax Compliance Certificate', icon: FileText },
                  ].map((docType) => {
                    const document = company.documents?.find((doc: any) => doc.type === docType.type);
                    const IconComponent = docType.icon;

                    return (
                      <div key={docType.type} className="p-4 border rounded-lg bg-card">
                        <div className="flex items-center space-x-3 mb-2">
                          <IconComponent className="h-5 w-5 text-muted-foreground" />
                          <h3 className="font-medium">{docType.label}</h3>
                        </div>

                        {document ? (
                          <div className="space-y-2">
                            <p className="text-sm text-muted-foreground truncate">{document.fileName}</p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleDownload}
                              data-company-id={company._id}
                              data-document-type={document.type}
                              data-file-name={document.fileName}
                              disabled={downloadingStates[`${company._id}-${document.type}`]}
                              className="w-full"
                            >
                              <Download className="h-4 w-4 mr-2" />
                              {downloadingStates[`${company._id}-${document.type}`]
                                ? 'Downloading...'
                                : 'Download'}
                            </Button>
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">Not uploaded</p>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <Card className="border shadow-sm">
          <CardContent className="p-8 text-center">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Loading...</h3>
            <p className="text-muted-foreground">Wait a moment as we load your company profile.</p>
          </CardContent>
        </Card>
      )}
      {isModalOpen && (
        <CompanyModal
          company={selectedCompany}
          onClose={() => setIsModalOpen(false)}
          onSave={fetchCompanies}
        />
      )}
    </div>
  );
}



function CompanyModal({ company, onClose, onSave }: any) {
  const [isLoading, setIsLoading] = useState(false);
  const [downloadingStates, setDownloadingStates] = useState<Record<string, boolean>>({});
  const [modalLogoPreviewUrl, setModalLogoPreviewUrl] = useState<string | null>(null);
  const [modalLogoLoading, setModalLogoLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: company?.name || '',
    contactPerson: company?.contactPerson || '',
    phone: company?.phone || '',
    email: company?.email || '',
    registrationNumber: company?.registrationNumber || '',
    address: company?.address || '',
    documents: {
      logoUrl: null,
      contract: null,
      kraPinCertificate: null,
      certificateOfIncorporation: null,
      tradingLicense: null,
      companyCR12: null,
      taxComplianceCertificate: null,
    },
  });

  // Download logo for modal preview
  useEffect(() => {
    const downloadModalLogoPreview = async () => {
      const logoDocument = company?.documents?.find((doc: any) => doc.type === 'logoUrl');

      if (!logoDocument) {
        setModalLogoPreviewUrl(null);
        return;
      }

      try {
        setModalLogoLoading(true);
        const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

        const token = document.cookie
          .split('; ')
          .find((row) => row.startsWith('token='))
          ?.split('=')[1];

        if (!token) {
          console.error('No token found');
          return;
        }

        const response = await fetch(
          `${BACKEND_API_URL}/api/admin/companies/documents/download?companyId=${company._id}&documentType=logoUrl&fileName=${logoDocument.fileName}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        );

        if (response.ok) {
          const blob = await response.blob();
          const imageUrl = URL.createObjectURL(blob);
          setModalLogoPreviewUrl(imageUrl);
        } else {
          console.error('Failed to download logo');
          setModalLogoPreviewUrl(null);
        }
      } catch (error) {
        console.error('Error downloading logo:', error);
        setModalLogoPreviewUrl(null);
      } finally {
        setModalLogoLoading(false);
      }
    };

    if (company) {
      downloadModalLogoPreview();
    }
  }, [company]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      // Handle all files (including logo) as documents
      setFormData((prev) => ({
        ...prev,
        documents: { ...prev.documents, [name]: files[0] },
      }));
    }
  };

  const handleDownload = async (e: React.MouseEvent<HTMLSpanElement>) => {
    e.preventDefault();
  
    const target = e.currentTarget as HTMLElement;
    const companyId = target.dataset.companyId;
    const documentType = target.dataset.documentType;
    const fileName = target.dataset.fileName;

    // Create unique key for this download
    const downloadKey = `${companyId}-${documentType}`;
    // Set downloading state to true
    setDownloadingStates(prev => ({
      ...prev,
      [downloadKey]: true
    }));

    setIsLoading(true);
  
    if (!companyId || !documentType || !fileName) {
      toast.error("Missing document metadata");
      return;
    }
  
    const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';
    const url = `${BACKEND_API_URL}/api/admin/companies/documents/download?companyId=${companyId}&documentType=${documentType}&fileName=${fileName}`;
  
    try {
      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];
  
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      if (!response.ok) throw new Error('Failed to download document');
  
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
  
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = fileName!;
      document.body.appendChild(a);
      a.click();
      a.remove();
  
      window.URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error(err);
      toast.error("Failed to download document");
    } finally {
      // Reset downloading state
      setDownloadingStates(prev => ({
        ...prev,
        [downloadKey]: false
      }));
      setIsLoading(false);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';
      const method = company ? 'PUT' : 'POST';
      const url = company
        ? `${BACKEND_API_URL}/api/admin/companies/${company._id}`
        : `${BACKEND_API_URL}/api/admin/companies`;

      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name);
      formDataToSend.append('contactPerson', formData.contactPerson);
      formDataToSend.append('phone', formData.phone);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('address', formData.address);
      formDataToSend.append('registrationNumber', formData.registrationNumber);

      // Handle all documents (including logo) the same way
      Object.entries(formData.documents).forEach(([key, file]) => {
        if (file) {
          formDataToSend.append(key, file);
        }
      });

      const response = await fetch(url, {
        method: method,
        body: formDataToSend,
        headers: {
          Authorization: `Bearer ${document.cookie
            .split('; ')
            .find((row) => row.startsWith('token='))
            ?.split('=')[1]}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to update company');
      }

      toast.success("Company updated successfully");
      onSave();
      onClose();
    } catch (error) {
      toast.error("Failed to save company");
    } finally {
      setIsLoading(false);
    }
  };

  const kraPinDocument = company?.documents?.find(
    (doc) => doc.type === 'kraPinCertificate'
  );
  const certificateOfIncorporationDocument = company?.documents?.find(
    (doc) => doc.type === 'certificateOfIncorporation'
  );
  const tradingLicenseDocument = company?.documents?.find(
    (doc) => doc.type === 'tradingLicense'
  );
  const companyCR12Document = company?.documents?.find(
    (doc) => doc.type === 'companyCR12'
  );
  const taxComplianceCertificateDocument = company?.documents?.find(
    (doc) => doc.type === 'taxComplianceCertificate'
  );
  const contractDocument = company?.documents?.find(
    (doc) => doc.type === 'contract'
  );

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl">
        <CardHeader className="relative">
          <CardTitle>{company ? 'Edit Company' : 'Add Company'}</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 h-8 w-8 rounded-sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} encType="multipart/form-data">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Logo Upload Section */}
              <div className="space-y-2 md:col-span-2">
                <Label>Company Logo</Label>
                {modalLogoLoading ? (
                  <div className="flex items-center space-x-4 p-3 bg-muted rounded-md">
                    <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Loading logo...</p>
                    </div>
                  </div>
                ) : modalLogoPreviewUrl ? (
                  <div className="flex items-center space-x-4 p-3 bg-muted rounded-md">
                    <img
                      src={modalLogoPreviewUrl}
                      alt="Current logo"
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium">Current Logo</p>
                      <p className="text-xs text-muted-foreground">Upload a new file to replace</p>
                    </div>
                  </div>
                ) : null}
                <Input
                  type="file"
                  name="logoUrl"
                  accept=".jpg,.jpeg,.png"
                  onChange={handleFileChange}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
                />
                <p className="text-xs text-muted-foreground">Accepted formats: JPG, JPEG, PNG</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Company Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact Name</Label>
                <Input
                  id="contactPerson"
                  name="contactPerson"
                  value={formData.contactPerson}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="registrationNumber">Registration Number</Label>
                <Input
                  id="registrationNumber"
                  name="registrationNumber"
                  value={formData.registrationNumber}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  required
                />
              </div>

              {/* Document Upload Sections */}
              <div className="space-y-2">
                <Label>Contract</Label>
                {contractDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{contractDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={contractDocument.type}
                      data-file-name={contractDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${contractDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${contractDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="contract"
                  onChange={handleFileChange}
                  required={!contractDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>KRA PIN Certificate</Label>
                {kraPinDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{kraPinDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={kraPinDocument.type}
                      data-file-name={kraPinDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${kraPinDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${kraPinDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="kraPinCertificate"
                  onChange={handleFileChange}
                  required={!kraPinDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Certificate of Incorporation</Label>
                {certificateOfIncorporationDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{certificateOfIncorporationDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={certificateOfIncorporationDocument.type}
                      data-file-name={certificateOfIncorporationDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${certificateOfIncorporationDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${certificateOfIncorporationDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="certificateOfIncorporation"
                  onChange={handleFileChange}
                  required={!certificateOfIncorporationDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Trading License</Label>
                {tradingLicenseDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{tradingLicenseDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={tradingLicenseDocument.type}
                      data-file-name={tradingLicenseDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${tradingLicenseDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${tradingLicenseDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="tradingLicense"
                  onChange={handleFileChange}
                  required={!tradingLicenseDocument}
                />
              </div>

              <div className="space-y-2">
                <Label>Company CR12</Label>
                {companyCR12Document && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{companyCR12Document.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={companyCR12Document.type}
                      data-file-name={companyCR12Document.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${companyCR12Document.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${companyCR12Document.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="companyCR12"
                  onChange={handleFileChange}
                  required={!companyCR12Document}
                />
              </div>

              <div className="space-y-2">
                <Label>Tax Compliance Certificate</Label>
                {taxComplianceCertificateDocument && (
                  <div className="flex items-center justify-between p-2 bg-muted rounded-md text-sm">
                    <span className="truncate max-w-[70%]">{taxComplianceCertificateDocument.fileName}</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleDownload}
                      data-company-id={company._id}
                      data-document-type={taxComplianceCertificateDocument.type}
                      data-file-name={taxComplianceCertificateDocument.fileName}
                      className="h-auto p-0"
                      disabled={downloadingStates[`${company._id}-${taxComplianceCertificateDocument.type}`]}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {downloadingStates[`${company._id}-${taxComplianceCertificateDocument.type}`] 
                        ? 'Downloading...' 
                        : 'Download'}
                    </Button>
                  </div>
                )}
                <Input
                  type="file"
                  name="taxComplianceCertificate"
                  onChange={handleFileChange}
                  required={!taxComplianceCertificateDocument}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit"
                disabled={isLoading}>
                {isLoading ? 'Loading...' : 'Save'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}