'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Co<PERSON><PERSON>, Pen, Trash2, Users, BarChart3 } from 'lucide-react';
import { Button } from "@/components/ui/button"
import { useParams } from 'next/navigation';
import { FileDown } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog"



const CategoriesPage = () => {
  const [companyId, setCompanyId] = useState<string>('');

useEffect(() => {
  if (typeof window !== 'undefined') {
    const storedCompany = localStorage.getItem('activeCompanyId');
    if (storedCompany) {
      setCompanyId( storedCompany);
      console.log('Stored active company', storedCompany);
    } else {
      console.error("activeCompanyId not found in localStorage");
      const pathParts = window.location.pathname.split('/');
      const companyId = pathParts[2];
      setCompanyId( companyId);
      console.log('Company ID:', companyId);
    
    }
  }
}, []);
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [token, setToken] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
//ttemplate
const [jobCategory, setCategoryTemplate] = useState(null);
const [isTemplateFieldsModalVisible, setIsTemplateFieldsModalVisible] = useState(false);
const [selectedField, setSelectedField] = useState(null);
const [editedField, setEditedField] = useState(null);


const params = useParams();
const [jobId, setJobId] = useState<string | null>(null);
const [systemCategories, setSystemCategories] = useState([]);
const [selectedJobCategories, setSelectedJobCategories] = useState([]);

useEffect(() => {
  if (params?.jobId && typeof params.jobId === 'string') {
    setJobId(params.jobId);
  }
}, [params]);

  const form = useForm({
    defaultValues: {
      title: '',
      description: '',
      type: '',
      status: 'active'
    }
  });

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);

  useEffect(() => {
    if (editingCategory) {
      form.reset({
        title: editingCategory.title,
        description: editingCategory.description,
        type: editingCategory.type,
        status: editingCategory.status
      });
    } else {
      form.reset({
        title: '',
        description: '',
        type: '',
        status: 'active'
      });
    }
  }, [editingCategory, form]);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${jobId}/categories`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log(data);
      setCategories(data);
    } catch (error) {
      toast.error('Failed to fetch categories');
    }
  };
  
  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category) =>
    category.title.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddCategory = () => {
    setEditingCategory(null);
    setIsModalVisible(true);
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setIsModalVisible(true);
  };

  const handleDeleteCategory = async (id) => {
    try {
      await axios.delete(`/api/categories?id=${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      toast.success('Company deleted successfully');
      fetchCategories();
    } catch (error) {
      toast.error('Failed to delete category');
    }
  };

  const onSubmit = async (values) => {
    console.log("Form values:", values);
    console.log("category update token", token);
    setIsSaving(true);
    try {
      if (editingCategory) {
        await axios.put(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${editingCategory.spJobId}/categories/${editingCategory._id}`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      } else {
        console.log('jobId', jobId);
        await axios.post(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${jobId}/categories`, values, {
          headers: {
            Authorization: `Bearer ${token}`,
          }
        });
      }
      toast.success('Category saved successfully');
      //reset the form
      fetchCategories();
    } catch (error) {
      toast.error('Failed to save category');
    } finally {
      setTimeout(()=>
      setIsSaving(false), 2000);
      setIsModalVisible(false);
    }
  };



  const openTemplateFieldsModal = async (category) => {
    try {
      setCategoryTemplate(category);
      setIsTemplateFieldsModalVisible(true);
    } catch (error) {
      toast.error('Failed to fetch category criteria');
    }
  };


  const openFieldEditModal = (field) => setEditedField(field);
  const closeFieldEditModal = () => setEditedField(null);

  const handleFieldChange = (key, value) => {
    console.log(`Before update - ${key}:`, editedField[key]);
    console.log(`Setting ${key} to:`, value);
    
    // Create a completely new object to ensure React detects the change
    const updatedField = { ...editedField };
    updatedField[key] = value;
    
    console.log("Updated field object:", updatedField);
    setEditedField(updatedField);
    
    setTimeout(() => {
      console.log(`After update - ${key}:`, editedField[key]);
    }, 0);
  };

  const handleTemplateUpdate = async (updatedCategory) => {
    try {
              //`/api/jobs/categories?jobId=${updatedCategory.spJobId}&categoryId=${jobCategory._id}`
      const { data } = await axios.put(
        `${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${updatedCategory.spJobId}/categories/${jobCategory._id}`,
        { jobCategory: updatedCategory },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setCategoryTemplate(data);
      toast.success('Category criteria updated successfully');
    } catch (error) {
      toast.error('Failed to update category criterria');
    }
  };
  
  const saveEditedField = async () => {
    try {
      // Check if the field already exists
      setIsSaving(true);
      console.log('Edited field:', editedField);
      const fieldExists = jobCategory.fields.some((field) => field.name === editedField.name);
  
      // Update or add the field
      const updatedFields = fieldExists
        ? jobCategory.fields.map((field) =>
            field.name === editedField.name ? editedField : field
          )
        : [...jobCategory.fields, editedField]; // Add new field if it doesn't exist
  
      // Create the updated template
      const updatedCategory = { ...jobCategory, fields: updatedFields };
      console.log(updatedCategory);
      // Update the state
      setCategoryTemplate(updatedCategory);
  
      // Persist the changes
      await handleTemplateUpdate(updatedCategory);
  
      // Clear the edited field
      setEditedField(null);
    } catch (err) {
      console.error('Failed to update field');
    } finally {
      setIsSaving(false);
    }
  };

  const deleteField = async (fieldName) => {
    try {
      const updatedFields = jobCategory.fields.filter((field) => field.name !== fieldName);
  
      // Update the state and persist the changes
      setCategoryTemplate((prev) => ({ ...prev, fields: updatedFields }));
      const updatedCategory = { ...jobCategory, fields: updatedFields };
      await handleTemplateUpdate(updatedCategory);
       } catch (err) {
      toast.error('Failed to delete field');
    }
  };

  const addField = async () => {
    const newField = {
      name: '',
      label: '',
      type: 'text',
      helpText: '',
      placeholder: '',
      tooltip: '',
      description: '',
      required: false,
      editable: true,
      visible: true,
      maxScore: null,
      options: [],
      subgroup: '',
      group: '',
      order: 0,
    };
    const updatedFields = [...jobCategory.fields, newField];

    try { 
      setEditedField(newField);
    } catch (err) {
      toast.error('Failed to add field');
    }
  };


  return (
    <div className="p-4">
            <Toaster />
            <h1 className="text-2xl font-bold mb-3">Job Categories ({filteredCategories.length > 0 ? filteredCategories[0].description: ''} )</h1>

      <div className="flex items-center justify-between mb-6">
        <div className="relative w-64">
          <input
            type="text"
            placeholder="Search categories"
            value={search}
            onChange={handleSearch}
            className="w-full p-2 border rounded"
          />
        </div>

      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border">
          <thead>
            <tr>
              <th className="px-4 py-2 border">Title</th>
              <th className="px-4 py-2 border">Description</th>
              <th className="px-4 py-2 border">Opens</th>
              <th className="px-4 py-2 border">Closes</th>
              <th className="px-4 py-2 border">Type</th>
              <th className="px-4 py-2 border">Bidders</th>
              <th className="px-4 py-2 border">Status</th>
              <th className="px-4 py-2 border">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredCategories.map((category) => (
              <tr key={category._id}>
                <td className="px-4 py-2 border">{category.title}</td>
                <td className="px-4 py-2 border">{category.description}</td>
                <td className="px-4 py-2 border">{category.starts}</td>
                <td className="px-4 py-2 border">{category.ends}</td>
                <td className="px-4 py-2 border">{category.type}</td>
                <td className="px-4 py-2 border">0</td>
                <td className="px-4 py-2 border">{category.status}</td>
                <td className="px-4 py-2 border">
                  <div className="flex space-x-2">
                  
                  {category.status === 'draft' && (
                    <Button variant="outline" size="sm" onClick={() => {console.log('clicked'); handleEditCategory(category) ;}} >
                      <Pen />Edit
                    </Button>
                  )}
                    <Button variant="outline" size="sm" onClick={() =>  openTemplateFieldsModal(category) } >
                      <Combine />Criteria
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => {
                      window.location.href = `/buyer/${companyId}/categories/${category._id}/applications`;
                    }} >
                      <Users />Applications
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => {
                      window.location.href = `/buyer/${companyId}/jobs/${jobId}/categories/${category._id}/reports`;
                    }} >
                      <BarChart3 />Reports
                    </Button>
                    {category.status === 'draft' && (
                    <Button variant="outline" size="sm" onClick={() => {console.log('clicked'); handleDeleteCategory(category._id) ;}} >
                      <Trash2 /> Delete
                    </Button>
                    )}

                    {(category.status === 'open' || category.status === 'closed') && (
                    <Button variant="outline" size="sm">
                      <FileDown /> Bid Report
                    </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

  

<Dialog open={isModalVisible} onOpenChange={ ()=> setIsModalVisible(false) }>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit {editingCategory?.title}</DialogTitle>
              <DialogDescription>
                Update the category details below.
              </DialogDescription>
            </DialogHeader>
            {/* Wrap form content in a form tag and use handleSubmit */}
          

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input type="text" {...form.register('title')} placeholder="Category title" className="w-full p-2 border rounded" required/>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea {...form.register('description')} placeholder="Category description" className="w-full p-2 border rounded" required/>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Type</label>
                <select {...form.register('type')} className="w-full p-2 border rounded" required>
                  <option value="">Select type</option>
                  <option value="supplier_registration">Supplier Registration</option>
                  <option value="supplier_prequalification">Supplier Prequalification</option>
                  <option value="rfq">RFQ</option>
                  <option value="tender">Tender</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <select {...form.register('status')} className="w-full p-2 border rounded" required>
                  <option value="draft">Draft</option>
                  <option value="open">Open</option>
                  <option value="closed">Closed</option>
                </select>
              </div>

              <DialogFooter>
                
              
              <Button variant="outline" size="sm" onClick={(e) => {e.preventDefault(); setIsModalVisible(false);}}>Cancel</Button>
            <Button type="submit" 
            disabled={isSaving}
             size="sm">{isSaving ? (
                    <>
                      <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : 'Save changes'}</Button>


                  </DialogFooter>
            </form>


          </DialogContent>
        </Dialog>




<Dialog open={isTemplateFieldsModalVisible} onOpenChange={ ()=> setIsTemplateFieldsModalVisible(false) }>
         <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Criteria for category: {jobCategory?.title}</DialogTitle>
              <DialogDescription>
                The following is the criteria and fields for bidders.
              </DialogDescription>
            </DialogHeader>
            {/* Wrap form content in a form tag and use handleSubmit */}
            <ul className="space-y-3">
              {jobCategory?.fields?.map((field, index) => (
                <li key={index} className="border p-3 rounded flex justify-between items-center">
                  <div>
                    <p className="font-medium">{field.label || 'Unnamed field'}</p>
                    <p className="text-sm text-gray-500">{field.name} — {field.type}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => openFieldEditModal(field)} >
                    <Pen />Edit
                    </Button>
                   
                    <Button variant="outline" size="sm"  onClick={() => deleteField(field.name)} >
                    <Trash2 /> Delete
                    </Button>

                  </div>
                </li>
              ))}
            </ul>

              <DialogFooter>
          

              <DialogClose asChild>
              <Button variant="outline" onClick={() => setIsTemplateFieldsModalVisible(false)}>Cancel</Button>
            </DialogClose>
            <Button onClick={addField}>Add Criteria Field</Button>

                  </DialogFooter>
          </DialogContent>
        </Dialog>


  
 {editedField && (
<Dialog open={editedField} onOpenChange={() => setEditedField(null)}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Modify criterion field {editedField?.name}</DialogTitle>
              <DialogDescription>
                Update the criteria below.
              </DialogDescription>
            </DialogHeader>
            {/* Wrap form content in a form tag and use handleSubmit */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Label</label>
                  <input type="text" value={editedField.label || ''} onChange={(e) => handleFieldChange('label', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <input type="text" value={editedField.description || ''} onChange={(e) => handleFieldChange('description', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Name</label>
                  <input type="text" value={editedField.name || ''} onChange={(e) => handleFieldChange('name', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Type</label>
                  <select value={editedField.type || 'text'} onChange={(e) => handleFieldChange('type', e.target.value)} className="w-full p-2 border rounded">
                    <option value="text">Text</option>
                    <option value="number">Number</option>
                    <option value="date">Date</option>
                    <option value="select">Select</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Placeholder</label>
                  <input type="text" value={editedField.placeholder || ''} onChange={(e) => handleFieldChange('placeholder', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Tooltip</label>
                  <input type="text" value={editedField.tooltip || ''} onChange={(e) => handleFieldChange('tooltip', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Help Text</label>
                  <input type="text" value={editedField.helpText || ''} onChange={(e) => handleFieldChange('helpText', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Required</label>
                  <input type="checkbox" checked={editedField.isRequired === true} onChange={(e) => handleFieldChange('isRequired', e.target.checked)} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Editable</label>
                  <input type="checkbox" checked={editedField.isEditable || false} onChange={(e) => handleFieldChange('isEditable', e.target.checked)} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Visible</label>
                  <input type="checkbox" checked={editedField.isVisible || false} onChange={(e) => handleFieldChange('isVisible', e.target.checked)} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Group</label>
                  <input type="text" value={editedField.group || ''} onChange={(e) => handleFieldChange('group', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Subgroup</label>
                  <input type="text" value={editedField.subGroup || ''} onChange={(e) => handleFieldChange('subGroup', e.target.value)} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Order</label>
                  <input type="number" value={editedField.order || 0} onChange={(e) => handleFieldChange('order', parseInt(e.target.value))} className="w-full p-2 border rounded" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Max Sccore</label>
                  <input type="number" value={editedField.maxScore || 0} onChange={(e) => handleFieldChange('maxScore', parseInt(e.target.value))} className="w-full p-2 border rounded" />
                </div>
                
              </div>


              <DialogFooter>
                  
              
              <Button variant="outline" size="sm" type="button" onClick={(e) => {e.preventDefault(); closeFieldEditModal;}}>Cancel</Button>
            <Button type="submit" 
                  onClick={saveEditedField}
            disabled={isSaving}
             size="sm">{isSaving ? (
                    <>
                      <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : 'Save changes'}</Button>

              </DialogFooter>


          </DialogContent>
        </Dialog>
 )}



    </div>
  );
};

export default CategoriesPage;
