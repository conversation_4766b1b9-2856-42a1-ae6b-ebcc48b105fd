"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ArrowLeft, 
  Download, 
  FileSpreadsheet, 
  FileArchive, 
  BarChart3, 
  Users, 
  TrendingUp, 
  Award,
  Clock,
  CheckCircle,
  XCircle,
  AlertCir<PERSON>,
  <PERSON>,
  <PERSON>
} from "lucide-react";
import toast, { Toaster } from 'react-hot-toast';

interface CategoryReportData {
  category: {
    _id: string;
    title: string;
    description: string;
    type: string;
    status: string;
    starts: string;
    ends: string;
  };
  job: {
    _id: string;
    title: string;
    status: string;
  };
  statistics: {
    totalApplications: number;
    submittedApplications: number;
    draftApplications: number;
    underReviewApplications: number;
    approvedApplications: number;
    rejectedApplications: number;
    averageSystemScore: number;
    averageComplianceScore: number;
    averageTotalScore: number;
    highestScore: number;
    lowestScore: number;
    uniqueSuppliers: number;
  };
  topPerformers: Array<{
    supplierId: string;
    supplierName: string;
    totalScore: number;
    systemScore: number;
    complianceScore: number;
    status: string;
    reviewStatus: string;
    reviewerNotes?: string;
  }>;
}

interface Application {
  _id: string;
  ref: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  systemScore: number;
  complianceScore: number;
  totalScore: number;
  submittedAt: string;
  reviewStatus: 'pending' | 'approved' | 'rejected' | 'under_review';
  reviewerNotes?: string;
  spSupplierCompanyId: {
    _id: string;
    name: string;
    registrationNumber: string;
  };
  submittedBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  reviewer?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function BuyerCategoryReportsPage() {
  const params = useParams();
  const router = useRouter();
  const { companyId, jobId, categoryId } = params;

  const [reportData, setReportData] = useState<CategoryReportData | null>(null);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);

  // Filters for applications table
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [reviewStatusFilter, setReviewStatusFilter] = useState('all');

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchReportData();
  }, [companyId, jobId, categoryId]);

  const fetchReportData = async () => {
    try {
      console.log('=== STARTING fetchReportData ===');
      setLoading(true);

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        console.log('No auth token found in fetchReportData');
        toast.error('Authentication token not found');
        return;
      }

      // Fetch job data
      console.log('Fetching job details for jobId:', jobId);
      let jobData = null;
      try {
        const jobResponse = await fetch(
          `${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${jobId}`,
          {
            headers: {
              'Authorization': `Bearer ${cookieToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Job API response status:', jobResponse.status);
        if (jobResponse.ok) {
          const data = await jobResponse.json();
          jobData = data.data || data;
          console.log('Successfully fetched job data:', jobData);
        } else {
          console.log('Job API call failed with status:', jobResponse.status);
        }
      } catch (jobError) {
        console.log('Job API error:', jobError);
      }

      // Fetch category data
      console.log('Fetching category details for categoryId:', categoryId);
      let categoryData = null;
      try {
        const categoryResponse = await fetch(
          `${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${jobId}/categories/${categoryId}`,
          {
            headers: {
              'Authorization': `Bearer ${cookieToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Category API response status:', categoryResponse.status);
        if (categoryResponse.ok) {
          const data = await categoryResponse.json();
          categoryData = data.data || data;
          console.log('Successfully fetched category data:', categoryData);
        } else {
          console.log('Category API call failed with status:', categoryResponse.status);
        }
      } catch (categoryError) {
        console.log('Category API error:', categoryError);
      }

      // Fetch applications for this category
      console.log('Fetching applications for categoryId:', categoryId);
      let applicationsData = [];
      try {
        const applicationsResponse = await fetch(
          `${BACKEND_API_URL}/api/buyer/${companyId}/categories/${categoryId}/applications`,
          {
            headers: {
              'Authorization': `Bearer ${cookieToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Applications API response status:', applicationsResponse.status);
        if (applicationsResponse.ok) {
          const rawData = await applicationsResponse.json();
          applicationsData = rawData.data?.applications || rawData;
          console.log(`Successfully fetched ${applicationsData.length} applications for category`);
        } else {
          console.log('Applications API call failed with status:', applicationsResponse.status);
        }
      } catch (applicationsError) {
        console.log('Applications API error:', applicationsError);
      }

      // Calculate statistics from real application data
      const statistics = {
        totalApplications: applicationsData.length,
        submittedApplications: applicationsData.filter((app: any) =>
          ['submitted', 'under_review', 'approved', 'rejected'].includes(app.status)
        ).length,
        draftApplications: applicationsData.filter((app: any) => app.status === 'draft').length,
        underReviewApplications: applicationsData.filter((app: any) => app.status === 'under_review').length,
        approvedApplications: applicationsData.filter((app: any) => app.status === 'approved').length,
        rejectedApplications: applicationsData.filter((app: any) => app.status === 'rejected').length,
        uniqueSuppliers: new Set(applicationsData.map((app: any) => app.spSupplierCompanyId?._id || app.spSupplierCompanyId)).size,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        highestScore: 0,
        lowestScore: 0
      };

      // Calculate scores from applications with valid scores
      const applicationsWithScores = applicationsData.filter((app: any) =>
        app.totalScore !== undefined && app.totalScore > 0
      );

      if (applicationsWithScores.length > 0) {
        const systemScores = applicationsWithScores.map((app: any) => app.systemScore || 0);
        const complianceScores = applicationsWithScores.map((app: any) => app.complianceScore || 0);
        const totalScores = applicationsWithScores.map((app: any) => app.totalScore);

        statistics.averageSystemScore = systemScores.reduce((sum: number, score: number) => sum + score, 0) / systemScores.length;
        statistics.averageComplianceScore = complianceScores.reduce((sum: number, score: number) => sum + score, 0) / complianceScores.length;
        statistics.averageTotalScore = totalScores.reduce((sum: number, score: number) => sum + score, 0) / totalScores.length;
        statistics.highestScore = Math.max(...totalScores);
        statistics.lowestScore = Math.min(...totalScores);
      }

      // Get top performers (top 3 highest scoring applications)
      const topPerformers = applicationsWithScores
        .sort((a: any, b: any) => b.totalScore - a.totalScore)
        .slice(0, 3)
        .map((app: any) => ({
          supplierId: app.spSupplierCompanyId?._id || app.spSupplierCompanyId,
          supplierName: app.spSupplierCompanyId?.name || app.spSupplierCompanyId?.companyName || 'Unknown Supplier',
          totalScore: app.totalScore,
          systemScore: app.systemScore || 0,
          complianceScore: app.complianceScore || 0,
          status: app.status,
          reviewStatus: app.reviewStatus || 'pending',
          reviewerNotes: app.reviewerNotes || ''
        }));

      // Build report data using real data with fallbacks
      const reportData: CategoryReportData = {
        category: {
          _id: categoryData?._id || categoryId as string,
          title: categoryData?.title || 'Technical Capability Assessment',
          description: categoryData?.description || 'Evaluation of technical skills and capabilities',
          type: categoryData?.type || 'supplier_prequalification',
          status: categoryData?.status || 'open',
          starts: categoryData?.starts || '2024-01-01',
          ends: categoryData?.ends || '2024-12-31'
        },
        job: {
          _id: jobData?._id || jobId as string,
          title: jobData?.title || 'Infrastructure Development Project',
          status: jobData?.status || 'open'
        },
        statistics,
        topPerformers
      };

      setReportData(reportData);
      setApplications(applicationsData);

      console.log('=== fetchReportData COMPLETED ===');
      toast.success('Category report loaded successfully');


    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('Failed to fetch report data');
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (reportType: string) => {
    try {
      setDownloading(true);
      
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        toast.error('Authentication token not found');
        return;
      }

      const queryParams = new URLSearchParams({
        includeScores: 'true',
        includeSystemScores: 'true',
        includeComplianceScores: 'true'
      });

      let endpoint = '';
      let filename = '';

      switch (reportType) {
        case 'excel':
          endpoint = `${BACKEND_API_URL}/api/buyer/${companyId}/categories/${categoryId}/applications/download/excel?${queryParams}`;
          filename = 'category_applications_report.xlsx';
          break;
        case 'zip':
          endpoint = `${BACKEND_API_URL}/api/buyer/${companyId}/categories/${categoryId}/applications/download/zip?includeDocuments=true`;
          filename = 'category_applications.zip';
          break;
        case 'zip-no-docs':
          endpoint = `${BACKEND_API_URL}/api/buyer/${companyId}/categories/${categoryId}/applications/download/zip?includeDocuments=false`;
          filename = 'category_applications_data.zip';
          break;
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Download started');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Failed to download report');
    } finally {
      setDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft', icon: Clock },
      open: { color: 'bg-green-100 text-green-800', label: 'Open', icon: CheckCircle },
      closed: { color: 'bg-red-100 text-red-800', label: 'Closed', icon: XCircle },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review', icon: AlertCircle },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected', icon: XCircle },
      pending: { color: 'bg-orange-100 text-orange-800', label: 'Pending', icon: Clock }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const IconComponent = config.icon;
    
    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading category reports...</div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Category not found</div>
      </div>
    );
  }

  const { category, job, statistics, topPerformers } = reportData;

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Category Report:{category.title}</h1>
            <p className="text-muted-foreground">Category Reports & Analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {getStatusBadge(category.status)}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={downloading}>
                <Download className="h-4 w-4 mr-2" />
                Download Reports
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => downloadReport('excel')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Excel Report
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP with Documents
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadReport('zip-no-docs')}>
                <FileArchive className="h-4 w-4 mr-2" />
                ZIP Data Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>



      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground"><b>Type:</b> {category.type}</p>
                <p className="text-sm text-muted-foreground"><b>Description:</b>{category.description}</p>
                <p className="text-sm text-muted-foreground"><b>Job:</b> {job.title}</p>
                <p className="text-sm text-muted-foreground"><b>Period</b> {category.starts ? new Date(category.starts).toLocaleDateString() : 'N/A'} &nbsp; 
                to &nbsp;{category.ends ? new Date(category.ends).toLocaleDateString() : 'N/A' }</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.totalApplications}</p>
                <p className="text-sm text-muted-foreground">Total Applications</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.submittedApplications}</p>
                <p className="text-sm text-muted-foreground">Submitted</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Award className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">{statistics.approvedApplications}</p>
                <p className="text-sm text-muted-foreground">Approved</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Application Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Application Status</CardTitle>
            <CardDescription>Distribution of application statuses</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { label: 'Submitted', count: statistics.submittedApplications, color: 'bg-blue-500' },
              { label: 'Under Review', count: statistics.underReviewApplications, color: 'bg-yellow-500' },
              { label: 'Approved', count: statistics.approvedApplications, color: 'bg-green-500' },
              { label: 'Rejected', count: statistics.rejectedApplications, color: 'bg-red-500' },
              { label: 'Draft', count: statistics.draftApplications, color: 'bg-gray-500' }
            ].map((item) => (
              <div key={item.label} className="flex items-center justify-between">
                <span className="text-sm font-medium">{item.label}</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm">{item.count}</span>
                  <div className="w-20 bg-muted rounded-full h-2">
                    <div 
                      className={`${item.color} h-2 rounded-full`}
                      style={{ width: `${(item.count / statistics.totalApplications) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Score Statistics</CardTitle>
            <CardDescription>Performance metrics overview</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{statistics.averageSystemScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg System Score</p>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold text-green-600">{statistics.averageComplianceScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg Compliance Score</p>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <p className="text-2xl font-bold text-green-600">{statistics.averageTotalScore.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Avg Total Score</p>
              </div>
            </div>
            <Separator />
            <div className="flex justify-between text-sm">
              <span>Score Range:</span>
              <span className="font-medium">{statistics.lowestScore} - {statistics.highestScore}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Applications Table */}
      <Card>
        <CardHeader>
          <CardTitle>Category Participants</CardTitle>
          <CardDescription>Detailed view of all participants for this category (Read-only)</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4 items-center mb-6">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search participants..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="under_review">Under Review</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={reviewStatusFilter} onValueChange={setReviewStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Review Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reviews</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Applications Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Application Ref</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Scores</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Review Status</TableHead>
                <TableHead>Review Comments</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applications
                .filter(application => {
                  const matchesSearch = application.ref.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                      application.spSupplierCompanyId.name.toLowerCase().includes(searchQuery.toLowerCase());
                  const matchesStatus = statusFilter === 'all' || application.status === statusFilter;
                  const matchesReviewStatus = reviewStatusFilter === 'all' || application.reviewStatus === reviewStatusFilter;
                  return matchesSearch && matchesStatus && matchesReviewStatus;
                })
                .map((application) => (
                  <TableRow key={application._id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{application.spSupplierCompanyId.name}</div>
                        <div className="text-sm text-muted-foreground">{application.spSupplierCompanyId.registrationNumber}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{application.ref}</TableCell>
                    <TableCell>
                      {getStatusBadge(application.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>System:</span>
                          <span className="font-medium">{application.systemScore}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Compliance:</span>
                          <span className="font-medium">{application.complianceScore}</span>
                        </div>
                        <div className="flex justify-between border-t pt-1">
                          <span className="font-medium">Total:</span>
                          <span className="font-bold">{application.totalScore}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(application.submittedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {getStatusBadge(application.reviewStatus)}
                        {application.reviewer && (
                          <div className="text-xs text-muted-foreground">
                            by {application.reviewer.firstName} {application.reviewer.lastName}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {application.reviewerNotes ? (
                          <div className="text-sm text-muted-foreground" title={application.reviewerNotes}>
                            {application.reviewerNotes.length > 50
                              ? `${application.reviewerNotes.substring(0, 50)}...`
                              : application.reviewerNotes}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">No comments</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Suppliers</CardTitle>
          <CardDescription>Highest scoring applications with review status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {topPerformers.map((performer, index) => (
              <div key={performer.supplierId} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{performer.supplierName}</p>
                    <p className="text-sm text-muted-foreground">
                      System: {performer.systemScore} | Compliance: {performer.complianceScore}
                    </p>
                    {performer.reviewerNotes && (
                      <p className="text-xs text-muted-foreground mt-1 max-w-md">
                        "{performer.reviewerNotes}"
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-lg font-bold">{performer.totalScore}</p>
                    <p className="text-sm text-muted-foreground">Total Score</p>
                  </div>
                  <div className="flex flex-col gap-1">
                    {getStatusBadge(performer.status)}
                    {getStatusBadge(performer.reviewStatus)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Navigate to detailed views</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              onClick={() => router.push(`/buyer/${companyId}/categories/${categoryId}/applications`)}
            >
              <Eye className="h-4 w-4 mr-2" />
              View All Applications
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push(`/buyer/${companyId}/jobs/${jobId}/reports`)}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Job Reports
            </Button>
          </div>
        </CardContent>
      </Card>

    </div>
  );
}
