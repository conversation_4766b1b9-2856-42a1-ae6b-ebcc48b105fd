"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Combine, Pen, Trash2, BarChart3 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"

export type Job = {
    _id: string
    title: string
    description: string
    status: "draft" | "published" | "completed" | "cancelled"
    categoryPrice: number
    contract: string
    location: string
    starts: string
    ends: string
    createdBy: string
    spCompanyId: string
    createdAt: string
    updatedAt: string
  }
  
  
  

export default function JobsPage() {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [jobs, setJobs] = useState([]);

  const [token, setToken] = useState<string | null>(null);
  const [companyId, setCompanyId] = useState<string>('');
const [type, setType] = useState<string | null >(null);

useEffect(() => {
  if (typeof window !== 'undefined') {
    const storedCompany = localStorage.getItem('activeCompanyId');
    if (storedCompany) {
      setCompanyId( storedCompany);
      console.log('Stored active company', storedCompany);
    } else {
      console.error("activeCompanyId not found in localStorage");
      const pathParts = window.location.pathname.split('/');
      const companyId = pathParts[2];
      setCompanyId( companyId);
      console.log('Company ID:', companyId);
    
    }
  }
}, []);

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const type = urlParams.get('type');
      console.log('Type', type);
      setType(type);
    }
  }, []);
  

  useEffect(() => {
    if (token) {
      fetchJobs();
    }
  }, [token, type]);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';


  const fetchJobs = async () => {
    try {
      const { data } = await axios.get(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs?type=${type}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log(data);
      setJobs(data);
    } catch (error) {
      toast.error('Failed to fetch jobs');
    }
  };


  const [editingJob, setEditingJob] = useState<Job | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Initialize react-hook-form
  const { register, handleSubmit, reset, setValue } = useForm<Job>();

  // Function to open the dialog and set the job data
  const handleEditClick = (job: Job) => {
    setEditingJob(job);
    setIsEditDialogOpen(true);
    // Set form default values
    setValue('title', job.title);
    setValue('description', job.description);
    // Format dates for datetime-local input
    setValue('starts', new Date(job.starts).toISOString().slice(0, 16));
    setValue('ends', new Date(job.ends).toISOString().slice(0, 16));
    setValue('categoryPrice', job.categoryPrice);
    setValue('location', job.location);
    setValue('status', job.status);
    // Do not set _id or spCompanyId as they are not edited via the form
  };

  // Function to handle form submission for editing
  const onEditSubmit = async (formData: Job) => {
    if (!editingJob || !token) {
      toast.error("Cannot save changes. No job selected or token missing.");
      return;
    }

    try {
        const updatedJobData = {
            ...formData,
            starts: new Date(formData.starts).toISOString(),
            ends: new Date(formData.ends).toISOString(),
            categoryPrice: parseFloat(formData.categoryPrice.toString()),
            spCompanyId: editingJob.spCompanyId,
            createdBy: editingJob.createdBy,
             _id: editingJob._id,
             createdAt: editingJob.createdAt,
             updatedAt: new Date().toISOString(), // Update updatedAt on client side or rely on backend
        };

     // const { data } = await axios.put(`/api/jobs?jobId=${editingJob._id}`, updatedJobData, {
        const { data } = await axios.put(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs/${editingJob._id}`, updatedJobData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log("Job updated successfully:", data);
      toast.success("Job updated successfully!");
      setIsEditDialogOpen(false); 
      reset();
      fetchJobs(); 
    } catch (error) {
      console.error("Failed to update job:", error);
      toast.error('Failed to update job');
    }
  };




const columns: ColumnDef<Job>[] = [
    
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => <div>{row.getValue("title")}</div>,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue("status")}</div>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => <div>{row.getValue("type")}</div>,
    },
    {
      accessorKey: "location",
      header: "Location",
      cell: ({ row }) => <div>{row.getValue("location")}</div>,
    },
    {
      accessorKey: "categoryPrice",
      header: () => <div className="">Price</div>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("categoryPrice"))

        // Format the amount as a kes amount
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "KES",
        }).format(amount)

        return <div className="font-medium">{formatted}</div>
      },
    },
    {
      accessorKey: "starts",
      header: "Start Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("starts"))
        const formatted = date.toLocaleDateString()
        return <div>{formatted}</div>
      },
    },
    {
      accessorKey: "ends",
      header: "Close Date",
      cell: ({ row }) => {
        const date = new Date(row.getValue("ends"))
        const formatted = date.toLocaleDateString()
        return <div>{formatted}</div>
      },
    },
    {
      id: "edit",
      header: "Edit",
      cell: ({ row }) => {
                  const job = row.original;

        return <div>
           <Button
                 variant="outline"
                 size="sm"
                 onClick={(e) => {
                    e.preventDefault();
                    handleEditClick(job);
                  }}
                  className="w-full sm:w-auto text-xs"
                >
                  <Pen className="h-3 w-3 mr-1" />
                  Edit
                </Button>
        </div>
      },
    },{
      id: "categories",
      header: "Categories",
      cell: ({ row }) => {
                  const job = row.original;
        return <div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                      window.location.href = `/buyer/${companyId}/jobs/${job._id}/categories`;
                    }}
                  className="w-full sm:w-auto text-xs"
                >
                  <Combine className="h-3 w-3 mr-1" />
                  Categories
                </Button>
        </div>
      },
    },
    {
      id: "reports",
      header: "Reports",
      cell: ({ row }) => {
                  const job = row.original;
        return <div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                      window.location.href = `/buyer/${companyId}/jobs/${job._id}/reports`;
                    }}
                  className="w-full sm:w-auto text-xs"
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  Reports
                </Button>
                </div>
      },
    }
  ]


    const table = useReactTable({
        data: jobs,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
          sorting,
          columnFilters,
          columnVisibility,
          rowSelection,
        },
      })

  

  return (
    <div className="p-6 w-full">
      <Toaster />

      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">
          {type ? `${type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ')} Jobs` : 'All Jobs'}
        </h1>
        <p className="text-muted-foreground mt-1">Manage and track your job postings</p>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            All Jobs ({jobs.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <Input
                placeholder="Filter titles..."
                value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
                onChange={(event) =>
                  table.getColumn("title")?.setFilterValue(event.target.value)
                }
                className="w-full sm:w-80"
              />
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-[140px]">
                    Columns <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {table
                    .getAllColumns()
                    .filter((column) => column.getCanHide())
                    .map((column) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) =>
                            column.toggleVisibility(!!value)
                          }
                        >
                          {column.id}
                        </DropdownMenuCheckboxItem>
                      )
                    })}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={`font-medium whitespace-normal overflow-hidden text-ellipsis ${
                            cell.column.id === 'actions'
                              ? 'min-w-[200px] sm:min-w-[280px]'
                              : 'max-w-[200px] md:max-w-[200px] lg:max-w-[200px]'
                          }`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-end space-x-2 py-4">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredSelectedRowModel().rows.length} of{" "}
              {table.getFilteredRowModel().rows.length} row(s) selected.
            </div>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit {editingJob?.title}</DialogTitle>
              <DialogDescription>
                Update the job details below.
              </DialogDescription>
            </DialogHeader>
            {/* Wrap form content in a form tag and use handleSubmit */}
            <form onSubmit={handleSubmit(onEditSubmit)}>
                <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="title" className="text-right">
                          Title
                          </Label>
                          <Input
                          id="title"
                          type="text"
                          className="col-span-3"
                          {...register('title', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="description" className="text-right">
                          Description
                          </Label>
                          <Textarea
                          id="description"
                          className="col-span-3"
                          {...register('description', { required: true })} // Register textarea
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="starts" className="text-right">
                          Starts
                          </Label>
                          <Input
                          id="starts"
                          type="datetime-local"
                          className="col-span-3"
                          {...register('starts', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="ends" className="text-right">
                          Closes
                          </Label>
                          <Input
                          id="ends"
                          type="datetime-local"
                          className="col-span-3"
                          {...register('ends', { required: true })} // Register input
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="categoryPrice" className="text-right">
                          Category price
                          </Label>
                          <Input
                          id="categoryPrice"
                          type="number"
                          className="col-span-3"
                           {...register('categoryPrice', { required: true, valueAsNumber: true })} // Register input, treat as number
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="location" className="text-right">
                          Location
                          </Label>
                          <Input
                          id="location"
                          type="text"
                          className="col-span-3"
                          {...register('location', { required: true })} // Register input
                          />
                      </div>

                      {/* Company ID - Display only, not editable */}
                       <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="company" className="text-right">
                          Company
                          </Label>
                          <Input
                          id="company"
                          type="text"
                          value={editingJob?.spCompanyId || ''} // Display value from state
                          className="col-span-3"
                          disabled // Keep disabled
                          />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="status" className="text-right">
                          Status
                          </Label>
                           {/* Use Controller or manage state manually for shadcn-ui Select with react-hook-form */}
                           {/* A simple way is to manage the value manually and use setValue on change */}
                          <Select
                            value={editingJob?.status} // Control value from state or form state
                            onValueChange={(value) => {
                                setEditingJob(prev => prev ? ({ ...prev, status: value as Job['status'] }) : null);
                                setValue('status', value as Job['status']); // Update form state
                            }}
                          >
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="open">Open</SelectItem> {/* Use 'published' */}
                                <SelectItem value="closed">Closed</SelectItem>
                            </SelectContent>
                            </Select>
                             {/* Also register a hidden input or use Controller if needed for stricter form control */}
                             <input type="hidden" {...register('status', { required: true })} />
                      </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit">Save changes</Button>
                  </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

    </div>
  )
}