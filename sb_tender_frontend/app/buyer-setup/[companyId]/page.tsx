"use client"

import * as React from "react"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/store/useAuthStore';

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function BuyerSetupPage({ params }) {
  const router = useRouter();
  const companyId = params?.companyId;
  console.log('Company ID: ', companyId);
  const [isLoading, setIsLoading] = useState(false);
  const notify = (message: string, icon: string) =>
    toast(message, {
      icon: icon,
    });
  // Initialize react-hook-form
  const { register, handleSubmit, watch, formState: { errors, isSubmitting } } = useForm();

  // Watch password to validate confirmation
  const password = watch("password", "");

  // Function to handle form submission
  const onSubmit = async (formData) => {
    setIsLoading(true);
    try {
      // Add companyId to the form data
      formData.companyId = companyId;
      
      // Submit data to API
      const { data } = await axios.post('/api/buyer/setup', formData);
      
if (data.token) {
        document.cookie = `token=${data.token}; path=/;`;
        // Access auth store methods directly
        const { setUser, setToken, setCompanies, setActiveCompanyId } = useAuthStore.getState();
          
        // Save user and token to store
        setUser(data.user);
        setToken(data.token);

        // Optionally store companies and active company
        if (data.user?.companies?.length > 0) {
          setCompanies(data.user.companies);
          setActiveCompanyId(data.user.companies[0]?.companyId); // Or another default
        }

        notify('Login successful! Redirecting to your buyer dashboard...', '👏');

        // Redirect to user's company dashboard
        const firstCompanyId = data.user?.companies?.[0]?.companyId;
        const firstCompanyType = data.user?.companies?.[0]?.companyType;

        if (firstCompanyId) {
          router.push(`/${firstCompanyType}/${firstCompanyId}/dashboard`);
        } else {
          router.push(`/${firstCompanyType}/dashboard`);
        }
        } else {
        notify('Error: ' + data.error, '😢');
        }
        
    } catch (error) {
      console.error("Failed to set up buyer account:", error);
      const errorMessage = error.response?.data?.error || 'Failed to set up the buyer account';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Toaster />
      
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Complete Your Account Setup</h1>
          <p className="mt-2 text-gray-600">Create your password to access your buyer dashboard</p>
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)} className="mt-8 space-y-6">
          {/* Hidden field for companyId */}
          <input type="hidden" {...register('companyId')} value={companyId} />
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register('email', { 
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address"
                }
              })}
              className="w-full"
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              {...register('password', { 
                required: "Password is required",
                minLength: {
                  value: 8,
                  message: "Password must be at least 8 characters"
                }
              })}
              className="w-full"
            />
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              {...register('confirmPassword', { 
                required: "Please confirm your password",
                validate: value => 
                  value === password || "Passwords do not match"
              })}
              className="w-full"
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isSubmitting || isLoading}
          >
            {isSubmitting || isLoading ? 'Setting up...' : 'Complete Setup'}
          </Button>
        </form>
      </div>
    </div>
  );
}