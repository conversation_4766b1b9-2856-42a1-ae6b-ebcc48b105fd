'use client'

import React, { useState, useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import Image from 'next/image';
import { 
  Medal, 
  Globe, 
  ShieldCheck, 
  BarChart3, 
  FileText, 
  Users,
  CheckCircle,
  Percent,
  CheckCircle2,
  ArrowRight,
  Building2,
  Briefcase,
  Store,
  DollarSign,
  TrendingUp,
  Clock
} from 'lucide-react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import BuyerRequestModal from "@/components/BuyerRequestModal";
import toast, { Toaster } from 'react-hot-toast';

export default function Home() {
  const [scrollY, setScrollY] = useState(0);
  const [demoRequest, setDemoRequest] = useState({
    name: '',
    email: '',
    phone: '',
    organization: '',
    role: 'supplier' // Default to supplier
  });
  const [buyerModalOpen, setBuyerModalOpen] = useState(false);
  const [isSubmittingDemo, setIsSubmittingDemo] = useState(false);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  // References for scroll animations
  const modulesSectionRef = useRef(null);
  const modulesSectionInView = useInView(modulesSectionRef, { once: false, amount: 0.2 });
  const modulesControls = useAnimation();

  const benefitsSectionRef = useRef(null);
  const benefitsInView = useInView(benefitsSectionRef, { once: false, amount: 0.2 });
  const benefitsControls = useAnimation();

  const statsSectionRef = useRef(null);
  const statsInView = useInView(statsSectionRef, { once: false, amount: 0.3 });
  
  const supplierSectionRef = useRef(null);
  const supplierInView = useInView(supplierSectionRef, { once: false, amount: 0.1 });

  const buyerSectionRef = useRef(null);
  const buyerInView = useInView(buyerSectionRef, { once: false, amount: 0.1 });

  // Animation for counters
  const [counts, setCounts] = useState({
    jobs: 0,
    categories: 0,
    suppliers: 0,
    countries: 0
  });

  const targetCounts = {
    jobs: 13,
    categories: 9051,
    suppliers: 11083,
    countries: 6
  };



    const clientLogos = [
      'logoipsum-212.svg',
      'logoipsum-222.svg',
      'logoipsum-224.svg',
      'logoipsum-228.svg',
      'logoipsum-232.svg',
      'logoipsum-233.svg',
      'logoipsum-234.svg',
      'logoipsum-245.svg',
      'logoipsum-269.svg',
      'logoipsum-287.svg',
      'logoipsum-295.svg',
      'logoipsum-331.svg',
      'logoipsum-341.svg',
      'logoipsum-348.svg',
      'logoipsum-374.svg'
    ];


  useEffect(() => {
    if (statsInView) {
      const interval = setInterval(() => {
        setCounts(prevCounts => ({
          jobs: Math.min(prevCounts.jobs + Math.ceil(targetCounts.jobs / 20), targetCounts.jobs),
          categories: Math.min(prevCounts.categories + Math.ceil(targetCounts.categories / 20), targetCounts.categories),
          suppliers: Math.min(prevCounts.suppliers + Math.ceil(targetCounts.suppliers / 20), targetCounts.suppliers),
          countries: Math.min(prevCounts.countries + 1, targetCounts.countries)
        }));
      }, 50);

      return () => clearInterval(interval);
    }
  }, [statsInView]);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (modulesSectionInView) {
      modulesControls.start("visible");
    }
    if (benefitsInView) {
      benefitsControls.start("visible");
    }
  }, [modulesSectionInView, benefitsInView, modulesControls, benefitsControls]);

  const handleDemoRequestChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDemoRequest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateDemoForm = () => {
    if (!demoRequest.name.trim()) {
      toast.error('Name is required');
      return false;
    }
    if (!demoRequest.email.trim()) {
      toast.error('Email is required');
      return false;
    }
    if (!demoRequest.organization.trim()) {
      toast.error('Organization is required');
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(demoRequest.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleDemoRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateDemoForm()) {
      return;
    }

    setIsSubmittingDemo(true);

    try {
      const response = await fetch(`${BACKEND_API_URL}/api/notifications/public`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'demo',
          name: demoRequest.name,
          email: demoRequest.email,
          phone: demoRequest.phone,
          company: demoRequest.organization,
          subject: `Demo Request - ${demoRequest.role}`,
          message: `I would like to request a personalized demo of TenderAsili. I am interested in the ${demoRequest.role} features and would like to learn more about how TenderAsili can help my organization.`
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Demo request submitted successfully! We will contact you within 24 hours to schedule your demo.');

        // Reset form
        setDemoRequest({
          name: '',
          email: '',
          phone: '',
          organization: '',
          role: 'supplier'
        });
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to submit demo request. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting demo request:', error);
      toast.error('Failed to submit demo request. Please check your connection and try again.');
    } finally {
      setIsSubmittingDemo(false);
    }
  };

  const modules = [
    {
      icon: <Globe className="w-12 h-12 text-blue-600" />,
      title: "Sourcing",
      description: "Access RFQs, tenders, auctions, and more in one integrated platform with instant notification features.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: <FileText className="w-12 h-12 text-green-600" />,
      title: "Contract Management",
      description: "Streamline contracts from drafting to renewal with robust oversight and automated compliance tracking.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: <BarChart3 className="w-12 h-12 text-purple-600" />,
      title: "Procure to Pay",
      description: "Seamlessly manage the entire procurement cycle from requisition to vendor payment with full visibility.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: <ShieldCheck className="w-12 h-12 text-orange-600" />,
      title: "Risk Management",
      description: "Identify and mitigate supply chain risks with real-time monitoring and AI-powered predictive analytics.",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  const supplierBenefits = [
    {
      icon: <Globe className="w-8 h-8 text-green-600" />,
      title: "Access More Opportunities",
      description: "Connect with hundreds of buyers across multiple industries and regions.",
      color: "green"
    },
    {
      icon: <Building2 className="w-8 h-8 text-blue-600" />,
      title: "Simplified Registration",
      description: "Register once and qualify for multiple tenders across various organizations.",
      color: "blue"
    },
    {
      icon: <Clock className="w-8 h-8 text-purple-600" />,
      title: "Faster Processing",
      description: "Receive real-time updates on bid status and automated payment processing.",
      color: "purple"
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-orange-600" />,
      title: "Business Growth",
      description: "Build your reputation and grow your business with verified performance ratings.",
      color: "orange"
    }
  ];

  const buyerBenefits = [
    {
      icon: <DollarSign className="w-8 h-8 text-green-600" />,
      title: "Cost Savings",
      description: "Reduce procurement costs through efficient processes and competitive bidding.",
      color: "green"
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-blue-600" />,
      title: "Compliance & Transparency",
      description: "Ensure regulatory compliance with complete audit trails and transparent workflows.",
      color: "blue"
    },
    {
      icon: <Users className="w-8 h-8 text-purple-600" />,
      title: "Supplier Diversity",
      description: "Access our database of over 11,083 verified suppliers across multiple categories.",
      color: "purple"
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-orange-600" />,
      title: "Data-Driven Insights",
      description: "Make informed decisions with comprehensive analytics and reporting tools.",
      color: "orange"
    }
  ];

  const frequentlyAskedQuestions = [
    {
      question: "What is TenderAsili?",
      answer: "TenderAsili is an end-to-end eProcurement platform that digitizes the entire tendering process—from advertising bids and collecting vendor documents to evaluating submissions and awarding contracts. Our goal is to simplify compliance, enhance transparency, and drive cost savings for organizations of all sizes."
    },
    {
      question: "How do I register as a supplier on TenderAsili?",
      answer: "Registration is simple. Click on the 'Supplier Signup' button at the top of the page, complete your company profile, upload the required documents, and select the categories you wish to provide services or products for. Once verified, you'll be notified of relevant opportunities."
    },
    {
      question: "How secure is my data on TenderAsili?",
      answer: "TenderAsili employs enterprise-grade security measures including end-to-end encryption, secure data centers, and regular security audits. Our platform has been recognized for its security features and compliance with international data protection standards."
    },
    {
      question: "Can TenderAsili integrate with existing ERP or accounting systems?",
      answer: "Yes, TenderAsili offers seamless integration with major ERP systems, accounting software, and other business applications through our comprehensive API. Our integration specialists can work with your technical team to ensure smooth data flow between systems."
    }
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-100">
        <div className="container mx-auto flex justify-between items-center py-4 px-2">
          <div className="flex items-center">
            <Image
              src="/Tender-Asili-Logo.png"
              height={40}
              width={40}
              alt="TenderAsili Logo"
              className="mr-2"
            />
            <span className="font-bold text-2xl text-gray-900 hidden md:inline">TenderAsili</span>
          </div>
          <div className="hidden lg:flex space-x-8 items-center">
            <a href="#solutions" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">Solutions</a>
            <a href="#about" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">About</a>
            <a href="#suppliers" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">For Suppliers</a>
            <a href="#buyers" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">For Buyers</a>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => setBuyerModalOpen(true)}
              variant="outline"
              className="relative border-blue-400 text-blue-700 bg-white/80 font-semibold px-3 py-2 rounded-xl shadow-sm transition-all duration-200 hover:scale-105 hover:shadow-blue-200/60 hover:bg-blue-50 focus-visible:ring-2 focus-visible:ring-blue-400/60 focus-visible:ring-offset-2"
            >
              <span className="transition-all duration-200 group-hover:tracking-wide">Start as Buyer</span>
            </Button>
            <Button
              onClick={() => { window.location.href = '/auth/register' }}
              variant="outline"
              className="relative border-green-400 text-green-700 bg-white/80 font-semibold px-3 py-2 rounded-xl shadow-sm transition-all duration-200 hover:scale-105 hover:shadow-green-200/60 hover:bg-green-50 focus-visible:ring-2 focus-visible:ring-green-400/60 focus-visible:ring-offset-2"
            >
              <span className="transition-all duration-200 group-hover:tracking-wide">Supplier Signup</span>
            </Button>
            <Button
              onClick={() => { window.location.href = '/auth/login' }}
              className="relative bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold px-3 py-2 rounded-xl shadow-lg transition-all duration-200 hover:scale-105 hover:shadow-blue-400/40 focus-visible:ring-2 focus-visible:ring-blue-400/60 focus-visible:ring-offset-2"
            >
              <span className="transition-all duration-200 group-hover:tracking-wide">Sign In</span>
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <motion.header
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="pt-32 pb-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>

        <div className="container mx-auto px-6 grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-left space-y-8">
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6 }}
              className="space-y-6"
            >
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                🏆 Award-winning procurement platform
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                The Future of
                <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  Procurement
                </span>
              </h1>

              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                Transform your procurement process with TenderAsili's comprehensive Source-to-Pay platform.
                Trusted by 30,000+ suppliers and 800+ organizations across Africa.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                  onClick={() => document.getElementById('demo-form')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 text-lg font-semibold"
                  onClick={() => document.getElementById('demo-form')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  Watch Demo
                </Button>
              </div>
            </motion.div>
          </div>

          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative">
              {/* Main Dashboard Image */}
              <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-white p-4">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 z-10 rounded-2xl"></div>
                <Image
                  src="/tender-asili-supplier-dashboard.png"
                  alt="TenderAsili Platform Dashboard"
                  layout="fill"
                  objectFit="cover"
                  className="rounded-xl"
                />
              </div>

              {/* Floating Stats Cards */}
              <div className="absolute -top-6 -left-6 bg-white p-4 rounded-xl shadow-lg border border-gray-100 z-20">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900">300K+ Suppliers</p>
                    <p className="text-xs text-gray-500">Active Network</p>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-xl shadow-lg border border-gray-100 z-20">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900">20 Countries</p>
                    <p className="text-xs text-gray-500">Across Africa</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.header>

      {/* Stats Section */}
      <section
        ref={statsSectionRef}
        className="py-16 bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.05] -z-10"></div>

        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Trusted by Organizations Worldwide</h2>
            <p className="text-blue-200 text-lg">Join the largest procurement network in Africa</p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={statsInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
              transition={{ duration: 0.6, delay: 0 }}
              className="text-center group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                <p className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-2">
                  {counts.jobs.toLocaleString()}
                </p>
                <p className="text-blue-200 font-medium">Active Jobs</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={statsInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                <p className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-2">
                  {counts.categories.toLocaleString()}
                </p>
                <p className="text-blue-200 font-medium">Categories</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={statsInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                <p className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
                  {counts.suppliers.toLocaleString()}
                </p>
                <p className="text-blue-200 font-medium">Registered Suppliers</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={statsInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center group"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                <p className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-2">
                  {counts.countries}
                </p>
                <p className="text-blue-200 font-medium">Countries</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section id="solutions" ref={modulesSectionRef} className="py-20 bg-gray-50">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={modulesSectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6 }}
            className="mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
              Complete Procurement Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Streamline your entire source-to-pay process with our integrated suite of procurement tools
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {modules.map((module, index) => (
              <motion.div
                key={module.title}
                initial="hidden"
                animate={modulesSectionInView ? "visible" : "hidden"}
                variants={{
                  hidden: { opacity: 0, y: 50 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: {
                      duration: 0.6,
                      delay: index * 0.15
                    }
                  }
                }}
                className="group cursor-pointer"
              >
                <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 group-hover:border-gray-200 h-full">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${module.gradient} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <div className="text-white">
                      {React.cloneElement(module.icon, { className: "w-8 h-8 text-white" })}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-blue-600 transition-colors">
                    {module.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">{module.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              ref={benefitsSectionRef}
              initial="hidden"
              animate={benefitsControls}
              variants={{
                hidden: { x: -50, opacity: 0 },
                visible: { x: 0, opacity: 1, transition: { duration: 0.6 } }
              }}
              className="space-y-8"
            >
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 rounded-full text-sm font-medium">
                  <Medal className="w-4 h-4 mr-2" />
                  Award-Winning Platform
                </div>

                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                  Trusted by Organizations
                  <span className="block text-blue-600">Across Africa</span>
                </h2>

                <p className="text-xl text-gray-600 leading-relaxed">
                  Since 2005, TenderAsili has been recognized for digital excellence and transparency in eProcurement.
                  The EU has commended us as "a highly traceable and auditable system, which helps to combat corruption."
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-green-100 flex items-center justify-center flex-shrink-0">
                    <ShieldCheck className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Trusted Across 5+ Countries</h4>
                    <p className="text-gray-600">Operations across diverse industries including banking, healthcare, manufacturing, and public sector</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center flex-shrink-0">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">11,050+ Active Suppliers</h4>
                    <p className="text-gray-600">Benefiting from increased opportunity access and streamlined processes</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-purple-100 flex items-center justify-center flex-shrink-0">
                    <CheckCircle2 className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Digital Excellence Award Winner</h4>
                    <p className="text-gray-600">Best eProcurement solutions provider since 2018</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              id="demo-form"
              initial="hidden"
              animate={benefitsControls}
              variants={{
                hidden: { x: 50, opacity: 0 },
                visible: { x: 0, opacity: 1, transition: { duration: 0.6, delay: 0.2 } }
              }}
              className="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-3xl border border-blue-100 shadow-xl"
            >
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-gray-900 mb-3">
                  Get Your Free Demo
                </h3>
                <p className="text-gray-600">
                  See how TenderAsili can transform your procurement process
                </p>
              </div>

              <form onSubmit={handleDemoRequestSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="name" className="text-gray-700 font-medium">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={demoRequest.name}
                    onChange={handleDemoRequestChange}
                    placeholder="Enter your full name"
                    className="mt-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-gray-700 font-medium">Business Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={demoRequest.email}
                    onChange={handleDemoRequestChange}
                    placeholder="Enter your business email"
                    className="mt-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <Label htmlFor="phone" className="text-gray-700 font-medium">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={demoRequest.phone}
                    onChange={handleDemoRequestChange}
                    placeholder="Enter your phone number"
                    className="mt-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <Label htmlFor="organization" className="text-gray-700 font-medium">Organization</Label>
                  <Input
                    id="organization"
                    name="organization"
                    value={demoRequest.organization}
                    onChange={handleDemoRequestChange}
                    placeholder="Enter your organization name"
                    className="mt-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <Label className="text-gray-700 font-medium">I am a:</Label>
                  <div className="flex space-x-6 mt-3">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="role"
                        value="supplier"
                        checked={demoRequest.role === 'supplier'}
                        onChange={handleDemoRequestChange}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="text-gray-700 font-medium">Supplier</span>
                    </label>
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="role"
                        value="buyer"
                        checked={demoRequest.role === 'buyer'}
                        onChange={handleDemoRequestChange}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="text-gray-700 font-medium">Buyer</span>
                    </label>
                  </div>
                </div>
                <Button
                  type="submit"
                  disabled={isSubmittingDemo}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                >
                  {isSubmittingDemo ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      Request Free Demo
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </>
                  )}
                </Button>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Split Section for Suppliers and Buyers */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-6">
          <Tabs defaultValue="suppliers" className="w-full">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
                Built for Your Success
              </h2>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Whether you're a supplier looking for opportunities or a buyer streamlining procurement
              </p>
              <TabsList className="inline-flex bg-white p-2 rounded-2xl shadow-lg border border-gray-200">
                <TabsTrigger
                  value="suppliers"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-green-600 data-[state=active]:text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300"
                >
                  For Suppliers
                </TabsTrigger>
                <TabsTrigger
                  value="buyers"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300"
                >
                  For Buyers
                </TabsTrigger>
              </TabsList>
            </div>
            
            {/* Suppliers Tab Content */}
            <TabsContent value="suppliers" id="suppliers" ref={supplierSectionRef}>
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={supplierInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
                  transition={{ duration: 0.5 }}
                  className="bg-gradient-to-br from-green-50 to-emerald-50 p-10 rounded-3xl relative overflow-hidden border border-green-100"
                >
                  <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full -mr-20 -mt-20 opacity-50"></div>
                  <div className="relative z-10 space-y-8">
                    <div>
                      <h3 className="text-3xl font-bold mb-4 text-gray-900">
                        Unlock New Opportunities
                      </h3>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Connect with hundreds of procurement opportunities from top organizations across Africa.
                        Simplify your bidding process and grow your business with TenderAsili.
                      </p>
                    </div>

                    <div className="grid gap-6">
                      {supplierBenefits.map((benefit) => (
                        <div key={benefit.title} className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-xl bg-${benefit.color}-100 flex items-center justify-center flex-shrink-0`}>
                            {React.cloneElement(benefit.icon, { className: `w-6 h-6 text-${benefit.color}-600` })}
                          </div>
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 mb-1">{benefit.title}</h4>
                            <p className="text-gray-600">{benefit.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div id="supplier-signup" className="pt-4">
                      <Button
                        onClick={()=>{window.location.href = "/auth/register"}}
                        className="bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        Start as Supplier
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={supplierInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="space-y-8"
                >
                  <div>
                    <h3 className="text-3xl font-bold mb-4 text-gray-900">
                      How It Works
                    </h3>
                    <p className="text-lg text-gray-600">
                      Get started in minutes and access thousands of opportunities
                    </p>
                  </div>

                  <div className="space-y-8">
                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        1
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Quick Registration</h4>
                        <p className="text-gray-600 leading-relaxed">Create your supplier profile in minutes. Upload your documents once and use them across all applications.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        2
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Find Opportunities</h4>
                        <p className="text-gray-600 leading-relaxed">Get instant notifications for relevant tenders that match your business profile and expertise.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        3
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Submit Proposals</h4>
                        <p className="text-gray-600 leading-relaxed">Submit bids electronically with our intuitive interface. No more physical document delivery.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        4
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Track & Win</h4>
                        <p className="text-gray-600 leading-relaxed">Monitor your bid status in real-time and receive instant updates on your applications.</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </TabsContent>
            
            {/* Buyers Tab Content */}
            <TabsContent value="buyers" id="buyers" ref={buyerSectionRef}>
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={buyerInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-8"
                >
                  <div>
                    <h3 className="text-3xl font-bold mb-4 text-gray-900">
                      Streamline Procurement
                    </h3>
                    <p className="text-lg text-gray-600">
                      Transform your procurement process with powerful automation and insights
                    </p>
                  </div>

                  <div className="space-y-8">
                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        1
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Smart Sourcing</h4>
                        <p className="text-gray-600 leading-relaxed">Create and publish tenders with customizable templates. Automate supplier notifications and bid collection.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        2
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Intelligent Evaluation</h4>
                        <p className="text-gray-600 leading-relaxed">Evaluate bids with collaborative scoring. Compare suppliers with automated compliance checks.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        3
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Contract Automation</h4>
                        <p className="text-gray-600 leading-relaxed">Generate contracts from winning bids. Track milestones and monitor performance automatically.</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                        4
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">Advanced Analytics</h4>
                        <p className="text-gray-600 leading-relaxed">Access detailed reports on procurement activities, spending patterns, and supplier performance.</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={buyerInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="bg-gradient-to-br from-blue-50 to-indigo-50 p-10 rounded-3xl relative overflow-hidden border border-blue-100"
                >
                  <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-full -mr-20 -mt-20 opacity-50"></div>
                  <div className="relative z-10 space-y-8">
                    <div>
                      <h3 className="text-3xl font-bold mb-4 text-gray-900">
                        Maximize Efficiency
                      </h3>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Empower your procurement team with advanced tools to streamline sourcing, ensure compliance,
                        and make data-driven decisions that reduce costs.
                      </p>
                    </div>

                    <div className="grid gap-6">
                      {buyerBenefits.map((benefit) => (
                        <div key={benefit.title} className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-xl bg-${benefit.color}-100 flex items-center justify-center flex-shrink-0`}>
                            {React.cloneElement(benefit.icon, { className: `w-6 h-6 text-${benefit.color}-600` })}
                          </div>
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 mb-1">{benefit.title}</h4>
                            <p className="text-gray-600">{benefit.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="pt-4">
                      <Button
                        onClick={() => setBuyerModalOpen(true)}
                        className="bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        Start as Buyer
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>









      {/* Futuristic Procurement Experience Section */}
      <section className="py-32 bg-gradient-to-br from-emerald-900 via-teal-800 to-green-600 text-white relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 bg-grid-white/[0.03] -z-10"></div>

        {/* Flying Procurement Icons */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Icon 1 - Document */}
          <motion.div
            className="absolute top-20 left-10"
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <FileText className="w-8 h-8 text-emerald-300/30" />
          </motion.div>

          {/* Icon 2 - Globe */}
          <motion.div
            className="absolute top-40 right-20"
            animate={{
              x: [0, -80, 0],
              y: [0, 60, 0],
              rotate: [0, -180, -360],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <Globe className="w-10 h-10 text-teal-300/40" />
          </motion.div>

          {/* Icon 3 - Shield */}
          <motion.div
            className="absolute bottom-32 left-1/4"
            animate={{
              x: [0, 120, 0],
              y: [0, -80, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <ShieldCheck className="w-6 h-6 text-green-300/35" />
          </motion.div>

          {/* Icon 4 - Chart */}
          <motion.div
            className="absolute top-1/3 right-1/3"
            animate={{
              x: [0, -60, 0],
              y: [0, 40, 0],
              rotate: [0, 90, 180],
            }}
            transition={{
              duration: 22,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <BarChart3 className="w-7 h-7 text-emerald-400/30" />
          </motion.div>

          {/* Icon 5 - Building */}
          <motion.div
            className="absolute bottom-20 right-10"
            animate={{
              x: [0, -90, 0],
              y: [0, -70, 0],
              scale: [1, 0.8, 1],
            }}
            transition={{
              duration: 16,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <Building2 className="w-9 h-9 text-teal-400/25" />
          </motion.div>
        </div>

        <div className="container mx-auto px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, amount: 0.3 }}
            className="max-w-5xl mx-auto"
          >
            {/* Glowing Badge */}
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 backdrop-blur-sm border border-emerald-400/30 rounded-full text-emerald-200 text-sm font-medium mb-8"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                className="mr-2"
              >
                ⚡
              </motion.div>
              Revolutionizing Procurement Worldwide
            </motion.div>

            {/* Main Heading */}
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl lg:text-7xl font-bold mb-8 leading-tight"
            >
              Experience the
              <span className="block bg-gradient-to-r from-emerald-300 via-teal-200 to-green-300 bg-clip-text text-transparent">
                Future of Procurement
              </span>
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-xl lg:text-2xl text-emerald-100 mb-12 max-w-4xl mx-auto leading-relaxed"
            >
              Join thousands of organizations transforming their procurement with AI-powered solutions,
              real-time analytics, and seamless supplier collaboration.
            </motion.p>

            {/* Stats Grid */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
            >
              <div className="text-center">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl lg:text-4xl font-bold text-emerald-300 mb-2"
                >
                  98%
                </motion.div>
                <div className="text-emerald-200 text-sm">Cost Reduction</div>
              </div>
              <div className="text-center">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl lg:text-4xl font-bold text-teal-300 mb-2"
                >
                  75%
                </motion.div>
                <div className="text-teal-200 text-sm">Time Saved</div>
              </div>
              <div className="text-center">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl lg:text-4xl font-bold text-green-300 mb-2"
                >
                  11K+
                </motion.div>
                <div className="text-green-200 text-sm">Active Suppliers</div>
              </div>
              <div className="text-center">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="text-3xl lg:text-4xl font-bold text-emerald-400 mb-2"
                >
                  5+
                </motion.div>
                <div className="text-emerald-200 text-sm">Countries</div>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row justify-center gap-6"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              viewport={{ once: true, amount: 0.3 }}
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-bold px-10 py-5 rounded-2xl shadow-2xl hover:shadow-emerald-500/25 transition-all duration-300 border-0 min-w-[200px] text-lg"
                  onClick={() => document.getElementById('demo-form')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    Talk to an Expert
                  </motion.span>
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-emerald-300 text-emerald-100 hover:bg-emerald-300 hover:text-emerald-900 font-bold px-10 py-5 rounded-2xl shadow-2xl hover:shadow-emerald-300/25 transition-all duration-300 backdrop-blur-sm bg-emerald-500/10 min-w-[200px] text-lg"
                  onClick={() => setBuyerModalOpen(true)}
                >
                  Start your Journey
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-emerald-400/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </section>


      <section className="py-16 bg-gray-50 overflow-hidden">
      <div className="container mx-auto text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-800">
          Trusted by Leading Organizations
        </h2>
        <p className="text-gray-600 mt-4">
          Join a network of satisfied clients who have transformed their procurement with TenderAsili.
        </p>
      </div>

      {/* Scrolling container */}
      <div className="relative w-full flex overflow-hidden [mask-image:linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]">
        {/* Duplicate the logo list to create the infinite scroll effect */}
        {[...clientLogos, ...clientLogos].map((logo, index) => (
          // motion.div for initial animation effect (zoom in)
          <motion.div
            key={index} // Use index here as logos are duplicated
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true, amount: 0.5 }} // Animate when 50% in view
            transition={{ duration: 0.5, delay: index * 0.05 }} // Slight delay for each logo
            className="flex-shrink-0 flex items-center justify-center h-20 mx-8" // Adjust mx for spacing
            // Apply the scroll animation after the initial animation completes
            style={{ animation: `scroll-logos 10s linear infinite` }} // Adjust duration for speed
          >
             {/* Image component for the logo */}
            <Image
              src={`/logos/${logo}`} // Path to your logo files
              alt={`Client Logo ${index % clientLogos.length + 1}`} // Alt text
              width={120} // Adjust width as needed
              height={60} // Adjust height as needed
              objectFit="contain" // Ensure logo fits without distortion
              className="grayscale hover:grayscale-0 transition-all duration-300" // Optional: grayscale effect on hover
            />
          </motion.div>
        ))}
      </div>

      {/* Define the keyframes for the scrolling animation */}
      {/* This should ideally be in your global CSS or a styled component */}
      {/* For demonstration, included here in a style tag */}
      <style jsx>{`
        @keyframes scroll-logos {
          from {
            transform: translateX(0);
          }
          to {
            transform: translateX(-50%); /* Scrolls half the width of the duplicated list */
          }
        }
      `}</style>
    </section>



      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">
            What Our Clients Say
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, amount: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                  <Building2 className="w-6 h-6 text-gray-500" />
                </div>
                <div>
                  <p className="font-semibold">Sarah Kamau</p>
                  <p className="text-sm text-gray-500">Procurement Director, Kinari Hospital</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "TenderAsili has transformed our procurement process. We've reduced our sourcing cycle time by 60% and improved our supplier diversity. The platform is intuitive and the support team is exceptional."
              </p>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.15 }}
              viewport={{ once: true, amount: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                  <Store className="w-6 h-6 text-gray-500" />
                </div>
                <div>
                  <p className="font-semibold">Michael Ochieng</p>
                  <p className="text-sm text-gray-500">CEO, Nairobi Office Supplies Ltd</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "As a supplier, TenderAsili has opened doors to opportunities we wouldn't have accessed otherwise. The platform's ease of use and transparent process has helped us win multiple contracts and grow our business."
              </p>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true, amount: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                  <Briefcase className="w-6 h-6 text-gray-500" />
                </div>
                <div>
                  <p className="font-semibold">Grace Mwangi</p>
                  <p className="text-sm text-gray-500">Procurement Manager, East African Bank</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "The compliance and transparency features of TenderAsili have been game-changing for our organization. We've significantly reduced risk and improved our audit outcomes while making our procurement process more efficient."
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">
            Frequently Asked Questions
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {frequentlyAskedQuestions.map((faq, index) => (
                <motion.div 
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true, amount: 0.8 }}
                  className="bg-gray-50 rounded-lg p-6"
                >
                  <h3 className="text-xl font-semibold mb-2 text-gray-800">{faq.question}</h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-white/[0.05] -z-10"></div>
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto space-y-8"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6">
              Ready to Transform
              <span className="block bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Your Procurement?
              </span>
            </h2>
            <p className="text-xl lg:text-2xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
              Join 300,000+ suppliers and 800+ organizations already benefiting from Africa's most trusted procurement platform.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-6 pt-8">
              <Button
                size="lg"
                className="bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                onClick={()=>{window.location.href = "/auth/register"}}
              >
                Start as Supplier
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                onClick={() => setBuyerModalOpen(true)}
              >
                Start as Buyer
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white text-gray-900 bg-white font-semibold px-10 py-4 text-lg transition-all duration-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-xl"
                onClick={() => document.getElementById('demo-form')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Watch Demo
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12" id="tenderasili-footer">
        <div className="container mx-auto px-4 grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <Image
                src="/Tender-Asili-Logo.png"
                height={40} 
                width={40}
                alt="TenderAsili Logo"
                className="mr-2 rounded-4xl"
              />
              <span className="font-bold text-xl">TenderAsili</span>
            </div>
            <p className="text-gray-400">
              An award-winning Source to Pay (S2P) solution developed in 2005, serving organizations across Africa with transparent and efficient procurement solutions.
            </p>
          </div>
          <div>
            <h4 className="text-xl font-bold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-red-300 transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Available Jobs</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Supplier Directory</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Tender Bulletin</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-xl font-bold mb-4">Solutions</h4>
            <ul className="space-y-2">
              <li><a href="#" className="hover:text-red-300 transition-colors">Sourcing</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Contract Management</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Procure to Pay</a></li>
              <li><a href="#" className="hover:text-red-300 transition-colors">Risk Management</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-xl font-bold mb-4">Contact</h4>
            <p className="flex items-center mb-2">
              <span className="mr-2">📧</span> <EMAIL>
            </p>
            <p className="flex items-center mb-2">
              <span className="mr-2">📞</span> +(254) 714 555 554
            </p>
            <p className="flex items-center">
              <span className="mr-2">📍</span> Kunde Road, Nairobi,
            </p>
          </div>
        </div>
        <div className="container mx-auto mt-8 text-center text-gray-500 border-t border-gray-700 pt-4">
          © 2025 TenderAsili. All rights reserved.
        </div>
      </footer>

      {/* Buyer Request Modal */}
      <BuyerRequestModal
        isOpen={buyerModalOpen}
        onClose={() => setBuyerModalOpen(false)}
      />

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </div>
  );
}



