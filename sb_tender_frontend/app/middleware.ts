import { NextResponse } from 'next/server';

export function middleware(request: Request) {
  const sourceIp = request.headers.get('x-forwarded-for') || request.headers.get('cf-connecting-ip') || 'Unknown IP';

  const logEntry = {
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    headers: Object.fromEntries(request.headers.entries()),
    sourceIp, // Include the source IP
  };

  console.log(JSON.stringify(logEntry)); // Log to console or external service

  return NextResponse.next();
}
