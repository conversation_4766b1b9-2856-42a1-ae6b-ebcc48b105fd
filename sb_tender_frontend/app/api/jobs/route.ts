import { NextRequest, NextResponse } from 'next/server';

// This route redirects to the new admin jobs API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const queryString = searchParams.toString();
  const redirectUrl = `/api/admin/jobs${queryString ? `?${queryString}` : ''}`;

  return NextResponse.redirect(new URL(redirectUrl, request.url));
}

export async function POST(request: NextRequest) {
  // Redirect POST requests to admin jobs API
  const redirectUrl = `/api/admin/jobs`;
  return NextResponse.redirect(new URL(redirectUrl, request.url), 307); // 307 preserves POST method
}

export async function PUT(request: NextRequest) {
  // Redirect PUT requests to admin jobs API
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('jobId');
  const redirectUrl = `/api/admin/jobs${jobId ? `?id=${jobId}` : ''}`;
  return NextResponse.redirect(new URL(redirectUrl, request.url), 307); // 307 preserves PUT method
}

export async function DELETE(request: NextRequest) {
  // Redirect DELETE requests to admin jobs API
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  const redirectUrl = `/api/admin/jobs${id ? `?id=${id}` : ''}`;
  return NextResponse.redirect(new URL(redirectUrl, request.url), 307); // 307 preserves DELETE method
}
