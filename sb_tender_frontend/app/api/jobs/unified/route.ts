import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Sp<PERSON>ob from '@/lib/models/sp_job';
import { createMultiRoleHandler, buildRoleBasedQuery, ApiContext } from '@/lib/utils/apiHandler';

// Unified jobs handler that works for all roles
async function handleJobsRequest(context: ApiContext) {
  await connectToDatabase();

  const { user, company, searchParams } = context;
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const search = searchParams.get('search');
  const status = searchParams.get('status');

  // Build base query
  let query: any = {};
  
  // Add search filters
  if (search) {
    query.$and = query.$and || [];
    query.$and.push({
      $or: [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ]
    });
  }
  
  if (status) {
    query.status = status;
  }

  // Apply role-based filtering
  const roleQuery = buildRoleBasedQuery(user.type, user.userId, company?.companyId);
  if (Object.keys(roleQuery).length > 0) {
    query = { ...query, ...roleQuery };
  }

  // Execute query with pagination
  const skip = (page - 1) * limit;
  const [jobs, total] = await Promise.all([
    SpJob.find(query)
      .populate('spCompanyId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    SpJob.countDocuments(query)
  ]);

  // Return role-specific data structure
  const response: any = {
    jobs,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };

  // Add role-specific metadata
  if (user.type === 'admin') {
    // Admin gets additional statistics
    const stats = await SpJob.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    response.statistics = stats;
  }

  return response;
}

// Create handlers for different HTTP methods
export const GET = createMultiRoleHandler(handleJobsRequest, ['admin', 'buyer', 'supplier']);

// POST handler - only admin and buyers can create jobs
export const POST = createMultiRoleHandler(async (context: ApiContext) => {
  const { user, company } = context;
  
  // Only admin and buyers can create jobs
  if (!['admin', 'buyer'].includes(user.type)) {
    throw new Error('Only admins and buyers can create jobs');
  }

  await connectToDatabase();

  const body = await context.request?.json();
  const {
    title,
    description,
    location,
    contract,
    starts,
    ends,
    status = 'active'
  } = body;

  // Validation
  const errors: string[] = [];
  if (!title) errors.push('Title is required');
  if (!description) errors.push('Description is required');
  if (!location) errors.push('Location is required');
  if (!contract) errors.push('Contract type is required');
  if (!starts) errors.push('Start date is required');
  if (!ends) errors.push('End date is required');

  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.join(', ')}`);
  }

  // For buyers, use their company ID; for admin, require spCompanyId
  let spCompanyId;
  if (user.type === 'buyer') {
    spCompanyId = company?.companyId;
  } else {
    spCompanyId = body.spCompanyId;
    if (!spCompanyId) {
      throw new Error('Company ID is required for admin users');
    }
  }

  // Create job
  const job = new SpJob({
    title,
    description,
    spCompanyId,
    location,
    contract,
    starts: new Date(starts),
    ends: new Date(ends),
    status,
    createdBy: user.userId,
  });

  await job.save();

  // Populate the created job
  await job.populate('spCompanyId', 'name type');
  await job.populate('createdBy', 'firstName lastName email');

  return job;
}, ['admin', 'buyer']);
