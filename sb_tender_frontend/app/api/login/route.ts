import { NextRequest } from 'next/server';
import bcrypt from 'bcryptjs';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpRole from '@/lib/models/sp_role';
import SpUserRole from '@/lib/models/sp_user_role';
import SpCompany from '@/lib/models/sp_company';
import { generateToken } from '@/lib/utils/token';
import { successResponse, errorResponse, validationError } from '@/lib/utils/apiResponse';
import {getUserCompanyRoles}from '@/lib/utils/user';


export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    console.log('Login request:', { email });

    // Validation
    if (!email || !password) {
      return validationError(['Email and password are required']);
    }

    await connectToDatabase();

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      console.log('User not found:', email);
      return errorResponse('Invalid credentials', 401);
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('Invalid password for user:', email);
      return errorResponse('Invalid credentials', 401);
    }

    // Check user status
    if (user.status !== 'active') {
      console.log('User account not active:', email);
      return errorResponse('Account is not active', 401);
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id.toString());

    // For non-admin users, get their company information
    let companies: Array<{
      companyId: string;
      companyName: string;
      companyType: string;
      companyRegistrationNumber: string;
      roles: any[];
    }> = [];
    let userDataWithRoles: any = null;

    if (user.type !== 'admin') {
      const userCompanies = await SpCompany.find({ createdBy: user._id });
       userDataWithRoles = await getUserCompanyRoles(user._id);
     // console.log('User data with roles:', JSON.stringify(userDataWithRoles, null, 2));
      
      companies = userCompanies.map(company => ({
        companyId: company._id,
        companyName: company.name,
        companyType: company.type,
        companyRegistrationNumber: company.registrationNumber,
        roles: [] // Will be populated later when roles are implemented
      }));
    }

    // Remove password from response and include company info
    const userResponse = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      type: user.type,
      status: user.status,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      companies: companies,
      user: userDataWithRoles
    };

    console.log('Login successful:', { userId: user._id, email });

    return successResponse(
      { user: userResponse, token },
      'Login successful'
    );
  } catch (error: any) {
    console.error('Login error:', error);
    return errorResponse('Internal server error', 500);
  }
}
