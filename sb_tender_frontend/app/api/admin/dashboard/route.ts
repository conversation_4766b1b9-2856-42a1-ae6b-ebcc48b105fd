import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpApplication from '@/lib/models/sp_application';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import SpCompany from '@/lib/models/sp_company';
import SpNotification from '@/lib/models/sp_notification';
import User from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    console.log('=== GET Admin Dashboard - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    // Calculate date ranges
    const now = new Date();
    const tenDaysAgo = new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000);
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);

    console.log('Calculating dashboard statistics...');

    // Parallel data fetching for better performance
    const [
      openCategories,
      totalRevenue,
      activeBids,
      awards,
      activeUsers,
      recentApplications,
      systemAlerts,
      recentPayments,
      monthlyData
    ] = await Promise.all([
      // Open Categories - job categories with status open
      SpJobCategory.countDocuments({ status: 'open' }),

      // Total Revenue - sum of all orders with status paid
      SpOrder.aggregate([
        { $match: { status: 'paid' } },
        {
          $addFields: {
            totalAmountValue: {
              $cond: {
                if: { $type: '$totalAmount' },
                then: { $toDouble: '$totalAmount' },
                else: 0
              }
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$totalAmountValue' } } }
      ]),

      // Active Bids - applications that are still before end date
      SpApplication.aggregate([
        {
          $lookup: {
            from: 'spjobs',
            localField: 'spJobId',
            foreignField: '_id',
            as: 'job'
          }
        },
        { $unwind: '$job' },
        { $match: { 'job.ends': { $gt: now } } },
        { $count: 'activeBids' }
      ]),

      // Awards - applications with status = approved
      SpApplication.countDocuments({ status: 'approved' }),

      // Active Users - users who logged in in last 10 days
      User.countDocuments({ lastLogin: { $gte: tenDaysAgo } }),

      // Recent Applications (Recent Bids)
      SpApplication.find()
        .populate('spSupplierCompanyId', 'name')
        .populate('spJobCategoryId', 'title')
        .sort({ createdAt: -1 })
        .limit(5),

      // System Alerts - recent notifications
      SpNotification.find()
        .populate('userId', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(5),

      // Recent Transactions - recent payments
      SpPayment.find()
        .populate({
          path: 'spOrderId',
          populate: {
            path: 'spCompanyId',
            select: 'name'
          }
        })
        .sort({ createdAt: -1 })
        .limit(5),

      // Monthly data for charts - applications over last 6 months
      SpApplication.aggregate([
        { $match: { createdAt: { $gte: sixMonthsAgo } } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            applications: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ])
    ]);

    console.log('Dashboard data fetched successfully');

    // Process total revenue
    const revenue = totalRevenue[0]?.total || 0;

    // Process active bids
    const activeBidsCount = activeBids[0]?.activeBids || 0;

    // Process monthly chart data
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const chartData = [];

    // Generate last 6 months data
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthData = monthlyData.find(item =>
        item._id.year === date.getFullYear() && item._id.month === date.getMonth() + 1
      );

      chartData.push({
        name: monthNames[date.getMonth()],
        applications: monthData?.applications || 0,
        categories: Math.floor((monthData?.applications || 0) / 3) // Rough estimate
      });
    }

    // Format response data
    const dashboardData = {
      statistics: {
        openCategories,
        totalRevenue: revenue,
        activeBids: activeBidsCount,
        awards,
        activeUsers,
        revenueGrowth: 8.6, // This would need historical data to calculate properly
      },
      chartData,
      recentApplications: recentApplications.map(app => ({
        _id: app._id,
        supplierName: app.spSupplierCompanyId?.name || 'Unknown Supplier',
        categoryTitle: app.spJobCategoryId?.title || 'Unknown Category',
        status: app.status,
        totalScore: app.totalScore || 0,
        createdAt: app.createdAt
      })),
      systemAlerts: systemAlerts.map(alert => ({
        _id: alert._id,
        title: alert.title,
        message: alert.message,
        type: alert.type || 'info',
        createdAt: alert.createdAt,
        userName: alert.userId ? `${alert.userId.firstName} ${alert.userId.lastName}` : 'System'
      })),
      recentPayments: recentPayments.map(payment => ({
        _id: payment._id,
        amount: payment.amount,
        status: payment.status === 'success' ? 'successful' : payment.status,
        companyName: payment.spOrderId?.spCompanyId?.name || 'Unknown Company',
        createdAt: payment.createdAt,
        paymentMethod: payment.channel || payment.source || 'Payment'
      }))
    };

    console.log('=== GET Admin Dashboard - Success ===');
    return successResponse(dashboardData);
  } catch (error: any) {
    console.error('=== GET Admin Dashboard - Error ===');
    console.error('Dashboard error:', error);
    console.error('Error stack:', error.stack);

    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    return errorResponse(error.message || 'Failed to fetch dashboard data', 500);
  }
}
