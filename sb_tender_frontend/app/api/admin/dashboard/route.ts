import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpApplication from '@/lib/models/sp_application';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Monthly Revenue (from payments)
    const monthlyRevenue = await SpPayment.aggregate([
      {
        $match: {
          status: 'successful',
          createdAt: { $gte: startDate }
        }
      },
      {
        $addFields: {
          amountValue: {
            $cond: {
              if: { $type: '$amount' },
              then: { $toDouble: '$amount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amountValue' }
        }
      }
    ]);

    // Key Metrics
    const [
      totalUsers,
      totalCompanies,
      totalJobs,
      totalApplications,
      activeJobs,
      pendingApplications,
      totalOrders,
      paidOrders
    ] = await Promise.all([
      User.countDocuments({ status: 'active' }),
      SpCompany.countDocuments({ status: 'active' }),
      SpJob.countDocuments(),
      SpApplication.countDocuments(),
      SpJob.countDocuments({ status: 'active' }),
      SpApplication.countDocuments({ status: 'pending' }),
      SpOrder.countDocuments(),
      SpOrder.countDocuments({ status: 'paid' })
    ]);

    // User Analytics (by type)
    const userAnalytics = await User.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          active: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          }
        }
      }
    ]);

    // Recent Activity (last 7 days)
    const recentActivityDate = new Date();
    recentActivityDate.setDate(recentActivityDate.getDate() - 7);

    const recentActivity = await Promise.all([
      User.countDocuments({ createdAt: { $gte: recentActivityDate } }),
      SpCompany.countDocuments({ createdAt: { $gte: recentActivityDate } }),
      SpJob.countDocuments({ createdAt: { $gte: recentActivityDate } }),
      SpApplication.countDocuments({ createdAt: { $gte: recentActivityDate } })
    ]);

    // Platform Performance
    const platformPerformance = await SpApplication.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Revenue Trend (last 12 months)
    const revenueTrend = await SpPayment.aggregate([
      {
        $match: {
          status: 'successful',
          createdAt: { $gte: new Date(new Date().setMonth(new Date().getMonth() - 12)) }
        }
      },
      {
        $addFields: {
          amountValue: {
            $cond: {
              if: { $type: '$amount' },
              then: { $toDouble: '$amount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$amountValue' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Top Companies by Orders
    const topCompanies = await SpOrder.aggregate([
      {
        $addFields: {
          totalAmountValue: {
            $cond: {
              if: { $type: '$totalAmount' },
              then: { $toDouble: '$totalAmount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: '$spCompanyId',
          orderCount: { $sum: 1 },
          totalSpent: { $sum: '$totalAmountValue' }
        }
      },
      {
        $lookup: {
          from: 'spcompanies',
          localField: '_id',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $unwind: '$company'
      },
      {
        $sort: { totalSpent: -1 }
      },
      {
        $limit: 5
      }
    ]);

    return successResponse({
      monthlyRevenue: monthlyRevenue[0]?.total || 0,
      metrics: {
        totalUsers,
        totalCompanies,
        totalJobs,
        totalApplications,
        activeJobs,
        pendingApplications,
        totalOrders,
        paidOrders
      },
      userAnalytics: userAnalytics.reduce((acc, item) => {
        acc[item._id] = { count: item.count, active: item.active };
        return acc;
      }, {}),
      recentActivity: {
        newUsers: recentActivity[0],
        newCompanies: recentActivity[1],
        newJobs: recentActivity[2],
        newApplications: recentActivity[3]
      },
      platformPerformance: platformPerformance.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      revenueTrend,
      topCompanies
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin dashboard fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}
