import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpPayment from '@/lib/models/sp_payment';
import SpOrder from '@/lib/models/sp_order';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/transactions - List transactions
export async function GET(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    // Ensure models are registered
    SpCompany;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const method = searchParams.get('method');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query
    let query: any = {};
    
    if (search) {
      query.$or = [
        { reference: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (method) {
      query.method = method;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Calculate summary statistics
    const summaryStats = await SpPayment.aggregate([
      {
        $addFields: {
          amountValue: {
            $cond: {
              if: { $type: '$amount' },
              then: { $toDouble: '$amount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalTransactions: { $sum: 1 },
          totalAmount: { $sum: '$amountValue' },
          successfulTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'successful'] }, 1, 0] }
          },
          pendingTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          failedTransactions: {
            $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
          }
        }
      }
    ]);

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [transactions, total] = await Promise.all([
      SpPayment.find(query)
        .populate('spOrderId', 'totalAmount status spCompanyId')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpPayment.countDocuments(query)
    ]);

    // Manually populate company information
    for (const transaction of transactions) {
      if (transaction.spOrderId && transaction.spOrderId.spCompanyId) {
        const company = await SpCompany.findById(transaction.spOrderId.spCompanyId).select('name type');
        if (company) {
          transaction.spOrderId.spCompanyId = company;
        }
      }
    }

    return successResponse({
      transactions,
      summary: summaryStats[0] || {
        totalTransactions: 0,
        totalAmount: 0,
        successfulTransactions: 0,
        pendingTransactions: 0,
        failedTransactions: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List transactions error:', error);
    return errorResponse(error.message || 'Failed to fetch transactions', 500);
  }
}

// POST /api/admin/transactions - Create manual payment
export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const body = await request.json();
    const {
      spOrderId,
      amount,
      method = 'manual',
      reference,
      notes,
      status = 'successful'
    } = body;

    // Validation
    const errors: string[] = [];
    if (!spOrderId) errors.push('Order ID is required');
    if (!amount || amount <= 0) errors.push('Valid amount is required');
    if (!reference) errors.push('Payment reference is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify order exists
    const order = await SpOrder.findById(spOrderId);
    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Check if reference already exists
    const existingPayment = await SpPayment.findOne({ reference });
    if (existingPayment) {
      return errorResponse('Payment with this reference already exists', 409);
    }

    // Create payment
    const payment = new SpPayment({
      spOrderId,
      amount,
      channel: method,
      reference,
      status: status === 'successful' ? 'success' : status,
      description: notes || 'Manual payment created by admin',
      createdBy: user.userId
    });

    await payment.save();

    // Update order status if payment is successful
    if (status === 'success') {
      // Calculate total paid amount
      const allPayments = await SpPayment.find({
        spOrderId,
        status: 'success'
      });

      const totalPaid = allPayments.reduce((sum, p) => {
        const amt = typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount;
        return sum + amt;
      }, 0);

      const orderAmount = typeof order.totalAmount === 'object' 
        ? parseFloat(order.totalAmount.$numberDecimal) 
        : order.totalAmount;

      // Update order status
      if (totalPaid >= orderAmount) {
        order.status = 'paid';
      } else {
        order.status = 'pending';
      }
      
      order.updatedBy = user.userId;
      order.updatedAt = new Date();
      await order.save();
    }

    // Populate and return
    await payment.populate('spOrderId', 'totalAmount status spCompanyId');
    await payment.populate('createdBy', 'firstName lastName email');

    // Manually populate company information
    if (payment.spOrderId && payment.spOrderId.spCompanyId) {
      const company = await SpCompany.findById(payment.spOrderId.spCompanyId).select('name type');
      if (company) {
        payment.spOrderId.spCompanyId = company;
      }
    }

    return successResponse(payment, 'Payment created successfully', 201);

  } catch (error: any) {
    console.error('Create manual payment error:', error);
    return errorResponse(error.message || 'Failed to create manual payment', 500);
  }
}
