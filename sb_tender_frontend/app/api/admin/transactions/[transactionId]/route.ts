import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpPayment from '@/lib/models/sp_payment';
import SpOrder from '@/lib/models/sp_order';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// PUT /api/admin/transactions/:transactionId - Update transaction
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { transactionId } = await params;
    const body = await request.json();

    if (!transactionId) {
      return errorResponse('Transaction ID is required', 400);
    }

    const {
      status,
      method,
      reference,
      notes,
      amount
    } = body;

    // Find existing transaction
    const transaction = await SpPayment.findById(transactionId);
    if (!transaction) {
      return errorResponse('Transaction not found', 404);
    }

    // Store original status for comparison
    const originalStatus = transaction.status;

    // Check if reference already exists (excluding current transaction)
    if (reference && reference !== transaction.reference) {
      const existingPayment = await SpPayment.findOne({ 
        reference, 
        _id: { $ne: transactionId } 
      });
      if (existingPayment) {
        return errorResponse('Payment with this reference already exists', 409);
      }
    }

    // Update transaction
    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    if (status) updateData.status = status;
    if (method) updateData.method = method;
    if (reference) updateData.reference = reference;
    if (notes) updateData.notes = notes;
    if (amount) updateData.amount = amount;

    Object.assign(transaction, updateData);
    await transaction.save();

    // Update related order status if payment status changed
    if (status && status !== originalStatus) {
      const order = await SpOrder.findById(transaction.spOrderId);
      if (order) {
        // Recalculate total paid amount
        const allPayments = await SpPayment.find({
          spOrderId: transaction.spOrderId,
          status: 'success'
        });

        const totalPaid = allPayments.reduce((sum, p) => {
          const amt = typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount;
          return sum + amt;
        }, 0);

        const orderAmount = typeof order.totalAmount === 'object' 
          ? parseFloat(order.totalAmount.$numberDecimal) 
          : order.totalAmount;

        // Update order status based on payment status
        if (totalPaid >= orderAmount) {
          order.status = 'paid';
        } else if (totalPaid > 0) {
          order.status = 'pending';
        } else {
          order.status = 'checkout';
        }
        
        order.updatedBy = user.userId;
        order.updatedAt = new Date();
        await order.save();
      }
    }

    // Populate and return
    await transaction.populate('spOrderId', 'totalAmount status spCompanyId');
    await transaction.populate('createdBy', 'firstName lastName email');
    await transaction.populate('updatedBy', 'firstName lastName email');

    // Manually populate company information
    if (transaction.spOrderId && transaction.spOrderId.spCompanyId) {
      const company = await SpCompany.findById(transaction.spOrderId.spCompanyId).select('name type');
      if (company) {
        transaction.spOrderId.spCompanyId = company;
      }
    }

    return successResponse(transaction);

  } catch (error: any) {
    console.error('Update transaction error:', error);
    return errorResponse(error.message || 'Failed to update transaction', 500);
  }
}

// GET /api/admin/transactions/:transactionId - Get transaction details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { transactionId } = await params;

    if (!transactionId) {
      return errorResponse('Transaction ID is required', 400);
    }

    // Find transaction with full details
    const transaction = await SpPayment.findById(transactionId)
      .populate('spOrderId', 'totalAmount status orderItems spCompanyId')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    // Manually populate company information
    if (transaction && transaction.spOrderId && transaction.spOrderId.spCompanyId) {
      const company = await SpCompany.findById(transaction.spOrderId.spCompanyId).select('name type email phone');
      if (company) {
        transaction.spOrderId.spCompanyId = company;
      }
    }

    if (!transaction) {
      return errorResponse('Transaction not found', 404);
    }

    return successResponse(transaction);

  } catch (error: any) {
    console.error('Get transaction details error:', error);
    return errorResponse(error.message || 'Failed to fetch transaction details', 500);
  }
}

// DELETE /api/admin/transactions/:transactionId - Delete transaction
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { transactionId } = await params;

    if (!transactionId) {
      return errorResponse('Transaction ID is required', 400);
    }

    const transaction = await SpPayment.findById(transactionId);
    if (!transaction) {
      return errorResponse('Transaction not found', 404);
    }

    // Don't allow deletion of successful payments
    if (transaction.status === 'success') {
      return errorResponse('Cannot delete successful payments', 400);
    }

    await SpPayment.findByIdAndDelete(transactionId);

    return successResponse({ message: 'Transaction deleted successfully' });

  } catch (error: any) {
    console.error('Delete transaction error:', error);
    return errorResponse(error.message || 'Failed to delete transaction', 500);
  }
}
