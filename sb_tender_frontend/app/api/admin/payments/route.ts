import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpPayment from '@/lib/models/sp_payment';
import SpOrder from '@/lib/models/sp_order';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const method = searchParams.get('method');
    const orderId = searchParams.get('orderId');

    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (method) query.method = method;
    if (orderId) query.orderId = orderId;
    
    if (search) {
      query.$or = [
        { transactionId: { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
        { method: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [payments, total] = await Promise.all([
      SpPayment.find(query)
        .populate('orderId', 'totalAmount status')
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpPayment.countDocuments(query)
    ]);

    // Calculate summary statistics
    const summaryStats = await SpPayment.aggregate([
      {
        $addFields: {
          amountValue: {
            $cond: {
              if: { $type: '$amount' },
              then: { $toDouble: '$amount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalPayments: { $sum: 1 },
          totalAmount: { $sum: '$amountValue' },
          successfulPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'successful'] }, 1, 0] }
          },
          pendingPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          failedPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
          }
        }
      }
    ]);

    return successResponse({
      payments,
      summary: summaryStats[0] || {
        totalPayments: 0,
        totalAmount: 0,
        successfulPayments: 0,
        pendingPayments: 0,
        failedPayments: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin payments fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const body = await request.json();
    
    await connectToDatabase();

    const {
      orderId,
      amount,
      method,
      reference,
      transactionId,
      status = 'pending'
    } = body;

    // Validation
    const errors: string[] = [];
    if (!orderId) errors.push('Order ID is required');
    if (!amount) errors.push('Amount is required');
    if (!method) errors.push('Payment method is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`);
    }

    // Verify order exists
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Create payment
    const payment = new SpPayment({
      orderId,
      amount: { $numberDecimal: amount.toString() },
      method,
      reference,
      transactionId,
      status,
      createdBy: user.userId,
    });

    await payment.save();

    // Update order status if payment is successful
    if (status === 'successful') {
      await SpOrder.findByIdAndUpdate(orderId, { 
        status: 'paid',
        updatedBy: user.userId 
      });
    } else if (status === 'pending') {
      await SpOrder.findByIdAndUpdate(orderId, { 
        status: 'pending',
        updatedBy: user.userId 
      });
    }

    // Populate the created payment
    await payment.populate('orderId', 'totalAmount status');
    await payment.populate('createdBy', 'firstName lastName email');

    return successResponse(payment, 'Payment created successfully', 201);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin payment creation error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const paymentId = searchParams.get('id');

    if (!paymentId) {
      return errorResponse('Payment ID is required');
    }

    const body = await request.json();
    await connectToDatabase();

    const { status, reference, transactionId, adminNotes } = body;

    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
    };

    if (status) updateData.status = status;
    if (reference) updateData.reference = reference;
    if (transactionId) updateData.transactionId = transactionId;
    if (adminNotes) updateData.adminNotes = adminNotes;

    // Update payment
    const payment = await SpPayment.findByIdAndUpdate(
      paymentId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('orderId', 'totalAmount status')
      .populate('createdBy', 'firstName lastName email');

    if (!payment) {
      return errorResponse('Payment not found', 404);
    }

    // Update corresponding order status
    if (status === 'successful') {
      await SpOrder.findByIdAndUpdate(payment.orderId, {
        status: 'paid',
        updatedBy: user.userId
      });
    } else if (status === 'failed') {
      await SpOrder.findByIdAndUpdate(payment.orderId, {
        status: 'cancelled',
        updatedBy: user.userId
      });
    }

    return successResponse(payment, 'Payment updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin payment update error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const paymentId = searchParams.get('id');

    if (!paymentId) {
      return errorResponse('Payment ID is required');
    }

    await connectToDatabase();

    const payment = await SpPayment.findByIdAndDelete(paymentId);
    if (!payment) {
      return errorResponse('Payment not found', 404);
    }

    return successResponse(null, 'Payment deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin payment deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}
