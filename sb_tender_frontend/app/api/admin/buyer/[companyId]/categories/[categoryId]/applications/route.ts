import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/categories/:categoryId/applications - Category applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    // Verify buyer company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Verify category belongs to this company
    const category = await SpBuyerCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    });

    if (!category) {
      return errorResponse('Category not found for this buyer company', 404);
    }

    // Build query for applications in this category
    let query: any = { spJobCategoryId: categoryId };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'name price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate category statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobCategoryId: categoryId } },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' },
          averageTotalScore: { $avg: '$totalScore' },
          maxScore: { $max: '$totalScore' },
          minScore: { $min: '$totalScore' },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          }
        }
      }
    ]);

    return successResponse({
      applications,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      category: {
        _id: category._id,
        name: category.name,
        type: category.type,
        price: category.price,
        maxScore: category.maxScore
      },
      statistics: stats[0] || {
        totalApplications: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        maxScore: 0,
        minScore: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List buyer category applications error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer category applications', 500);
  }
}
