import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/jobs/:jobId/categories/:categoryId - Get specific buyer job category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string; categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, jobId, categoryId } = await params;

    if (!companyId || !jobId || !categoryId) {
      return errorResponse('Company ID, Job ID, and Category ID are required', 400);
    }

    // Verify company exists and is a buyer
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Verify job belongs to this company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      return errorResponse('Job not found for this buyer company', 404);
    }

    // Find the specific job category
    const jobCategory = await SpJobCategory.findOne({
      _id: categoryId,
      spJobId: jobId
    })
      .populate('spJobId', 'title description')
      .populate('spCategoryId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!jobCategory) {
      return errorResponse('Job category not found', 404);
    }

    // Get additional statistics for this category
    const categoryWithStats = {
      ...jobCategory.toObject(),
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      statistics: {
        // These would be calculated from applications
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        averageScore: 0
      }
    };

    return successResponse(categoryWithStats);

  } catch (error: any) {
    console.error('Get buyer job category error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer job category', 500);
  }
}
