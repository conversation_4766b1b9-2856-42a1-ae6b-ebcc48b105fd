import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/jobs/:jobId - Get buyer job
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify company exists and is a buyer
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Find job belonging to this buyer company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    })
      .populate('spCompanyId', 'name type email phone address')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!job) {
      return errorResponse('Job not found for this buyer company', 404);
    }

    // Get additional buyer-specific data
    const jobWithStats = {
      ...job.toObject(),
      company: company,
      statistics: {
        // These would be calculated from applications
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0
      }
    };

    return successResponse(jobWithStats);

  } catch (error: any) {
    console.error('Get buyer job error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer job', 500);
  }
}
