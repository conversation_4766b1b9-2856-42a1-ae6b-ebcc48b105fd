import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/jobs/:jobId/categories/:categoryId/applications - Job category apps
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string; categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, jobId, categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId || !jobId || !categoryId) {
      return errorResponse('Company ID, Job ID, and Category ID are required', 400);
    }

    // Verify buyer company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Verify job belongs to this company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      return errorResponse('Job not found for this buyer company', 404);
    }

    // Verify job category exists
    const jobCategory = await SpJobCategory.findOne({
      _id: categoryId,
      spJobId: jobId
    });

    if (!jobCategory) {
      return errorResponse('Job category not found', 404);
    }

    // Build query for applications in this job category
    let query: any = { 
      spJobId: jobId,
      spJobCategoryId: categoryId 
    };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'name price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate statistics
    const stats = await SpApplication.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' },
          averageTotalScore: { $avg: '$totalScore' },
          maxScore: { $max: '$totalScore' },
          minScore: { $min: '$totalScore' },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          }
        }
      }
    ]);

    return successResponse({
      applications,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        location: job.location,
        starts: job.starts,
        ends: job.ends
      },
      jobCategory: {
        _id: jobCategory._id,
        name: jobCategory.name,
        price: jobCategory.price,
        maxScore: jobCategory.maxScore
      },
      statistics: stats[0] || {
        totalApplications: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        maxScore: 0,
        minScore: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List job category applications error:', error);
    return errorResponse(error.message || 'Failed to fetch job category applications', 500);
  }
}
