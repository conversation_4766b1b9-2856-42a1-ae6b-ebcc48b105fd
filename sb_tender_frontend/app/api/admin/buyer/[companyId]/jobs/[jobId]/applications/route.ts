import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/jobs/:jobId/applications - Job applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const categoryId = searchParams.get('categoryId');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify buyer company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Verify job belongs to this company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      return errorResponse('Job not found for this buyer company', 404);
    }

    // Build query for applications in this job
    let query: any = { spJobId: jobId };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (categoryId) {
      query.spJobCategoryId = categoryId;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'name price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate job-level statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobId: jobId } },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' },
          averageTotalScore: { $avg: '$totalScore' },
          maxScore: { $max: '$totalScore' },
          minScore: { $min: '$totalScore' },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          }
        }
      }
    ]);

    // Get category breakdown
    const categoryStats = await SpApplication.aggregate([
      { $match: { spJobId: jobId } },
      {
        $group: {
          _id: '$spJobCategoryId',
          applicationCount: { $sum: 1 },
          averageScore: { $avg: '$totalScore' },
          maxScore: { $max: '$totalScore' },
          minScore: { $min: '$totalScore' }
        }
      },
      {
        $lookup: {
          from: 'spjobcategories',
          localField: '_id',
          foreignField: '_id',
          as: 'category'
        }
      },
      {
        $unwind: '$category'
      },
      {
        $project: {
          categoryId: '$_id',
          categoryName: '$category.name',
          categoryPrice: '$category.price',
          applicationCount: 1,
          averageScore: 1,
          maxScore: 1,
          minScore: 1
        }
      }
    ]);

    // Get top suppliers for this job
    const topSuppliers = await SpApplication.find({ spJobId: jobId })
      .populate('spCompanyId', 'name type')
      .populate('spJobCategoryId', 'name')
      .sort({ totalScore: -1 })
      .limit(10)
      .select('spCompanyId spJobCategoryId totalScore systemScore complianceScore status');

    return successResponse({
      applications,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        location: job.location,
        contract: job.contract,
        starts: job.starts,
        ends: job.ends,
        status: job.status
      },
      statistics: stats[0] || {
        totalApplications: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        maxScore: 0,
        minScore: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0
      },
      categoryBreakdown: categoryStats,
      topSuppliers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List job applications error:', error);
    return errorResponse(error.message || 'Failed to fetch job applications', 500);
  }
}
