import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/buyer/:companyId/jobs/:jobId/categories - List buyer job categories
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const isActive = searchParams.get('isActive');

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify company exists and is a buyer
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'buyer'
    });

    if (!company) {
      return errorResponse('Buyer company not found', 404);
    }

    // Verify job belongs to this company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      return errorResponse('Job not found for this buyer company', 404);
    }

    // Build query
    let query: any = { spJobId: jobId };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpJobCategory.find(query)
        .populate('spJobId', 'title description')
        .populate('spCategoryId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJobCategory.countDocuments(query)
    ]);

    return successResponse({
      categories,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      job: {
        _id: job._id,
        title: job.title,
        description: job.description
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List buyer job categories error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer job categories', 500);
  }
}
