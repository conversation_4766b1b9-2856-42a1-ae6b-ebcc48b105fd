import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpActivityLog from '@/lib/models/sp_activity_log';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/activities - System activities
export async function GET(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const action = searchParams.get('action');
    const resource = searchParams.get('resource');
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query
    let query: any = {};

    if (search) {
      query.$or = [
        { action: { $regex: search, $options: 'i' } },
        { resource: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'metadata.additionalInfo': { $regex: search, $options: 'i' } }
      ];
    }

    if (action) {
      query.action = action;
    }

    if (resource) {
      query.resource = resource;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [activities, total] = await Promise.all([
      SpActivityLog.find(query)
        .populate('userId', 'firstName lastName email type')
        .populate('resourceId') // This will populate based on resource type
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpActivityLog.countDocuments(query)
    ]);

    // Calculate activity statistics
    const stats = await SpActivityLog.aggregate([
      {
        $group: {
          _id: null,
          totalActivities: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' },
          actionBreakdown: {
            $push: '$action'
          },
          resourceBreakdown: {
            $push: '$resource'
          }
        }
      },
      {
        $project: {
          totalActivities: 1,
          uniqueUsersCount: { $size: '$uniqueUsers' },
          actionBreakdown: 1,
          resourceBreakdown: 1
        }
      }
    ]);

    // Get recent activity summary
    const recentStats = await SpActivityLog.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        }
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get top active users
    const topUsers = await SpActivityLog.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
        }
      },
      {
        $group: {
          _id: '$userId',
          activityCount: { $sum: 1 },
          lastActivity: { $max: '$createdAt' },
          actions: { $addToSet: '$action' }
        }
      },
      {
        $lookup: {
          from: 'spusers',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          userId: '$_id',
          userName: { $concat: ['$user.firstName', ' ', '$user.lastName'] },
          userEmail: '$user.email',
          userType: '$user.type',
          activityCount: 1,
          lastActivity: 1,
          uniqueActions: { $size: '$actions' }
        }
      },
      {
        $sort: { activityCount: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get activity trends (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const activityTrends = await SpActivityLog.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            action: '$action'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          actions: {
            $push: {
              action: '$_id.action',
              count: '$count'
            }
          },
          totalCount: { $sum: '$count' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    return successResponse({
      activities,
      statistics: {
        overview: stats[0] || {
          totalActivities: 0,
          uniqueUsersCount: 0,
          actionBreakdown: [],
          resourceBreakdown: []
        },
        recent24Hours: recentStats,
        topUsers,
        trends: activityTrends
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List activities error:', error);
    return errorResponse(error.message || 'Failed to fetch activities', 500);
  }
}

// POST /api/admin/activities - Create activity log (for system use)
export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const body = await request.json();
    const {
      action,
      resource,
      resourceId,
      description,
      metadata = {}
    } = body;

    // Validation
    const errors: string[] = [];
    if (!action) errors.push('Action is required');
    if (!resource) errors.push('Resource type is required');
    if (!description) errors.push('Description is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Create activity
    const activity = new SpActivityLog({
      action,
      resource,
      resourceId,
      description,
      metadata: {
        ...metadata,
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown'
      },
      userId: user.userId
    });

    await activity.save();

    // Populate and return
    await activity.populate('userId', 'firstName lastName email type');

    return successResponse(activity, 201);

  } catch (error: any) {
    console.error('Create activity error:', error);
    return errorResponse(error.message || 'Failed to create activity', 500);
  }
}
