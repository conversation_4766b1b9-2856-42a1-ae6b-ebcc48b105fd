import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/companies/:companyId/roles - List company roles
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Define default roles based on company type
    const defaultRoles = {
      buyer: [
        {
          name: 'admin',
          displayName: 'Administrator',
          description: 'Full access to company resources',
          permissions: ['read', 'write', 'delete', 'manage_users', 'manage_jobs', 'manage_categories']
        },
        {
          name: 'manager',
          displayName: 'Manager',
          description: 'Manage jobs and categories',
          permissions: ['read', 'write', 'manage_jobs', 'manage_categories']
        },
        {
          name: 'user',
          displayName: 'User',
          description: 'Basic access to view and apply',
          permissions: ['read']
        }
      ],
      supplier: [
        {
          name: 'admin',
          displayName: 'Administrator',
          description: 'Full access to company resources',
          permissions: ['read', 'write', 'delete', 'manage_users', 'manage_applications']
        },
        {
          name: 'manager',
          displayName: 'Manager',
          description: 'Manage applications and bids',
          permissions: ['read', 'write', 'manage_applications']
        },
        {
          name: 'user',
          displayName: 'User',
          description: 'Basic access to view and apply',
          permissions: ['read']
        }
      ],
      admin: [
        {
          name: 'super_admin',
          displayName: 'Super Administrator',
          description: 'Full system access',
          permissions: ['*']
        },
        {
          name: 'admin',
          displayName: 'Administrator',
          description: 'System administration',
          permissions: ['read', 'write', 'delete', 'manage_all']
        }
      ]
    };

    // Get roles for this company type
    const roles = defaultRoles[company.type as keyof typeof defaultRoles] || defaultRoles.user;

    return successResponse({
      roles,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      }
    });

  } catch (error: any) {
    console.error('List company roles error:', error);
    return errorResponse(error.message || 'Failed to fetch company roles', 500);
  }
}
