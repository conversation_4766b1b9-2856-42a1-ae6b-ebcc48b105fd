import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// POST /api/admin/companies/:companyId/categories - Create buyer category
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();
    
    const {
      name,
      description,
      type,
      fields,
      price,
      isActive = true,
      maxScore,
      requirements
    } = body;

    // Validation
    const errors: string[] = [];
    if (!companyId) errors.push('Company ID is required');
    if (!name) errors.push('Category name is required');
    if (!type) errors.push('Category type is required');
    if (!fields || !Array.isArray(fields)) errors.push('Fields array is required');
    if (!price || price <= 0) errors.push('Valid price is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Check for duplicate name within company
    const existingCategory = await SpBuyerCategory.findOne({ 
      spCompanyId: companyId, 
      name 
    });
    if (existingCategory) {
      return errorResponse('Category with this name already exists for this company', 409);
    }

    // Calculate total max score from fields
    const calculatedMaxScore = fields.reduce((total: number, field: any) => {
      return total + (field.maxScore || 0);
    }, 0);

    // Create buyer category
    const category = new SpBuyerCategory({
      name,
      description,
      type,
      fields,
      price,
      isActive,
      maxScore: maxScore || calculatedMaxScore,
      requirements,
      spCompanyId: companyId,
      createdBy: user.userId
    });

    await category.save();

    // Populate the created category
    await category.populate('spCompanyId', 'name type');
    await category.populate('createdBy', 'firstName lastName email');

    return successResponse(category, 201);

  } catch (error: any) {
    console.error('Create buyer category error:', error);
    return errorResponse(error.message || 'Failed to create buyer category', 500);
  }
}

// GET /api/admin/companies/:companyId/categories - List buyer categories
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Build query
    let query: any = { spCompanyId: companyId };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { type: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (type) {
      query.type = type;
    }
    
    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpBuyerCategory.find(query)
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpBuyerCategory.countDocuments(query)
    ]);

    return successResponse({
      categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List buyer categories error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer categories', 500);
  }
}

// PUT /api/admin/companies/:companyId/categories/:categoryId - Update buyer category
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const categoryId = pathParts[pathParts.length - 1];

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    const body = await request.json();
    const {
      name,
      description,
      type,
      fields,
      price,
      isActive,
      maxScore,
      requirements
    } = body;

    // Find existing category
    const category = await SpBuyerCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    });
    
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Check for duplicate name (excluding current category)
    if (name && name !== category.name) {
      const existingCategory = await SpBuyerCategory.findOne({ 
        spCompanyId: companyId,
        name, 
        _id: { $ne: categoryId } 
      });
      if (existingCategory) {
        return errorResponse('Category with this name already exists for this company', 409);
      }
    }

    // Calculate total max score from fields if fields are updated
    let calculatedMaxScore = category.maxScore;
    if (fields && Array.isArray(fields)) {
      calculatedMaxScore = fields.reduce((total: number, field: any) => {
        return total + (field.maxScore || 0);
      }, 0);
    }

    // Update category
    const updateData = {
      ...(name && { name }),
      ...(description && { description }),
      ...(type && { type }),
      ...(fields && { fields }),
      ...(price && { price }),
      ...(isActive !== undefined && { isActive }),
      maxScore: maxScore || calculatedMaxScore,
      ...(requirements && { requirements }),
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    Object.assign(category, updateData);
    await category.save();

    // Populate and return
    await category.populate('spCompanyId', 'name type');
    await category.populate('createdBy', 'firstName lastName email');
    await category.populate('updatedBy', 'firstName lastName email');

    return successResponse(category);

  } catch (error: any) {
    console.error('Update buyer category error:', error);
    return errorResponse(error.message || 'Failed to update buyer category', 500);
  }
}

// DELETE /api/admin/companies/:companyId/categories/:id - Delete buyer category
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const categoryId = pathParts[pathParts.length - 1];

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    const category = await SpBuyerCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    });
    
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    await SpBuyerCategory.findByIdAndDelete(categoryId);

    return successResponse({ message: 'Buyer category deleted successfully' });

  } catch (error: any) {
    console.error('Delete buyer category error:', error);
    return errorResponse(error.message || 'Failed to delete buyer category', 500);
  }
}
