import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpCategoryTemplate from '@/lib/models/sp_category_template';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';



// PUT /api/admin/companies/:companyId/categories/:categoryId - Update buyer category
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const categoryId = pathParts[pathParts.length - 1];

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    const body = await request.json();
       const { name, description, type, status } = body;
       const category = await SpBuyerCategory.findByIdAndUpdate(categoryId,
         { name, description, type, status, updatedBy: user.userId },
         { new: true }
       );
       if (!category)
        return errorResponse('Category not found', 404);
             
    // Populate and return
    await category.populate('spCompanyId', 'name type');
    await category.populate('createdBy', 'firstName lastName email');
    await category.populate('updatedBy', 'firstName lastName email');

    return successResponse(category);

  } catch (error: any) {
    console.error('Update buyer category error:', error);
    return errorResponse(error.message || 'Failed to update buyer category', 500);
  }
}