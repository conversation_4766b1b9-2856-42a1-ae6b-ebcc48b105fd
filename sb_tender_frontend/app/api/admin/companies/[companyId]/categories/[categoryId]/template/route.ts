import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpBuyerCategoryTemplate from '@/lib/models/sp_buyer_category_template';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/companies/:companyId/categories/:categoryId/template - Get buyer category template
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, categoryId } = await params;

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    // Find buyer category
    const category = await SpBuyerCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    })
      .populate('spCompanyId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Check if there's a specific template for this category
    let template = await SpBuyerCategoryTemplate.findOne({
      spCompanyId: companyId,
      spCategoryId: categoryId
    })
      .populate('spCompanyId', 'name type')
      .populate('spCategoryId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    // If no specific template, return the category itself as template
    if (!template) {
      template = {
        _id: category._id,
        spCompanyId: category.spCompanyId,
        spCategoryId: category._id,
        name: category.name,
        description: category.description,
        type: category.type,
        fields: category.fields || [],
        price: category.price,
        maxScore: category.maxScore,
        requirements: category.requirements,
        isActive: category.isActive,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
        createdBy: category.createdBy,
        updatedBy: category.updatedBy,
        isFromCategory: true // Flag to indicate this is from category, not template
      };
    }

    return successResponse(template);

  } catch (error: any) {
    console.error('Get buyer category template error:', error);
    return errorResponse(error.message || 'Failed to fetch buyer category template', 500);
  }
}
