import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpBuyerCategoryTemplate from '@/lib/models/sp_buyer_category_template';
import { requireAdmin } from '@/lib/middleware/auth';

export const dynamic = 'force-dynamic';

// PUT /api/admin/companies/[companyId]/categories/[categoryId]/templates/[templateId] - Update buyer category template by ID (matches Express backend exactly)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; categoryId: string; templateId: string }> }
) {
  try {
    console.log('=== PUT Buyer Category Template by ID - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId, categoryId, templateId } = await params;
    const body = await request.json();
    
    console.log('Template Request Params:', { companyId, categoryId, templateId });
    console.log('Request Body keys:', Object.keys(body));

    // Get the actual updates - they're nested inside categoryTemplate
    const updateData = body.categoryTemplate || body;
    console.log('Buyer Template Update data structure:', Object.keys(updateData));

    console.log('Step 1: Finding buyer category template by ID...');
    // Find the template by ID, category and company (matching Express backend exactly)
    const template = await SpBuyerCategoryTemplate.findOne({ 
      _id: templateId,
      spBuyerCategoryId: categoryId, 
      spCompanyId: companyId 
    });

    if (!template) {
      console.error('Buyer Template not found for company ID:', companyId, 'and category ID:', categoryId);
      return NextResponse.json(
        { message: 'Buyer Template not found' },
        { status: 404 }
      );
    }

    console.log('Template found:', template.name);

    console.log('Step 2: Processing updates...');
    // Remove restricted fields from updates (matching Express backend exactly)
    const { 
      _id, 
      spCategoryId, 
      spCompanyId, 
      spBuyerCategoryId, 
      createdAt, 
      createdBy, 
      __v, 
      ...allowedUpdates 
    } = updateData;
    
    console.log('Fields being updated:', Object.keys(allowedUpdates));
    
    // Update fields array if it exists (matching Express backend exactly)
    if (allowedUpdates.fields) {
      console.log('Fields array length:', allowedUpdates.fields.length);
      template.fields = allowedUpdates.fields;
    }
    
    // Update other properties (matching Express backend exactly)
    for (const [key, value] of Object.entries(allowedUpdates)) {
      if (key !== 'fields') {
        template[key] = value;
      }
    }
    
    // Set updatedBy (matching Express backend exactly)
    template.updatedBy = user.userId;
    
    // Log the template before saving (matching Express backend exactly)
    console.log('Buyer Template before save - Fields count:', template.fields?.length);
    
    console.log('Step 3: Saving template...');
    // Save the updated template
    await template.save();
    console.log('Buyer Template saved');

    console.log('=== PUT Buyer Category Template by ID - Success ===');
    // Respond with the updated template (matching Express backend exactly)
    return NextResponse.json(template);

  } catch (error: any) {
    console.error('=== PUT Buyer Category Template by ID - Error ===');
    console.error('Buyer Template Update Error:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { message: 'Server Error', error: error.message },
      { status: 500 }
    );
  }
}
