import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpBuyerCategoryTemplate from '@/lib/models/sp_buyer_category_template';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// PUT /api/admin/companies/:companyId/categories/:categoryId/templates/:templateId - Update buyer category template
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; categoryId: string; templateId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId, categoryId, templateId } = await params;

    if (!companyId || !categoryId || !templateId) {
      return errorResponse('Company ID, Category ID, and Template ID are required', 400);
    }

    const body = await request.json();
    const {
      name,
      description,
      fields,
      price,
      isActive,
      maxScore,
      requirements
    } = body;

    // Find existing template
    let template = await SpBuyerCategoryTemplate.findOne({
      _id: templateId,
      spCompanyId: companyId,
      spCategoryId: categoryId
    });

    // If no template exists, create one based on the category
    if (!template) {
      const category = await SpBuyerCategory.findOne({
        _id: categoryId,
        spCompanyId: companyId
      });

      if (!category) {
        return errorResponse('Category not found', 404);
      }

      template = new SpBuyerCategoryTemplate({
        spCompanyId: companyId,
        spCategoryId: categoryId,
        name: category.name,
        description: category.description,
        type: category.type,
        fields: category.fields,
        price: category.price,
        maxScore: category.maxScore,
        requirements: category.requirements,
        isActive: category.isActive,
        createdBy: user.userId
      });
    }

    // Calculate total max score from fields if fields are updated
    let calculatedMaxScore = template.maxScore;
    if (fields && Array.isArray(fields)) {
      calculatedMaxScore = fields.reduce((total: number, field: any) => {
        return total + (field.maxScore || 0);
      }, 0);
    }

    // Update template
    const updateData = {
      ...(name && { name }),
      ...(description && { description }),
      ...(fields && { fields }),
      ...(price && { price }),
      ...(isActive !== undefined && { isActive }),
      maxScore: maxScore || calculatedMaxScore,
      ...(requirements && { requirements }),
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    Object.assign(template, updateData);
    await template.save();

    // Also update the original category if this is the primary template
    const category = await SpBuyerCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    });

    if (category) {
      Object.assign(category, {
        ...(name && { name }),
        ...(description && { description }),
        ...(fields && { fields }),
        ...(price && { price }),
        ...(isActive !== undefined && { isActive }),
        maxScore: maxScore || calculatedMaxScore,
        ...(requirements && { requirements }),
        updatedBy: user.userId,
        updatedAt: new Date()
      });
      await category.save();
    }

    // Populate and return
    await template.populate('spCompanyId', 'name type');
    await template.populate('spCategoryId', 'name type');
    await template.populate('createdBy', 'firstName lastName email');
    await template.populate('updatedBy', 'firstName lastName email');

    return successResponse(template);

  } catch (error: any) {
    console.error('Update buyer category template error:', error);
    return errorResponse(error.message || 'Failed to update buyer category template', 500);
  }
}
