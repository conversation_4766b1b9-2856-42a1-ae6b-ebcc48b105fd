import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// POST /api/admin/companies/:companyId/jobs - Create job
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();
    
    const {
      title,
      description,
      location,
      contract,
      starts,
      ends,
      status = 'active',
      categories = []
    } = body;

    // Validation
    const errors: string[] = [];
    if (!companyId) errors.push('Company ID is required');
    if (!title) errors.push('Job title is required');
    if (!description) errors.push('Job description is required');
    if (!location) errors.push('Location is required');
    if (!contract) errors.push('Contract type is required');
    if (!starts) errors.push('Start date is required');
    if (!ends) errors.push('End date is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Validate dates
    const startDate = new Date(starts);
    const endDate = new Date(ends);
    
    if (startDate >= endDate) {
      return errorResponse('End date must be after start date', 400);
    }

    // Create job
    const job = new SpJob({
      title,
      description,
      spCompanyId: companyId,
      location,
      contract,
      starts: startDate,
      ends: endDate,
      status,
      categories,
      createdBy: user.userId
    });

    await job.save();

    // Populate the created job
    await job.populate('spCompanyId', 'name type');
    await job.populate('createdBy', 'firstName lastName email');

    return successResponse(job, 201);

  } catch (error: any) {
    console.error('Create job error:', error);
    return errorResponse(error.message || 'Failed to create job', 500);
  }
}

// GET /api/admin/companies/:companyId/jobs - List jobs (companyId optional)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId?: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const contract = searchParams.get('contract');

    // Build query
    let query: any = {};
    
    // If companyId is provided, filter by company
    if (companyId) {
      query.spCompanyId = companyId;
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (contract) {
      query.contract = contract;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [jobs, total] = await Promise.all([
      SpJob.find(query)
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJob.countDocuments(query)
    ]);

    return successResponse({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List jobs error:', error);
    return errorResponse(error.message || 'Failed to fetch jobs', 500);
  }
}

// DELETE /api/admin/companies/:companyId/jobs/:jobId - Delete job
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const jobId = pathParts[pathParts.length - 1];

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });
    
    if (!job) {
      return errorResponse('Job not found', 404);
    }

   // await SpJob.findByIdAndDelete(jobId);

    return successResponse({ message: 'Job deleted successfully' });

  } catch (error: any) {
    console.error('Delete job error:', error);
    return errorResponse(error.message || 'Failed to delete job', 500);
  }
}



const updateJobCategories = async (job, userId) => {
  const jobId = job._id;
  console.log('Job', job);
  console.log('User', userId);

  try {
    // Add await here to get the actual documents
    const jobCategories = await SpJobCategory.find({ spJobId: jobId }).exec();
    console.log('Found job categories', jobCategories.length);

    // Use for...of for array iteration
    for (const category of jobCategories) {
      // Create a new object with the updated values
      const updates = {
        title: category.title,
        description: category.description, //
        status: job.status,
        price: job.categoryPrice,
        starts: job.starts,
        ends: job.ends,
        location: job.location,
        spCompanyId: job.spCompanyId,
        type: job.type,
        updatedBy: userId
      };

      await SpJobCategory.findByIdAndUpdate(
        category._id,
        updates,
        { new: true }
      );
    }

    return true;
  } catch (error) {
    console.error('Could not update job categories', error);
    return false;
  }
};
const createJobCategories = async function(companyId, spJob, spBuyerCategoryIds, userId) {
  console.log('Creating category for job ID:', spJob._id);
  let fields = null;
// Use findOne() if expecting single document, or handle array from find()
let refCount = 1;
for (const spBuyerCategoryId of spBuyerCategoryIds) {
  const buyerCategory = await SpBuyerCategory.findOne({ _id: spBuyerCategoryId,
    spCompanyId: companyId,
  });
  const buyerCategoryTemplate = await SpBuyerCategoryTemplate.findOne({
    spBuyerCategoryId,
    spCompanyId: companyId,
  });
  
  const company = await SpCompany.findOne({_id: companyId});

  if(!buyerCategory || !buyerCategoryTemplate || !company) {
    console.log('No buyer category found for this category');
    return null;
  }
  
    const ref = generateReference(company.name, refCount);
    fields = buyerCategoryTemplate.fields;
    console.log('Buyer Category template found:', buyerCategoryTemplate._id);

  try {
    const jobCategory = new SpJobCategory({
      ref: ref,
      title: buyerCategory.description,
      description: spJob.title,
      status: spJob.status,
      price: spJob.categoryPrice,
      starts: spJob.starts,
      ends: spJob.ends,
      location: spJob.location,
      spBuyerCategoryId: buyerCategory._id,
      spBuyerCategoryTemplateId: buyerCategoryTemplate._id,
      spCompanyId: buyerCategory.spCompanyId,
      spJobId: spJob._id,
      type: buyerCategory.type,
      createdBy: userId,
      fields: fields,
    });
    await jobCategory.save();
    console.log('Job Category template created:', buyerCategoryTemplate._id);
  } catch (error) {
    console.error('Error creating job category template:', error);
  }
  refCount++;

}
return true;
}
function getFirstLetters(companyName) {
  return companyName.split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}
function getNumericEquivalent(companyName) {
  let numericValue = 0;
  for (let i = 0; i < companyName.length; i++) {
    const char = companyName.toUpperCase().charAt(i);
    if (char >= 'A' && char <= 'Z') {
      numericValue += char.charCodeAt(0) - 64; // A=1, B=2,...Z=26
    }
  }
  return numericValue;
}
function generateReference(companyName, refCount) {
  const firstLetters = getFirstLetters(companyName);
  const numericEquivalent = getNumericEquivalent(companyName);
  const year = new Date().getFullYear();
  return `${firstLetters}_${numericEquivalent}_${year}_${refCount}`;
}