import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpBuyerCategoryTemplate from '@/lib/models/sp_buyer_category_template';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// POST /api/admin/companies/:companyId/jobs - Create buyer job (matches Express backend exactly)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== POST Create Buyer Job - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();

    console.log('Request body', body);

    const { title, description, contract, starts, ends, location, categoryPrice, selectedJobCategories, type, passMark } = body;

    console.log(title, description, contract, starts, ends, location, categoryPrice, companyId, type, passMark );

    console.log('Step 1: Creating job...');
    const spJob = new SpJob({
      title,
      description,
      contract,
      starts,
      ends,
      location,
      type,
      status: 'draft',
      categoryPrice,
      passMark,
      spCompanyId: companyId,
      createdBy: user.userId,
    });
    await spJob.save();
    console.log('Job created:', spJob._id);

    console.log('Step 2: Creating job categories...');
    //create category template fields
    const jobCategories = await createJobCategories(companyId, spJob, selectedJobCategories, user.userId);

    if (jobCategories) {
      console.log('Job Categories created successfully');
    } else {
      console.log('No job categories created');
    }

    console.log('=== POST Create Buyer Job - Success ===');
    return NextResponse.json(spJob, { status: 201 });

  } catch (error: any) {
    console.error('=== POST Create Buyer Job - Error ===');
    console.error('Error creating job and categories:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// GET /api/admin/companies/:companyId/jobs - List jobs (companyId optional)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId?: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const contract = searchParams.get('contract');

    // Build query
    let query: any = {};
    
    // If companyId is provided, filter by company
    if (companyId) {
      query.spCompanyId = companyId;
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (contract) {
      query.contract = contract;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [jobs, total] = await Promise.all([
      SpJob.find(query)
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJob.countDocuments(query)
    ]);

    return successResponse({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List jobs error:', error);
    return errorResponse(error.message || 'Failed to fetch jobs', 500);
  }
}

// PUT /api/admin/companies/:companyId/jobs/:jobId - Update buyer job (matches Express backend exactly)
export async function PUT(request: NextRequest) {
  try {
    console.log('=== PUT Update Buyer Job - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const jobId = pathParts[pathParts.length - 1];
    const body = await request.json();

    console.log('Body server', body);
    console.log('Job ID', jobId);

    const { title, description, contract, starts, ends, location, categoryPrice, passMark } = body;

    console.log('Step 1: Updating job...');
    const job = await SpJob.findByIdAndUpdate(
      jobId,
      { title, description, contract, starts, ends, location, categoryPrice, passMark },
      { new: true }
    );

    if (!job) {
      console.log('Job not found');
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('Step 2: Updating job categories...');
    //update all categories with new job information
    let updatedCategories = await updateJobCategories(job, user.userId);

    if (updatedCategories) {
      console.log('Job categories updated successfully');
    } else {
      console.log('Failed to update job categories');
    }

    console.log('=== PUT Update Buyer Job - Success ===');
    return NextResponse.json(job);

  } catch (error: any) {
    console.error('=== PUT Update Buyer Job - Error ===');
    console.error('Error', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/companies/:companyId/jobs/:jobId - Delete job and associated job categories
export async function DELETE(request: NextRequest) {
  try {
    console.log('=== DELETE Buyer Job - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const jobId = pathParts[pathParts.length - 1];

    console.log('Deleting job ID:', jobId, 'for company:', companyId);

    if (!companyId || !jobId) {
      return NextResponse.json(
        { error: 'Company ID and Job ID are required' },
        { status: 400 }
      );
    }

    console.log('Step 1: Finding job...');
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      console.error('Job not found:', jobId);
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('Step 2: Deleting associated job categories...');
    // Delete all job categories associated with this job
    const deletedJobCategories = await SpJobCategory.deleteMany({ spJobId: jobId });
    console.log('Deleted job categories:', deletedJobCategories.deletedCount);

    console.log('Step 3: Deleting job...');
    await SpJob.findByIdAndDelete(jobId);
    console.log('Job deleted successfully');

    console.log('=== DELETE Buyer Job - Success ===');
    return NextResponse.json({ message: 'Job deleted successfully' });

  } catch (error: any) {
    console.error('=== DELETE Buyer Job - Error ===');
    console.error('Delete job error:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}



const updateJobCategories = async (job: any, userId: string) => {
  const jobId = job._id;
  console.log('Job', job);
  console.log('User', userId);

  try {
    // Add await here to get the actual documents
    const jobCategories = await SpJobCategory.find({ spJobId: jobId }).exec();
    console.log('Found job categories', jobCategories.length);

    // Use for...of for array iteration
    for (const category of jobCategories) {
      // Create a new object with the updated values
      const updates = {
        title: category.title,
        description: category.description, //
        status: job.status,
        price: job.categoryPrice,
        passMark: job.passMark,
        starts: job.starts,
        ends: job.ends,
        location: job.location,
        spCompanyId: job.spCompanyId,
        type: job.type,
        updatedBy: userId
      };

      await SpJobCategory.findByIdAndUpdate(
        category._id,
        updates,
        { new: true }
      );
    }

    return true;
  } catch (error) {
    console.error('Could not update job categories', error);
    return false;
  }
};
const createJobCategories = async function(companyId: string, spJob: any, spBuyerCategoryIds: string[], userId: string) {
  console.log('Creating category for job ID:', spJob._id);
  let fields = null;
// Use findOne() if expecting single document, or handle array from find()
let refCount = 1;
for (const spBuyerCategoryId of spBuyerCategoryIds) {
  const buyerCategory = await SpBuyerCategory.findOne({ _id: spBuyerCategoryId,
    spCompanyId: companyId,
  });
  const buyerCategoryTemplate = await SpBuyerCategoryTemplate.findOne({
    spBuyerCategoryId,
    spCompanyId: companyId,
  });
  
  const company = await SpCompany.findOne({_id: companyId});

  if(!buyerCategory || !buyerCategoryTemplate || !company) {
    console.log('No buyer category found for this category');
    return null;
  }
  
    const ref = generateReference(company.name, refCount);
    fields = buyerCategoryTemplate.fields;
    console.log('Buyer Category template found:', buyerCategoryTemplate._id);

  try {
    const jobCategory = new SpJobCategory({
      ref: ref,
      title: buyerCategory.description,
      description: spJob.title,
      status: spJob.status,
      price: spJob.categoryPrice,
      passMark: spJob.passMark,
      starts: spJob.starts,
      ends: spJob.ends,
      location: spJob.location,
      spBuyerCategoryId: buyerCategory._id,
      spBuyerCategoryTemplateId: buyerCategoryTemplate._id,
      spCompanyId: buyerCategory.spCompanyId,
      spJobId: spJob._id,
      type: buyerCategory.type,
      createdBy: userId,
      fields: fields,
    });
    await jobCategory.save();
    console.log('Job Category template created:', buyerCategoryTemplate._id);
  } catch (error) {
    console.error('Error creating job category template:', error);
  }
  refCount++;

}
return true;
}
function getFirstLetters(companyName: string): string {
  return companyName.split(' ')
    .map((word: string) => word.charAt(0).toUpperCase())
    .join('');
}

function getNumericEquivalent(companyName: string): number {
  let numericValue = 0;
  for (let i = 0; i < companyName.length; i++) {
    const char = companyName.toUpperCase().charAt(i);
    if (char >= 'A' && char <= 'Z') {
      numericValue += char.charCodeAt(0) - 64; // A=1, B=2,...Z=26
    }
  }
  return numericValue;
}

function generateReference(companyName: string, refCount: number): string {
  const firstLetters = getFirstLetters(companyName);
  const numericEquivalent = getNumericEquivalent(companyName);
  const year = new Date().getFullYear();
  return `${firstLetters}_${numericEquivalent}_${year}_${refCount}`;
}