import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpUser from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import SpUserRole from '@/lib/models/sp_user_role';
import SpRole from '@/lib/models/sp_role';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

const TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];
// GET /api/admin/companies/:companyId/users - List company users
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    console.log('Fetching users for company:', companyId);

    // Find all SpUserRoles where companyId matches
    const companyUserRoles = await SpUserRole.find({ companyId });

    if (!companyUserRoles || companyUserRoles.length === 0) {
      return successResponse({
        users: [],
        company: {
          _id: company._id,
          name: company.name,
          type: company.type
        },
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      });
    }

    // Extract userIds and roleIds from companyRoles
    const userIds = companyUserRoles.map(role => role.userId);
    const companyRoleIds = companyUserRoles.map(role => role.roleId);

    console.log('Found user IDs:', userIds.length);
    console.log('Found role IDs:', companyRoleIds.length);

    // Build user query with search and filters
    const userQuery: any = { _id: { $in: userIds } };

    if (search) {
      userQuery.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      userQuery.type = type;
    }

    if (isActive !== null && isActive !== undefined) {
      userQuery.isActive = isActive === 'true';
    }

    // Find all users whose _id matches the userIds from companyRoles with pagination
    const skip = (page - 1) * limit;
    const [companyUsers, total] = await Promise.all([
      SpUser.find(userQuery)
        .select('-password') // Exclude password from response
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpUser.countDocuments(userQuery)
    ]);

    // Find all roles for the company
    const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });

    console.log('Found users:', companyUsers.length);
    console.log('Found roles:', companyRoles.length);

    // Map roles to users - CONVERT TO STRINGS for proper comparison
    const userRolesMap = companyUserRoles.reduce((map: any, userRole: any) => {
      const userIdStr = userRole.userId.toString();
      if (!map[userIdStr]) {
        map[userIdStr] = [];
      }
      map[userIdStr].push(userRole.roleId.toString());
      return map;
    }, {});

    const companyUsersWithRoles = companyUsers.map(user => ({
      ...user.toObject(),
      // FIXED: Convert ObjectIds to strings for comparison
      roles: companyRoles.filter(role =>
        userRolesMap[user._id.toString()]?.includes(role._id.toString())
      )
    }));

    return successResponse({
      users: companyUsersWithRoles,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List company users error:', error);
    return errorResponse(error.message || 'Failed to fetch company users', 500);
  }
}

// POST /api/admin/companies/:companyId/users - Create company user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const adminUser = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();
    /*
    const {
      firstName,
      lastName,
      email,
      password,
      type,
      role = 'user',
      isActive = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!companyId) errors.push('Company ID is required');
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!type) errors.push('User type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Check if user already exists
    const existingUser = await SpUser.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = new SpUser({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      type,
      role,
      isActive,
      spCompanyId: companyId,
      createdBy: adminUser.userId
    });

    await user.save();

    // Populate and return (excluding password)
    await user.populate('spCompanyId', 'name type');
    await user.populate('createdBy', 'firstName lastName email');

    const userResponse = user.toObject();
    delete userResponse.password;

    return successResponse(userResponse, 201);
    */
   const {
       firstName,
       lastName,
       email,
       password, // Password is required for creation
       phone,
       type,     // 'admin', 'buyer', 'buyer'
       status,   // 'active', 'inactive', 'suspended' - optional, will use default if not provided
       roleIds,
       emailVerified, // Optional boolean, will use default
       phoneVerified, // Optional boolean, will use default
       otpVerified,   // Optional boolean, will use default
       mustUpdatePassword // Optional boolean, will use default
     } = body;
   
     console.log(body);
   
     // Basic validation
     if (!firstName || !lastName || !email || !password || !phone || !type) {
       return errorResponse('Missing required fields', 400);
     }
   
     // Check if user with email already exists
     const existingUser = await SpUser.findOne({ email });
     if (existingUser) {
       return errorResponse('User with this email already exists', 409);
     }
      // Check if user with phone already exists
     const existingPhoneUser = await SpUser.findOne({ phone });
     if (existingPhoneUser) {
       return errorResponse('User with this phone already exists', 409);
     }
   
     const salt = await bcrypt.genSalt(10);
     const hashedPassword = await bcrypt.hash(password, salt);
   
     const newUser = new SpUser({
       firstName,
       lastName,
       email,
       password: hashedPassword, // Save the hashed password
       phone,
       type,
       status: status || 'active', // Use provided status or default
       emailVerified: emailVerified ?? false, // Use provided or default
       phoneVerified: phoneVerified ?? false, // Use provided or default
       otpVerified: otpVerified ?? false,   // Use provided or default
       mustUpdatePassword: mustUpdatePassword ?? false, // Use provided or default
       createdBy: adminUser.userId,//req.user.userId, // Assuming auth middleware adds user ID to req.user
       // Mongoose Timestamps will handle createdAt and updatedAt
     });
   
     // Save the user to the database
     await newUser.save();
   
   
          await ensureDefaultRolesExist(type, adminUser.userId);
         console.log('Ensure default roles check/creation finished.');
     
   
    // const userTypeRoles = await SpRole.find({ context: 'buyer', _id: roleIds  });
     const userTypeRoles = await SpRole.find({ context: type });
   
          console.log(`Step 3a: Found ${userTypeRoles.length} roles with context ${type}.`);
          console.log('Found  roles objects:', userTypeRoles.map(r => ({ _id: r._id, name: r.name, context: r.context, type: r.type })));
     
     
         if (userTypeRoles.length === 0) {
             console.warn(`No ${type} roles found with context 'buyer'. Cannot assign any roles.`);
             await ensureDefaultRolesExist('buyer', newUser._id);
   
            
         } else {
             console.log(`Mapping ${userTypeRoles.length} found buyer roles to SpUserRole objects.`);
             const SpUserRoles = userTypeRoles.map(role => ({
               userId: newUser._id,
               companyId,
               roleId: role._id,
             }));
             console.log('Step 3b: Prepared SpUserRoles array for insertion:', SpUserRoles);
             console.log('Number of SpUserRoles entries to insert:', SpUserRoles.length);
     
             console.log('Attempting to insert SpUserRoles into the database...');
             // Ensure your model is correctly imported as SpUserRole, not SpUserRole
             await SpUserRole.insertMany(SpUserRoles);
             console.log(`Step 3 Successful: Successfully inserted ${SpUserRoles.length} SpUserRole documents.`);
         }

     
     // Return the created user, excluding sensitive fields
    return successResponse(excludeSensitiveFields(newUser));

  } catch (error: any) {
    console.error('Create company user error:', error);
    return errorResponse(error.message || 'Failed to create company user', 500);
  }
}

// PUT /api/admin/companies/:companyId/users/:userId - Update company user
export async function PUT(request: NextRequest) {
  try {
    const adminUser = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const userId = pathParts[pathParts.length - 1];

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      email,
      password,
      type,
      role,
      isActive
    } = body;

    // Find existing user
    const user = await SpUser.findOne({
      _id: userId,
      spCompanyId: companyId
    });

    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Check for duplicate email (excluding current user)
    if (email && email !== user.email) {
      const existingUser = await SpUser.findOne({
        email,
        _id: { $ne: userId }
      });
      if (existingUser) {
        return errorResponse('User with this email already exists', 409);
      }
    }

    // Update user
    const updateData: any = {
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(email && { email }),
      ...(type && { type }),
      ...(role && { role }),
      ...(isActive !== undefined && { isActive }),
      updatedBy: adminUser.userId,
      updatedAt: new Date()
    };

    // Hash new password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 12);
    }

    Object.assign(user, updateData);
    await user.save();

    // Populate and return (excluding password)
    await user.populate('spCompanyId', 'name type');
    await user.populate('createdBy', 'firstName lastName email');
    await user.populate('updatedBy', 'firstName lastName email');

    const userResponse = user.toObject();
    delete userResponse.password;

    return successResponse(userResponse);

  } catch (error: any) {
    console.error('Update company user error:', error);
    return errorResponse(error.message || 'Failed to update company user', 500);
  }
}

// DELETE /api/admin/companies/:companyId/users/:userId - Delete company user
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const userId = pathParts[pathParts.length - 1];

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    const user = await SpUser.findOne({
      _id: userId,
      spCompanyId: companyId
    });

    if (!user) {
      return errorResponse('User not found', 404);
    }

    await SpUser.findByIdAndDelete(userId);

    return successResponse({ message: 'Company user deleted successfully' });

  } catch (error: any) {
    console.error('Delete company user error:', error);
    return errorResponse(error.message || 'Failed to delete company user', 500);
  }
}



 const ensureDefaultRolesExist = async (context, createdBy) => {
  const existingRoles = await SpRole.find({ context });
  const existingKeys = new Set(existingRoles.map(r => r.type));

  const missingTypes = TYPES.filter(type => !existingKeys.has(type));

  if (missingTypes.length === 0) return; // All roles exist

  const newRoles = missingTypes.map(type => ({
    name: `${context.toUpperCase()} - ${type}`,
    context,
    type,
    permissions: DEFAULT_PERMISSIONS,
    description: `Auto-created role for ${context} - ${type}`,
    createdBy,
  }));

  await SpRole.insertMany(newRoles);
};

// Helper function to exclude sensitive fields from the user object
const excludeSensitiveFields = (user) => {
    if (!user) return null;
    const userObject = user.toObject ? user.toObject() : user; // Convert Mongoose doc to plain object
    delete userObject.password;
    delete userObject.otp;
    delete userObject.__v; // Mongoose version key
    return userObject;
};
