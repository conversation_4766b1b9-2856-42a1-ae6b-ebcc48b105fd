import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpUser from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

// GET /api/admin/companies/:companyId/users - List company users
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Build query
    let query: any = { spCompanyId: companyId };
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (type) {
      query.type = type;
    }
    
    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [users, total] = await Promise.all([
      SpUser.find(query)
        .select('-password') // Exclude password from response
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpUser.countDocuments(query)
    ]);

    return successResponse({
      users,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List company users error:', error);
    return errorResponse(error.message || 'Failed to fetch company users', 500);
  }
}

// POST /api/admin/companies/:companyId/users - Create company user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const adminUser = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();
    
    const {
      firstName,
      lastName,
      email,
      password,
      type,
      role = 'user',
      isActive = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!companyId) errors.push('Company ID is required');
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!type) errors.push('User type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Check if user already exists
    const existingUser = await SpUser.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = new SpUser({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      type,
      role,
      isActive,
      spCompanyId: companyId,
      createdBy: adminUser.userId
    });

    await user.save();

    // Populate and return (excluding password)
    await user.populate('spCompanyId', 'name type');
    await user.populate('createdBy', 'firstName lastName email');

    const userResponse = user.toObject();
    delete userResponse.password;

    return successResponse(userResponse, 201);

  } catch (error: any) {
    console.error('Create company user error:', error);
    return errorResponse(error.message || 'Failed to create company user', 500);
  }
}

// PUT /api/admin/companies/:companyId/users/:userId - Update company user
export async function PUT(request: NextRequest) {
  try {
    const adminUser = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const userId = pathParts[pathParts.length - 1];

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      email,
      password,
      type,
      role,
      isActive
    } = body;

    // Find existing user
    const user = await SpUser.findOne({
      _id: userId,
      spCompanyId: companyId
    });

    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Check for duplicate email (excluding current user)
    if (email && email !== user.email) {
      const existingUser = await SpUser.findOne({
        email,
        _id: { $ne: userId }
      });
      if (existingUser) {
        return errorResponse('User with this email already exists', 409);
      }
    }

    // Update user
    const updateData: any = {
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(email && { email }),
      ...(type && { type }),
      ...(role && { role }),
      ...(isActive !== undefined && { isActive }),
      updatedBy: adminUser.userId,
      updatedAt: new Date()
    };

    // Hash new password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 12);
    }

    Object.assign(user, updateData);
    await user.save();

    // Populate and return (excluding password)
    await user.populate('spCompanyId', 'name type');
    await user.populate('createdBy', 'firstName lastName email');
    await user.populate('updatedBy', 'firstName lastName email');

    const userResponse = user.toObject();
    delete userResponse.password;

    return successResponse(userResponse);

  } catch (error: any) {
    console.error('Update company user error:', error);
    return errorResponse(error.message || 'Failed to update company user', 500);
  }
}

// DELETE /api/admin/companies/:companyId/users/:userId - Delete company user
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const companyId = pathParts[pathParts.length - 3];
    const userId = pathParts[pathParts.length - 1];

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    const user = await SpUser.findOne({
      _id: userId,
      spCompanyId: companyId
    });

    if (!user) {
      return errorResponse('User not found', 404);
    }

    await SpUser.findByIdAndDelete(userId);

    return successResponse({ message: 'Company user deleted successfully' });

  } catch (error: any) {
    console.error('Delete company user error:', error);
    return errorResponse(error.message || 'Failed to delete company user', 500);
  }
}
