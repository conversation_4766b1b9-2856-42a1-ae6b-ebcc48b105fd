import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== GET Company By ID - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    console.log('Step 1: Finding company...');
    const company = await SpCompany.findById(companyId);
    
    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }

    console.log('Company found:', { _id: company._id, name: company.name, type: company.type });
    console.log('=== GET Company By ID - Success ===');
    
    return successResponse(company);

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

// PUT /api/admin/companies/:id - Update company (matches Express backend exactly)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== PUT Update Company - Starting ===');
    console.log('updating company');
    
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);
    console.log('Admin user:', { userId: user.userId, type: user.type });

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }
 
    const formData = await request.formData();
    console.log('Request body fields:', Array.from(formData.keys()));
    
    // Extract basic company data (matching Express backend)
    const { name, registrationNumber, address, contactPerson, email, phone } = {
      name: formData.get('name') as string,
      registrationNumber: formData.get('registrationNumber') as string,
      address: formData.get('address') as string,
      contactPerson: formData.get('contactPerson') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
    };

    console.log('Step 1: Finding existing company...');
    const company = await SpCompany.findById(companyId);
    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }

    console.log('Company found:', { _id: company._id, name: company.name });

    // Start with existing documents (matching Express backend logic)
    console.log('Step 2: Processing documents...');
    const updatedDocuments = [...company.documents];

    // Handle file uploads (matching Express backend exactly)
    const fileFields = [
      'logoUrl', 'contract', 'kraPinCertificate', 'certificateOfIncorporation',
      'tradingLicense', 'companyCR12', 'taxComplianceCertificate'
    ];

    let hasUploadedFiles = false;
    for (const fieldName of fileFields) {
      const file = formData.get(fieldName) as File;
      if (file && file.size > 0) {
        hasUploadedFiles = true;
        console.log('File:', { name: file.name, size: file.size, type: file.type });
        console.log('File name:', file.name);
        
        try {
          const buffer = Buffer.from(await file.arrayBuffer());
          console.log('File buffer length:', buffer.length);
          
          // Upload to S3 (matching Express backend path structure)
          //if logo url, upload publicly
          const acl = fieldName === 'logoUrl' ? 'public-read' : 'private';
          const fileUrl = await uploadToS3(buffer, 'company-documents', fieldName, companyId, acl);
          
          // Check if a document of this type already exists (matching Express backend)
          const existingDocIndex = updatedDocuments.findIndex(doc => doc.type === fieldName);
          
          const newDocument = {
            url: fileUrl,
            filePath: fileUrl,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            type: fieldName,
            name: file.name,
            expiresAt: null,
          };

          // Update logoUrl if fieldname is logoUrl (matching Express backend)
          if (fieldName === 'logoUrl') {
            company.logoUrl = fileUrl;
            console.log('Updated company logoUrl:', fileUrl);
          }

          if (existingDocIndex >= 0) {
            // Replace existing document of this type
            updatedDocuments[existingDocIndex] = newDocument;
            console.log('Replaced existing document of type:', fieldName);
          } else {
            // Add new document
            updatedDocuments.push(newDocument);
            console.log('Added new document of type:', fieldName);
          }

          console.log('Uploaded file:', newDocument);
          console.log('File URL:', fileUrl);
        } catch (uploadError) {
          console.error(`Failed to upload ${fieldName}:`, uploadError);
        }
      }
    }

    if (hasUploadedFiles) {
      console.log('Has uploaded files:');
    }

    console.log('Step 3: Updating company fields...');
    // Only update fields that were provided (matching Express backend logic)
    company.name = name || company.name;
    company.registrationNumber = registrationNumber || company.registrationNumber;
    company.address = address || company.address;
    company.contactPerson = contactPerson || company.contactPerson;
    company.email = email || company.email;
    company.phone = phone || company.phone;
    company.documents = updatedDocuments; // Use the merged documents array
    company.updatedBy = user.userId;

    console.log('Step 4: Saving company...');
    await company.save();
    
    console.log('=== PUT Update Company - Success ===');
    return successResponse({ message: 'Company updated successfully', company });

  } catch (error: any) {
    console.error('=== PUT Update Company - Error ===');
    console.error('Update company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to update company', 500);
  }
}

// DELETE /api/admin/companies/:id - Delete company (matches Express backend exactly)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== DELETE Company - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    console.log('Step 1: Finding company...');
    const company = await SpCompany.findById(companyId);

    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }

    console.log('Company found:', { _id: company._id, name: company.name });

    // Delete documents from S3 (matching Express backend exactly)
    if (company.documents) {
      console.log('Step 2: Deleting documents from S3...');
      console.log('Documents:', company.documents.length);
      
      for (const document of company.documents) {
        try {
          await deleteFromS3(document.filePath);
          console.log('Deleted from S3 successfully:', document.filePath);
        } catch (error) {
          console.error('Error deleting from S3:', error);
          return errorResponse('Error deleting file from S3', 500);
        }
      }
    }

    console.log('Step 3: Deleting company from database...');
    await SpCompany.deleteOne({ _id: companyId });
    
    console.log('=== DELETE Company - Success ===');
    return successResponse({ message: 'Company deleted successfully' });

  } catch (error: any) {
    console.error('=== DELETE Company - Error ===');
    console.error('Delete company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to delete company', 500);
  }
}
