import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import { getObjectFromS3 } from '@/lib/services/s3Service';

export const dynamic = 'force-dynamic';

// GET /api/admin/companies/documents/download - Download company document
export async function GET(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const documentType = searchParams.get('documentType');
    const fileName = searchParams.get('fileName');

    if (!companyId || !documentType || !fileName) {
      return errorResponse('Company ID, document type, and file name are required', 400);
    }

    // Find company and document
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    const document = company.documents.find((doc: any) => 
      doc.type === documentType && doc.fileName === fileName
    );

    if (!document) {
      return errorResponse('Document not found', 404);
    }

    try {
      // Get file from S3
      const fileData = await getObjectFromS3(document.filePath);
      
      // Create response with proper headers
      const response = new NextResponse(fileData.Body);
      
      response.headers.set('Content-Type', document.fileType || 'application/octet-stream');
      response.headers.set('Content-Disposition', `attachment; filename="${document.fileName}"`);
      response.headers.set('Content-Length', document.fileSize?.toString() || '0');
      
      return response;

    } catch (s3Error) {
      console.error('S3 download error:', s3Error);
      return errorResponse('Failed to download file from storage', 500);
    }

  } catch (error: any) {
    console.error('Download company document error:', error);
    return errorResponse(error.message || 'Failed to download document', 500);
  }
}
