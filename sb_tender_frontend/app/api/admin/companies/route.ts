import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpUser from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, getObjectFromS3 } from '@/lib/services/s3Service';

export const dynamic = 'force-dynamic';

// POST /api/admin/companies - Create company
export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const formData = await request.formData();
    
    // Extract basic company data
    const companyData = {
      name: formData.get('name') as string,
      registrationNumber: formData.get('registrationNumber') as string,
      type: formData.get('type') as string,
      address: formData.get('address') as string,
      contactPerson: formData.get('contactPerson') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      createdBy: user.userId
    };

    // Validation
    const errors: string[] = [];
    if (!companyData.name) errors.push('Company name is required');
    if (!companyData.registrationNumber) errors.push('Registration number is required');
    if (!companyData.type) errors.push('Company type is required');
    if (!companyData.email) errors.push('Email is required');
    if (!companyData.phone) errors.push('Phone is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Create company
    const company = new SpCompany(companyData);
    await company.save();

    // Handle file uploads
    const documents = [];
    const fileFields = [
      'logoUrl', 'contract', 'kraPinCertificate', 'certificateOfIncorporation',
      'tradingLicense', 'companyCR12', 'taxComplianceCertificate'
    ];

    for (const fieldName of fileFields) {
      const file = formData.get(fieldName) as File;
      if (file && file.size > 0) {
        try {
          const buffer = Buffer.from(await file.arrayBuffer());
          const fileName = `${fieldName}_${Date.now()}_${file.name}`;
          const s3Key = await uploadToS3(buffer, `company-documents/${company._id}`, fileName, company._id.toString());
          
          const document = {
            type: fieldName,
            name: file.name,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            filePath: s3Key,
            uploadedAt: new Date()
          };
          
          documents.push(document);
        } catch (uploadError) {
          console.error(`Failed to upload ${fieldName}:`, uploadError);
        }
      }
    }

    // Update company with documents
    if (documents.length > 0) {
      company.documents = documents;
      await company.save();
    }

    // Populate the created company
    await company.populate('createdBy', 'firstName lastName email');

    return successResponse(company, 201);

  } catch (error: any) {
    console.error('Create company error:', error);
    return errorResponse(error.message || 'Failed to create company', 500);
  }
}

// GET /api/admin/companies - List companies
export async function GET(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    // Build query
    let query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { registrationNumber: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { contactPerson: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (type) {
      query.type = type;
    }
    
    if (status) {
      query.status = status;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [companies, total] = await Promise.all([
      SpCompany.find(query)
        // .populate('categories', 'name') // Temporarily disabled
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpCompany.countDocuments(query)
    ]);

    return successResponse({
      companies,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List companies error:', error);
    return errorResponse(error.message || 'Failed to fetch companies', 500);
  }
}

// PUT /api/admin/companies/:id - Update company
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const companyId = url.pathname.split('/').pop();

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    const formData = await request.formData();
    
    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    // Update basic fields if provided
    const fields = ['name', 'registrationNumber', 'type', 'address', 'contactPerson', 'email', 'phone', 'status'];
    fields.forEach(field => {
      const value = formData.get(field);
      if (value) updateData[field] = value;
    });

    // Find existing company
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Handle file uploads
    const fileFields = [
      'logoUrl', 'contract', 'kraPinCertificate', 'certificateOfIncorporation',
      'tradingLicense', 'companyCR12', 'taxComplianceCertificate'
    ];

    for (const fieldName of fileFields) {
      const file = formData.get(fieldName) as File;
      if (file && file.size > 0) {
        try {
          const buffer = Buffer.from(await file.arrayBuffer());
          const fileName = `${fieldName}_${Date.now()}_${file.name}`;
          const s3Key = await uploadToS3(buffer, `company-documents/${companyId}`, fileName, companyId);
          
          const document = {
            type: fieldName,
            name: file.name,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            filePath: s3Key,
            uploadedAt: new Date()
          };
          
          // Update or add document
          const existingDocIndex = company.documents.findIndex((doc: any) => doc.type === fieldName);
          if (existingDocIndex >= 0) {
            company.documents[existingDocIndex] = document;
          } else {
            company.documents.push(document);
          }
        } catch (uploadError) {
          console.error(`Failed to upload ${fieldName}:`, uploadError);
        }
      }
    }

    // Update company
    Object.assign(company, updateData);
    await company.save();

    // Populate and return
    await company.populate('createdBy', 'firstName lastName email');
    await company.populate('updatedBy', 'firstName lastName email');

    return successResponse(company);

  } catch (error: any) {
    console.error('Update company error:', error);
    return errorResponse(error.message || 'Failed to update company', 500);
  }
}

// DELETE /api/admin/companies/:id - Delete company
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const companyId = url.pathname.split('/').pop();

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    await SpCompany.findByIdAndDelete(companyId);

    return successResponse({ message: 'Company deleted successfully' });

  } catch (error: any) {
    console.error('Delete company error:', error);
    return errorResponse(error.message || 'Failed to delete company', 500);
  }
}
