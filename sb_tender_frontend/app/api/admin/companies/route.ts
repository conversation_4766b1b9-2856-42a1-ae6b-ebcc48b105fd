import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpUser from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, getObjectFromS3 } from '@/lib/services/s3Service';

export const dynamic = 'force-dynamic';

// POST /api/admin/companies - Create company (matches Express backend exactly)
export async function POST(request: NextRequest) {
  try {
    console.log('=== POST Create Company - Starting ===');
    console.log('creating company');

    const user = await requireAdmin(request);
    await connectToDatabase();

    const formData = await request.formData();
    console.log('Request body fields:', Array.from(formData.keys()));

    // Extract basic company data (matching Express backend)
    const { name, registrationNumber, address, contactPerson, email, phone } = {
      name: formData.get('name') as string,
      registrationNumber: formData.get('registrationNumber') as string,
      address: formData.get('address') as string,
      contactPerson: formData.get('contactPerson') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
    };

    console.log('Request body:', { name, registrationNumber, address, contactPerson, email, phone });

    console.log('Step 1: Creating company...');
    // Create company (matching Express backend - type is hardcoded to 'buyer')
    const company = new SpCompany({
      name,
      registrationNumber,
      type: 'buyer', // Hardcoded like Express backend
      address,
      contactPerson,
      email,
      phone,
      createdBy: user.userId,
    });

    await company.save();
    console.log('Company created with ID:', company._id);

    console.log('Step 2: Processing file uploads...');
    // Handle file uploads (matching Express backend exactly)
    const documents = [];

    const fileFields = [
      'logoUrl', 'contract', 'kraPinCertificate', 'certificateOfIncorporation',
      'tradingLicense', 'companyCR12', 'taxComplianceCertificate'
    ];

    for (const fieldName of fileFields) {
      const file = formData.get(fieldName) as File;
      if (file && file.size > 0) {
        console.log('File:', { name: file.name, size: file.size, type: file.type });
        console.log('File name:', file.name);

        try {
          const buffer = Buffer.from(await file.arrayBuffer());
          console.log('File buffer length:', buffer.length);

          // Upload to S3 (matching Express backend path structure)
          const fileUrl = await uploadToS3(buffer, 'company-documents', fieldName, company._id);

          // Create document object (matching Express backend structure exactly)
          const document = {
            url: fileUrl,
            filePath: fileUrl,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            type: fieldName,
            name: file.name,
            expiresAt: null,
          };

          documents.push(document);
          console.log('Uploaded file:', document);
          console.log('File URL:', fileUrl);

          // Update logoUrl if fieldname is logoUrl (matching Express backend)
          if (fieldName === 'logoUrl') {
            company.logoUrl = fileUrl;
          }
        } catch (uploadError) {
          console.error(`Failed to upload ${fieldName}:`, uploadError);
        }
      }
    }

    console.log('Step 3: Updating company with documents...');
    // Update company with documents (matching Express backend)
    company.documents = documents;
    company.updatedBy = user.userId;
    await company.save();

    console.log('=== POST Create Company - Success ===');
    return successResponse({ message: 'Company created successfully', company }, 201);

  } catch (error: any) {
    console.error('=== POST Create Company - Error ===');
    console.error('Create company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse('Internal server error', 500);
  }
}

// GET /api/admin/companies - List companies (matches Express backend exactly)
export async function GET(request: NextRequest) {
  try {
    console.log('=== GET Companies List - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    console.log('Request query:', { type });

    // Build filter (matching Express backend exactly)
    const filter: any = {};

    if (type === 'buyer' || type === 'supplier') {
      filter.type = type;
    }
    // Note: Express backend has commented validation for invalid type

    console.log('Step 1: Finding companies with filter:', filter);
    const companies = await SpCompany.find(filter);

    console.log('Companies found:', companies.length);
    console.log('=== GET Companies List - Success ===');

    return successResponse(companies);

  } catch (error: any) {
    console.error('=== GET Companies List - Error ===');
    console.error('List companies error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse('Internal server error', 500);
  }
}

// Note: PUT and DELETE operations for individual companies are handled in [companyId]/route.ts
