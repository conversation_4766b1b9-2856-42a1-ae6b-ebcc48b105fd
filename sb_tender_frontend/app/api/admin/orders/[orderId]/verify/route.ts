import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/orders/:orderId/verify - Verify payment
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { orderId } = await params;

    if (!orderId) {
      return errorResponse('Order ID is required', 400);
    }

    // Find order
    const order = await SpOrder.findById(orderId)
      .populate('spCompanyId', 'name type email phone');

    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Get all payments for this order
    const payments = await SpPayment.find({ spOrderId: orderId })
      .sort({ createdAt: -1 });

    // Calculate payment totals
    const totalPaid = payments
      .filter(p => p.status === 'success')
      .reduce((sum, p) => {
        const amount = typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount;
        return sum + amount;
      }, 0);

    const orderAmount = typeof order.totalAmount === 'object' 
      ? parseFloat(order.totalAmount.$numberDecimal) 
      : order.totalAmount;

    // Determine verification status
    let verificationStatus = 'unpaid';
    let verificationMessage = 'No payments found';

    if (totalPaid >= orderAmount) {
      verificationStatus = 'fully_paid';
      verificationMessage = 'Order is fully paid';
      
      // Update order status if not already paid
      if (order.status !== 'paid') {
        order.status = 'paid';
        order.updatedBy = user.userId;
        order.updatedAt = new Date();
        await order.save();
      }
    } else if (totalPaid > 0) {
      verificationStatus = 'partially_paid';
      verificationMessage = `Partially paid: ${totalPaid} of ${orderAmount}`;
    }

    const verificationResult = {
      orderId: order._id,
      orderAmount,
      totalPaid,
      remainingAmount: Math.max(0, orderAmount - totalPaid),
      status: verificationStatus,
      message: verificationMessage,
      payments: payments.map(p => ({
        _id: p._id,
        amount: typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount,
        status: p.status,
        method: p.method,
        reference: p.reference,
        createdAt: p.createdAt
      })),
      verifiedAt: new Date(),
      verifiedBy: user.userId
    };

    return successResponse(verificationResult);

  } catch (error: any) {
    console.error('Verify payment error:', error);
    return errorResponse(error.message || 'Failed to verify payment', 500);
  }
}
