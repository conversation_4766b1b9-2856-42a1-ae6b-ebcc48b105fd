import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/orders/:orderId - Get order details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { orderId } = await params;

    if (!orderId) {
      return errorResponse('Order ID is required', 400);
    }

    // Find order with full details
    const order = await SpOrder.findById(orderId)
      .populate('spCompanyId', 'name type email phone address')
      .populate('orderItems.spJobCategoryId', 'name price type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Get related payments
    const payments = await SpPayment.find({ spOrderId: orderId })
      .populate('createdBy', 'firstName lastName email')
      .sort({ createdAt: -1 });

    // Calculate order statistics
    const orderStats = {
      totalAmount: order.totalAmount,
      paidAmount: payments
        .filter(p => p.status === 'success')
        .reduce((sum, p) => sum + (typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount), 0),
      pendingAmount: payments
        .filter(p => p.status === 'pending')
        .reduce((sum, p) => sum + (typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount), 0),
      totalPayments: payments.length,
      successfulPayments: payments.filter(p => p.status === 'success').length,
      failedPayments: payments.filter(p => p.status === 'failed').length
    };

    return successResponse({
      order,
      payments,
      statistics: orderStats
    });

  } catch (error: any) {
    console.error('Get order details error:', error);
    return errorResponse(error.message || 'Failed to fetch order details', 500);
  }
}

// PUT /api/admin/orders/:orderId - Update order
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { orderId } = await params;
    const body = await request.json();

    if (!orderId) {
      return errorResponse('Order ID is required', 400);
    }

    const { status, notes } = body;

    // Validation
    const validStatuses = ['checkout', 'pending', 'paid', 'cancelled', 'suspend'];
    if (status && !validStatuses.includes(status)) {
      return errorResponse(`Invalid status. Must be one of: ${validStatuses.join(', ')}`, 400);
    }

    // Find the order
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Update order fields
    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    if (status) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;

    // Update the order
    Object.assign(order, updateData);
    await order.save();

    // If status is being updated to 'paid', we might want to create applications
    // This logic should match the backend payment controller
    if (status === 'paid' && order.status !== 'paid') {
      // Here you could add logic to create applications from the order
      // Similar to what's done in the backend payment controller
      console.log(`Order ${orderId} status updated to paid by admin`);
    }

    // Populate the updated order
    await order.populate('spCompanyId', 'name type email phone');
    await order.populate('orderItems.spJobCategoryId', 'name price');
    await order.populate('createdBy', 'firstName lastName email');
    await order.populate('updatedBy', 'firstName lastName email');

    return successResponse({
      order,
      message: 'Order updated successfully'
    });

  } catch (error: any) {
    console.error('Update order error:', error);
    return errorResponse(error.message || 'Failed to update order', 500);
  }
}
