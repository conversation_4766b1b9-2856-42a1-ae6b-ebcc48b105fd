import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/orders/:orderId - Get order details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { orderId } = await params;

    if (!orderId) {
      return errorResponse('Order ID is required', 400);
    }

    // Find order with full details
    const order = await SpOrder.findById(orderId)
      .populate('spCompanyId', 'name type email phone address')
      .populate('orderItems.spJobCategoryId', 'name price type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Get related payments
    const payments = await SpPayment.find({ spOrderId: orderId })
      .populate('createdBy', 'firstName lastName email')
      .sort({ createdAt: -1 });

    // Calculate order statistics
    const orderStats = {
      totalAmount: order.totalAmount,
      paidAmount: payments
        .filter(p => p.status === 'success')
        .reduce((sum, p) => sum + (typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount), 0),
      pendingAmount: payments
        .filter(p => p.status === 'pending')
        .reduce((sum, p) => sum + (typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount), 0),
      totalPayments: payments.length,
      successfulPayments: payments.filter(p => p.status === 'success').length,
      failedPayments: payments.filter(p => p.status === 'failed').length
    };

    return successResponse({
      order,
      payments,
      statistics: orderStats
    });

  } catch (error: any) {
    console.error('Get order details error:', error);
    return errorResponse(error.message || 'Failed to fetch order details', 500);
  }
}
