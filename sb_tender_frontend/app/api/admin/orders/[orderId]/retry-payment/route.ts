import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// POST /api/admin/orders/:orderId/retry-payment - Retry payment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { orderId } = await params;
    const body = await request.json();

    if (!orderId) {
      return errorResponse('Order ID is required', 400);
    }

    const {
      paymentMethod = 'manual',
      amount,
      reference,
      notes
    } = body;

    // Find order
    const order = await SpOrder.findById(orderId)
      .populate('spCompanyId', 'name type email phone');

    if (!order) {
      return errorResponse('Order not found', 404);
    }

    // Validate amount
    const orderAmount = typeof order.totalAmount === 'object' 
      ? parseFloat(order.totalAmount.$numberDecimal) 
      : order.totalAmount;

    const paymentAmount = amount || orderAmount;

    if (paymentAmount <= 0) {
      return errorResponse('Payment amount must be greater than 0', 400);
    }

    // Check if order is already fully paid
    const existingPayments = await SpPayment.find({
      spOrderId: orderId,
      status: 'success'
    });

    const totalPaid = existingPayments.reduce((sum, p) => {
      const amt = typeof p.amount === 'object' ? parseFloat(p.amount.$numberDecimal) : p.amount;
      return sum + amt;
    }, 0);

    if (totalPaid >= orderAmount) {
      return errorResponse('Order is already fully paid', 400);
    }

    // Create new payment record
    const payment = new SpPayment({
      spOrderId: orderId,
      amount: paymentAmount,
      channel: paymentMethod,
      reference: reference || `RETRY_${Date.now()}`,
      status: paymentMethod === 'manual' ? 'success' : 'pending',
      description: notes || 'Admin retry payment',
      createdBy: user.userId
    });

    await payment.save();

    // Update order status if payment is successful
    if (payment.status === 'success') {
      const newTotalPaid = totalPaid + paymentAmount;

      if (newTotalPaid >= orderAmount) {
        order.status = 'paid';
      } else {
        order.status = 'pending';
      }

      order.updatedBy = user.userId;
      order.updatedAt = new Date();
      await order.save();
    }

    // Populate payment details
    await payment.populate('spOrderId', 'totalAmount status spCompanyId');
    await payment.populate('createdBy', 'firstName lastName email');

    // Manually populate company information
    if (payment.spOrderId && payment.spOrderId.spCompanyId) {
      const company = await SpCompany.findById(payment.spOrderId.spCompanyId).select('name type');
      if (company) {
        payment.spOrderId.spCompanyId = company;
      }
    }

    return successResponse({
      payment,
      order: {
        _id: order._id,
        status: order.status,
        totalAmount: orderAmount,
        totalPaid: totalPaid + paymentAmount
      },
      message: `Payment ${payment.status === 'successful' ? 'processed successfully' : 'initiated'}`
    }, 201);

  } catch (error: any) {
    console.error('Retry payment error:', error);
    return errorResponse(error.message || 'Failed to retry payment', 500);
  }
}
