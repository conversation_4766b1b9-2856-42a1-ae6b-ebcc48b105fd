import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const companyId = searchParams.get('companyId');

    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (companyId) query.spCompanyId = companyId;
    
    if (search) {
      // Search will be enhanced after population
      query.$or = [
        { status: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [orders, total] = await Promise.all([
      SpOrder.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('orderItems.spJobCategoryId', 'name price')
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpOrder.countDocuments(query)
    ]);

    // Calculate summary statistics
    const summaryStats = await SpOrder.aggregate([
      {
        $addFields: {
          totalAmountValue: {
            $cond: {
              if: { $type: '$totalAmount' },
              then: { $toDouble: '$totalAmount' },
              else: 0
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmountValue' },
          pendingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          paidOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
          }
        }
      }
    ]);

    return successResponse({
      orders,
      summary: summaryStats[0] || {
        totalOrders: 0,
        totalRevenue: 0,
        pendingOrders: 0,
        paidOrders: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin orders fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('id');
    
    if (!orderId) {
      return errorResponse('Order ID is required');
    }

    const body = await request.json();
    await connectToDatabase();

    const {
      status,
      adminNotes,
      paymentMethod,
      paymentReference
    } = body;

    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
    };

    if (status) updateData.status = status;
    if (adminNotes) updateData.adminNotes = adminNotes;
    if (paymentMethod) updateData.paymentMethod = paymentMethod;
    if (paymentReference) updateData.paymentReference = paymentReference;

    // Update order
    const order = await SpOrder.findByIdAndUpdate(
      orderId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('spCompanyId', 'name type email phone')
      .populate('orderItems.spJobCategoryId', 'name price')
      .populate('createdBy', 'firstName lastName email');

    if (!order) {
      return errorResponse('Order not found', 404);
    }

    return successResponse(order, 'Order updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin order update error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('id');

    if (!orderId) {
      return errorResponse('Order ID is required');
    }

    await connectToDatabase();

    const order = await SpOrder.findByIdAndDelete(orderId);
    if (!order) {
      return errorResponse('Order not found', 404);
    }

    return successResponse(null, 'Order deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin order deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}
