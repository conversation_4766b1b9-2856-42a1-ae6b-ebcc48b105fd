import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const companyId = searchParams.get('companyId');

    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (companyId) query.spCompanyId = companyId;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [jobs, total] = await Promise.all([
      SpJob.find(query)
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJob.countDocuments(query)
    ]);

    return successResponse({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin jobs fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const body = await request.json();
    
    await connectToDatabase();

    const {
      title,
      description,
      spCompanyId,
      location,
      contract,
      starts,
      ends,
      status = 'active'
    } = body;

    // Validation
    const errors: string[] = [];
    if (!title) errors.push('Title is required');
    if (!description) errors.push('Description is required');
    if (!spCompanyId) errors.push('Company ID is required');
    if (!location) errors.push('Location is required');
    if (!contract) errors.push('Contract type is required');
    if (!starts) errors.push('Start date is required');
    if (!ends) errors.push('End date is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`);
    }

    // Verify company exists
    const company = await SpCompany.findById(spCompanyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Create job
    const job = new SpJob({
      title,
      description,
      spCompanyId,
      location,
      contract,
      starts: new Date(starts),
      ends: new Date(ends),
      status,
      createdBy: user.userId,
    });

    await job.save();

    // Populate the created job
    await job.populate('spCompanyId', 'name type');
    await job.populate('createdBy', 'firstName lastName email');

    return successResponse(job, 'Job created successfully', 201);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin job creation error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('id');
    
    if (!jobId) {
      return errorResponse('Job ID is required');
    }

    const body = await request.json();
    await connectToDatabase();

    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
    };

    const allowedFields = ['title', 'description', 'location', 'contract', 'starts', 'ends', 'status'];
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        if (field === 'starts' || field === 'ends') {
          updateData[field] = new Date(body[field]);
        } else {
          updateData[field] = body[field];
        }
      }
    }

    // Update job
    const job = await SpJob.findByIdAndUpdate(
      jobId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('spCompanyId', 'name type')
      .populate('createdBy', 'firstName lastName email');

    if (!job) {
      return errorResponse('Job not found', 404);
    }

    return successResponse(job, 'Job updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin job update error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('id');

    if (!jobId) {
      return errorResponse('Job ID is required');
    }

    await connectToDatabase();

    const job = await SpJob.findByIdAndDelete(jobId);
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    return successResponse(null, 'Job deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin job deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}
