import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId - Get job by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    // Find job
    const job = await SpJob.findById(jobId)
      .populate('spCompanyId', 'name type email phone address')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!job) {
      return errorResponse('Job not found', 404);
    }

    return successResponse(job);

  } catch (error: any) {
    console.error('Get job error:', error);
    return errorResponse(error.message || 'Failed to fetch job', 500);
  }
}

// PUT /api/admin/jobs/:jobId - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;
    const body = await request.json();

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    const {
      title,
      description,
      location,
      contract,
      starts,
      ends,
      status,
      categories
    } = body;

    // Find existing job
    const job = await SpJob.findById(jobId);
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Validate dates if provided
    if (starts && ends) {
      const startDate = new Date(starts);
      const endDate = new Date(ends);
      
      if (startDate >= endDate) {
        return errorResponse('End date must be after start date', 400);
      }
    }

    // Update job
    const updateData = {
      ...(title && { title }),
      ...(description && { description }),
      ...(location && { location }),
      ...(contract && { contract }),
      ...(starts && { starts: new Date(starts) }),
      ...(ends && { ends: new Date(ends) }),
      ...(status && { status }),
      ...(categories && { categories }),
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    Object.assign(job, updateData);
    await job.save();

    // Populate and return
    await job.populate('spCompanyId', 'name type email phone address');
    await job.populate('createdBy', 'firstName lastName email');
    await job.populate('updatedBy', 'firstName lastName email');

    return successResponse(job);

  } catch (error: any) {
    console.error('Update job error:', error);
    return errorResponse(error.message || 'Failed to update job', 500);
  }
}
