import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

import SpJobCategory from '@/lib/models/sp_job_category';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId - Get job by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    // Find job
    const job = await SpJob.findById(jobId)
      .populate('spCompanyId', 'name type email phone address')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!job) {
      return errorResponse('Job not found', 404);
    }

    return successResponse(job);

  } catch (error: any) {
    console.error('Get job error:', error);
    return errorResponse(error.message || 'Failed to fetch job', 500);
  }
}

// PUT /api/admin/jobs/:jobId - Update job
export async function PUT(request: NextRequest) {
  try {
    console.log('=== PUT Update Buyer Job - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const jobId = pathParts[pathParts.length - 1];
    const body = await request.json();

    console.log('Body server', body);
    console.log('Job ID', jobId);

    const { title, description, contract, starts, ends, location, categoryPrice, passMark } = body;
    console.log('Pass Mark received:', passMark);
    console.log('Pass Mark type:', typeof passMark);

    // Ensure passMark is properly handled
    const processedPassMark = passMark !== undefined && passMark !== null ? Number(passMark) : 0;
    console.log('Processed Pass Mark:', processedPassMark);

    const updateData = {
      title,
      description,
      contract,
      starts,
      ends,
      location,
      categoryPrice,
      passmark: processedPassMark  // <- Use lowercase to match database field
    };
    console.log('Update data being sent to MongoDB:', updateData);

    console.log('Step 1: Updating job...');
    const job = await SpJob.findByIdAndUpdate(
      jobId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!job) {
      console.log('Job not found');
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    console.log('Job updated:', job);
    console.log('Job passMark after update:', (job as any).passmark);

    // If passMark is still not set, try a separate update operation
    if ((job as any).passmark !== processedPassMark) {
      console.log('PassMark not updated correctly, trying separate update...');
      await SpJob.updateOne(
        { _id: jobId },
        { $set: { passmark: processedPassMark } }  // <- Use lowercase field name
      );
      console.log('Separate passMark update completed');
    }

    // Double-check by querying the job directly from database
    const verifyJob = await SpJob.findById(jobId).lean();
    console.log('Verification query - Job passmark:', (verifyJob as any)?.passmark);
    console.log('Verification query - Full job object:', JSON.stringify(verifyJob, null, 2));

    console.log('Step 2: Updating job categories...');
    //update all categories with new job information
    let updatedCategories = await updateJobCategories(job, user.userId);

    if (updatedCategories) {
      console.log('Job categories updated successfully');
    } else {
      console.log('Failed to update job categories');
    }

    console.log('=== PUT Update Buyer Job - Success ===');
    return NextResponse.json(job);

  } catch (error: any) {
    console.error('=== PUT Update Buyer Job - Error ===');
    console.error('Error', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

const updateJobCategories = async (job: any, userId: string) => {
  const jobId = job._id;
  console.log('Job', job);
  console.log('User', userId);

  try {
    // Add await here to get the actual documents
    const jobCategories = await SpJobCategory.find({ spJobId: jobId }).exec();
    console.log('Found job categories', jobCategories.length);

    // Use for...of for array iteration
    for (const category of jobCategories) {
      // Create a new object with the updated values
      const updates = {
        title: category.title,
        description: category.description, //
        status: job.status,
        price: job.categoryPrice,
        passMark: job.passMark,
        starts: job.starts,
        ends: job.ends,
        location: job.location,
        spCompanyId: job.spCompanyId,
        type: job.type,
        updatedBy: userId
      };

      await SpJobCategory.findByIdAndUpdate(
        category._id,
        updates,
        { new: true }
      );
    }

    return true;
  } catch (error) {
    console.error('Could not update job categories', error);
    return false;
  }
};
