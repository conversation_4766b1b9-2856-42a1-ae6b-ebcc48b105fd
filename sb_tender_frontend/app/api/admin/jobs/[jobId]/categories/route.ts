import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId/categories - List job categories
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const isActive = searchParams.get('isActive');

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    // Verify job exists
    const job = await SpJob.findById(jobId);
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Build query
    let query: any = { spJobId: jobId };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpJobCategory.find(query)
        .populate('spJobId', 'title description')
        .populate('spCategoryId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJobCategory.countDocuments(query)
    ]);

    return successResponse({
      categories,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List job categories error:', error);
    return errorResponse(error.message || 'Failed to fetch job categories', 500);
  }
}
