import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId/categories/:categoryId/applications - Job category applications with stats
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string; categoryId: string }> }
) {
  try {
    console.log('=== GET Job Category Applications - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId, categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    console.log('Job ID:', jobId);
    console.log('Category ID:', categoryId);

    if (!jobId || !categoryId) {
      return errorResponse('Job ID and Category ID are required', 400);
    }

    // Verify job exists
    const job = await SpJob.findById(jobId);
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Verify job category exists and belongs to this job
    const jobCategory = await SpJobCategory.findOne({
      _id: categoryId,
      spJobId: jobId
    });

    if (!jobCategory) {
      return errorResponse('Job category not found', 404);
    }

    console.log('Job found:', job.title);
    console.log('Job category found:', jobCategory.title);

    // Build query for applications in this job category
    let query: any = { 
      spJobId: jobId,
      spJobCategoryId: categoryId 
    };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    console.log('Application query:', query);

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spBuyerCompanyId', 'name type email phone')
        .populate('spSupplierCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'title price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    console.log('Found applications:', applications.length);
    console.log('Total applications:', total);

    // Calculate comprehensive statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobId: jobId, spJobCategoryId: categoryId } },
      {
        $lookup: {
          from: 'spcompanies',
          localField: 'spSupplierCompanyId',
          foreignField: '_id',
          as: 'supplierCompany'
        }
      },
      {
        $lookup: {
          from: 'spcompanies',
          localField: 'spBuyerCompanyId',
          foreignField: '_id',
          as: 'buyerCompany'
        }
      },
      {
        $unwind: { path: '$supplierCompany', preserveNullAndEmptyArrays: true }
      },
      {
        $unwind: { path: '$buyerCompany', preserveNullAndEmptyArrays: true }
      },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          submittedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'submitted'] }, 1, 0] }
          },
          draftApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          underReviewApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'under_review'] }, 1, 0] }
          },
          approvedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' },
          averageTotalScore: { $avg: '$totalScore' },
          highestScore: { $max: '$totalScore' },
          lowestScore: { $min: '$totalScore' },
          uniqueSuppliers: { $addToSet: '$spSupplierCompanyId' }
        }
      },
      {
        $addFields: {
          uniqueSuppliers: { $size: '$uniqueSuppliers' }
        }
      }
    ]);

    console.log('Statistics calculated:', stats[0]);

    // Get top performers
    const topPerformers = await SpApplication.find({
      spJobId: jobId,
      spJobCategoryId: categoryId
    })
      .populate('spSupplierCompanyId', 'name type')
      .sort({ totalScore: -1 })
      .limit(5)
      .select('spSupplierCompanyId totalScore systemScore complianceScore status');

    console.log('Top performers found:', topPerformers.length);

    const responseData = {
      applications,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        status: job.status,
        location: job.location,
        starts: job.starts,
        ends: job.ends
      },
      category: {
        _id: jobCategory._id,
        title: jobCategory.title,
        description: jobCategory.description,
        type: jobCategory.type,
        status: jobCategory.status,
        maxScore: jobCategory.maxScore,
        passMark: jobCategory.passMark
      },
      statistics: stats[0] || {
        totalApplications: 0,
        submittedApplications: 0,
        draftApplications: 0,
        underReviewApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        highestScore: 0,
        lowestScore: 0,
        uniqueSuppliers: 0
      },
      topPerformers: topPerformers.map(app => ({
        supplierId: app.spSupplierCompanyId._id,
        supplierName: app.spSupplierCompanyId.name,
        totalScore: app.totalScore,
        systemScore: app.systemScore,
        complianceScore: app.complianceScore,
        status: app.status
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

    console.log('=== GET Job Category Applications - Success ===');
    return successResponse(responseData);

  } catch (error: any) {
    console.error('=== GET Job Category Applications - Error ===');
    console.error('Job category applications error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch job category applications', 500);
  }
}
