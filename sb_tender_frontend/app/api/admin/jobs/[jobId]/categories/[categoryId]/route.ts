import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// PUT /api/admin/jobs/:jobId/categories/:categoryId - Update job category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string; categoryId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { jobId, categoryId } = await params;
    const body = await request.json();

    if (!jobId || !categoryId) {
      return errorResponse('Job ID and Category ID are required', 400);
    }

    const {
      title,
      description,
      price,
      isActive,
      maxScore,
      requirements,
      fields
    } = body;

    // Find existing job category
    const jobCategory = await SpJobCategory.findOne({
      _id: categoryId,
      spJobId: jobId
    });
    
    if (!jobCategory) {
      return errorResponse('Job category not found', 404);
    }

    // Calculate total max score from fields if fields are updated
    let calculatedMaxScore = jobCategory.maxScore;
    if (fields && Array.isArray(fields)) {
      calculatedMaxScore = fields.reduce((total: number, field: any) => {
        return total + (field.maxScore || 0);
      }, 0);
    }

    // Update job category
    const updateData = {
      ...(title && { title }),
      ...(description && { description }),
      ...(price && { price }),
      ...(isActive !== undefined && { isActive }),
      maxScore: maxScore || calculatedMaxScore,
      ...(requirements && { requirements }),
      ...(fields && { fields }),
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    Object.assign(jobCategory, updateData);
    await jobCategory.save();

    // Populate and return
    await jobCategory.populate('spJobId', 'title description');
    await jobCategory.populate('spCategoryId', 'name type');
    await jobCategory.populate('createdBy', 'firstName lastName email');
    await jobCategory.populate('updatedBy', 'firstName lastName email');

    return successResponse(jobCategory);

  } catch (error: any) {
    console.error('Update job category error:', error);
    return errorResponse(error.message || 'Failed to update job category', 500);
  }
}
