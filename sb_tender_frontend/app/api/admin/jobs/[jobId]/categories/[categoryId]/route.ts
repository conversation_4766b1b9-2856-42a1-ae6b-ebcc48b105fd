import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireAdmin } from '@/lib/middleware/auth';

export const dynamic = 'force-dynamic';

// PUT /api/admin/jobs/:jobId/categories/:categoryId - Update job category (matches Express backend exactly)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string; categoryId: string }> }
) {
  try {
    console.log('=== PUT Update Job Category - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { jobId, categoryId } = await params; // Extract category ID and job ID
    const body = await request.json();
    console.log('Job ID', jobId);
    console.log('Category ID', categoryId);

    // Get the actual updates - they're nested inside jobCategory or direct body
    const updateData = body.jobCategory || body;
    console.log('Job Category Update data structure:', Object.keys(updateData));

    console.log('Step 1: Finding job category...');
    const jobCategory = await SpJobCategory.findOne({ _id: categoryId, spJobId: jobId });

    if (!jobCategory) {
      console.error('Job Category not found for job category ID:', categoryId);
      return NextResponse.json(
        { message: 'Job Category not found' },
        { status: 404 }
      );
    }

    console.log('Job category found:', jobCategory.title);

    console.log('Step 2: Processing updates...');
    // Remove restricted fields from updates
    const { _id, spJobId, spCompanyId, createdAt, createdBy, __v, ...allowedUpdates } = updateData;
    console.log('Fields being updated:', Object.keys(allowedUpdates));

    // Handle fields array update
    if (allowedUpdates.fields) {
      console.log('Fields array length:', allowedUpdates.fields.length);
      jobCategory.fields = allowedUpdates.fields;
    }

    // Update other properties
    for (const [key, value] of Object.entries(allowedUpdates)) {
      if (key !== 'fields') {
        jobCategory[key] = value;
      }
    }

    // Set updatedBy
    jobCategory.updatedBy = user.userId;

    console.log('Step 3: Saving job category...');
    console.log('Job category before save - Fields count:', jobCategory.fields?.length);
    await jobCategory.save();
    console.log('Job category saved');

    console.log('=== PUT Update Job Category - Success ===');
    return NextResponse.json(jobCategory);

  } catch (error: any) {
    console.error('=== PUT Update Job Category - Error ===');
    console.error('Job category Update Error:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { message: 'Server Error', error: error.message },
      { status: 500 }
    );
  }
}
