import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import * as XLSX from 'xlsx';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId/applications/download/excel - Job report
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const categoryId = searchParams.get('categoryId');
    const includeFields = searchParams.get('includeFields') === 'true';

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    // Verify job exists
    const job = await SpJob.findById(jobId)
      .populate('spCompanyId', 'name type');
    
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Build query for applications
    let query: any = { spJobId: jobId };
    if (status) {
      query.status = status;
    }
    if (categoryId) {
      query.spJobCategoryId = categoryId;
    }

    // Find applications
    const applications = await SpApplication.find(query)
      .populate('spCompanyId', 'name type email phone address')
      .populate('spJobId', 'title description location')
      .populate('spJobCategoryId', 'name price type')
      .sort({ totalScore: -1 });

    if (applications.length === 0) {
      return errorResponse('No applications found for this job', 404);
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Job summary sheet
    const jobSummary = {
      'Job Title': job.title,
      'Job Description': job.description,
      'Location': job.location,
      'Contract Type': job.contract,
      'Start Date': job.starts ? new Date(job.starts).toLocaleDateString() : '',
      'End Date': job.ends ? new Date(job.ends).toLocaleDateString() : '',
      'Status': job.status,
      'Buyer Company': job.spCompanyId?.name || 'Unknown',
      'Total Applications': applications.length,
      'Report Generated': new Date().toLocaleString()
    };

    const jobSummaryData = Object.entries(jobSummary).map(([key, value]) => ({
      'Property': key,
      'Value': value
    }));

    const jobSummarySheet = XLSX.utils.json_to_sheet(jobSummaryData);
    XLSX.utils.book_append_sheet(workbook, jobSummarySheet, 'Job Summary');

    // Applications summary sheet
    const applicationsData = applications.map((app, index) => ({
      'Rank': index + 1,
      'Company Name': app.spCompanyId?.name || 'Unknown',
      'Company Type': app.spCompanyId?.type || 'Unknown',
      'Email': app.spCompanyId?.email || '',
      'Phone': app.spCompanyId?.phone || '',
      'Category': app.spJobCategoryId?.name || 'Unknown',
      'Category Price': app.spJobCategoryId?.price || 0,
      'Status': app.status,
      'System Score': app.systemScore || 0,
      'Compliance Score': app.complianceScore || 0,
      'Total Score': app.totalScore || 0,
      'Submitted Date': app.createdAt ? new Date(app.createdAt).toLocaleDateString() : '',
      'Last Updated': app.updatedAt ? new Date(app.updatedAt).toLocaleDateString() : '',
      'Admin Notes': app.adminNotes || ''
    }));

    const applicationsSheet = XLSX.utils.json_to_sheet(applicationsData);
    XLSX.utils.book_append_sheet(workbook, applicationsSheet, 'Applications');

    // Category breakdown sheet
    const categoryStats = applications.reduce((acc: any, app) => {
      const categoryName = app.spJobCategoryId?.name || 'Unknown';
      if (!acc[categoryName]) {
        acc[categoryName] = {
          'Category Name': categoryName,
          'Category Price': app.spJobCategoryId?.price || 0,
          'Applications Count': 0,
          'Average Score': 0,
          'Highest Score': 0,
          'Lowest Score': Infinity,
          'Pending': 0,
          'Approved': 0,
          'Rejected': 0
        };
      }
      
      acc[categoryName]['Applications Count']++;
      acc[categoryName]['Average Score'] += app.totalScore || 0;
      acc[categoryName]['Highest Score'] = Math.max(acc[categoryName]['Highest Score'], app.totalScore || 0);
      acc[categoryName]['Lowest Score'] = Math.min(acc[categoryName]['Lowest Score'], app.totalScore || 0);
      acc[categoryName][app.status === 'pending' ? 'Pending' : app.status === 'approved' ? 'Approved' : 'Rejected']++;
      
      return acc;
    }, {});

    const categoryData = Object.values(categoryStats).map((category: any) => ({
      ...category,
      'Average Score': Math.round((category['Average Score'] / category['Applications Count']) * 100) / 100,
      'Lowest Score': category['Lowest Score'] === Infinity ? 0 : category['Lowest Score']
    }));

    const categorySheet = XLSX.utils.json_to_sheet(categoryData);
    XLSX.utils.book_append_sheet(workbook, categorySheet, 'Category Breakdown');

    // Statistics sheet
    const stats = {
      'Total Applications': applications.length,
      'Unique Companies': new Set(applications.map(app => app.spCompanyId?._id)).size,
      'Categories Count': new Set(applications.map(app => app.spJobCategoryId?._id)).size,
      'Pending Applications': applications.filter(app => app.status === 'pending').length,
      'Approved Applications': applications.filter(app => app.status === 'approved').length,
      'Rejected Applications': applications.filter(app => app.status === 'rejected').length,
      'Average System Score': applications.reduce((sum, app) => sum + (app.systemScore || 0), 0) / applications.length,
      'Average Compliance Score': applications.reduce((sum, app) => sum + (app.complianceScore || 0), 0) / applications.length,
      'Average Total Score': applications.reduce((sum, app) => sum + (app.totalScore || 0), 0) / applications.length,
      'Highest Total Score': Math.max(...applications.map(app => app.totalScore || 0)),
      'Lowest Total Score': Math.min(...applications.map(app => app.totalScore || 0))
    };

    const statsData = Object.entries(stats).map(([key, value]) => ({
      'Metric': key,
      'Value': typeof value === 'number' ? Math.round(value * 100) / 100 : value
    }));

    const statsSheet = XLSX.utils.json_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Statistics');

    // Field details sheet (if requested)
    if (includeFields) {
      const fieldData: any[] = [];
      
      applications.forEach((app, appIndex) => {
        const companyName = app.spCompanyId?.name || 'Unknown';
        const categoryName = app.spJobCategoryId?.name || 'Unknown';
        
        app.fields.forEach((field: any) => {
          fieldData.push({
            'Rank': appIndex + 1,
            'Company Name': companyName,
            'Category': categoryName,
            'Field Name': field.fieldName,
            'Field Type': field.fieldType,
            'Value': field.value || '',
            'Score': field.score || 0,
            'Max Score': field.maxScore || 0,
            'Score Percentage': field.maxScore ? Math.round((field.score / field.maxScore) * 100) : 0,
            'Document Count': field.documents?.length || 0,
            'Admin Comments': field.adminComments || ''
          });
        });
      });

      const fieldSheet = XLSX.utils.json_to_sheet(fieldData);
      XLSX.utils.book_append_sheet(workbook, fieldSheet, 'Field Details');
    }

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Create filename
    const timestamp = new Date().toISOString().split('T')[0];
    const statusSuffix = status ? `_${status}` : '';
    const categorySuffix = categoryId ? `_category` : '';
    const jobTitle = job.title.replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${jobTitle}_applications_report${statusSuffix}${categorySuffix}_${timestamp}.xlsx`;

    // Return Excel file
    const response = new NextResponse(excelBuffer);
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    response.headers.set('Content-Length', excelBuffer.length.toString());
    response.headers.set('X-Application-Count', applications.length.toString());

    return response;

  } catch (error: any) {
    console.error('Job Excel report error:', error);
    return errorResponse(error.message || 'Failed to generate job Excel report', 500);
  }
}
