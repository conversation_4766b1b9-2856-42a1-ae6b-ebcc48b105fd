import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/jobs/:jobId/applications - Get all applications for a job
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    console.log('=== GET Job Applications - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    console.log('Job ID:', jobId);

    if (!jobId) {
      return errorResponse('Job ID is required', 400);
    }

    // Verify job exists
    const job = await SpJob.findById(jobId);
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    console.log('Job found:', job.title);

    // Build query for applications in this job
    let query: any = { spJobId: jobId };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    console.log('Application query:', query);

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spBuyerCompanyId', 'name type email phone')
        .populate('spSupplierCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'title price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    console.log('Found applications:', applications.length);
    console.log('Total applications:', total);

    const responseData = {
      applications,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        status: job.status,
        location: job.location,
        starts: job.starts,
        ends: job.ends
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

    console.log('=== GET Job Applications - Success ===');
    return successResponse(responseData);

  } catch (error: any) {
    console.error('=== GET Job Applications - Error ===');
    console.error('Job applications error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch job applications', 500);
  }
}
