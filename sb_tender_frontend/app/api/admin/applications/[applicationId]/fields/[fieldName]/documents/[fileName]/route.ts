import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import { getObjectFromS3 } from '@/lib/services/s3Service';

export const dynamic = 'force-dynamic';

// GET /api/admin/applications/:applicationId/fields/:fieldName/documents/:fileName - Download document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ applicationId: string; fieldName: string; fileName: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { applicationId, fieldName, fileName } = await params;

    if (!applicationId || !fieldName || !fileName) {
      return errorResponse('Application ID, field name, and file name are required', 400);
    }

    // Find application
    const application = await SpApplication.findById(applicationId)
      .populate('spCompanyId', 'name type')
      .populate('spJobId', 'title')
      .populate('spJobCategoryId', 'name');

    if (!application) {
      return errorResponse('Application not found', 404);
    }

    // Find the specific field
    const field = application.fields.find((f: any) => f.fieldName === fieldName);
    if (!field) {
      return errorResponse('Field not found in application', 404);
    }

    // Find the specific document
    const document = field.documents?.find((doc: any) => doc.fileName === fileName);
    if (!document) {
      return errorResponse('Document not found in field', 404);
    }

    try {
      // Get file from S3
      const fileData = await getObjectFromS3(document.filePath);
      
      // Create response with proper headers
      const response = new NextResponse(fileData.Body);
      
      response.headers.set('Content-Type', document.fileType || 'application/octet-stream');
      response.headers.set('Content-Disposition', `attachment; filename="${document.fileName}"`);
      response.headers.set('Content-Length', document.fileSize?.toString() || '0');
      
      // Add metadata headers
      response.headers.set('X-Application-ID', applicationId);
      response.headers.set('X-Company-Name', application.spCompanyId?.name || 'Unknown');
      response.headers.set('X-Field-Name', fieldName);
      
      return response;

    } catch (s3Error) {
      console.error('S3 download error:', s3Error);
      return errorResponse('Failed to download file from storage', 500);
    }

  } catch (error: any) {
    console.error('Download application document error:', error);
    return errorResponse(error.message || 'Failed to download document', 500);
  }
}
