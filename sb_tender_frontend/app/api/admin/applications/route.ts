import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJob from '@/lib/models/sp_job';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const jobId = searchParams.get('jobId');
    const categoryId = searchParams.get('categoryId');
    const companyId = searchParams.get('companyId');

    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (jobId) query.spJobId = jobId;
    if (categoryId) query.spCategoryId = categoryId;
    if (companyId) query.spCompanyId = companyId;
    
    if (search) {
      // Search in populated fields will be handled after population
      query.$or = [
        { status: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spJobId', 'title description location')
        .populate('spCategoryId', 'name type')
        .populate('spCompanyId', 'name type email')
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    return successResponse({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin applications fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const applicationId = searchParams.get('id');
    
    if (!applicationId) {
      return errorResponse('Application ID is required');
    }

    const body = await request.json();
    await connectToDatabase();

    const {
      status,
      complianceScore,
      adminNotes,
      reviewedBy
    } = body;

    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
    };

    if (status) updateData.status = status;
    if (complianceScore !== undefined) updateData.complianceScore = complianceScore;
    if (adminNotes) updateData.adminNotes = adminNotes;
    if (reviewedBy) updateData.reviewedBy = reviewedBy;

    // Update application
    const application = await SpApplication.findByIdAndUpdate(
      applicationId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('spJobId', 'title description location')
      .populate('spCategoryId', 'name type')
      .populate('spCompanyId', 'name type email')
      .populate('createdBy', 'firstName lastName email');

    if (!application) {
      return errorResponse('Application not found', 404);
    }

    return successResponse(application, 'Application updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin application update error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const applicationId = searchParams.get('id');

    if (!applicationId) {
      return errorResponse('Application ID is required');
    }

    await connectToDatabase();

    const application = await SpApplication.findByIdAndDelete(applicationId);
    if (!application) {
      return errorResponse('Application not found', 404);
    }

    return successResponse(null, 'Application deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin application deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}
