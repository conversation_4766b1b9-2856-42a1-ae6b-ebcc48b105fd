export const dynamic = 'force-dynamic';

import { NextResponse } from 'next/server';
import axios from 'axios';

const API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(request: Request) {
  try {
    const formData = await request.formData(); // Retrieve FormData from the request
    const body = new FormData();

    // Append all fields and files from the FormData
    formData.forEach((value, key) => {
      if (value instanceof File) {
        body.append(key, value, value.name); // Append files with their original names
      } else {
        body.append(key, value); // Append regular fields
      }
    });

    // Send the FormData to the Node.js backend
    const response = await axios.post(`${API_URL}/api/admin/companies`, body, {
      headers: {
        'Content-Type': 'multipart/form-data', // Ensure the correct content type
      },
    });

    return NextResponse.json(response.data);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.response?.data?.error || 'Error creating buyer.' },
      { status: 401 }
    );
  }
}
