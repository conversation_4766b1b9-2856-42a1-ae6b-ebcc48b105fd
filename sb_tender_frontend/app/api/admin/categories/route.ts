import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpCategory.find(query)
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpCategory.countDocuments(query)
    ]);

    return successResponse({
      categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin categories fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const body = await request.json();
    
    await connectToDatabase();

    const {
      name,
      description,
      type,
      status = 'active',
      fields = []
    } = body;

    // Validation
    const errors: string[] = [];
    if (!name) errors.push('Name is required');
    if (!description) errors.push('Description is required');
    if (!type) errors.push('Type is required');
    if (!['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'].includes(type)) {
      errors.push('Type must be one of: supplier_registration, supplier_prequalification, rfq, tender');
    }

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`);
    }

    // Check if category with same name already exists
    const existingCategory = await SpCategory.findOne({ name });
    if (existingCategory) {
      return errorResponse('Category with this name already exists', 409);
    }

    // Create category
    const category = new SpCategory({
      name,
      description,
      type,
      status,
      fields,
      createdBy: user.userId,
    });

    await category.save();

    // Populate the created category
    await category.populate('createdBy', 'firstName lastName email');

    return successResponse(category, 'Category created successfully', 201);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin category creation error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('id');
    
    if (!categoryId) {
      return errorResponse('Category ID is required');
    }

    const body = await request.json();
    await connectToDatabase();

    // Extract update data
    const updateData: any = {
      updatedBy: user.userId,
    };

    const allowedFields = ['name', 'description', 'type', 'status', 'fields'];
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    // Update category
    const category = await SpCategory.findByIdAndUpdate(
      categoryId,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email');

    if (!category) {
      return errorResponse('Category not found', 404);
    }

    return successResponse(category, 'Category updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin category update error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('id');

    if (!categoryId) {
      return errorResponse('Category ID is required');
    }

    await connectToDatabase();

    const category = await SpCategory.findByIdAndDelete(categoryId);
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    return successResponse(null, 'Category deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin category deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}
