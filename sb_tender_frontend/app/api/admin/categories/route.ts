import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategory from '@/lib/models/sp_category';
import SpCategoryTemplate from '@/lib/models/sp_category_template';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';
import defaultPrequalificationFields from '@/lib/models/default_prequalification_fields';
import defaultRegistrationFields from '@/lib/models/default_registration_fields';

export const dynamic = 'force-dynamic';

// Helper function to create category template (matches Express backend exactly)
const createCategoryTemplate = async function(category: any, userId: string) {
  console.log('Creating category template for category ID:', category._id);
  let fields = null;
  if(category.type === 'supplier_prequalification') {
    console.log('Category type is supplier_prequalification');
    fields = defaultPrequalificationFields;
  }
  if(category.type === 'supplier_registration') {
    console.log('Category type is supplier_registration');
    fields = defaultRegistrationFields;
  }
  try {
    const categoryTemplate = new SpCategoryTemplate({
      name: category.name,
      spCategoryId: category._id,
      type: category.type,
      status: category.status,
      createdBy: userId,
      fields: fields,
    });
    await categoryTemplate.save();
    console.log('Category template created:', categoryTemplate._id);
    console.log('Category template created for category:', categoryTemplate.spCategoryId);

    return categoryTemplate;
  } catch (error) {
    console.error('Error creating category:', error);
    return null;
  }
}

// GET /api/admin/categories - Get all categories (matches Express backend exactly)
export async function GET(request: NextRequest) {
  try {
    console.log('=== GET Categories - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    console.log('Step 1: Finding all categories...');
    const categories = await SpCategory.find();

    console.log('Categories found:', categories.length);
    console.log('=== GET Categories - Success ===');

    return NextResponse.json(categories);

  } catch (error: any) {
    console.error('=== GET Categories - Error ===');
    console.error('Error fetching categories:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// POST /api/admin/categories - Create category (matches Express backend exactly)
export async function POST(request: NextRequest) {
  try {
    console.log('=== POST Create Category - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const body = await request.json();
    const { name, description, type, status } = body;

    console.log('Request body:', { name, description, type, status });
    console.log('User ID:', user.userId);

    console.log('Step 1: Creating category...');
    const category = new SpCategory({
      name,
      description,
      type,
      status,
      createdBy: user.userId,
    });
    await category.save();
    console.log('Category created:', category._id);

    console.log('Step 2: Creating category template fields...');
    //create category template fields
    const categoryTemplate = await createCategoryTemplate(category, user.userId);

    if (categoryTemplate) {
      console.log('Category template created successfully');
    } else {
      console.log('Category template creation failed, but category was created');
    }

    console.log('=== POST Create Category - Success ===');
    return NextResponse.json(category, { status: 201 });

  } catch (error: any) {
    console.error('=== POST Create Category - Error ===');
    console.error('Error creating category:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// Note: PUT and DELETE operations for individual categories are handled in [id]/route.ts
