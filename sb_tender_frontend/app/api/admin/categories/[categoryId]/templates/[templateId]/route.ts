import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategoryTemplate from '@/lib/models/sp_category_template';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// PUT /api/admin/categories/:categoryId/templates/:templateId - Update category template
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string; templateId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { categoryId, templateId } = await params;

    if (!categoryId || !templateId) {
      return errorResponse('Category ID and Template ID are required', 400);
    }

    const body = await request.json();
    const {
      name,
      description,
      fields,
      isDefault,
      isActive
    } = body;

    // Find existing template
    const template = await SpCategoryTemplate.findById(templateId);
    if (!template) {
      return errorResponse('Template not found', 404);
    }

    // Check for duplicate name (excluding current template)
    if (name && name !== template.name) {
      const existingTemplate = await SpCategoryTemplate.findOne({ 
        name, 
        categoryType: template.categoryType,
        _id: { $ne: templateId } 
      });
      if (existingTemplate) {
        return errorResponse('Template with this name already exists for this category type', 409);
      }
    }

    // If setting as default, unset other defaults for this category type
    if (isDefault && !template.isDefault) {
      await SpCategoryTemplate.updateMany(
        { categoryType: template.categoryType, isDefault: true },
        { isDefault: false }
      );
    }

    // Calculate total max score from fields if fields are updated
    let maxScore = template.maxScore;
    if (fields && Array.isArray(fields)) {
      maxScore = fields.reduce((total: number, field: any) => {
        return total + (field.maxScore || 0);
      }, 0);
    }

    // Update template
    const updateData = {
      ...(name && { name }),
      ...(description && { description }),
      ...(fields && { fields, maxScore }),
      ...(isDefault !== undefined && { isDefault }),
      ...(isActive !== undefined && { isActive }),
      updatedBy: user.userId,
      updatedAt: new Date()
    };

    Object.assign(template, updateData);
    await template.save();

    // Populate and return
    await template.populate('createdBy', 'firstName lastName email');
    await template.populate('updatedBy', 'firstName lastName email');

    return successResponse(template);

  } catch (error: any) {
    console.error('Update category template error:', error);
    return errorResponse(error.message || 'Failed to update category template', 500);
  }
}
