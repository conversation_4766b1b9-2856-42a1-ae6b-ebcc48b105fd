import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategory from '@/lib/models/sp_category';
import SpCategoryTemplate from '@/lib/models/sp_category_template';
import { requireAdmin } from '@/lib/middleware/auth';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/[categoryId] - Get category by ID (matches Express backend exactly)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    console.log('=== GET Category by ID - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    console.log('Category ID:', categoryId);

    console.log('Step 1: Finding category by ID...');
    const category = await SpCategory.findById(categoryId);
    
    if (!category) {
      console.error('Category not found:', categoryId);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    console.log('Category found:', category.name);
    console.log('=== GET Category by ID - Success ===');
    
    return NextResponse.json(category);

  } catch (error: any) {
    console.error('=== GET Category by ID - Error ===');
    console.error('Error fetching category:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// PUT /api/admin/categories/[categoryId] - Update category (matches Express backend exactly)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    console.log('=== PUT Update Category - Starting ===');
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    const body = await request.json();
    
    console.log('Updating category server');
    console.log('Body server', body);
    console.log('ID', categoryId);

    const { name, description, type, status } = body;

    console.log('Step 1: Updating category...');
    const category = await SpCategory.findByIdAndUpdate(
      categoryId,
      { name, description, type, status, updatedBy: user.userId },
      { new: true }
    );

    if (!category) {
      console.error('Category not found:', categoryId);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    console.log('Category updated:', category.name);
    console.log('=== PUT Update Category - Success ===');
    
    return NextResponse.json(category);

  } catch (error: any) {
    console.error('=== PUT Update Category - Error ===');
    console.error('Error', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/categories/[categoryId] - Delete category (matches Express backend exactly)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    console.log('=== DELETE Category - Starting ===');
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    console.log('Deleting category ID:', categoryId);

    console.log('Step 1: Deleting category...');
    const category = await SpCategory.findByIdAndDelete(categoryId);
    
    if (!category) {
      console.error('Category not found:', categoryId);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    console.log('Step 2: Deleting category template...');
    const categoryTemplate = await SpCategoryTemplate.findOneAndDelete({spCategoryId: category._id});
    
    if (categoryTemplate) {
      console.log('Category template deleted:', categoryTemplate);
    } else {
      console.log('No category template found for this category');
    }

    console.log('=== DELETE Category - Success ===');
    return NextResponse.json({ message: 'Category deleted successfully' });

  } catch (error: any) {
    console.error('=== DELETE Category - Error ===');
    console.error('Error deleting category:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
