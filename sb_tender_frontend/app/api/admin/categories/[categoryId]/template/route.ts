import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/:categoryId/template - Get category template
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;

    if (!categoryId) {
      return errorResponse('Category ID is required', 400);
    }

    // Find category
    const category = await SpCategory.findById(categoryId)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Return category template (fields structure)
    const template = {
      categoryId: category._id,
      categoryName: category.name,
      categoryType: category.type,
      fields: category.fields || [],
      maxScore: category.maxScore,
      requirements: category.requirements,
      isActive: category.isActive,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      createdBy: category.createdBy,
      updatedBy: category.updatedBy
    };

    return successResponse(template);

  } catch (error: any) {
    console.error('Get category template error:', error);
    return errorResponse(error.message || 'Failed to fetch category template', 500);
  }
}
