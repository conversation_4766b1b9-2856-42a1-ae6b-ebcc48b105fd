import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import { getObjectFromS3 } from '@/lib/services/s3Service';
import JSZip from 'jszip';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/:categoryId/applications/download/section/:sectionName - Section docs
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string; sectionName: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId, sectionName } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const format = searchParams.get('format') || 'zip'; // zip or individual

    if (!categoryId || !sectionName) {
      return errorResponse('Category ID and section name are required', 400);
    }

    // Verify category exists
    const category = await SpCategory.findById(categoryId);
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Build query for applications
    let query: any = { spCategoryId: categoryId };
    if (status) {
      query.status = status;
    }

    // Find applications
    const applications = await SpApplication.find(query)
      .populate('spCompanyId', 'name type')
      .populate('spJobId', 'title')
      .sort({ totalScore: -1 });

    if (applications.length === 0) {
      return errorResponse('No applications found for this category', 404);
    }

    // Find fields that match the section name
    const sectionFields: any[] = [];
    
    applications.forEach(application => {
      const companyName = application.spCompanyId?.name || 'Unknown Company';
      
      application.fields.forEach((field: any) => {
        // Check if field belongs to the requested section
        if (field.fieldName.toLowerCase().includes(sectionName.toLowerCase()) ||
            field.section?.toLowerCase() === sectionName.toLowerCase()) {
          
          if (field.documents && field.documents.length > 0) {
            field.documents.forEach((document: any) => {
              sectionFields.push({
                applicationId: application._id,
                companyName,
                fieldName: field.fieldName,
                document,
                totalScore: application.totalScore,
                status: application.status
              });
            });
          }
        }
      });
    });

    if (sectionFields.length === 0) {
      return errorResponse(`No documents found for section: ${sectionName}`, 404);
    }

    if (format === 'zip') {
      // Create ZIP file
      const zip = new JSZip();
      let fileCount = 0;

      // Group by company
      const companiesMap = new Map();
      
      sectionFields.forEach(item => {
        const sanitizedCompanyName = item.companyName.replace(/[^a-zA-Z0-9]/g, '_');
        if (!companiesMap.has(sanitizedCompanyName)) {
          companiesMap.set(sanitizedCompanyName, []);
        }
        companiesMap.get(sanitizedCompanyName).push(item);
      });

      // Add files to ZIP
      for (const [companyName, items] of companiesMap) {
        const companyFolder = zip.folder(companyName);
        
        if (companyFolder) {
          // Add company summary
          const companySummary = {
            companyName: items[0].companyName,
            totalScore: items[0].totalScore,
            status: items[0].status,
            sectionDocuments: items.map((item: any) => ({
              fieldName: item.fieldName,
              fileName: item.document.fileName,
              fileType: item.document.fileType,
              fileSize: item.document.fileSize,
              uploadedAt: item.document.uploadedAt
            }))
          };
          
          companyFolder.file('section_summary.json', JSON.stringify(companySummary, null, 2));

          // Add documents
          for (const item of items) {
            try {
              const fileData = await getObjectFromS3(item.document.filePath);
              const buffer = Buffer.from(await fileData.Body.transformToByteArray());
              
              // Create subfolder for field if multiple fields
              const fieldFolder = companyFolder.folder(item.fieldName);
              if (fieldFolder) {
                fieldFolder.file(item.document.fileName, buffer);
                fileCount++;
              }
            } catch (s3Error) {
              console.error(`Failed to download ${item.document.fileName}:`, s3Error);
              // Add error file
              companyFolder.file(`ERROR_${item.document.fileName}.txt`, 
                `Failed to download this file: ${s3Error.message}`);
            }
          }
        }
      }

      if (fileCount === 0) {
        return errorResponse('No documents could be downloaded', 500);
      }

      // Generate ZIP buffer
      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

      // Create filename
      const timestamp = new Date().toISOString().split('T')[0];
      const statusSuffix = status ? `_${status}` : '';
      const filename = `${category.name}_${sectionName}_documents${statusSuffix}_${timestamp}.zip`;

      // Return ZIP file
      const response = new NextResponse(zipBuffer);
      response.headers.set('Content-Type', 'application/zip');
      response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
      response.headers.set('Content-Length', zipBuffer.length.toString());
      response.headers.set('X-File-Count', fileCount.toString());
      response.headers.set('X-Company-Count', companiesMap.size.toString());

      return response;

    } else {
      // Return JSON list of available documents
      const documentList = sectionFields.map(item => ({
        applicationId: item.applicationId,
        companyName: item.companyName,
        fieldName: item.fieldName,
        fileName: item.document.fileName,
        fileType: item.document.fileType,
        fileSize: item.document.fileSize,
        uploadedAt: item.document.uploadedAt,
        downloadUrl: `/api/admin/applications/${item.applicationId}/fields/${item.fieldName}/documents/${item.document.fileName}`,
        totalScore: item.totalScore,
        status: item.status
      }));

      return new NextResponse(JSON.stringify({
        success: true,
        data: {
          sectionName,
          categoryName: category.name,
          totalDocuments: documentList.length,
          documents: documentList
        }
      }), {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

  } catch (error: any) {
    console.error('Section documents download error:', error);
    return errorResponse(error.message || 'Failed to download section documents', 500);
  }
}
