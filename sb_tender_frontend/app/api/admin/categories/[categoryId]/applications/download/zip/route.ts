import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import { getObjectFromS3 } from '@/lib/services/s3Service';
import JSZip from 'jszip';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/:categoryId/applications/download/zip - ZIP download
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const fieldName = searchParams.get('fieldName');

    if (!categoryId) {
      return errorResponse('Category ID is required', 400);
    }

    // Verify category exists
    const category = await SpCategory.findById(categoryId);
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Build query for applications
    let query: any = { spCategoryId: categoryId };
    if (status) {
      query.status = status;
    }

    // Find applications
    const applications = await SpApplication.find(query)
      .populate('spCompanyId', 'name type')
      .populate('spJobId', 'title')
      .sort({ totalScore: -1 });

    if (applications.length === 0) {
      return errorResponse('No applications found for this category', 404);
    }

    // Create ZIP file
    const zip = new JSZip();
    let fileCount = 0;

    for (const application of applications) {
      const companyName = application.spCompanyId?.name || 'Unknown Company';
      const sanitizedCompanyName = companyName.replace(/[^a-zA-Z0-9]/g, '_');
      
      // Create company folder
      const companyFolder = zip.folder(sanitizedCompanyName);
      
      if (companyFolder) {
        // Add application summary
        const summary = {
          applicationId: application._id,
          companyName: companyName,
          jobTitle: application.spJobId?.title || 'Unknown Job',
          status: application.status,
          systemScore: application.systemScore,
          complianceScore: application.complianceScore,
          totalScore: application.totalScore,
          submittedAt: application.createdAt,
          fields: application.fields.map((field: any) => ({
            fieldName: field.fieldName,
            fieldType: field.fieldType,
            value: field.value,
            score: field.score,
            maxScore: field.maxScore,
            documentCount: field.documents?.length || 0
          }))
        };
        
        companyFolder.file('application_summary.json', JSON.stringify(summary, null, 2));

        // Add documents
        for (const field of application.fields) {
          if (fieldName && field.fieldName !== fieldName) continue;
          
          if (field.documents && field.documents.length > 0) {
            const fieldFolder = companyFolder.folder(field.fieldName);
            
            if (fieldFolder) {
              for (const document of field.documents) {
                try {
                  const fileData = await getObjectFromS3(document.filePath);
                  const buffer = Buffer.from(await fileData.Body.transformToByteArray());
                  fieldFolder.file(document.fileName, buffer);
                  fileCount++;
                } catch (s3Error) {
                  console.error(`Failed to download ${document.fileName}:`, s3Error);
                  // Add error file instead
                  fieldFolder.file(`ERROR_${document.fileName}.txt`, 
                    `Failed to download this file: ${s3Error.message}`);
                }
              }
            }
          }
        }
      }
    }

    if (fileCount === 0) {
      return errorResponse('No documents found to download', 404);
    }

    // Generate ZIP buffer
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    // Create filename
    const timestamp = new Date().toISOString().split('T')[0];
    const statusSuffix = status ? `_${status}` : '';
    const fieldSuffix = fieldName ? `_${fieldName}` : '';
    const filename = `${category.name}_applications${statusSuffix}${fieldSuffix}_${timestamp}.zip`;

    // Return ZIP file
    const response = new NextResponse(zipBuffer);
    response.headers.set('Content-Type', 'application/zip');
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    response.headers.set('Content-Length', zipBuffer.length.toString());
    response.headers.set('X-File-Count', fileCount.toString());
    response.headers.set('X-Application-Count', applications.length.toString());

    return response;

  } catch (error: any) {
    console.error('ZIP download error:', error);
    return errorResponse(error.message || 'Failed to create ZIP download', 500);
  }
}
