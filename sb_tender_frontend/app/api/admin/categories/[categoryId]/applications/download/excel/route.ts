import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { errorResponse } from '@/lib/utils/apiResponse';
import * as XLSX from 'xlsx';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/:categoryId/applications/download/excel - Excel report
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const includeFields = searchParams.get('includeFields') === 'true';

    if (!categoryId) {
      return errorResponse('Category ID is required', 400);
    }

    // Verify category exists
    const category = await SpCategory.findById(categoryId);
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Build query for applications
    let query: any = { spCategoryId: categoryId };
    if (status) {
      query.status = status;
    }

    // Find applications
    const applications = await SpApplication.find(query)
      .populate('spCompanyId', 'name type email phone address')
      .populate('spJobId', 'title description location')
      .populate('spJobCategoryId', 'name price')
      .sort({ totalScore: -1 });

    if (applications.length === 0) {
      return errorResponse('No applications found for this category', 404);
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Summary sheet
    const summaryData = applications.map((app, index) => ({
      'Rank': index + 1,
      'Company Name': app.spCompanyId?.name || 'Unknown',
      'Company Type': app.spCompanyId?.type || 'Unknown',
      'Email': app.spCompanyId?.email || '',
      'Phone': app.spCompanyId?.phone || '',
      'Job Title': app.spJobId?.title || 'Unknown',
      'Category': app.spJobCategoryId?.name || 'Unknown',
      'Status': app.status,
      'System Score': app.systemScore || 0,
      'Compliance Score': app.complianceScore || 0,
      'Total Score': app.totalScore || 0,
      'Submitted Date': app.createdAt ? new Date(app.createdAt).toLocaleDateString() : '',
      'Last Updated': app.updatedAt ? new Date(app.updatedAt).toLocaleDateString() : '',
      'Admin Notes': app.adminNotes || ''
    }));

    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Applications Summary');

    // Statistics sheet
    const stats = {
      'Total Applications': applications.length,
      'Pending Applications': applications.filter(app => app.status === 'pending').length,
      'Approved Applications': applications.filter(app => app.status === 'approved').length,
      'Rejected Applications': applications.filter(app => app.status === 'rejected').length,
      'Average System Score': applications.reduce((sum, app) => sum + (app.systemScore || 0), 0) / applications.length,
      'Average Compliance Score': applications.reduce((sum, app) => sum + (app.complianceScore || 0), 0) / applications.length,
      'Average Total Score': applications.reduce((sum, app) => sum + (app.totalScore || 0), 0) / applications.length,
      'Highest Score': Math.max(...applications.map(app => app.totalScore || 0)),
      'Lowest Score': Math.min(...applications.map(app => app.totalScore || 0))
    };

    const statsData = Object.entries(stats).map(([key, value]) => ({
      'Metric': key,
      'Value': typeof value === 'number' ? Math.round(value * 100) / 100 : value
    }));

    const statsSheet = XLSX.utils.json_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Statistics');

    // Field details sheet (if requested)
    if (includeFields) {
      const fieldData: any[] = [];
      
      applications.forEach((app, appIndex) => {
        const companyName = app.spCompanyId?.name || 'Unknown';
        
        app.fields.forEach((field: any) => {
          fieldData.push({
            'Rank': appIndex + 1,
            'Company Name': companyName,
            'Field Name': field.fieldName,
            'Field Type': field.fieldType,
            'Value': field.value || '',
            'Score': field.score || 0,
            'Max Score': field.maxScore || 0,
            'Score Percentage': field.maxScore ? Math.round((field.score / field.maxScore) * 100) : 0,
            'Document Count': field.documents?.length || 0,
            'Admin Comments': field.adminComments || ''
          });
        });
      });

      const fieldSheet = XLSX.utils.json_to_sheet(fieldData);
      XLSX.utils.book_append_sheet(workbook, fieldSheet, 'Field Details');
    }

    // Company breakdown sheet
    const companyStats = applications.reduce((acc: any, app) => {
      const companyName = app.spCompanyId?.name || 'Unknown';
      if (!acc[companyName]) {
        acc[companyName] = {
          'Company Name': companyName,
          'Company Type': app.spCompanyId?.type || 'Unknown',
          'Applications Count': 0,
          'Average Score': 0,
          'Best Score': 0,
          'Status Breakdown': {}
        };
      }
      
      acc[companyName]['Applications Count']++;
      acc[companyName]['Average Score'] += app.totalScore || 0;
      acc[companyName]['Best Score'] = Math.max(acc[companyName]['Best Score'], app.totalScore || 0);
      
      const status = app.status;
      acc[companyName]['Status Breakdown'][status] = (acc[companyName]['Status Breakdown'][status] || 0) + 1;
      
      return acc;
    }, {});

    const companyData = Object.values(companyStats).map((company: any) => ({
      ...company,
      'Average Score': Math.round((company['Average Score'] / company['Applications Count']) * 100) / 100,
      'Pending': company['Status Breakdown']['pending'] || 0,
      'Approved': company['Status Breakdown']['approved'] || 0,
      'Rejected': company['Status Breakdown']['rejected'] || 0
    }));

    const companySheet = XLSX.utils.json_to_sheet(companyData);
    XLSX.utils.book_append_sheet(workbook, companySheet, 'Company Breakdown');

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Create filename
    const timestamp = new Date().toISOString().split('T')[0];
    const statusSuffix = status ? `_${status}` : '';
    const filename = `${category.name}_applications_report${statusSuffix}_${timestamp}.xlsx`;

    // Return Excel file
    const response = new NextResponse(excelBuffer);
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    response.headers.set('Content-Length', excelBuffer.length.toString());
    response.headers.set('X-Application-Count', applications.length.toString());

    return response;

  } catch (error: any) {
    console.error('Excel report error:', error);
    return errorResponse(error.message || 'Failed to generate Excel report', 500);
  }
}
