import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCategory from '@/lib/models/sp_category';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/categories/:categoryId/applications - Admin category apps
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const companyType = searchParams.get('companyType');
    const sortBy = searchParams.get('sortBy') || 'totalScore';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!categoryId) {
      return errorResponse('Category ID is required', 400);
    }

    // Verify category exists
    const category = await SpCategory.findById(categoryId);
    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Build query for applications in this category
    let query: any = { spCategoryId: categoryId };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    let applicationsQuery = SpApplication.find(query)
      .populate('spCompanyId', 'name type email phone')
      .populate('spJobId', 'title description')
      .populate('spJobCategoryId', 'name price type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Filter by company type if specified
    if (companyType) {
      applicationsQuery = applicationsQuery.populate({
        path: 'spCompanyId',
        match: { type: companyType },
        select: 'name type email phone'
      });
    }

    const [applications, total] = await Promise.all([
      applicationsQuery,
      SpApplication.countDocuments(query)
    ]);

    // Filter out applications where company didn't match the type filter
    const filteredApplications = companyType 
      ? applications.filter(app => app.spCompanyId) 
      : applications;

    // Calculate comprehensive statistics
    const stats = await SpApplication.aggregate([
      { $match: { spCategoryId: categoryId } },
      {
        $lookup: {
          from: 'spcompanies',
          localField: 'spCompanyId',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $unwind: '$company'
      },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' },
          averageTotalScore: { $avg: '$totalScore' },
          maxScore: { $max: '$totalScore' },
          minScore: { $min: '$totalScore' },
          pendingCount: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          supplierCount: {
            $sum: { $cond: [{ $eq: ['$company.type', 'supplier'] }, 1, 0] }
          },
          buyerCount: {
            $sum: { $cond: [{ $eq: ['$company.type', 'buyer'] }, 1, 0] }
          }
        }
      }
    ]);

    // Get top performers
    const topPerformers = await SpApplication.find({ spCategoryId: categoryId })
      .populate('spCompanyId', 'name type')
      .sort({ totalScore: -1 })
      .limit(5)
      .select('spCompanyId totalScore systemScore complianceScore status');

    return successResponse({
      applications: filteredApplications,
      category: {
        _id: category._id,
        name: category.name,
        type: category.type,
        maxScore: category.maxScore,
        isActive: category.isActive
      },
      statistics: stats[0] || {
        totalApplications: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        averageTotalScore: 0,
        maxScore: 0,
        minScore: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
        supplierCount: 0,
        buyerCount: 0
      },
      topPerformers,
      pagination: {
        page,
        limit,
        total: companyType ? filteredApplications.length : total,
        pages: Math.ceil((companyType ? filteredApplications.length : total) / limit)
      }
    });

  } catch (error: any) {
    console.error('List admin category applications error:', error);
    return errorResponse(error.message || 'Failed to fetch admin category applications', 500);
  }
}
