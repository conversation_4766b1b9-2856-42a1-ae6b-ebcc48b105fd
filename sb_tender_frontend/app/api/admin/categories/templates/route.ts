import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCategoryTemplate from '@/lib/models/sp_category_template';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// POST /api/admin/categories/templates - Create category template
export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const body = await request.json();
    const {
      name,
      description,
      categoryType,
      fields,
      isDefault = false,
      isActive = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!name) errors.push('Template name is required');
    if (!categoryType) errors.push('Category type is required');
    if (!fields || !Array.isArray(fields)) errors.push('Fields array is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Check for duplicate name
    const existingTemplate = await SpCategoryTemplate.findOne({ name, categoryType });
    if (existingTemplate) {
      return errorResponse('Template with this name already exists for this category type', 409);
    }

    // If setting as default, unset other defaults for this category type
    if (isDefault) {
      await SpCategoryTemplate.updateMany(
        { categoryType, isDefault: true },
        { isDefault: false }
      );
    }

    // Calculate total max score from fields
    const maxScore = fields.reduce((total: number, field: any) => {
      return total + (field.maxScore || 0);
    }, 0);

    // Create template
    const template = new SpCategoryTemplate({
      name,
      description,
      categoryType,
      fields,
      maxScore,
      isDefault,
      isActive,
      createdBy: user.userId
    });

    await template.save();

    // Populate the created template
    await template.populate('createdBy', 'firstName lastName email');

    return successResponse(template, 201);

  } catch (error: any) {
    console.error('Create category template error:', error);
    return errorResponse(error.message || 'Failed to create category template', 500);
  }
}

// GET /api/admin/categories/templates - List category templates
export async function GET(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const categoryType = searchParams.get('categoryType');
    const isDefault = searchParams.get('isDefault');
    const isActive = searchParams.get('isActive');

    // Build query
    let query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (categoryType) {
      query.categoryType = categoryType;
    }
    
    if (isDefault !== null && isDefault !== undefined) {
      query.isDefault = isDefault === 'true';
    }
    
    if (isActive !== null && isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [templates, total] = await Promise.all([
      SpCategoryTemplate.find(query)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ isDefault: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpCategoryTemplate.countDocuments(query)
    ]);

    return successResponse({
      templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List category templates error:', error);
    return errorResponse(error.message || 'Failed to fetch category templates', 500);
  }
}

// DELETE /api/admin/categories/templates/:id - Delete category template
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const url = new URL(request.url);
    const templateId = url.pathname.split('/').pop();

    if (!templateId) {
      return errorResponse('Template ID is required', 400);
    }

    const template = await SpCategoryTemplate.findById(templateId);
    if (!template) {
      return errorResponse('Template not found', 404);
    }

    // Don't allow deletion of default templates
    if (template.isDefault) {
      return errorResponse('Cannot delete default template', 400);
    }

    await SpCategoryTemplate.findByIdAndDelete(templateId);

    return successResponse({ message: 'Category template deleted successfully' });

  } catch (error: any) {
    console.error('Delete category template error:', error);
    return errorResponse(error.message || 'Failed to delete category template', 500);
  }
}
