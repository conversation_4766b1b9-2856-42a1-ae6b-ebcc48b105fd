import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpNotification from '@/lib/models/sp_notification';
import User from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';
import { sendMail } from '@/lib/services/EmailService';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const channel = searchParams.get('channel');
    const status = searchParams.get('status');

    // Build query
    const query: any = {};
    if (type) query.type = type;
    if (channel) query.channel = channel;
    if (status) query.status = status;
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } },
        { type: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [notifications, total] = await Promise.all([
      SpNotification.find(query)
        .populate('userId', 'firstName lastName email type')
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpNotification.countDocuments(query)
    ]);

    // Calculate summary statistics
    const summaryStats = await SpNotification.aggregate([
      {
        $group: {
          _id: null,
          totalNotifications: { $sum: 1 },
          sentNotifications: {
            $sum: { $cond: [{ $eq: ['$status', 'sent'] }, 1, 0] }
          },
          deliveredNotifications: {
            $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
          },
          failedNotifications: {
            $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
          }
        }
      }
    ]);

    return successResponse({
      notifications,
      summary: summaryStats[0] || {
        totalNotifications: 0,
        sentNotifications: 0,
        deliveredNotifications: 0,
        failedNotifications: 0
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin notifications fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const body = await request.json();
    
    await connectToDatabase();

    const {
      audience,
      channels,
      title,
      message,
      priority = 'normal',
      scheduledFor,
      userIds,
      userEmails
    } = body;

    // Validation
    const errors: string[] = [];
    if (!audience) errors.push('Audience is required');
    if (!channels || !Array.isArray(channels) || channels.length === 0) {
      errors.push('At least one channel is required');
    }
    if (!title) errors.push('Title is required');
    if (!message) errors.push('Message is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`);
    }

    // Determine target users based on audience
    let targetUsers: any[] = [];
    
    switch (audience) {
      case 'all_users':
        targetUsers = await User.find({ status: 'active' });
        break;
      case 'all_admins':
        targetUsers = await User.find({ type: 'admin', status: 'active' });
        break;
      case 'all_suppliers':
        targetUsers = await User.find({ type: 'supplier', status: 'active' });
        break;
      case 'all_buyers':
        targetUsers = await User.find({ type: 'buyer', status: 'active' });
        break;
      case 'search_users':
        if (userIds && userIds.length > 0) {
          targetUsers = await User.find({ _id: { $in: userIds }, status: 'active' });
        }
        if (userEmails && userEmails.length > 0) {
          const emailUsers = await User.find({ email: { $in: userEmails }, status: 'active' });
          targetUsers = [...targetUsers, ...emailUsers];
        }
        break;
      default:
        return errorResponse('Invalid audience type');
    }

    if (targetUsers.length === 0) {
      return errorResponse('No target users found for the specified audience');
    }

    // Create notifications for each user and channel
    const notifications = [];
    const emailPromises = [];

    for (const targetUser of targetUsers) {
      for (const channel of channels) {
        const notification = new SpNotification({
          userId: targetUser._id,
          type: 'admin_broadcast',
          channel,
          title,
          message,
          priority,
          status: scheduledFor ? 'scheduled' : 'sent',
          scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
          createdBy: user.userId,
        });

        await notification.save();
        notifications.push(notification);

        // Send email immediately if channel is email and not scheduled
        if (channel === 'email' && !scheduledFor) {
          emailPromises.push(
            sendMail({
              from: process.env.DEFAULT_FROM_EMAIL!,
              to: targetUser.email,
              subject: title,
              html: `
                <h2>${title}</h2>
                <p>Dear ${targetUser.firstName} ${targetUser.lastName},</p>
                <p>${message}</p>
                <br>
                <p>Best regards,<br>TenderAsili Team</p>
              `
            }).catch(error => {
              console.error(`Failed to send email to ${targetUser.email}:`, error);
              // Update notification status to failed
              SpNotification.findByIdAndUpdate(notification._id, { status: 'failed' });
            })
          );
        }
      }
    }

    // Send all emails
    if (emailPromises.length > 0) {
      await Promise.allSettled(emailPromises);
    }

    return successResponse({
      message: 'Notifications created successfully',
      notificationsCreated: notifications.length,
      usersTargeted: targetUsers.length,
      channelsUsed: channels
    }, 'Notifications sent successfully', 201);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Admin notification creation error:', error);
    return errorResponse('Internal server error', 500);
  }
}
