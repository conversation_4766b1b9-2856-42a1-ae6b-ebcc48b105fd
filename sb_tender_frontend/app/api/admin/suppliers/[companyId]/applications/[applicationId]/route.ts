import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/suppliers/:companyId/applications/:applicationId - Specific application
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    // Verify supplier company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'supplier'
    });

    if (!company) {
      return errorResponse('Supplier company not found', 404);
    }

    // Find application
    const application = await SpApplication.findOne({
      _id: applicationId,
      spCompanyId: companyId
    })
      .populate('spCompanyId', 'name type email phone address')
      .populate('spJobId', 'title description location contract starts ends')
      .populate('spJobCategoryId', 'name price type fields maxScore')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!application) {
      return errorResponse('Application not found', 404);
    }

    // Get related applications for comparison
    const relatedApplications = await SpApplication.find({
      spJobCategoryId: application.spJobCategoryId,
      _id: { $ne: applicationId }
    })
      .select('spCompanyId systemScore complianceScore totalScore status')
      .populate('spCompanyId', 'name')
      .sort({ totalScore: -1 })
      .limit(5);

    return successResponse({
      application,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      relatedApplications,
      ranking: {
        // This would be calculated based on all applications in the category
        position: 1,
        totalApplications: relatedApplications.length + 1
      }
    });

  } catch (error: any) {
    console.error('Get supplier application error:', error);
    return errorResponse(error.message || 'Failed to fetch supplier application', 500);
  }
}
