import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// PUT /api/admin/suppliers/:companyId/applications/:applicationId/review - Review application
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;
    const body = await request.json();

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    const {
      status,
      complianceScore,
      adminNotes,
      fieldScores,
      reviewComments,
      recommendation
    } = body;

    // Verify supplier company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'supplier'
    });

    if (!company) {
      return errorResponse('Supplier company not found', 404);
    }

    // Find application
    const application = await SpApplication.findOne({
      _id: applicationId,
      spCompanyId: companyId
    });

    if (!application) {
      return errorResponse('Application not found', 404);
    }

    // Update application fields if field scores provided
    if (fieldScores && Array.isArray(fieldScores)) {
      fieldScores.forEach((fieldScore: any) => {
        const fieldIndex = application.fields.findIndex(
          (field: any) => field.fieldName === fieldScore.fieldName
        );
        
        if (fieldIndex >= 0) {
          application.fields[fieldIndex].score = fieldScore.score;
          application.fields[fieldIndex].adminComments = fieldScore.comments;
        }
      });

      // Recalculate system score
      application.systemScore = application.fields.reduce(
        (total: number, field: any) => total + (field.score || 0), 
        0
      );
    }

    // Update application
    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date(),
      reviewedAt: new Date(),
      reviewedBy: user.userId
    };

    if (status) updateData.status = status;
    if (complianceScore !== undefined) updateData.complianceScore = complianceScore;
    if (adminNotes) updateData.adminNotes = adminNotes;
    if (reviewComments) updateData.reviewComments = reviewComments;
    if (recommendation) updateData.recommendation = recommendation;

    // Calculate total score
    const systemScore = application.systemScore || 0;
    const compliance = complianceScore !== undefined ? complianceScore : (application.complianceScore || 0);
    updateData.totalScore = systemScore + compliance;

    Object.assign(application, updateData);
    await application.save();

    // Populate and return
    await application.populate('spCompanyId', 'name type email phone');
    await application.populate('spJobId', 'title description');
    await application.populate('spJobCategoryId', 'name price type');
    await application.populate('createdBy', 'firstName lastName email');
    await application.populate('updatedBy', 'firstName lastName email');
    await application.populate('reviewedBy', 'firstName lastName email');

    // Get updated ranking
    const totalApplications = await SpApplication.countDocuments({
      spJobCategoryId: application.spJobCategoryId
    });

    const higherScoredApplications = await SpApplication.countDocuments({
      spJobCategoryId: application.spJobCategoryId,
      totalScore: { $gt: application.totalScore }
    });

    const ranking = {
      position: higherScoredApplications + 1,
      totalApplications
    };

    return successResponse({
      application,
      ranking,
      message: 'Application reviewed successfully'
    });

  } catch (error: any) {
    console.error('Review application error:', error);
    return errorResponse(error.message || 'Failed to review application', 500);
  }
}
