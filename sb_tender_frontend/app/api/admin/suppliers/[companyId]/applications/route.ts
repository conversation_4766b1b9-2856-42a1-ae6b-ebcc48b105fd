import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpCompany from '@/lib/models/sp_company';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/admin/suppliers/:companyId/applications - Supplier applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const categoryId = searchParams.get('categoryId');
    const jobId = searchParams.get('jobId');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify supplier company exists
    const company = await SpCompany.findOne({
      _id: companyId,
      type: 'supplier'
    });

    if (!company) {
      return errorResponse('Supplier company not found', 404);
    }

    // Build query
    let query: any = { spCompanyId: companyId };
    
    if (search) {
      query.$or = [
        { 'fields.value': { $regex: search, $options: 'i' } },
        { adminNotes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (categoryId) {
      query.spJobCategoryId = categoryId;
    }
    
    if (jobId) {
      query.spJobId = jobId;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('spJobId', 'title description')
        .populate('spJobCategoryId', 'name price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate statistics
    const stats = await SpApplication.aggregate([
      { $match: { spCompanyId: companyId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          avgSystemScore: { $avg: '$systemScore' },
          avgComplianceScore: { $avg: '$complianceScore' },
          avgTotalScore: { $avg: '$totalScore' }
        }
      }
    ]);

    return successResponse({
      applications,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      statistics: stats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('List supplier applications error:', error);
    return errorResponse(error.message || 'Failed to fetch supplier applications', 500);
  }
}
