import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import { generateToken } from '@/lib/utils/token';
import { successResponse, errorResponse, validationError } from '@/lib/utils/apiResponse';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { companyName, firstName, lastName, email, password, phone, type } = body;

    console.log('Registration request:', { companyName, firstName, lastName, email, phone, type });

    // Validation
    const errors: string[] = [];
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!phone) errors.push('Phone is required');
    if (!type || !['buyer', 'supplier', 'admin'].includes(type)) {
      errors.push('Type must be buyer, supplier, or admin');
    }
    if (type !== 'admin' && !companyName) {
      errors.push('Company name is required for non-admin users');
    }

    if (errors.length > 0) {
      return validationError(errors);
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return errorResponse('Invalid email format');
    }

    // Password validation
    if (password.length < 6) {
      return errorResponse('Password must be at least 6 characters long');
    }

    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user first
    const user = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      type,
      status: 'active',
      emailVerified: false,
      phoneVerified: false,
    });

    await user.save();

    // Create company only for non-admin users
    let company = null;
    if (type !== 'admin') {
      company = new SpCompany({
        name: companyName,
        registrationNumber: `REG-${Date.now()}`, // Temporary registration number
        type,
        address: 'To be updated', // Will be updated later
        contactPerson: `${firstName} ${lastName}`,
        email,
        phone,
        createdBy: user._id,
        status: 'active',
        verified: false,
      });

      await company.save();
    }

    // Generate token
    const token = generateToken(user._id.toString());

    // Remove password from response and include company info for frontend routing
    const userResponse = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      type: user.type,
      status: user.status,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      createdAt: user.createdAt,
      // Add companies array for frontend routing compatibility (empty for admin users)
      companies: company ? [{
        companyId: company._id,
        companyName: company.name,
        companyType: company.type,
        companyRegistrationNumber: company.registrationNumber,
        roles: [] // Will be populated later when roles are assigned
      }] : []
    };

    const companyResponse = company ? {
      _id: company._id,
      name: company.name,
      type: company.type,
      status: company.status,
      verified: company.verified,
    } : null;

    console.log('Registration successful:', {
      userId: user._id,
      companyId: company?._id || 'N/A (admin user)',
      userType: user.type
    });

    return successResponse(
      { user: userResponse, company: companyResponse, token },
      'Registration successful',
      201
    );
  } catch (error: any) {
    console.error('Registration error:', error);
    return errorResponse('Internal server error', 500);
  }
}
