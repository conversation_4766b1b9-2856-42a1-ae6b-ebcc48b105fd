import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpRole from '@/lib/models/sp_role';
import SpUserRole from '@/lib/models/sp_user_role';
import { getUserCompanyRoles } from '@/lib/utils/user';
import SpCompany from '@/lib/models/sp_company';
import { generateToken } from '@/lib/utils/token';
import { successResponse, errorResponse, validationError } from '@/lib/utils/apiResponse';
const TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];

export const ensureDefaultRolesExist = async (context, createdBy) => {
  const existingRoles = await SpRole.find({ context });
  const existingKeys = new Set(existingRoles.map(r => r.type));

  const missingTypes = TYPES.filter(type => !existingKeys.has(type));

  if (missingTypes.length === 0) return; // All roles exist

  const newRoles = missingTypes.map(type => ({
    name: `${context.toUpperCase()} - ${type}`,
    context,
    type,
    permissions: DEFAULT_PERMISSIONS,
    description: `Auto-created role for ${context} - ${type}`,
    createdBy,
  }));

  await SpRole.insertMany(newRoles);
};


export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { companyName, firstName, lastName, email, password, phone, type } = body;

    console.log('Registration request:', { companyName, firstName, lastName, email, phone, type });

    // Validation
    const errors: string[] = [];
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!phone) errors.push('Phone is required');
    if (!type || !['buyer', 'supplier', 'admin'].includes(type)) {
      errors.push('Type must be buyer, supplier, or admin');
    }
    if (type !== 'admin' && !companyName) {
      errors.push('Company name is required for non-admin users');
    }

    if (errors.length > 0) {
      return validationError(errors);
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return errorResponse('Invalid email format');
    }

    // Password validation
    if (password.length < 6) {
      return errorResponse('Password must be at least 6 characters long');
    }

    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user first
    const user = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      type,
      status: 'active',
      emailVerified: false,
      phoneVerified: false,
    });

    await user.save();

    // Create company
    const company = new SpCompany({
      name: companyName,
      registrationNumber: `REG-${Date.now()}`, // Temporary registration number
      type,
      address: 'To be updated', // Will be updated later
      contactPerson: `${firstName} ${lastName}`,
      email,
      phone,
      createdBy: user._id,
      status: 'active',
      verified: false,
    });

    await company.save();




        // --- Ensure default roles exist (if applicable) ---
        // This function should ideally run during setup, not every registration.
        // If roles might not exist, ensure this function creates them.
        console.log(`Ensuring default roles exist for company type: ${company.type}...`);
        // Assuming ensureDefaultRolesExist is defined elsewhere and works
         await ensureDefaultRolesExist(company.type, user._id);
        console.log('Ensure default roles check/creation finished.');
    
    
        // --- Step 3: Assign supplier roles to user ---
        console.log('Attempting to find supplier roles for assignment...');
        // Criteria: Roles with context matching the company type, OR context 'supplier' if that's how your supplier roles are structured
        // Based on your previous code and requirement "supplier user role for all roles", let's find roles with context matching company.type AND context 'supplier'. This might mean finding roles with *either* context. Or maybe find roles with name 'Company Supplier' and context matching company.type?
        // Let's refine based on your earlier code finding `context: 'supplier'`:
        const supplierRoles = await SpRole.find({ context: 'supplier' });
         console.log(`Step 3a: Found ${supplierRoles.length} roles with context 'supplier'.`);
         console.log('Found supplier roles objects:', supplierRoles.map(r => ({ _id: r._id, name: r.name, context: r.context, type: r.type })));
    
    
         // *** Potential Issue: If you intended to find roles with context matching company.type,
         // the query should be: const supplierRoles = await SpRole.find({ context: company.type });
         // Or if you have a specific supplier role name: const supplierRoles = await SpRole.find({ name: 'Company Supplier', context: company.type });
         // The current query `context: 'supplier'` finds roles specifically defined for an 'supplier' context,
         // which might be site supplier roles, not necessarily company-specific supplier roles.
         // Review your SpRole data to confirm which query is correct for the role you want to assign.
         // For now, proceeding with `context: 'supplier'` as per your code.
    
    
        if (supplierRoles.length === 0) {
            console.warn(`No supplier roles found with context 'supplier'. Cannot assign any roles.`);
             // Decide if registration should fail here if no supplier role can be assigned.
             // For now, it will proceed without assigning roles.
        } else {
            console.log(`Mapping ${supplierRoles.length} found supplier roles to SpUserRole objects.`);
            const SpUserRoles = supplierRoles.map(role => ({
              userId: user._id,
              companyId: company._id,
              roleId: role._id,
            }));
            console.log('Step 3b: Prepared SpUserRoles array for insertion:', SpUserRoles);
            console.log('Number of SpUserRoles entries to insert:', SpUserRoles.length);
    
    
            console.log('Attempting to insert SpUserRoles into the database...');
            // Ensure your model is correctly imported as SpUserRole, not SpUserRole
            await SpUserRole.insertMany(SpUserRoles);
            console.log(`Step 3 Successful: Successfully inserted ${SpUserRoles.length} SpUserRole documents.`);
        }
    
        // Send welcome notification using helper
        try {
          //await NotificationHelper.sendWelcomeEmail(user, company._id);
          console.log('Welcome email sent successfully');
        } catch (notificationError) {
          console.error('Failed to send welcome email:', notificationError);
          // Don't fail the registration if notification fails
        }
    
        // --- Step 4: Prepare and Return Response ---
        console.log('Generating JWT token...');
        //const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '72h' });
        console.log('JWT token generated.');
    
    
        console.log('Attempting to fetch user data with company/roles using getUserCompanyRoles...');
        const userDataWithRoles = await getUserCompanyRoles(user._id);
        console.log('getUserCompanyRoles finished.');
        console.log('Result of getUserCompanyRoles:', userDataWithRoles ? 'Data fetched' : 'No data fetched');
        const token = generateToken(user._id.toString());

    
        if (!userDataWithRoles || !userDataWithRoles.companies || userDataWithRoles.companies.length === 0) {
          console.error(`Could not fetch complete company/role data for user ID: ${user._id}. Check getUserCompanyRoles logic and if UserRoles were actually created and linked correctly.`);
           console.log('Sending minimal user info in response.');
            // Send minimal user info if getUserCompanyRoles failed or returned no companies/roles
            return errorResponse('User created but roles could not be registtered.', 500);

        }
    
         console.log('Step 4 Successful: User data with companies/roles fetched.');
         console.log('Responding with token and full user data.');
         // Respond with token and structured user data including companies and roles




    // Generate token

    // Remove password from response and include company info for frontend routing

    console.log('Registration successful:', { userId: user._id, companyId: company._id });

    return successResponse(
      { user: userDataWithRoles, message: 'Registration successful', token },
      'Registration successful',
      201
    );
  } catch (error: any) {
    console.error('Registration error:', error);
    return errorResponse('Internal server error', 500);
  }
}
