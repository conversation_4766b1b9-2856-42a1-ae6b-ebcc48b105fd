import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import { generateToken } from '@/lib/utils/token';
import { successResponse, errorResponse, validationError } from '@/lib/utils/apiResponse';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { firstName, lastName, email, password, phone, type } = body;

    // Validation
    const errors: string[] = [];
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!phone) errors.push('Phone is required');
    if (!type || !['buyer', 'supplier'].includes(type)) {
      errors.push('Type must be either buyer or supplier');
    }

    if (errors.length > 0) {
      return validationError(errors);
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return errorResponse('Invalid email format');
    }

    // Password validation
    if (password.length < 6) {
      return errorResponse('Password must be at least 6 characters long');
    }

    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      type,
      status: 'active',
      emailVerified: false,
      phoneVerified: false,
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id.toString());

    // Remove password from response
    const userResponse = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      type: user.type,
      status: user.status,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      createdAt: user.createdAt,
    };

    return successResponse(
      { user: userResponse, token },
      'User registered successfully',
      201
    );
  } catch (error: any) {
    console.error('Registration error:', error);
    return errorResponse('Internal server error', 500);
  }
}
