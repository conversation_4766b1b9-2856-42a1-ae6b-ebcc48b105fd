import { NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> } // Updated type here
) {
  try {
    const { id } = await params; // Await params here
    const response = await axios.get(`http://localhost:5000/api/users/${id}`);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('GET error:', error);
    return NextResponse.json(
      { error: error.response?.data?.error || 'User not found' },
      { status: error.response?.status || 500 }
    );
  }
}