import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/users`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    console.error('POST error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating user.' }, { status: error.response?.status || 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const token = req.headers.get('authorization') || '';
    console.log('Token:', token);
    const endpoint = userId ? `/api/admin/users/${userId}` : '/api/admin/users';
    console.log('Endpoint:', BACKEND_API_URL + endpoint);
    const backendRes = await fetch(`${BACKEND_API_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    if (!userId) {
         return NextResponse.json({ error: 'Missing User ID' }, { status: 400 });
    }


    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/users/${userId}`, {
      method: 'PUT',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to update user' }, { status: backendRes.status });
    }
    console.log('Backend response status:', backendRes.status);
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
     console.error('PUT error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating user.' }, { status: error.response?.status || 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const token = req.headers.get('authorization') || '';

    if (!userId) {
      return NextResponse.json({ error: 'Missing User ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/users/${userId}`, {
      method: 'DELETE',
      headers: {
        Authorization: token,
      },
    });

    if (!backendRes.ok) {
        console.error('Backend response error:', backendRes.statusText);
        return NextResponse.json({ error: 'Failed to delete user' }, { status: backendRes.status });
    }


    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('DELETE error:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
export {};