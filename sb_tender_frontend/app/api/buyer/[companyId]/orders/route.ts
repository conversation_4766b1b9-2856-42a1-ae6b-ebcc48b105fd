import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpCompany from '@/lib/models/sp_company';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/orders - Company orders
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    SpJobCategory;

    // Build query for orders from this company
    let query: any = { spCompanyId: companyId };
    
    if (search) {
      query.$or = [
        { _id: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [orders, total] = await Promise.all([
      SpOrder.find(query)
        .populate('spCompanyId', 'name type email phone')
        .populate('orderItems.spJobCategoryId', 'title price type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpOrder.countDocuments(query)
    ]);

    // Calculate order statistics
    const stats = await SpOrder.aggregate([
      { $match: { spCompanyId: companyId } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalAmount: { 
            $sum: {
              $cond: {
                if: { $type: '$totalAmount' },
                then: { $toDouble: '$totalAmount' },
                else: 0
              }
            }
          },
          checkoutOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'checkout'] }, 1, 0] }
          },
          pendingOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          paidOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
          },
          cancelledOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          }
        }
      }
    ]);

    // Get recent orders (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentOrdersCount = await SpOrder.countDocuments({
      spCompanyId: companyId,
      createdAt: { $gte: thirtyDaysAgo }
    });

    return successResponse({
      orders,
      statistics: stats[0] || {
        totalOrders: 0,
        totalAmount: 0,
        checkoutOrders: 0,
        pendingOrders: 0,
        paidOrders: 0,
        cancelledOrders: 0
      },
      recentOrdersCount,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching buyer orders:', error);
    return errorResponse(error.message || 'Failed to fetch buyer orders', 500);
  }
}
