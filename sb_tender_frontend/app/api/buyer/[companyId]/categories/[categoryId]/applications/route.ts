import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/categories/:categoryId/applications - Category applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; categoryId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, categoryId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId || !categoryId) {
      return errorResponse('Company ID and Category ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    User;

    // Verify the category belongs to this company
    const category = await SpJobCategory.findOne({
      _id: categoryId,
      spCompanyId: companyId
    });

    if (!category) {
      return errorResponse('Category not found or access denied', 404);
    }

    // Build query for applications to this specific category
    let query: any = {
      spJobCategoryId: categoryId
    };
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { ref: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    // Build sort object
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber phone email')
        .populate('spJobCategoryId', 'title type price ref')
        .populate('submittedBy', 'firstName lastName email')
        .populate('reviewer', 'firstName lastName email')
        .populate('reviewedBy', 'firstName lastName email')
        .select('title ref status systemScore complianceScore totalScore submittedAt reviewDate reviewStatus spSupplierCompanyId spJobCategoryId submittedBy reviewer reviewedBy type fields')
        .sort(sortObj)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate category-specific application statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobCategoryId: categoryId } },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          pendingApplications: {
            $sum: { $cond: [{ $in: ['$status', ['submitted', 'under_review']] }, 1, 0] }
          },
          approvedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          averageScore: { $avg: '$totalScore' },
          highestScore: { $max: '$totalScore' },
          lowestScore: { $min: '$totalScore' },
          averageSystemScore: { $avg: '$systemScore' },
          averageComplianceScore: { $avg: '$complianceScore' }
        }
      }
    ]);

    // Get top performing suppliers for this category
    const topSuppliers = await SpApplication.aggregate([
      { $match: { spJobCategoryId: categoryId, status: 'approved' } },
      {
        $group: {
          _id: '$spSupplierCompanyId',
          averageScore: { $avg: '$totalScore' },
          applicationCount: { $sum: 1 },
          highestScore: { $max: '$totalScore' }
        }
      },
      {
        $lookup: {
          from: 'spcompanies',
          localField: '_id',
          foreignField: '_id',
          as: 'company'
        }
      },
      {
        $unwind: '$company'
      },
      {
        $project: {
          companyId: '$_id',
          companyName: '$company.name',
          averageScore: { $round: ['$averageScore', 2] },
          applicationCount: 1,
          highestScore: 1
        }
      },
      {
        $sort: { averageScore: -1 }
      },
      {
        $limit: 5
      }
    ]);

    // Get recent applications (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentApplicationsCount = await SpApplication.countDocuments({
      spJobCategoryId: categoryId,
      createdAt: { $gte: sevenDaysAgo }
    });

    return successResponse({
      applications,
      category: {
        _id: category._id,
        title: category.title,
        type: category.type,
        status: category.status,
        price: category.price,
        ref: category.ref
      },
      statistics: stats[0] ? {
        ...stats[0],
        averageScore: Math.round((stats[0].averageScore || 0) * 100) / 100,
        averageSystemScore: Math.round((stats[0].averageSystemScore || 0) * 100) / 100,
        averageComplianceScore: Math.round((stats[0].averageComplianceScore || 0) * 100) / 100,
        recentApplicationsCount
      } : {
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        averageSystemScore: 0,
        averageComplianceScore: 0,
        recentApplicationsCount: 0
      },
      topSuppliers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching category applications:', error);
    return errorResponse(error.message || 'Failed to fetch category applications', 500);
  }
}
