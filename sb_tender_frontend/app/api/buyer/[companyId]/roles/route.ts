import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpRole from '@/lib/models/sp_role';
import SpUserRole from '@/lib/models/sp_user_role';
import User from '@/lib/models/sp_user';
import { requireBuyer } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { ensureDefaultRolesExist } from '@/lib/utils/user';

export const dynamic = 'force-dynamic';

// GET /api/admin/companies/:companyId/roles - List company roles
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyer(request);
    await connectToDatabase();
    await ensureDefaultRolesExist('buyer', user.userId);
    

    const { companyId } = await params;

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

        const companyUserRoles = await SpUserRole.find({ companyId });
        if (!companyUserRoles || companyUserRoles.length === 0) {
          console.log('No SpUserRoles found for company, returning empty result');
          return successResponse({
            roles: [],
            company: {
              _id: company._id,
              name: company.name,
              type: company.type
            }
          });
        }
        const userIds = companyUserRoles.map(role => role.userId);
        const companyRoleIds = companyUserRoles.map(role => role.roleId);
        const companyUsers = await User.find({ _id: { $in: userIds } }).select('-password -otp');
        const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });
        //console.log('Users', companyUsers);
        console.log('Roles', companyRoles);

        //res.status(200).json({ roles: companyRoles });

    return successResponse({
      roles: companyRoles,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      }
    });

  } catch (error: any) {
    console.error('List company roles error:', error);
    return errorResponse(error.message || 'Failed to fetch company roles', 500);
  }
}
