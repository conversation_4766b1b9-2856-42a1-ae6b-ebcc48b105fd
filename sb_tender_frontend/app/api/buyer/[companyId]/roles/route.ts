import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpRole from '@/lib/models/sp_role';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/roles - Company roles
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50'); // Higher limit for roles
    const search = searchParams.get('search');
    const category = searchParams.get('category');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Build query for roles
    // Note: Roles are typically global or company-specific based on your business logic
    // For now, we'll get all roles that are either global or specific to this company
    let query: any = {
      $or: [
        { companyId: companyId }, // Company-specific roles
        { companyId: { $exists: false } }, // Global roles
        { companyId: null } // Global roles
      ]
    };
    
    if (search) {
      query.$and = query.$and || [];
      query.$and.push({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      });
    }
    
    if (category) {
      query.$and = query.$and || [];
      query.$and.push({ category });
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [roles, total] = await Promise.all([
      SpRole.find(query)
        .sort({ name: 1 }) // Sort by name alphabetically
        .skip(skip)
        .limit(limit),
      SpRole.countDocuments(query)
    ]);

    // Group roles by category for better organization
    const rolesByCategory = roles.reduce((acc: any, role) => {
      const cat = role.category || 'general';
      if (!acc[cat]) {
        acc[cat] = [];
      }
      acc[cat].push(role);
      return acc;
    }, {});

    // Calculate role statistics
    const stats = await SpRole.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalRoles: { $sum: 1 },
          categoryBreakdown: { $push: '$category' }
        }
      }
    ]);

    return successResponse({
      roles,
      rolesByCategory,
      statistics: stats[0] || {
        totalRoles: 0,
        categoryBreakdown: []
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching company roles:', error);
    return errorResponse(error.message || 'Failed to fetch company roles', 500);
  }
}
