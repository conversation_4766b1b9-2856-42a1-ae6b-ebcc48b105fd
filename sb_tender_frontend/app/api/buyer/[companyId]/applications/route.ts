import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/applications - Company applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    User;

    // Get all job categories for this company to filter applications
    const companyJobCategories = await SpJobCategory.find({
      spCompanyId: companyId
    }).select('_id');

    const categoryIds = companyJobCategories.map(cat => cat._id);

    if (categoryIds.length === 0) {
      return successResponse({
        applications: [],
        statistics: {
          totalApplications: 0,
          pendingApplications: 0,
          approvedApplications: 0,
          rejectedApplications: 0,
          averageScore: 0
        },
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      }, 'No applications found');
    }

    // Build query for applications to company's job categories
    let query: any = {
      spJobCategoryId: { $in: categoryIds }
    };
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { ref: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }

    // Build sort object
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber')
        .populate('spJobCategoryId', 'title type price ref')
        .populate('submittedBy', 'firstName lastName email')
        .populate('reviewer', 'firstName lastName email')
        .select('title ref status systemScore complianceScore totalScore submittedAt reviewDate reviewStatus spSupplierCompanyId spJobCategoryId submittedBy reviewer type')
        .sort(sortObj)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate application statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobCategoryId: { $in: categoryIds } } },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          pendingApplications: {
            $sum: { $cond: [{ $in: ['$status', ['submitted', 'under_review']] }, 1, 0] }
          },
          approvedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          averageScore: { $avg: '$totalScore' },
          statusBreakdown: { $push: '$status' },
          typeBreakdown: { $push: '$type' }
        }
      }
    ]);

    // Get recent applications (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentApplicationsCount = await SpApplication.countDocuments({
      spJobCategoryId: { $in: categoryIds },
      createdAt: { $gte: sevenDaysAgo }
    });

    return successResponse({
      applications,
      statistics: stats[0] ? {
        ...stats[0],
        averageScore: Math.round((stats[0].averageScore || 0) * 100) / 100,
        recentApplicationsCount
      } : {
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        averageScore: 0,
        recentApplicationsCount: 0,
        statusBreakdown: [],
        typeBreakdown: []
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching company applications:', error);
    return errorResponse(error.message || 'Failed to fetch company applications', 500);
  }
}
