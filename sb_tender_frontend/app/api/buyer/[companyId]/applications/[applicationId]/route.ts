import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/applications/:applicationId - Specific application
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    User;

    // Get all job categories for this company to verify application belongs to company
    const companyJobCategories = await SpJobCategory.find({
      spCompanyId: companyId
    }).select('_id');

    const categoryIds = companyJobCategories.map(cat => cat._id);

    // Find the application and verify it belongs to this company
    const application = await SpApplication.findOne({
      _id: applicationId,
      spJobCategoryId: { $in: categoryIds }
    })
      .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber phone email website')
      .populate('spJobCategoryId', 'title type price ref description status starts ends location')
      .populate('submittedBy', 'firstName lastName email phone')
      .populate('reviewer', 'firstName lastName email')
      .populate('reviewedBy', 'firstName lastName email');

    if (!application) {
      return errorResponse('Application not found or access denied', 404);
    }

    return successResponse(application);

  } catch (error: any) {
    console.error('Error fetching application:', error);
    return errorResponse(error.message || 'Failed to fetch application', 500);
  }
}

// PUT /api/buyer/:companyId/applications/:applicationId - Update application
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;
    const body = await request.json();

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Get all job categories for this company to verify application belongs to company
    const companyJobCategories = await SpJobCategory.find({
      spCompanyId: companyId
    }).select('_id');

    const categoryIds = companyJobCategories.map(cat => cat._id);

    // Find the application and verify it belongs to this company
    const application = await SpApplication.findOne({
      _id: applicationId,
      spJobCategoryId: { $in: categoryIds }
    });

    if (!application) {
      return errorResponse('Application not found or access denied', 404);
    }

    const {
      status,
      reviewStatus,
      complianceScore,
      reviewNotes,
      reviewDate,
      internalNotes
    } = body;

    // Prepare update data
    const updateData: any = {
      reviewedBy: user.userId,
      updatedAt: new Date()
    };

    if (status !== undefined) updateData.status = status;
    if (reviewStatus !== undefined) updateData.reviewStatus = reviewStatus;
    if (complianceScore !== undefined) {
      updateData.complianceScore = complianceScore;
      // Recalculate total score
      updateData.totalScore = (application.systemScore || 0) + complianceScore;
    }
    if (reviewNotes !== undefined) updateData.reviewNotes = reviewNotes;
    if (reviewDate !== undefined) updateData.reviewDate = new Date(reviewDate);
    if (internalNotes !== undefined) updateData.internalNotes = internalNotes;

    // If status is being changed to approved/rejected, set review date if not provided
    if ((status === 'approved' || status === 'rejected') && !reviewDate) {
      updateData.reviewDate = new Date();
    }

    // Update the application
    const updatedApplication = await SpApplication.findByIdAndUpdate(
      applicationId,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber phone email website')
      .populate('spJobCategoryId', 'title type price ref description status starts ends location')
      .populate('submittedBy', 'firstName lastName email phone')
      .populate('reviewer', 'firstName lastName email')
      .populate('reviewedBy', 'firstName lastName email');

    if (!updatedApplication) {
      return errorResponse('Failed to update application', 500);
    }

    return successResponse(updatedApplication, 'Application updated successfully');

  } catch (error: any) {
    console.error('Error updating application:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return errorResponse(`Validation error: ${error.message}`, 400);
    }
    
    return errorResponse(error.message || 'Failed to update application', 500);
  }
}
