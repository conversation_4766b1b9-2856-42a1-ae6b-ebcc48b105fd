import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobcategories - All categories
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    SpBuyerCategory;
    User;

    // Build query for job categories belonging to this company
    let query: any = { spCompanyId: companyId };
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { ref: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpJobCategory.find(query)
        .populate('spJobId', 'title description status')
        .populate('spCompanyId', 'name type')
        .populate('spBuyerCategoryId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJobCategory.countDocuments(query)
    ]);

    if (!categories || categories.length === 0) {
      return successResponse({
        categories: [],
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      }, 'No job categories found');
    }

    // Transform the data to include companyName directly (for compatibility)
    const categoriesWithCompany = categories.map(category => ({
      ...category.toObject(),
      companyName: category.spCompanyId?.name || 'Unknown Company'
    }));

    // Calculate category statistics
    const stats = await SpJobCategory.aggregate([
      { $match: { spCompanyId: companyId } },
      {
        $group: {
          _id: null,
          totalCategories: { $sum: 1 },
          draftCategories: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          openCategories: {
            $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] }
          },
          closedCategories: {
            $sum: { $cond: [{ $eq: ['$status', 'closed'] }, 1, 0] }
          },
          typeBreakdown: { $push: '$type' }
        }
      }
    ]);

    return successResponse({
      categories: categoriesWithCompany,
      statistics: stats[0] || {
        totalCategories: 0,
        draftCategories: 0,
        openCategories: 0,
        closedCategories: 0,
        typeBreakdown: []
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching buyer job categories:', error);
    return errorResponse(error.message || 'Failed to fetch buyer job categories', 500);
  }
}
