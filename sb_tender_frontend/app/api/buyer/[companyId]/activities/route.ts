import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpActivityLog from '@/lib/models/sp_activity_log';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/activities - Activity logs
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const action = searchParams.get('action');
    const resource = searchParams.get('resource');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Get users from this company to filter activities
    const companyUsers = await User.find({
      companyId: companyId,
      status: { $ne: 'inactive' }
    }).select('_id');

    const userIds = companyUsers.map(u => u._id);

    // Build query for activities by company users
    let query: any = {
      userId: { $in: userIds }
    };
    
    if (search) {
      query.$or = [
        { action: { $regex: search, $options: 'i' } },
        { resource: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (action) {
      query.action = action;
    }
    
    if (resource) {
      query.resource = resource;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [activities, total] = await Promise.all([
      SpActivityLog.find(query)
        .populate('userId', 'firstName lastName email type')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpActivityLog.countDocuments(query)
    ]);

    // Calculate activity statistics
    const stats = await SpActivityLog.aggregate([
      { $match: { userId: { $in: userIds } } },
      {
        $group: {
          _id: null,
          totalActivities: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' },
          actionBreakdown: { $push: '$action' },
          resourceBreakdown: { $push: '$resource' }
        }
      },
      {
        $project: {
          totalActivities: 1,
          uniqueUsersCount: { $size: '$uniqueUsers' },
          actionBreakdown: 1,
          resourceBreakdown: 1
        }
      }
    ]);

    // Get recent activity summary (last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentStats = await SpActivityLog.aggregate([
      {
        $match: {
          userId: { $in: userIds },
          createdAt: { $gte: yesterday }
        }
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    return successResponse({
      activities,
      statistics: {
        overview: stats[0] || {
          totalActivities: 0,
          uniqueUsersCount: 0,
          actionBreakdown: [],
          resourceBreakdown: []
        },
        recent24Hours: recentStats
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching buyer activities:', error);
    return errorResponse(error.message || 'Failed to fetch buyer activities', 500);
  }
}
