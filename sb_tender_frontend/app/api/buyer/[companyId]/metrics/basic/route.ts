import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/metrics/basic - Basic metrics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Calculate metrics in parallel
    const [
      totalCategories,
      openCategories,
      closedCategories,
      totalUsers
    ] = await Promise.all([
      // Total Categories: Count all job categories for this company
      SpJobCategory.countDocuments({
        spCompanyId: companyId
      }),

      // Open Categories: Count categories that are currently open
      SpJobCategory.countDocuments({
        spCompanyId: companyId,
        status: { $in: ['draft', 'open'] }
      }),

      // Closed Categories: Count categories that have reached a final decision
      SpJobCategory.countDocuments({
        spCompanyId: companyId,
        status: { $in: ['closed'] }
      }),

      // Total Users: Count users associated with this company
      User.countDocuments({
        companyId: companyId,
        status: { $ne: 'inactive' }
      })
    ]);

    // Calculate savings (placeholder - would need actual business logic)
    const savings = 0;

    // Send the calculated metrics in the response
    return successResponse({
      metrics: {
        totalCategories,
        openCategories,
        closedCategories,
        savings,
        totalUsers,
      },
    });

  } catch (error: any) {
    console.error('Error fetching buyer dashboard metrics:', error);
    return errorResponse(error.message || 'Failed to fetch buyer dashboard metrics', 500);
  }
}
