import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpApplication from '@/lib/models/sp_application';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/metrics/analytic - Analytics metrics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Get date ranges for analytics
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get job categories for this company to use in application queries
    const companyJobCategories = await SpJobCategory.find({
      spCompanyId: companyId
    }).select('_id');

    const categoryIds = companyJobCategories.map(cat => cat._id);

    // Calculate analytics metrics in parallel
    const [
      totalApplications,
      pendingApplications,
      approvedApplications,
      rejectedApplications,
      recentApplications,
      applicationTrends,
      categoryPerformance,
      totalUsers
    ] = await Promise.all([
      // Total Applications: Count all applications for this company's categories
      SpApplication.countDocuments({
        spJobCategoryId: { $in: categoryIds }
      }),

      // Pending Applications: Count applications under review
      SpApplication.countDocuments({
        spJobCategoryId: { $in: categoryIds },
        status: { $in: ['submitted', 'under_review'] }
      }),

      // Approved Applications: Count approved applications
      SpApplication.countDocuments({
        spJobCategoryId: { $in: categoryIds },
        status: 'approved'
      }),

      // Rejected Applications: Count rejected applications
      SpApplication.countDocuments({
        spJobCategoryId: { $in: categoryIds },
        status: 'rejected'
      }),

      // Recent Applications (last 7 days)
      SpApplication.countDocuments({
        spJobCategoryId: { $in: categoryIds },
        createdAt: { $gte: sevenDaysAgo }
      }),

      // Application trends over last 30 days
      SpApplication.aggregate([
        {
          $match: {
            spJobCategoryId: { $in: categoryIds },
            createdAt: { $gte: thirtyDaysAgo }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
            },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { '_id.date': 1 }
        }
      ]),

      // Category performance
      SpApplication.aggregate([
        {
          $match: {
            spJobCategoryId: { $in: categoryIds }
          }
        },
        {
          $group: {
            _id: '$spJobCategoryId',
            totalApplications: { $sum: 1 },
            averageScore: { $avg: '$totalScore' },
            approvedCount: {
              $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
            },
            rejectedCount: {
              $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
            }
          }
        },
        {
          $lookup: {
            from: 'spjobcategories',
            localField: '_id',
            foreignField: '_id',
            as: 'category'
          }
        },
        {
          $unwind: '$category'
        },
        {
          $project: {
            categoryId: '$_id',
            categoryName: '$category.title',
            categoryType: '$category.type',
            totalApplications: 1,
            averageScore: { $round: ['$averageScore', 2] },
            approvedCount: 1,
            rejectedCount: 1,
            approvalRate: {
              $round: [
                { $multiply: [{ $divide: ['$approvedCount', '$totalApplications'] }, 100] },
                2
              ]
            }
          }
        },
        {
          $sort: { totalApplications: -1 }
        }
      ]),

      // Total Users
      User.countDocuments({
        companyId: companyId,
        status: { $ne: 'inactive' }
      })
    ]);

    // Calculate completion rate
    const completedApplications = approvedApplications + rejectedApplications;
    const completionRate = totalApplications > 0 
      ? Math.round((completedApplications / totalApplications) * 100) 
      : 0;

    // Send the calculated analytics metrics
    return successResponse({
      metrics: {
        totalApplications,
        pendingApplications,
        completedApplications,
        approvedApplications,
        rejectedApplications,
        recentApplications,
        completionRate,
        totalUsers,
      },
      analytics: {
        applicationTrends,
        categoryPerformance
      }
    });

  } catch (error: any) {
    console.error('Error fetching buyer analytics metrics:', error);
    return errorResponse(error.message || 'Failed to fetch buyer analytics metrics', 500);
  }
}
