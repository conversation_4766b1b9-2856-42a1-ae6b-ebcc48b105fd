import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import SpRole from '@/lib/models/sp_role';
import SpUser<PERSON>ole from '@/lib/models/sp_user_role';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

// Helper function to exclude sensitive fields
const excludeSensitiveFields = (data: any) => {
  if (data.user) {
    const { password, ...userWithoutPassword } = data.user.toObject ? data.user.toObject() : data.user;
    return { ...data, user: userWithoutPassword };
  }
  return data;
};

// PUT /api/buyer/:companyId/users/:userId - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; userId: string }> }
) {
  try {
    const currentUser = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, userId } = await params;
    const body = await request.json();

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    // Verify user has access to this company
    if (currentUser.companyId !== companyId && currentUser.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    const {
      firstName,
      lastName,
      email,
      password, // Optional for updates
      phone,
      type,
      status,
      roleIds,
      emailVerified,
      phoneVerified,
      otpVerified,
      mustUpdatePassword
    } = body;

    console.log('Update request body:', body);
    console.log('User ID to update:', userId);
    console.log('Company ID:', companyId);

    // Check if user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      return errorResponse('User not found', 404);
    }

    // Verify user belongs to the company
    if (existingUser.companyId !== companyId) {
      return errorResponse('User does not belong to this company', 403);
    }

    // Check for email conflicts (excluding current user)
    if (email && email !== existingUser.email) {
      const emailExists = await User.findOne({ 
        email, 
        _id: { $ne: userId } 
      });
      if (emailExists) {
        return errorResponse('Email already exists', 409);
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: currentUser.userId,
      updatedAt: new Date()
    };

    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (type !== undefined) updateData.type = type;
    if (status !== undefined) updateData.status = status;
    if (emailVerified !== undefined) updateData.emailVerified = emailVerified;
    if (phoneVerified !== undefined) updateData.phoneVerified = phoneVerified;
    if (otpVerified !== undefined) updateData.otpVerified = otpVerified;
    if (mustUpdatePassword !== undefined) updateData.mustUpdatePassword = mustUpdatePassword;

    // Hash password if provided
    if (password) {
      const saltRounds = 12;
      updateData.password = await bcrypt.hash(password, saltRounds);
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!updatedUser) {
      return errorResponse('Failed to update user', 500);
    }

    // Handle role updates if provided
    let userRoles = [];
    if (roleIds !== undefined) {
      // Remove existing roles
      await SpUserRole.deleteMany({ userId });

      // Add new roles if provided
      if (roleIds && roleIds.length > 0) {
        const newUserRoles = roleIds.map((roleId: string) => ({
          userId,
          roleId,
          assignedBy: currentUser.userId,
          assignedAt: new Date()
        }));

        await SpUserRole.insertMany(newUserRoles);
      }

      // Get updated roles
      userRoles = await SpUserRole.find({ userId })
        .populate('roleId', 'name description permissions');
    } else {
      // Get existing roles if not updating
      userRoles = await SpUserRole.find({ userId })
        .populate('roleId', 'name description permissions');
    }

    console.log(`User update completed. User now has ${userRoles.length} roles assigned.`);

    // Return the updated user with roles
    const userWithRoles = {
      ...updatedUser.toObject(),
      roles: userRoles.map(ur => ur.roleId)
    };

    return successResponse(
      excludeSensitiveFields({ user: userWithRoles }),
      'Company user updated successfully'
    );

  } catch (error: any) {
    console.error('Error updating user:', error);
    
    // Handle potential duplicate key errors
    if (error.code === 11000) {
      return errorResponse('Duplicate field value entered', 409);
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return errorResponse(`Validation error: ${error.message}`, 400);
    }
    
    return errorResponse(error.message || 'Failed to update user', 500);
  }
}

// DELETE /api/buyer/:companyId/users/:userId - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; userId: string }> }
) {
  try {
    const currentUser = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, userId } = await params;

    if (!companyId || !userId) {
      return errorResponse('Company ID and User ID are required', 400);
    }

    // Verify user has access to this company
    if (currentUser.companyId !== companyId && currentUser.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Verify user belongs to the company
    if (user.companyId !== companyId) {
      return errorResponse('User does not belong to this company', 403);
    }

    // Soft delete by setting status to inactive
    user.status = 'inactive';
    user.updatedBy = currentUser.userId;
    user.updatedAt = new Date();
    await user.save();

    return successResponse(
      { message: 'User deleted successfully' },
      'User deleted successfully'
    );

  } catch (error: any) {
    console.error('Error deleting user:', error);
    
    // Handle invalid ID format errors
    if (error.kind === 'ObjectId') {
      return errorResponse('Invalid User ID format', 400);
    }
    
    return errorResponse(error.message || 'Failed to delete user', 500);
  }
}
