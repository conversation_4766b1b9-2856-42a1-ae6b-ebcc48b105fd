import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import SpRole from '@/lib/models/sp_role';
import SpUser<PERSON>ole from '@/lib/models/sp_user_role';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

// Helper function to exclude sensitive fields
const excludeSensitiveFields = (data: any) => {
  if (data.user) {
    const { password, ...userWithoutPassword } = data.user.toObject ? data.user.toObject() : data.user;
    return { ...data, user: userWithoutPassword };
  }
  return data;
};

// GET /api/buyer/:companyId/users - Company users
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    SpRole;
    SpUserRole;

    console.log('Fetching users for company:', companyId);

    // Find all SpUserRoles where companyId matches
    const companyUserRoles = await SpUserRole.find({ companyId });

    if (!companyUserRoles || companyUserRoles.length === 0) {
      return successResponse({
        users: [],
        statistics: {
          totalUsers: 0,
          activeUsers: 0,
          suspendedUsers: 0,
          typeBreakdown: []
        },
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      });
    }

    // Extract userIds and roleIds from companyRoles
    const userIds = companyUserRoles.map(role => role.userId);
    const companyRoleIds = companyUserRoles.map(role => role.roleId);

    console.log('Found user IDs:', userIds.length);
    console.log('Found role IDs:', companyRoleIds.length);

    // Build user query with search and filters
    const userQuery: any = {
      _id: { $in: userIds },
      status: { $ne: 'inactive' } // Exclude inactive users
    };

    if (search) {
      userQuery.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      userQuery.type = type;
    }

    if (status) {
      userQuery.status = status;
    }

    // Find all users whose _id matches the userIds from companyRoles with pagination
    const skip = (page - 1) * limit;
    const [companyUsers, total] = await Promise.all([
      User.find(userQuery)
        .select('-password') // Exclude password field
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(userQuery)
    ]);

    // Find all roles for the company
    const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });

    console.log('Found users:', companyUsers.length);
    console.log('Found roles:', companyRoles.length);

    // Map roles to users - CONVERT TO STRINGS for proper comparison
    const userRolesMap = companyUserRoles.reduce((map: any, userRole: any) => {
      const userIdStr = userRole.userId.toString();
      if (!map[userIdStr]) {
        map[userIdStr] = [];
      }
      map[userIdStr].push(userRole.roleId.toString());
      return map;
    }, {});

    const usersWithRoles = companyUsers.map(user => ({
      ...user.toObject(),
      // FIXED: Convert ObjectIds to strings for comparison
      roles: companyRoles.filter(role =>
        userRolesMap[user._id.toString()]?.includes(role._id.toString())
      )
    }));

    // Calculate user statistics from the filtered users
    const allCompanyUsers = await User.find({
      _id: { $in: userIds },
      status: { $ne: 'inactive' }
    });

    const stats = {
      totalUsers: allCompanyUsers.length,
      activeUsers: allCompanyUsers.filter(u => u.status === 'active').length,
      suspendedUsers: allCompanyUsers.filter(u => u.status === 'suspended').length,
      typeBreakdown: allCompanyUsers.map(u => u.type)
    };

    return successResponse({
      users: usersWithRoles,
      statistics: stats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching company users:', error);
    return errorResponse(error.message || 'Failed to fetch company users', 500);
  }
}

// POST /api/buyer/:companyId/users - Create user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const currentUser = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (currentUser.companyId !== companyId && currentUser.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    const {
      firstName,
      lastName,
      email,
      password,
      phone,
      type,
      status = 'active',
      roleIds = [],
      emailVerified = false,
      phoneVerified = false,
      otpVerified = false,
      mustUpdatePassword = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!type) errors.push('User type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user (without companyId field since users can belong to multiple companies)
    const newUser = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      type,
      status,
      emailVerified,
      phoneVerified,
      otpVerified,
      mustUpdatePassword,
      createdBy: currentUser.userId
    });

    await newUser.save();

    // Create user-company association through SpUserRole
    // If no specific roles provided, assign a default role
    const rolesToAssign = roleIds && roleIds.length > 0 ? roleIds : [];

    // Always create at least one role entry to associate user with company
    if (rolesToAssign.length === 0) {
      // Find a default role for the company (e.g., 'user' or 'member')
      const defaultRole = await SpRole.findOne({ name: 'user' }) || await SpRole.findOne({ name: 'member' });
      if (defaultRole) {
        rolesToAssign.push(defaultRole._id.toString());
      }
    }

    if (rolesToAssign.length > 0) {
      const userRoles = rolesToAssign.map((roleId: string) => ({
        userId: newUser._id,
        roleId,
        companyId, // Associate user with this company
        assignedBy: currentUser.userId,
        assignedAt: new Date()
      }));

      await SpUserRole.insertMany(userRoles);
    }

    // Get user with roles for this company
    const userRoles = await SpUserRole.find({
      userId: newUser._id,
      companyId: companyId
    });

    const roleIds = userRoles.map(ur => ur.roleId);
    const roles = await SpRole.find({ _id: { $in: roleIds } });

    const userWithRoles = {
      ...newUser.toObject(),
      roles: roles
    };

    return successResponse(
      excludeSensitiveFields({ user: userWithRoles }),
      'User created successfully',
      201
    );

  } catch (error: any) {
    console.error('Error creating user:', error);

    // Handle duplicate key errors
    if (error.code === 11000) {
      return errorResponse('Duplicate field value entered', 409);
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return errorResponse(`Validation error: ${error.message}`, 400);
    }

    return errorResponse(error.message || 'Failed to create user', 500);
  }
}