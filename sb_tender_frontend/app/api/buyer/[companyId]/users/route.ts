import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import SpRole from '@/lib/models/sp_role';
import SpUser<PERSON>ole from '@/lib/models/sp_user_role';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

// Helper function to exclude sensitive fields
const excludeSensitiveFields = (data: any) => {
  if (data.user) {
    const { password, ...userWithoutPassword } = data.user.toObject ? data.user.toObject() : data.user;
    return { ...data, user: userWithoutPassword };
  }
  return data;
};

// GET /api/buyer/:companyId/users - Company users
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    SpRole;
    SpUserRole;

    // Build query
    let query: any = {
      companyId: companyId,
      status: { $ne: 'inactive' } // Exclude inactive users
    };

    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      query.type = type;
    }

    if (status) {
      query.status = status;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [users, total] = await Promise.all([
      User.find(query)
        .select('-password') // Exclude password field
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(query)
    ]);

    // Get roles for each user
    const usersWithRoles = await Promise.all(
      users.map(async (user) => {
        const userRoles = await SpUserRole.find({ userId: user._id })
          .populate('roleId', 'name description permissions');

        return {
          ...user.toObject(),
          roles: userRoles.map(ur => ur.roleId)
        };
      })
    );

    // Calculate user statistics
    const stats = await User.aggregate([
      { $match: { companyId: companyId, status: { $ne: 'inactive' } } },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          activeUsers: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          suspendedUsers: {
            $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
          },
          typeBreakdown: { $push: '$type' }
        }
      }
    ]);

    return successResponse({
      users: usersWithRoles,
      statistics: stats[0] || {
        totalUsers: 0,
        activeUsers: 0,
        suspendedUsers: 0,
        typeBreakdown: []
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching company users:', error);
    return errorResponse(error.message || 'Failed to fetch company users', 500);
  }
}

// POST /api/buyer/:companyId/users - Create user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const currentUser = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (currentUser.companyId !== companyId && currentUser.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    const {
      firstName,
      lastName,
      email,
      password,
      phone,
      type,
      status = 'active',
      roleIds = [],
      emailVerified = false,
      phoneVerified = false,
      otpVerified = false,
      mustUpdatePassword = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!type) errors.push('User type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const newUser = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      type,
      status,
      companyId,
      emailVerified,
      phoneVerified,
      otpVerified,
      mustUpdatePassword,
      createdBy: currentUser.userId
    });

    await newUser.save();

    // Assign roles if provided
    if (roleIds && roleIds.length > 0) {
      const userRoles = roleIds.map((roleId: string) => ({
        userId: newUser._id,
        roleId,
        assignedBy: currentUser.userId,
        assignedAt: new Date()
      }));

      await SpUserRole.insertMany(userRoles);
    }

    // Get user with roles
    const userRoles = await SpUserRole.find({ userId: newUser._id })
      .populate('roleId', 'name description permissions');

    const userWithRoles = {
      ...newUser.toObject(),
      roles: userRoles.map(ur => ur.roleId)
    };

    return successResponse(
      excludeSensitiveFields({ user: userWithRoles }),
      'User created successfully',
      201
    );

  } catch (error: any) {
    console.error('Error creating user:', error);

    // Handle duplicate key errors
    if (error.code === 11000) {
      return errorResponse('Duplicate field value entered', 409);
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return errorResponse(`Validation error: ${error.message}`, 400);
    }

    return errorResponse(error.message || 'Failed to create user', 500);
  }
}