import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params; // Await params here
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const body = await request.json();

    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/${companyId}/users}`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    console.error('POST error:', error);
    return NextResponse.json(
      { error: error.response?.data?.error || 'Error creating user.' },
      { status: error.response?.status || 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Add params here for consistency if needed, though not strictly required by the error
) {
  try {
     const { companyId } = await params; // Await params here if you intend to use the dynamic companyId
    const { searchParams } = new URL(req.url);
    // If you intend to get companyId from searchParams *instead* of dynamic route:
    // const companyId = searchParams.get('companyId');

    const token = req.headers.get('authorization') || '';
    // Adjust the endpoint based on where you expect companyId (params or searchParams)
    const endpoint = `/api/buyer/${companyId}/users`;

    const backendRes = await fetch(`${BACKEND_API_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params; // Await params here
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    if (!userId) {
         return NextResponse.json({ error: 'Missing User ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/${companyId}/users/${userId}`, {
      method: 'PUT',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });

    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to update user' }, { status: backendRes.status });
    }

    console.log('Backend response status:', backendRes.status);
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
     console.error('PUT error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating user.' }, { status: error.response?.status || 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
     const { companyId } = await params; // Await params here
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const token = req.headers.get('authorization') || '';

    if (!userId) {
      return NextResponse.json({ error: 'Missing User ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/${companyId}/users/${userId}`, {
      method: 'DELETE',
      headers: {
        Authorization: token,
      },
    });

    if (!backendRes.ok) {
         console.error('Backend response error:', backendRes.statusText);
         return NextResponse.json({ error: 'Failed to delete user' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('DELETE error:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}

// Remove the empty export {}; as it's not necessary
// export {};