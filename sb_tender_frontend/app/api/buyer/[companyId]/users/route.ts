import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpCompany from '@/lib/models/sp_company';
import SpUserRole from '@/lib/models/sp_user_role';
import SpRole from '@/lib/models/sp_role';
import { requireBuyer } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';


// GET /api/admin/companies/:companyId/users - List company users
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== GET Company Users - Starting ===');
    await requireBuyer(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');

    console.log('Request parameters:', {
      companyId,
      page,
      limit,
      search,
      type,
      isActive
    });

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    // Verify company exists
    console.log('Step 1: Verifying company exists...');
    const company = await SpCompany.findById(companyId);
    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }
    console.log('Company found:', { _id: company._id, name: company.name, type: company.type });

    console.log('Step 2: Fetching SpUserRoles for company:', companyId);
    console.log('Converting companyId to ObjectId:', new mongoose.Types.ObjectId(companyId));

    // Debug: Check model and collection info
    console.log('SpUserRole model name:', SpUserRole.modelName);
    console.log('SpUserRole collection name:', SpUserRole.collection.name);

    // Let's first check if there are any SpUserRoles at all
    const allUserRoles = await SpUserRole.find({});
    console.log('Total SpUserRoles in database:', allUserRoles.length);
    console.log('Sample SpUserRoles:', allUserRoles.slice(0, 3).map(ur => ({
      _id: ur._id,
      userId: ur.userId,
      roleId: ur.roleId,
      companyId: ur.companyId,
      companyIdType: typeof ur.companyId,
      companyIdString: ur.companyId.toString()
    })));

    // Let's also check what collections exist
    const db = mongoose.connection.db;
    if (db) {
      const collections = await db.listCollections().toArray();
      console.log('Available collections:', collections.map(c => c.name));
    }

    // Find all SpUserRoles where companyId matches - convert string to ObjectId
    const companyUserRoles = await SpUserRole.find({ companyId: new mongoose.Types.ObjectId(companyId) });
    console.log('SpUserRoles found for company:', companyUserRoles.length);
    console.log('SpUserRoles details:', companyUserRoles.map(ur => ({
      userId: ur.userId,
      roleId: ur.roleId,
      companyId: ur.companyId
    })));

    // Let's also try a string query to see if that works
    const companyUserRolesString = await SpUserRole.find({ companyId: companyId });
    console.log('SpUserRoles found with string query:', companyUserRolesString.length);

    if (!companyUserRoles || companyUserRoles.length === 0) {
      console.log('No SpUserRoles found for company, returning empty result');
      return successResponse({
        users: [],
        company: {
          _id: company._id,
          name: company.name,
          type: company.type
        },
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      });
    }

    // Extract userIds and roleIds from companyRoles
    const userIds = companyUserRoles.map(role => role.userId);
    const companyRoleIds = companyUserRoles.map(role => role.roleId);

    console.log('Step 3: Extracted user IDs:', userIds.length, userIds);
    console.log('Step 3: Extracted role IDs:', companyRoleIds.length, companyRoleIds);

    // Build user query with search and filters
    console.log('Step 4: Building user query...');
    const userQuery: any = { _id: { $in: userIds } };

    if (search) {
      userQuery.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      userQuery.type = type;
    }

    if (isActive !== null && isActive !== undefined) {
      userQuery.isActive = isActive === 'true';
    }

    console.log('User query built:', JSON.stringify(userQuery, null, 2));

    // Find all users whose _id matches the userIds from companyRoles with pagination
    console.log('Step 5: Fetching users with pagination...');
    const skip = (page - 1) * limit;
    console.log('Pagination:', { page, limit, skip });

    const [companyUsers, total] = await Promise.all([
      User.find(userQuery)
        .select('-password') // Exclude password from response
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(userQuery)
    ]);

    console.log('Users fetched:', companyUsers.length, 'Total count:', total);
    console.log('User details:', companyUsers.map(u => ({
      _id: u._id,
      firstName: u.firstName,
      lastName: u.lastName,
      email: u.email,
      type: u.type,
      status: u.status
    })));

    // Find all roles for the company
    console.log('Step 6: Fetching company roles...');
    const companyRoles = await SpRole.find({ _id: { $in: companyRoleIds } });

    console.log('Company roles found:', companyRoles.length);
    console.log('Role details:', companyRoles.map(r => ({
      _id: r._id,
      name: r.name,
      context: r.context,
      type: r.type
    })));

    // Map roles to users - CONVERT TO STRINGS for proper comparison
    console.log('Step 7: Mapping roles to users...');
    const userRolesMap = companyUserRoles.reduce((map: any, userRole: any) => {
      const userIdStr = userRole.userId.toString();
      if (!map[userIdStr]) {
        map[userIdStr] = [];
      }
      map[userIdStr].push(userRole.roleId.toString());
      return map;
    }, {});

    console.log('User roles mapping:', userRolesMap);

    const companyUsersWithRoles = companyUsers.map(user => {
      const userRoles = companyRoles.filter(role =>
        userRolesMap[user._id.toString()]?.includes(role._id.toString())
      );

      console.log(`User ${user.email} has ${userRoles.length} roles:`, userRoles.map(r => r.name));

      return {
        ...user.toObject(),
        roles: userRoles
      };
    });

    console.log('Step 8: Final result preparation...');
    console.log('Total users with roles:', companyUsersWithRoles.length);

    const result = {
      users: companyUsersWithRoles,
      company: {
        _id: company._id,
        name: company.name,
        type: company.type
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

    console.log('=== GET Company Users - Success ===');
    return successResponse(result);

  } catch (error: any) {
    console.error('=== GET Company Users - Error ===');
    console.error('List company users error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company users', 500);
  }
}

// POST /api/admin/companies/:companyId/users - Create company user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('=== POST Create Company User - Starting ===');
    const adminUser = await requireBuyer(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();

    console.log('Request parameters:', { companyId });
    console.log('Admin user:', { userId: adminUser.userId, type: adminUser.type });
    console.log('Request body:', body);
    /*
    const {
      firstName,
      lastName,
      email,
      password,
      type,
      role = 'user',
      isActive = true
    } = body;

    // Validation
    const errors: string[] = [];
    if (!companyId) errors.push('Company ID is required');
    if (!firstName) errors.push('First name is required');
    if (!lastName) errors.push('Last name is required');
    if (!email) errors.push('Email is required');
    if (!password) errors.push('Password is required');
    if (!type) errors.push('User type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Verify company exists
    const company = await SpCompany.findById(companyId);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse('User with this email already exists', 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = new User({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      type,
      role,
      isActive,
      spCompanyId: companyId,
      createdBy: adminUser.userId
    });

    await user.save();

    // Populate and return (excluding password)
    await user.populate('spCompanyId', 'name type');
    await user.populate('createdBy', 'firstName lastName email');

    const userResponse = user.toObject();
    delete userResponse.password;

    return successResponse(userResponse, 201);
    */
   const {
       firstName,
       lastName,
       email,
       password, // Password is required for creation
       phone,
       type,     // 'admin', 'buyer', 'buyer'
       status,   // 'active', 'inactive', 'suspended' - optional, will use default if not provided
       roleIds,
       emailVerified, // Optional boolean, will use default
       phoneVerified, // Optional boolean, will use default
       otpVerified,   // Optional boolean, will use default
       mustUpdatePassword // Optional boolean, will use default
     } = body;
   
     console.log(body);
   
     // Basic validation
     console.log('Step 1: Validating required fields...');
     if (!firstName || !lastName || !email || !password || !phone || !type) {
       console.error('Missing required fields:', { firstName: !!firstName, lastName: !!lastName, email: !!email, password: !!password, phone: !!phone, type: !!type });
       return errorResponse('Missing required fields (firstName, lastName, email, password, phone, type)', 400);
     }
     console.log('Required fields validation passed');

     // Check if user with email already exists
     console.log('Step 2: Checking for existing email...');
     const existingUser = await User.findOne({ email });
     if (existingUser) {
       console.error('User with email already exists:', email);
       return errorResponse('User with this email already exists', 409);
     }
     console.log('Email check passed');

      // Check if user with phone already exists
     console.log('Step 3: Checking for existing phone...');
     const existingPhoneUser = await User.findOne({ phone });
     if (existingPhoneUser) {
       console.error('User with phone already exists:', phone);
       return errorResponse('User with this phone number already exists', 409);
     }
     console.log('Phone check passed');
   
     console.log('Step 4: Hashing password...');
     const salt = await bcrypt.genSalt(10);
     const hashedPassword = await bcrypt.hash(password, salt);
     console.log('Password hashed successfully');

     console.log('Step 5: Creating new user...');
     const newUser = new User({
       firstName,
       lastName,
       email,
       password: hashedPassword, // Save the hashed password
       phone,
       type,
       status: status || 'active', // Use provided status or default
       emailVerified: emailVerified ?? false, // Use provided or default
       phoneVerified: phoneVerified ?? false, // Use provided or default
       otpVerified: otpVerified ?? false,   // Use provided or default
       mustUpdatePassword: mustUpdatePassword ?? false, // Use provided or default
       createdBy: adminUser.userId,//req.user.userId, // Assuming auth middleware adds user ID to req.user
       // Mongoose Timestamps will handle createdAt and updatedAt
     });

     // Save the user to the database
     console.log('Step 6: Saving user to database...');
     await newUser.save();
     console.log('User saved successfully with ID:', newUser._id);
   
   
     console.log('Step 7: Ensuring default roles exist...');
     await ensureDefaultRolesExist(type, adminUser.userId);
     console.log('Ensure default roles check/creation finished.');

     console.log('Step 8: Finding roles for user type...');
     // const userTypeRoles = await SpRole.find({ context: 'buyer', _id: roleIds  });
     const userTypeRoles = await SpRole.find({ context: type });

     console.log(`Step 8a: Found ${userTypeRoles.length} roles with context '${type}'.`);
     console.log('Found roles objects:', userTypeRoles.map(r => ({ _id: r._id, name: r.name, context: r.context, type: r.type })));


     if (userTypeRoles.length === 0) {
         console.warn(`No ${type} roles found with context '${type}'. Cannot assign any roles.`);
         console.log('Attempting to create default roles...');
         await ensureDefaultRolesExist(type, adminUser.userId);
         console.log('Default roles creation attempt completed');
     } else {
         console.log(`Step 9: Mapping ${userTypeRoles.length} found ${type} roles to SpUserRole objects.`);
         const SpUserRoles = userTypeRoles.map(role => ({
           userId: newUser._id,
           companyId: new mongoose.Types.ObjectId(companyId),
           roleId: role._id,
         }));
         console.log('Step 9a: Prepared SpUserRoles array for insertion:', SpUserRoles);
         console.log('Number of SpUserRoles entries to insert:', SpUserRoles.length);

         console.log('Step 9b: Attempting to insert SpUserRoles into the database...');
         await SpUserRole.insertMany(SpUserRoles);
         console.log(`Step 9c: Successfully inserted ${SpUserRoles.length} SpUserRole documents.`);
     }

     console.log('Step 10: Preparing response...');
     const responseData = excludeSensitiveFields(newUser);
     console.log('User created successfully:', { userId: newUser._id, email: newUser.email, type: newUser.type });

     console.log('=== POST Create Company User - Success ===');
     return successResponse(responseData);

  } catch (error: any) {
    console.error('=== POST Create Company User - Error ===');
    console.error('Create company user error:', error);
    console.error('Error stack:', error.stack);

    // Handle potential duplicate key errors for email/phone if Mongoose schema doesn't catch it first
    if (error.code === 11000) {
        console.error('Duplicate key error detected:', error.message);
        return errorResponse('Duplicate field value entered', 409);
    }

    return errorResponse(error.message || 'Failed to create company user', 500);
  }
}

// Note: PUT and DELETE operations for individual users are handled in [userId]/route.ts

const ensureDefaultRolesExist = async (context: string, createdBy: string) => {
  const existingRoles = await SpRole.find({ context });
  const existingKeys = new Set(existingRoles.map(r => r.type));

  const defaultRoles = [
    { name: 'Registration', type: 'registration', permissions: ['r', 'w'] },
    { name: 'Prequalification', type: 'prequalification', permissions: ['r', 'w'] },
    { name: 'Settings', type: 'settings', permissions: ['r', 'w'] },
    { name: 'RFQ', type: 'rfq', permissions: ['r', 'w'] },
    { name: 'Tender', type: 'tender', permissions: ['r', 'w'] }
  ];

  const rolesToCreate = defaultRoles.filter(role => !existingKeys.has(role.type));

  if (rolesToCreate.length > 0) {
    const newRoles = rolesToCreate.map(role => ({
      ...role,
      context,
      description: `Default ${role.name} role for ${context}`,
      createdBy
    }));

    await SpRole.insertMany(newRoles);
    console.log(`Created ${newRoles.length} default roles for context: ${context}`);
  }
};

// Helper function to exclude sensitive fields from the user object
const excludeSensitiveFields = (user: any) => {
    if (!user) return null;
    const userObject = user.toObject ? user.toObject() : user; // Convert Mongoose doc to plain object
    delete userObject.password;
    delete userObject.otp;
    delete userObject.__v; // Mongoose version key
    return userObject;
};
