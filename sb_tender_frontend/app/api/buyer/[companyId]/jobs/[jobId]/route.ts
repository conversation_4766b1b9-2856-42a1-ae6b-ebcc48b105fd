import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobs/:jobId - Get job
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    User;

    // Find the job
    const job = await SpJob.findOne({ _id: jobId, spCompanyId: companyId })
      .populate('spCompanyId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Get job categories count
    const categoriesCount = await SpJobCategory.countDocuments({ spJobId: jobId });

    return successResponse({
      job,
      categoriesCount
    });

  } catch (error: any) {
    console.error('Error fetching buyer job:', error);
    return errorResponse(error.message || 'Failed to fetch buyer job', 500);
  }
}

// PUT /api/buyer/:companyId/jobs/:jobId - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;
    const body = await request.json();

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    const { title, description, contract, starts, ends, location, categoryPrice } = body;

    // Find and update the job
    const job = await SpJob.findOneAndUpdate(
      { _id: jobId, spCompanyId: companyId },
      {
        title,
        description,
        contract,
        starts: starts ? new Date(starts) : undefined,
        ends: ends ? new Date(ends) : undefined,
        location,
        categoryPrice,
        updatedBy: user.userId,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Update all related job categories with new job information
    await SpJobCategory.updateMany(
      { spJobId: jobId },
      {
        price: categoryPrice,
        starts: job.starts,
        ends: job.ends,
        location: job.location,
        updatedBy: user.userId,
        updatedAt: new Date()
      }
    );

    // Populate and return
    await job.populate('spCompanyId', 'name type');
    await job.populate('createdBy', 'firstName lastName email');
    await job.populate('updatedBy', 'firstName lastName email');

    return successResponse(job, 'Job updated successfully');

  } catch (error: any) {
    console.error('Error updating buyer job:', error);
    return errorResponse(error.message || 'Failed to update buyer job', 500);
  }
}

// DELETE /api/buyer/:companyId/jobs/:jobId - Delete job
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Check if job exists and belongs to company
    const job = await SpJob.findOne({ _id: jobId, spCompanyId: companyId });
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Delete all job categories first
    await SpJobCategory.deleteMany({ spJobId: jobId });

    // Delete the job
    await SpJob.findByIdAndDelete(jobId);

    return successResponse(
      { message: 'Job and associated categories deleted successfully' },
      'Job deleted successfully'
    );

  } catch (error: any) {
    console.error('Error deleting buyer job:', error);
    return errorResponse(error.message || 'Failed to delete buyer job', 500);
  }
}
