import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobs/:jobId/categories/:categoryId - Get category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string; categoryId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId, categoryId } = await params;

    if (!companyId || !jobId || !categoryId) {
      return errorResponse('Company ID, Job ID, and Category ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpJob;
    SpCompany;
    SpBuyerCategory;
    User;

    // Find the category
    const category = await SpJobCategory.findOne({ 
      _id: categoryId, 
      spJobId: jobId,
      spCompanyId: companyId 
    })
      .populate('spJobId', 'title description')
      .populate('spCompanyId', 'name type')
      .populate('spBuyerCategoryId', 'name type')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!category) {
      return errorResponse('Category not found', 404);
    }

    // Transform the data to include companyName directly (for compatibility)
    const categoryWithCompany = {
      ...category.toObject(),
      companyName: category.spCompanyId?.name || 'Unknown Company'
    };

    return successResponse(categoryWithCompany);

  } catch (error: any) {
    console.error('Error fetching job category:', error);
    return errorResponse(error.message || 'Failed to fetch job category', 500);
  }
}

// PUT /api/buyer/:companyId/jobs/:jobId/categories/:categoryId - Update category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string; categoryId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId, categoryId } = await params;
    const body = await request.json();

    if (!companyId || !jobId || !categoryId) {
      return errorResponse('Company ID, Job ID, and Category ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Get the actual updates - they might be nested inside jobCategory
    const updateData = body.jobCategory || body;
    
    console.log('Job Category Update data structure:', Object.keys(updateData));

    // Find the job category
    const jobCategory = await SpJobCategory.findOne({ 
      _id: categoryId, 
      spJobId: jobId,
      spCompanyId: companyId 
    });

    if (!jobCategory) {
      return errorResponse('Job Category not found', 404);
    }

    // Update the job category with provided data
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined && key !== '_id') {
        jobCategory[key] = updateData[key];
      }
    });

    // Set updatedBy and updatedAt
    jobCategory.updatedBy = user.userId;
    jobCategory.updatedAt = new Date();

    // Save the updated job category
    await jobCategory.save();

    // Populate and return
    await jobCategory.populate('spJobId', 'title description');
    await jobCategory.populate('spCompanyId', 'name type');
    await jobCategory.populate('spBuyerCategoryId', 'name type');
    await jobCategory.populate('createdBy', 'firstName lastName email');
    await jobCategory.populate('updatedBy', 'firstName lastName email');

    // Transform the data to include companyName directly (for compatibility)
    const categoryWithCompany = {
      ...jobCategory.toObject(),
      companyName: jobCategory.spCompanyId?.name || 'Unknown Company'
    };

    return successResponse(categoryWithCompany, 'Job Category updated successfully');

  } catch (error: any) {
    console.error('Error updating job category:', error);
    return errorResponse(error.message || 'Failed to update job category', 500);
  }
}
