import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobs/:jobId/categories - Job categories
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpJob;
    SpCompany;
    SpBuyerCategory;
    User;

    // Verify job exists and belongs to company
    const job = await SpJob.findOne({ _id: jobId, spCompanyId: companyId });
    if (!job) {
      return errorResponse('Job not found', 404);
    }

    // Build query
    let query: any = { spJobId: jobId };
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { ref: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [categories, total] = await Promise.all([
      SpJobCategory.find(query)
        .populate('spJobId', 'title description')
        .populate('spCompanyId', 'name type')
        .populate('spBuyerCategoryId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJobCategory.countDocuments(query)
    ]);

    // Transform the data to include companyName directly (for compatibility)
    const categoriesWithCompany = categories.map(category => ({
      ...category.toObject(),
      companyName: category.spCompanyId?.name || 'Unknown Company'
    }));

    return successResponse({
      categories: categoriesWithCompany,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        status: job.status
      },
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching job categories:', error);
    return errorResponse(error.message || 'Failed to fetch job categories', 500);
  }
}
