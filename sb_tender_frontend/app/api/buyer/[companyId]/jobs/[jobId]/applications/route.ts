import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpJob from '@/lib/models/sp_job';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobs/:jobId/applications - Job applications
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; jobId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId, jobId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const categoryId = searchParams.get('categoryId'); // Optional filter by specific category
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!companyId || !jobId) {
      return errorResponse('Company ID and Job ID are required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpJob;
    SpCompany;
    User;

    // Verify the job belongs to this company
    const job = await SpJob.findOne({
      _id: jobId,
      spCompanyId: companyId
    });

    if (!job) {
      return errorResponse('Job not found or access denied', 404);
    }

    // Get all categories for this job
    let categoryQuery: any = { spJobId: jobId };
    if (categoryId) {
      categoryQuery._id = categoryId;
    }

    const jobCategories = await SpJobCategory.find(categoryQuery).select('_id title type');
    const categoryIds = jobCategories.map(cat => cat._id);

    if (categoryIds.length === 0) {
      return successResponse({
        applications: [],
        job: {
          _id: job._id,
          title: job.title,
          description: job.description,
          status: job.status
        },
        categories: [],
        statistics: {
          totalApplications: 0,
          pendingApplications: 0,
          approvedApplications: 0,
          rejectedApplications: 0,
          averageScore: 0
        },
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0
        }
      }, 'No applications found for this job');
    }

    // Build query for applications to this job's categories
    let query: any = {
      spJobCategoryId: { $in: categoryIds }
    };
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { ref: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }

    // Build sort object
    const sortObj: any = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [applications, total] = await Promise.all([
      SpApplication.find(query)
        .populate('spSupplierCompanyId', 'name logoUrl address registrationNumber phone email')
        .populate('spJobCategoryId', 'title type price ref')
        .populate('submittedBy', 'firstName lastName email')
        .populate('reviewer', 'firstName lastName email')
        .populate('reviewedBy', 'firstName lastName email')
        .select('title ref status systemScore complianceScore totalScore submittedAt reviewDate reviewStatus spSupplierCompanyId spJobCategoryId submittedBy reviewer reviewedBy type')
        .sort(sortObj)
        .skip(skip)
        .limit(limit),
      SpApplication.countDocuments(query)
    ]);

    // Calculate job-specific application statistics
    const stats = await SpApplication.aggregate([
      { $match: { spJobCategoryId: { $in: categoryIds } } },
      {
        $group: {
          _id: null,
          totalApplications: { $sum: 1 },
          pendingApplications: {
            $sum: { $cond: [{ $in: ['$status', ['submitted', 'under_review']] }, 1, 0] }
          },
          approvedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedApplications: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          averageScore: { $avg: '$totalScore' },
          highestScore: { $max: '$totalScore' },
          lowestScore: { $min: '$totalScore' }
        }
      }
    ]);

    // Get applications breakdown by category
    const categoryBreakdown = await SpApplication.aggregate([
      { $match: { spJobCategoryId: { $in: categoryIds } } },
      {
        $group: {
          _id: '$spJobCategoryId',
          totalApplications: { $sum: 1 },
          averageScore: { $avg: '$totalScore' },
          approvedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          }
        }
      },
      {
        $lookup: {
          from: 'spjobcategories',
          localField: '_id',
          foreignField: '_id',
          as: 'category'
        }
      },
      {
        $unwind: '$category'
      },
      {
        $project: {
          categoryId: '$_id',
          categoryTitle: '$category.title',
          categoryType: '$category.type',
          totalApplications: 1,
          averageScore: { $round: ['$averageScore', 2] },
          approvedCount: 1,
          approvalRate: {
            $round: [
              { $multiply: [{ $divide: ['$approvedCount', '$totalApplications'] }, 100] },
              2
            ]
          }
        }
      },
      {
        $sort: { totalApplications: -1 }
      }
    ]);

    // Get recent applications (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentApplicationsCount = await SpApplication.countDocuments({
      spJobCategoryId: { $in: categoryIds },
      createdAt: { $gte: sevenDaysAgo }
    });

    return successResponse({
      applications,
      job: {
        _id: job._id,
        title: job.title,
        description: job.description,
        status: job.status,
        starts: job.starts,
        ends: job.ends,
        location: job.location
      },
      categories: jobCategories,
      statistics: stats[0] ? {
        ...stats[0],
        averageScore: Math.round((stats[0].averageScore || 0) * 100) / 100,
        recentApplicationsCount
      } : {
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        recentApplicationsCount: 0
      },
      categoryBreakdown,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching job applications:', error);
    return errorResponse(error.message || 'Failed to fetch job applications', 500);
  }
}
