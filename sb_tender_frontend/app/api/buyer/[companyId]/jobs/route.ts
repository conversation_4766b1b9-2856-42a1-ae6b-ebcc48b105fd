import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpBuyerCategory from '@/lib/models/sp_buyer_category';
import SpCompany from '@/lib/models/sp_company';
import User from '@/lib/models/sp_user';
import { requireBuyerAccess } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

export const dynamic = 'force-dynamic';

// GET /api/buyer/:companyId/jobs - List jobs
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    // Ensure models are registered
    SpCompany;
    User;

    // Build query
    let query: any = { spCompanyId: companyId };

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }

    if (status) {
      query.status = status;
    }

    if (type) {
      query.type = type;
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [jobs, total] = await Promise.all([
      SpJob.find(query)
        .populate('spCompanyId', 'name type')
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpJob.countDocuments(query)
    ]);

    return successResponse({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching buyer jobs:', error);
    return errorResponse(error.message || 'Failed to fetch buyer jobs', 500);
  }
}

// POST /api/buyer/:companyId/jobs - Create job
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await requireBuyerAccess(request);
    await connectToDatabase();

    const { companyId } = await params;
    const body = await request.json();

    if (!companyId) {
      return errorResponse('Company ID is required', 400);
    }

    // Verify user has access to this company
    if (user.companyId !== companyId && user.type !== 'admin') {
      return errorResponse('Access denied to this company', 403);
    }

    const {
      title,
      description,
      contract,
      starts,
      ends,
      location,
      categoryPrice,
      selectedJobCategories,
      type
    } = body;

    // Validation
    const errors: string[] = [];
    if (!title) errors.push('Title is required');
    if (!description) errors.push('Description is required');
    if (!contract) errors.push('Contract type is required');
    if (!starts) errors.push('Start date is required');
    if (!ends) errors.push('End date is required');
    if (!location) errors.push('Location is required');
    if (!categoryPrice) errors.push('Category price is required');
    if (!type) errors.push('Job type is required');

    if (errors.length > 0) {
      return errorResponse(`Validation failed: ${errors.join(', ')}`, 400);
    }

    // Create job
    const spJob = new SpJob({
      title,
      description,
      contract,
      starts: new Date(starts),
      ends: new Date(ends),
      location,
      type,
      status: 'draft',
      categoryPrice,
      spCompanyId: companyId,
      createdBy: user.userId,
    });

    await spJob.save();

    // Create job categories if provided
    if (selectedJobCategories && selectedJobCategories.length > 0) {
      const jobCategories = [];

      for (const categoryData of selectedJobCategories) {
        // Get buyer category details
        const buyerCategory = await SpBuyerCategory.findById(categoryData.spBuyerCategoryId);
        if (!buyerCategory) {
          console.warn(`Buyer category not found: ${categoryData.spBuyerCategoryId}`);
          continue;
        }

        const jobCategory = new SpJobCategory({
          title: categoryData.title || buyerCategory.name,
          ref: `${spJob.title}-${buyerCategory.name}-${Date.now()}`,
          description: categoryData.description || buyerCategory.description,
          status: 'draft',
          price: categoryData.price || categoryPrice,
          starts: spJob.starts,
          ends: spJob.ends,
          location: spJob.location,
          type: categoryData.type || type,
          spJobId: spJob._id,
          spCompanyId: companyId,
          spBuyerCategoryId: categoryData.spBuyerCategoryId,
          spBuyerCategoryTemplateId: categoryData.spBuyerCategoryTemplateId,
          passMark: categoryData.passMark || 0,
          fields: categoryData.fields || [],
          createdBy: user.userId
        });

        await jobCategory.save();
        jobCategories.push(jobCategory);
      }

      console.log(`Created ${jobCategories.length} job categories for job ${spJob._id}`);
    }

    // Populate and return
    await spJob.populate('spCompanyId', 'name type');
    await spJob.populate('createdBy', 'firstName lastName email');

    return successResponse(spJob, 'Job created successfully', 201);

  } catch (error: any) {
    console.error('Error creating buyer job:', error);
    return errorResponse(error.message || 'Failed to create buyer job', 500);
  }
}
