import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const body = await request.json();
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const companyId = searchParams.get('companyId');
    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating job.' }, { status: 401 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const companyId = searchParams.get('companyId');
    console.log('Company ID', companyId);
    if(!companyId){
      return NextResponse.json({ error: 'Failed to fetch jobs. Company not found' }, { status: 500 });

    }
    const token = req.headers.get('authorization') || '';
    console.log('Token:', token);
    const endpoint = `/api/buyer/${companyId}/jobs`;
  
    console.log('Endpoint:', BACKEND_API_URL + endpoint);
    const backendRes = await fetch(`${BACKEND_API_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch jobs' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch jobs' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    console.log('server comp reg', body);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/jobs/${jobId}`, {
      method: 'PUT',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch jobs' }, { status: backendRes.status });
    }
    console.log('Backend response status:', backendRes.status);
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating buyer.' }, { status: 401 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    const token = req.headers.get('authorization') || '';

    if (!id) {
      return NextResponse.json({ error: 'Missing Job ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/buyer/jobs/${id}`, {
      method: 'DELETE',
      headers: {
        Authorization: token,
      },
    });

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('DELETE error:', error);
    return NextResponse.json({ error: 'Failed to delete company' }, { status: 500 });
  }
}
export {};
