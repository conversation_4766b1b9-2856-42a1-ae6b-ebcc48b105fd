import { NextRequest, NextResponse } from 'next/server';
export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(req: NextRequest) {
    try {
    // Parse request body
    const requestData = await req.json();
    console.log('Buyer setup request data:', requestData);
    
    // Validate required fields
    const { email, password, companyId } = requestData;
    if (!email || !password || !companyId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Forward request to backend API
    const endpoint = `/api/setup/buyer`;
    console.log('Forwarding to backend endpoint:', BACKEND_API_URL + endpoint);
    
    const backendRes = await fetch(`${BACKEND_API_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });
    
    // Handle backend response
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      const errorData = await backendRes.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || 'Failed to set up buyer account' }, 
        { status: backendRes.status }
      );
    }

    // Return successful response
    const result = await backendRes.json();
    return NextResponse.json(result, { status: 200 });
    
  } catch (error) {
    console.error('Buyer setup API error:', error);
    return NextResponse.json({ error: 'Server error during buyer setup' }, { status: 500 });
  }
}