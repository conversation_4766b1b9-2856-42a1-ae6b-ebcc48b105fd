import { NextRequest, NextResponse } from 'next/server';
export const dynamic = 'force-dynamic';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import SpRole from '@/lib/models/sp_role';
import SpUser<PERSON>ole from '@/lib/models/sp_user_role';
import SpCompany from '@/lib/models/sp_company';
import { getUserCompanyRoles } from '@/lib/utils/user';
//import NotificationHelper from '@/lib/utils/notificationHelper';
import generateToken from '@/lib/utils/token';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(req: NextRequest) {
  
    // Parse request body
    const requestData = await req.json();
    console.log('Buyer setup request data:', requestData);
    
    // Validate required fields
    const { email, password, companyId } = requestData;
    if (!email || !password || !companyId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }


      try {
        // Step 1: Verify company exists with the provided ID
        console.log(`Checking if company exists with ID: ${companyId}`);
        const company = await SpCompany.findById(companyId);
        
        if (!company) {
          console.error(`Company with ID ${companyId} not found`);
          return NextResponse.json({ error: 'Company not found' });
        }
        console.log(`Company found: ${company.name}`);
    
        // Step 2: Check if the email is associated with the company
        if (company.email !== email) {
          console.error(`Email ${email} is not associated with company ${companyId}`);
          return NextResponse.json({ error: 'Email is not associated with this company' });
        }
        console.log('Email verification passed');
    
        // Step 3: Check if user already exists with this email
        console.log(`Checking if user exists with email: ${email}`);
        let user = await User.findOne({ email });
        let isNewUser = false;
    
        if (user) {
          console.log(`User found with ID: ${user._id}`);
          if (!user.password) {
            // User exists but doesn't have a password yet (e.g., was pre-created)
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);
            
            user.password = hashedPassword;
            await user.save();
            console.log(`Updated password for existing user ID: ${user._id}`);
          }
        } else {
          console.log('User not found, creating new user');
          isNewUser = true;
          
          // Hash the password
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);
    
          // Create new user
          user = new User({
            firstName: company.contactPerson?.split(' ')[0] || 'Buyer',
            lastName: company.contactPerson?.split(' ')[1] || 'User',
            email,
            password: hashedPassword,
            phone: company.phone || '',
            type: 'buyer'
          });
          
          await user.save();
          console.log(`New user created with ID: ${user._id}`);
        }
    
    
        // Step 4: Get or ensure buyer roles exist
        console.log('Checking for buyer roles');
        const buyerRoles = await SpRole.find({ context: 'buyer' });
        
        if (buyerRoles.length === 0) {
          console.log('No buyer roles found, creating default buyer roles');
          // Define default buyer role types and permissions
          const BUYER_TYPES = ['registration', 'prequalification', 'settings', 'rfq', 'tender'];
          const DEFAULT_PERMISSIONS = ['r', 'w', 'd'];
          
          // Create default buyer roles
          const newRoles = BUYER_TYPES.map(type => ({
            name: `BUYER - ${type}`,
            context: 'buyer',
            type,
            permissions: DEFAULT_PERMISSIONS,
            description: `Auto-created role for buyer - ${type}`,
            createdBy: user._id,
          }));
          
          await SpRole.insertMany(newRoles);
          console.log(`Created ${newRoles.length} default buyer roles`);
          
          // Refresh buyer roles list
          const freshBuyerRoles = await SpRole.find({ context: 'buyer' });
          buyerRoles.push(...freshBuyerRoles);
        }
    
        // Step 5: Check if user is already associated with the company
        console.log(`Checking if user ${user._id} is already associated with company ${companyId}`);
        const existingUserRole = await SpUserRole.findOne({ 
          userId: user._id,
          companyId: company._id
        });
    
        if (!existingUserRole) {
          console.log('No existing user-company association found, creating new associations');
          
          // Associate user with company by creating user roles
          const userRolesToCreate = buyerRoles.map(role => ({
            userId: user._id,
            companyId: company._id,
            roleId: role._id,
          }));
          
          await SpUserRole.insertMany(userRolesToCreate);
          console.log(`Created ${userRolesToCreate.length} role associations for user`);
        } else {
          console.log('User is already associated with this company');
        }
    
        // Send welcome notification using helper
        try {
         // await NotificationHelper.sendWelcomeEmail(user, company._id);
          console.log('Welcome email sent successfully');
        } catch (notificationError) {
          console.error('Failed to send welcome email:', notificationError);
          // Don't fail the registration if notification fails
        }
    
        // Step 6: Generate token and prepare response
        console.log('Generating JWT token');
        const token = generateToken(user._id.toString());
        //const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '72h' });
        
        // Get complete user data with company/roles
        console.log('Fetching complete user data with companies/roles');
        const userDataWithRoles = await getUserCompanyRoles(user._id);
        
        if (!userDataWithRoles || !userDataWithRoles.companies || userDataWithRoles.companies.length === 0) {
          console.error(`Could not fetch complete company/role data for user ID: ${user._id}`);
          // Provide minimal user info response
          return NextResponse.json({
            token,
            user: {
              _id: user._id,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              type: user.type,
              companies: [],
            },
            message: 'Buyer setup successful, but could not retrieve company/role details.'
          });
        }
    
        // Return successful response with token and user data
        console.log('Buyer setup completed successfully');
        return NextResponse.json({
          token,
          user: userDataWithRoles,
          message: isNewUser ? 'Buyer account created successfully' : 'Buyer account updated successfully'
        });
    
      } catch (error) {
        console.error('Error during buyer setup:', error);
        
        // Handle duplicate key errors
        if (error.code === 11000) {
          console.error('Caught Mongoose duplicate key error (code 11000).');
          const field = Object.keys(error.keyValue)[0];
          const value = error.keyValue[field];
          let message = `Duplicate value for ${field}: ${value}`;
          return NextResponse.json({ error: message }, { status: 409 });
        }
        
        // Handle specific error status if available
        if (error.status) {
          return NextResponse.json({ error: error.message || 'An error occurred' }, { status: error.status });
        }
        
        // Generic server error
        res.status(500).json({ error: 'Server error during buyer setup' });
      } finally {
        console.log('------------------------------------');
        console.log('Buyer setup endpoint processing finished.');
      }


    
   

}