import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import mongoose from 'mongoose';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';


// GET /api/admin/companies/:id - Get company by ID
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
 
        try {
            const body = await request.json();
            console.log("Received webhook data:", body);
           // const callbackData = req.body.Body.stkCallback;
            const callbackData = body.Body.stkCallback;
            console.log("Callback data:", callbackData);
            const payment = await SpPayment.findOne({
                reference: callbackData.MerchantRequestID,
            });
    
            if (!payment) {
                console.log("Payment not found for MerchantRequestID:", callbackData.MerchantRequestID);
                return errorResponse("Payment not found", 400);
            }
    
            const pendingPaymentAmount = payment.amount;
            console.log('Pending amount' + pendingPaymentAmount);
            const resultCode = callbackData.ResultCode;
    
            let responseMessage = "Payment processed";
            let responseStatus = 200;
    
            if (payment.status === "success") {
                console.log("Duplicate payment detected for MerchantRequestID:", callbackData.MerchantRequestID);
                const paymentOrder = await SpOrder.findOne({
                    _id: payment.spOrderId,
                });
        
                if (!paymentOrder) {
                    console.log("Order not found for payment:", payment._id);
                    return errorResponse("Payment updated but Order not found" , 400);
                }
                paymentOrder.status = 'paid';
                await paymentOrder.save();
                payment.balance = 0;
                console.log("Order completed for payment:", payment._id);
                
                const result = await createApplicationsFromOrder(paymentOrder);
      
                if (!result.success) {
                    responseMessage = "Payment processed but order update failed. Please contact support.";
                    responseStatus = 400;
                }
            }
    
            if (resultCode === 0) {
                console.log("Payment success for MerchantRequestID:", callbackData.MerchantRequestID);
                payment.status = "success";
                const mpesaReceipt = callbackData.CallbackMetadata.Item.find((item) => item.Name === "MpesaReceiptNumber");
                const amount = callbackData.CallbackMetadata.Item.find((item) => item.Name === "Amount");
                const phoneNumber = callbackData.CallbackMetadata.Item.find((item) => item.Name === "PhoneNumber");
    
                if (mpesaReceipt) {
                    payment.transID = mpesaReceipt.Value;
                    console.log("MpesaReceiptNumber:", mpesaReceipt.Value);
                }
    
                if (amount) {
                    payment.amount = amount.Value;
                    console.log("Amount:", amount.Value);
                }
    
                if (phoneNumber) {
                    payment.authorization = phoneNumber.Value;
                    console.log("PhoneNumber:", phoneNumber.Value);
                }
            } else {
                console.log("Payment failed for MerchantRequestID:", callbackData.MerchantRequestID, " with ResultCode:", resultCode);
                payment.status = "failed";
            }
    
           
            payment.transactionType = "Mpesa";
            payment.respDesc = callbackData.ResultDesc;
            payment.currency = "KES";
            payment.channel = "mobile-money";
            payment.balance = payment.amount;
            await payment.save();
            console.log(payment);
            console.log("Saving payment data for MerchantRequestID:", callbackData.MerchantRequestID);
    
            if (payment.status === "success" && Number(pendingPaymentAmount) === Number(payment.amount)) {
                console.log("Payment matches pending order amount, updating the order for MerchantRequestID:", callbackData.MerchantRequestID);
                const order = await SpOrder.findOne({
                    _id: payment.spOrderId,
                });
        
                if (!order) {
                    console.log("Order not found for payment:", payment._id);
                    return errorResponse("Payment updated but Order not found" , 400);
                }
                order.status = 'paid';
                await order.save();
                payment.balance = 0;
                await payment.save();
    
                const result = await createApplicationsFromOrder(order);
      
                if (!result.success) {
                    console.log("Payment processed but order update failed. Please contact support.");
                    responseMessage = "Payment processed but order update failed. Please contact support.";
                    responseStatus = 400;
                }    
    
                console.log("Order completed for payment:", payment._id);
            } else {
                console.log("No order updated for payment:", payment._id);
            }
    
    
            // await sendNotification(order); // Commented out as order variable is not defined in this scope
    
            // Only send the response once at the end
            return successResponse( responseMessage, 'Payment processed', responseStatus);
    
        } catch (error) {
            console.error("Error processing webhook for stkCallback:", error);
            return errorResponse( "Error processing payment", 500);
        }
    }
    
    // const sendNotification = async (order) => {
    //       try {
    //         const user = await User.findById(order.createdBy).exec();
    //         await NotificationHelper.sendOrderConfirmation(order, user, order.SpCompanyId);
    //       } catch (notificationError) {
    //         console.error('Failed to send order notification:', notificationError);
    //       }
    // }

  /**
   * Creates applications for each order item in an order
   * @param {string} userId - ID of the user creating the applications
   * @param {object} order - The order document containing order items
   * @returns {object} Result containing created applications and any errors
   */
  export const createApplicationsFromOrder = async (order) => {
    try {
      // Validate inputs
  
      if (!order || !order.orderItems || !Array.isArray(order.orderItems)) {
        console.error('Invalid order or order items missing');
        throw new Error('Invalid order or order items missing');
      }
      const userId = order.createdBy;

      console.info(`Starting application creation from order ${order._id} for user ${userId}`);
  
      const results = {
        success: [],
        skipped: [],
        errors: []
      };
  
      // Process each order item
      for (const [index, item] of order.orderItems.entries()) {
        try {
          console.debug(`Processing order item ${index + 1}/${order.orderItems.length} - category: ${item.spJobCategoryId}`);
  
          // Validate order item
          if (!mongoose.Types.ObjectId.isValid(item.spJobCategoryId)) {
            console.warn(`Skipping order item ${index} - invalid job category ID`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Invalid job category ID',
              details: item
            });
            continue;
          }
  
          // Check for existing application
          const existingApplication = await SpApplication.findOne({
            spSupplierCompanyId: order.spCompanyId,
            spBuyerCompanyId: item.buyerCompanyId,
            spJobCategoryId: item.spJobCategoryId
        });
  
          if (existingApplication) {
            console.warn(`Skipping order item ${index} - application already exists for this supplier and job category`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Application already exists for this supplier and job category',
              existingApplicationId: existingApplication._id,
              details: item
            });
            continue;
          }
  
          // Fetch the job category
          const category = await SpJobCategory.findById(item.spJobCategoryId)
            .populate('spJobId')
            .populate('spCompanyId');
  
          if (!category) {
            console.warn(`Skipping order item ${index} - job category not found`);
            results.skipped.push({
              orderItemIndex: index,
              reason: 'Job category not found',
              details: item
            });
            continue;
          }
  
          // Prepare application fields from category fields
          const applicationFields = category.fields.map(field => ({
            name: field.name,
            value: null,
            score: 0,
            comment: '',
            reviewerComment: '',
            documents: [],
            group: field.group,
            subGroup: field.subGroup,
            isParent: field.isParent,
            title: field.title,
            order: field.order,
            placeholder: field.placeholder,
            description: field.description,
            helpText: field.helpText,
            tooltip: field.tooltip,
            isVisible: field.isVisible,
            isEditable: field.isEditable,
            type: field.type,
            label: field.label,
            options: field.options,
            optionScore: field.optionScore,
            isRequired: field.isRequired,
            isScoreable: field.isScoreable,
            maxScore: field.maxScore,
            complianceScore: 0,
            requiresDd: field.requiresDd,
            validations: field.validations,
            maxComplianceScore: field.maxComplianceScore
          }));
  
          // Create application document
          const application = new SpApplication({
            title: category.title,
            ref: `APP-${Date.now()}-${index}`,
            description: category.description,
            spJobId: category.spJobId._id,
            spJobCategoryId: category._id,
            spOrder: order._id,
            spBuyerCompanyId: item.buyerCompanyId,
            spSupplierCompanyId: order.spCompanyId,
            starts: item.starts,
            ends: item.ends,
            location: item.location,
            type: item.type,
            fields: applicationFields,
            status: 'draft',
            submittedBy: userId,
            reviewerNotes: '',
            systemScore: 0,
            complianceScore: 0,
            totalScore: 0,
            passMark: category.passMark || 70,
            createdBy: userId
          });
  
          // Save the application
          const savedApplication = await application.save();
          console.info(`Created application ${savedApplication._id} for order item ${index}`);
  
          results.success.push({
            orderItemIndex: index,
            applicationId: savedApplication._id,
            applicationRef: savedApplication.ref
          });
  
        } catch (itemError) {
          console.error(`Error processing order item ${index}: ${itemError.message}`, {
            stack: itemError.stack,
            orderItem: item
          });
  
          results.errors.push({
            orderItemIndex: index,
            error: itemError.message,
            details: item
          });
        }
      }
  
      console.info(`Completed processing order ${order._id}. Results:`, {
        created: results.success.length,
        skipped: results.skipped.length,
        errors: results.errors.length
      });
  
      return successResponse(results,`Processed ${order.orderItems.length} order items`, 200);
      
  
    } catch (error) {
      console.error(`Failed to create applications from order: ${error.message}`, {
        stack: error.stack,
        userId: order?.createdBy,
        orderId: order?._id
      });
  
      return errorResponse(error.message || 'Failed to create applications from order', 500);
       
    }
  };