import { NextResponse, NextRequest } from 'next/server';
import axios from 'axios';
import { connectToDatabase } from '@/lib/mongodb';
import SpPayment from '@/lib/models/sp_payment';
import SpOrder from '@/lib/models/sp_order';
import SpJobCategory from '@/lib/models/sp_job_category';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';


export async function POST(request: NextRequest) {
  try {


        const user = await requireSupplier(request);
        await connectToDatabase();
    
        const body = await request.json();

          console.log('Request body', body);
          const { amount, mobile, items, companyId } = body;
          console.log(amount, mobile, items, companyId );
          
          const formattedMobile = formatPhoneNumber(mobile);
          const userId = user.userId;
  
          // Validate input
          if (!items || items.length === 0) {
              return errorResponse("Cart items are required",);
          }
  
          if (!formattedMobile || formattedMobile.length < 12) {
              return errorResponse("Valid mobile number is required 25472xxxxxxx", 400);
          }
  
          // Verify each item exists in job categories and calculate total
          const orderItems = [];
          let calculatedTotal = 0;
  
          for (const item of items) {
              const category = await SpJobCategory.findById(item.id)
                  .populate('spCompanyId') // Populate company info
                  .populate('spJobId'); // Populate job info
  
              if (!category) {
                  return errorResponse('Category not found: ' + item.id, 400 );
              }
  
              // Verify price matches
              if (item.price !== category.price) {
                  return errorResponse(`Price mismatch for category: ${category.title}`, 400);
                
              }
  
              orderItems.push({
                  title: category.title,
                  price: category.price,
                  starts: category.starts,
                  ends: category.ends,
                  location: category.location,
                  type: category.type,
                  supplierCompanyId: companyId, // Assuming user is the supplier
                  buyerCompanyId: category.spCompanyId,
                  spJobCategoryId: category._id
              });
  
              calculatedTotal += category.price;
          }
  
          // Verify total amount matches calculated total
          if (amount !== calculatedTotal) {
              return errorResponse(`Amount mismatch. Expected: ${calculatedTotal}, Received: ${amount}`
              , 400);
          }
  
          // Create the order first
          const order = new SpOrder({
              spCompanyId: companyId,
              orderItems,
              status: 'pending',
              totalAmount: calculatedTotal,
              createdBy: userId
          });
  
          await order.save();
          console.log('Order created');
          // Create payment record
          const payment = new SpPayment({
              amount: calculatedTotal,
              mobile: formattedMobile,
              status: 'pending',
              spOrderId: order._id,
              //reference: order._id,
              userId,
              paymentMethod: 'mpesa',
              reason: 'category'
          });
  
          await payment.save();
  
          console.log('Paymeent created');
  
          // Initiate M-Pesa STK push
          const stkResponse = await mpesaPaymentRequest(
              formattedMobile,
              calculatedTotal,
              `${order._id}`
          );
  
          console.log('STK response', stkResponse.data);
  
  
          if (!stkResponse.status === 200 || !stkResponse.statusText === 'OK' ) {
              console.error('Bad Request');
              throw new Error(stkResponse.data.message);
          }
  
          // Update payment with M-Pesa reference
          payment.reference = stkResponse.data.MerchantRequestID;
          payment.accessCode = stkResponse.data.CheckoutRequestID;
          await payment.save();
          console.log('Payment updated');
          const orderData = {
            orderId: order._id,
            paymentId: payment._id,
            message: "Payment initiated successfully"
          };
          console.log('Order data', orderData);
  
          return successResponse(orderData);
  
      } catch (error) {
          console.error("Checkout failed:", error.message);
          return errorResponse( error.message || "Checkout processing failed", 500);
      }


}




const formatPhoneNumber = (value) => {
    const numericOnly = value.replace(/\D/g, '');
    const lastNineDigits = numericOnly.slice(-9);
    
    if (lastNineDigits.length !== 9) {
      throw new Error('Phone number must contain at least 9 digits');
    }
    
    return `254${lastNineDigits}`;
  };


  async function getMpesaToken() {
      const secret = process.env.MPESA_CONSUMER_SECRET;
      const key = process.env.MPESA_CONSUMER_KEY;
      const credentials = btoa(`${key}:${secret}`); // base64 encode
      const url = process.env.MPESA_TOKEN_URL;
      try {
          const response = await fetch(url, {
              method: "GET",
              headers: {
                  Authorization: `Basic ${credentials}`,
              },
          });
  
          if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
          }
          const tokenResult = await response.json();
          const accessToken = tokenResult.access_token;
          console.log("Access Token:", accessToken);
          return accessToken;
      } catch (error) {
          console.error("Error fetching token:", error);
          throw error;
      }
  }


  async function mpesaPaymentRequest(mobile, amount, orderId) {
      console.log("New Mpesa payment request received");
      const timestamp = new Date().toISOString().replace(/[-:TZ]/g, "").slice(0, 14);
      console.log("Generated Timestamp:", timestamp);
      const passkey = process.env.MPESA_STK_PASSKEY;
      const shortCode = process.env.MPESA_SHORTCODE;
      const password = Buffer.from(shortCode + passkey + timestamp).toString("base64");
      console.log("Generated Password for Mpesa Request");
  
      const mpesaData = {
          BusinessShortCode: shortCode,
          Password: password,
          Timestamp: timestamp,
          TransactionType: "CustomerPayBillOnline",
          Amount: parseInt(amount, 10),
          PartyA: mobile,
          PartyB: shortCode,
          PhoneNumber: mobile,
          CallBackURL: `${process.env.MPESA_STK_CALLBACK}`,
          AccountReference: `${orderId}`,
          TransactionDesc: "Category Payment",
      };
  
      console.log("Mpesa Request Data Prepared:", mpesaData);
  
      const token = await getMpesaToken();
      console.log("Mpesa Token Retrieved:", token);
  
      // Make the Mpesa request
      const response = await axios.post(process.env.MPESA_STK_URL, mpesaData, {
          headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
          },
      });
  
      return response;
  }