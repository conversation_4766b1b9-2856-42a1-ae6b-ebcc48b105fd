import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import User from '@/lib/models/sp_user';
import { authenticateToken } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';
import { getUserCompanyRoles } from '@/lib/utils/user';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateToken(request);
    await connectToDatabase();

    // Get user with company roles
    const userWithCompanies = await getUserCompanyRoles(user.userId);
    
    if (!userWithCompanies) {
      return errorResponse('User not found', 404);
    }

    return successResponse(userWithCompanies);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token') {
      return unauthorizedResponse(error.message);
    }
    console.error('Profile fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateToken(request);
    const body = await request.json();
    
    await connectToDatabase();

    // Fields that can be updated
    const allowedFields = [
      'firstName',
      'lastName',
      'phone',
      'notificationPreferences'
    ];

    const updateData: any = {};
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return errorResponse('No valid fields to update');
    }

    updateData.updatedBy = user.userId;

    const updatedUser = await User.findByIdAndUpdate(
      user.userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -otp');

    if (!updatedUser) {
      return errorResponse('User not found', 404);
    }

    return successResponse(updatedUser, 'Profile updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token') {
      return unauthorizedResponse(error.message);
    }
    console.error('Profile update error:', error);
    return errorResponse('Internal server error', 500);
  }
}
