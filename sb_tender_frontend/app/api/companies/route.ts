import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpCategory from '@/lib/models/sp_category';
import User from '@/lib/models/sp_user';
import { requireAdmin } from '@/lib/middleware/auth';
import { successResponse, errorResponse, unauthorizedResponse } from '@/lib/utils/apiResponse';

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const formData = await request.formData();

    await connectToDatabase();

    // Extract form data
    const companyData = {
      name: formData.get('name') as string,
      registrationNumber: formData.get('registrationNumber') as string,
      type: formData.get('type') as string,
      address: formData.get('address') as string,
      contactPerson: formData.get('contactPerson') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      createdBy: user.userId,
    };

    // Validation
    const requiredFields = ['name', 'registrationNumber', 'type', 'address', 'contactPerson', 'email', 'phone'];
    for (const field of requiredFields) {
      if (!companyData[field as keyof typeof companyData]) {
        return errorResponse(`${field} is required`);
      }
    }

    // Check if company already exists
    const existingCompany = await SpCompany.findOne({
      $or: [
        { email: companyData.email },
        { registrationNumber: companyData.registrationNumber }
      ]
    });

    if (existingCompany) {
      return errorResponse('Company with this email or registration number already exists', 409);
    }

    // Create company
    const company = new SpCompany(companyData);
    await company.save();

    // Populate the created company
    await company.populate('createdBy', 'firstName lastName email');

    return successResponse(company, 'Company created successfully', 201);
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Company creation error:', error);
    return errorResponse('Internal server error', 500);
  }
}


export async function PUT(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('id');

    if (!companyId) {
      return errorResponse('Company ID is required');
    }

    const formData = await request.formData();
    await connectToDatabase();

    // Extract form data
    const updateData: any = {
      updatedBy: user.userId,
    };

    const allowedFields = ['name', 'registrationNumber', 'address', 'contactPerson', 'email', 'phone', 'status'];
    for (const field of allowedFields) {
      const value = formData.get(field);
      if (value) {
        updateData[field] = value;
      }
    }

    // Update company
    const company = await SpCompany.findByIdAndUpdate(
      companyId,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email');

    if (!company) {
      return errorResponse('Company not found', 404);
    }

    return successResponse(company, 'Company updated successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Company update error:', error);
    return errorResponse('Internal server error', 500);
  }
}


export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    // If ID is provided, return single company
    if (id) {
      const company = await SpCompany.findById(id)
        .populate('categories', 'name')
        .populate('createdBy', 'firstName lastName email');

      if (!company) {
        return errorResponse('Company not found', 404);
      }

      return successResponse(company);
    }

    // Build query for multiple companies
    const query: any = {};
    if (type) query.type = type;
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { registrationNumber: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [companies, total] = await Promise.all([
      SpCompany.find(query)
        // .populate('categories', 'name') // Temporarily disabled until categories are properly linked
        .populate('createdBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      SpCompany.countDocuments(query)
    ]);

    return successResponse({
      companies,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Companies fetch error:', error);
    return errorResponse('Internal server error', 500);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAdmin(request);
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return errorResponse('Company ID is required');
    }

    await connectToDatabase();

    const company = await SpCompany.findByIdAndDelete(id);
    if (!company) {
      return errorResponse('Company not found', 404);
    }

    return successResponse(null, 'Company deleted successfully');
  } catch (error: any) {
    if (error.message === 'No token provided' || error.message === 'Invalid token' || error.message === 'Insufficient permissions') {
      return unauthorizedResponse(error.message);
    }
    console.error('Company deletion error:', error);
    return errorResponse('Internal server error', 500);
  }
}