import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const body = await request.json();
    const { companyId } = await params; // Await params here
    console.log('server comp reg', body);
    console.log('companyId', companyId);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/categories`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
     console.error('POST error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating category.' }, { status: error.response?.status || 500 });
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const { companyId } = await params; // Await params here
    console.log('companyId', companyId);
    const token = req.headers.get('authorization') || '';
    console.log('Token:', token);
    const endpoint = `/api/admin/companies/${companyId}/categories`;
    console.log('Endpoint:', BACKEND_API_URL + endpoint);

    const backendRes = await fetch(`${BACKEND_API_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const { companyId } = await params; // Await params here
    const body = await request.json();
    const { searchParams } = new URL(request.url);

    console.log('companyId', companyId);

    const categoryId = searchParams.get('categoryId');
    console.log('categoryId', categoryId);

    console.log('server comp reg', body);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    if (!categoryId) {
         return NextResponse.json({ error: 'Missing Category ID' }, { status: 400 });
    }


    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/categories/${categoryId}`, {
      method: 'PUT',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to update category' }, { status: backendRes.status });
    }
    console.log('Backend response status:', backendRes.status);
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    console.error('PUT error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating category.' }, { status: error.response?.status || 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  const { companyId } = await params; // Await params here
  console.log('companyId', companyId);

  try {
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get('categoryId');
    const token = req.headers.get('authorization') || '';

    if (!categoryId || !companyId) {
      return NextResponse.json({ error: 'Missing Category or company ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/categories/${categoryId}`, {
      method: 'DELETE',
      headers: {
        Authorization: token,
      },
    });

    if (!backendRes.ok) {
        console.error('Backend response error:', backendRes.statusText);
        return NextResponse.json({ error: 'Failed to delete category' }, { status: backendRes.status });
    }


    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('DELETE error:', error);
    return NextResponse.json({ error: 'Failed to delete company' }, { status: 500 });
  }
}
// export {}; // Removed the empty export {} as it's not necessary