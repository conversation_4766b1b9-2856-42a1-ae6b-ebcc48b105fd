//export const dynamic = 'force-dynamic'; // This line is commented out, keeping it that way
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios'; // axios is imported but not used, keeping the import as is
export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const { companyId } = await params; // Await params here
    const { searchParams } = new URL(req.url);
    const categoryId = searchParams.get('categoryId');
    const token = req.headers.get('authorization') || '';

     if (!categoryId) {
         return NextResponse.json({ error: 'Missing Category ID' }, { status: 400 });
     }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/categories/${categoryId}/template`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch template' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch template' }, { status: 500 });
  }
}


export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const { companyId } = await params; // Await params here
    const { searchParams } = new URL(request.url);
    const buyerCategoryId = searchParams.get('buyerCategoryId');
    const buyerTemplateId = searchParams.get('templateId');
    const body = await request.json();

    if (!buyerCategoryId || !buyerTemplateId) {
      return NextResponse.json({ error: 'Category ID and Template ID are required' }, { status: 400 });
    }
    console.log('server comp reg', body);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/categories/${buyerCategoryId}/templates/${buyerTemplateId}`, {
          method: 'PUT',
          headers: {
            Authorization: token,
            'Content-Type': 'application/json',
          },
          body: newBody,
        });
        if (!backendRes.ok) {
          console.error('Backend response error:', backendRes.statusText);
          return NextResponse.json({ error: 'Failed to update category template' }, { status: backendRes.status });
        }
        const result = await backendRes.json();
        return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
     console.error('PUT error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating category template.' }, { status: error.response?.status || 500 });
  }
}

// The empty export {}; was not present in the provided code snippet, so it's not added here.