import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const body = await request.json();
    const { companyId } = await params; // Await params here
    console.log('server comp reg', body);
    console.log('companyId', companyId);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/jobs`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    console.error('POST error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating category.' }, { status: error.response?.status || 500 });
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Added params with correct type here
) {
  try {
    const { companyId } = await params; // Await params here
    const { searchParams } = new URL(req.url);
    // If you intended to get companyId from searchParams instead of dynamic route,
    // uncomment the line below and adjust the fetch URL accordingly.
    // const companyId = searchParams.get('companyId');

    const token = req.headers.get('authorization') || '';
     const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/jobs`, { // Using companyId from params
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch jobs' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch jobs' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  try {
    const { companyId } = await params; // Await params here
    const body = await request.json();
    const { searchParams } = new URL(request.url);

    console.log('companyId', companyId);

    const jobId = searchParams.get('jobId');
    console.log('jobId', jobId);

    console.log('server comp reg', body);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    if (!jobId) {
         return NextResponse.json({ error: 'Missing Job ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/jobs/${jobId}`, {
      method: 'PUT',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to update job' }, { status: backendRes.status });
    }
    console.log('Backend response status:', backendRes.status);
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
     console.error('PUT error:', error);
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating job.' }, { status: error.response?.status || 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> } // Updated type here
) {
  const { companyId } = await params; // Await params here
  console.log('companyId', companyId);

  try {
    const { searchParams } = new URL(req.url);
    const jobId = searchParams.get('jobId');
    const token = req.headers.get('authorization') || '';

    if (!jobId || !companyId) {
      return NextResponse.json({ error: 'Missing Job or company ID' }, { status: 400 });
    }

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/companies/${companyId}/jobs/${jobId}`, {
      method: 'DELETE',
      headers: {
        Authorization: token,
      },
    });

    if (!backendRes.ok) {
        console.error('Backend response error:', backendRes.statusText);
        return NextResponse.json({ error: 'Failed to delete job' }, { status: backendRes.status });
    }


    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('DELETE error:', error);
    return NextResponse.json({ error: 'Failed to delete job' }, { status: 500 });
  }
}
