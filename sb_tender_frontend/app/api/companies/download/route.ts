// app/api/companies/route.ts
import { NextRequest } from 'next/server';
const BACKEND_URL = process.env.NODE_API_URL || 'http://localhost:5001';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  console.log('Next.js API - Request params:', searchParams.toString());
  const companyId = searchParams.get('companyId');
  const documentType = searchParams.get('documentType');
  const fileName = searchParams.get('fileName');
  const headers = new Headers(req.headers);

  console.log('Next.js API - Download request:', { companyId, documentType, fileName });

  if (!companyId || !documentType || !fileName) {
    console.error('Next.js API - Missing query parameters');
    return new Response('Missing query parameters', { status: 400 });
  }

  try {
    const query = new URLSearchParams({
        companyId,
        documentType,
        fileName,
      }).toString();
      const token = headers.get('authorization') || '';
      const backendUrl = `${BACKEND_URL}/api/admin/companies/documents/download/?${query}`;
      console.log('Next.js API - Calling backend:', backendUrl);

      const backendRes = await fetch(backendUrl, {
        method: 'GET',
        headers: {
            Authorization: token,
        },
        next: { revalidate: 0 }, //  disables Next.js caching
      });

    console.log('Next.js API - Backend response status:', backendRes.status, 'ok:', backendRes.ok);
    if (!backendRes.ok) {
      const errorText = await backendRes.text();
      console.error('Next.js API - Backend error:', errorText);
      return new Response(`Failed to fetch from backend: ${errorText}`, { status: backendRes.status });
    }

    const blob = await backendRes.blob();
    console.log('Next.js API - Received blob size:', blob.size, 'type:', blob.type);

    return new Response(blob, {
      headers: {
        'Content-Type': backendRes.headers.get('Content-Type') || 'application/octet-stream',
        'Content-Disposition': backendRes.headers.get('Content-Disposition') || '',
      },
    });
  } catch (err) {
    console.error('Next.js API - Error:', err);
    return new Response('Internal proxy error', { status: 500 });
  }
}
