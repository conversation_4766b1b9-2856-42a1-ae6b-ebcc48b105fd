//export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
export const dynamic = 'force-dynamic';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('categoryId');
    const token = req.headers.get('authorization') || '';
    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/categories/${id}/template`, {
      method: 'GET',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (!backendRes.ok) {
      console.error('Backend response error:', backendRes.statusText);
      return NextResponse.json({ error: 'Failed to fetch template' }, { status: backendRes.status });
    }

    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error) {
    console.error('GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch template' }, { status: 500 });
  }
}


export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const categoryTemplateId = searchParams.get('templateId');
    if (!categoryId || !categoryTemplateId) {
      return NextResponse.json({ error: 'Category ID and Template ID are required' }, { status: 400 });
    }
    console.log('server comp reg', body);
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);

    const backendRes = await fetch(`${BACKEND_API_URL}/api/admin/categories/${categoryId}/templates/${categoryTemplateId}`, {
          method: 'PUT',
          headers: {
            Authorization: token,
            'Content-Type': 'application/json',
          },
          body: newBody,
        });
        if (!backendRes.ok) {
          console.error('Backend response error:', backendRes.statusText);
          return NextResponse.json({ error: 'Failed to fetch categories' }, { status: backendRes.status });
        }
        const result = await backendRes.json();
        return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error updating buyer.' }, { status: 401 });
  }
}


