import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import SpJobCategory from '@/lib/models/sp_job_category';

export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
     await requireSupplier(request);
    await connectToDatabase();

    const { jobId } = await params;
    console.log('Job ID:', jobId);

    if (!jobId) {
      console.error('Job ID is missing');
      return errorResponse('Job ID is required', 404);
    }
          const categories = await SpJobCategory.find({ spJobId: jobId })
              .populate({
                  path: 'spCompanyId',
                 select: 'name'
              })
              .lean(); // Convert to plain JavaScript objects
    
          if (!categories || categories.length === 0) {
              console.error('No job categories found');
              return errorResponse('No job categories found', 404);
          }
    
          // Transform the data to include companyName directly
          const categoriesWithCompany = categories.map(category => ({
              ...category,
              companyName: category.spCompanyId?.name || 'Unknown Company'
          }));
    
          return successResponse(categoriesWithCompany);

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

