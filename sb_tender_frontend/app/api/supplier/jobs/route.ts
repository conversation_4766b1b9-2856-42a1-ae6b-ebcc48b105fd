import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpApplication from '@/lib/models/sp_application';
import SpUserRole from '@/lib/models/sp_user_role';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';
import SpJob from '@/lib/models/sp_job';

// GET /api/supplier/companyIid - Get company by ID
export async function GET( request: NextRequest) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

      const query = { status: 'open' };
        let jobs = null;    
        // Find jobs that are in open status and populate company information
        jobs = await SpJob.find(query)
          .populate({
            path: 'spCompanyId',
            select: 'name' // Only get the company name
          })
          .lean(); // Convert to plain JavaScript objects
    
        if (!jobs || jobs.length === 0) {
          console.log('No jobs found');
          return errorResponse('No jobs found', 404);
        }
    
        // Transform the data to include companyName directly
        const jobsWithCompany = jobs.map(job => ({
          ...job,
          companyName: job.spCompanyId?.name || 'Unknown Company'
        }));
    
        console.log('Found jobs:', jobsWithCompany.length);
        return successResponse(jobsWithCompany);
      } catch (error) {
        console.error('Could not fetch jobs', error);
        return errorResponse(error.message || 'Failed to fetch jobs', 500);
      }
}


