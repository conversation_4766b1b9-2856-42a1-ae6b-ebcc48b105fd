import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpApplication from '@/lib/models/sp_application';
import SpUserRole from '@/lib/models/sp_user_role';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ companyId: string }> }
) {
    try {
        console.log('=== GET Company By ID - Starting ===');
        await requireSupplier(request);
        await connectToDatabase();

        const { companyId } = await params;
        console.log('Company ID:', companyId);

        if (!companyId) {
            console.error('Company ID is missing');
            return errorResponse('Company ID is required', 400);
        }

        const filter = { spSupplierCompanyId: companyId };
        const { searchParams } = new URL(request.url);
        const type = searchParams.get('type');

        if (type === 'supplier_prequalification' || type === 'supplier_registration') {
            filter.type = type;
        } else {
            //return res.status(404).json({ 
            //    message: 'Specify either supplier registtration or prequalification for the company',
            //    success: false,
            //    applicationsData: [] 
            //});
        }
        // Validate companyId
        if (!mongoose.Types.ObjectId.isValid(companyId)) {
            return errorResponse('Invalid company ID', 404);
        }

        // Find applications for the specified company
        const applications = await SpApplication.find(filter).sort({ createdAt: -1 });

        // If no applications found
        if (!applications || applications.length === 0) {
            return errorResponse('No applications found for this company', 200);
        }

        // Fetch buyer company details for each application
        const applicationsWithBuyerDetails = await Promise.all(
            applications.map(async (application) => {
                // Find buyer company details
                const buyerCompany = await SpCompany.findById(application.spBuyerCompanyId)
                    .select('name logoUrl address');

                // Create a plain object to modify
                const appObject = application.toObject();

                // Add buyer company details
                appObject.buyerData = {
                    name: buyerCompany.name,
                    logoUrl: buyerCompany.logoUrl,
                    address: buyerCompany.address
                };

                return appObject;
            })
        );

        return successResponse({
            message: 'Applications retrieved successfully',
            success: true,
            applicationsData: applicationsWithBuyerDetails
        });



    } catch (error: any) {
        console.error('Get supplier applications error:', error);
        console.error('Error stack:', error.stack);
        return errorResponse(error.message || 'Failed to fetch applications', 500);
    }
}

