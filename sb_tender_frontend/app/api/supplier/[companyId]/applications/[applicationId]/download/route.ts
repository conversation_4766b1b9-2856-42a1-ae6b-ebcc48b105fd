



 //* @route   GET /api/applications/:applicationId/fields/:fieldName/documents/:fileName

/**
 * @route   GET /api/applications/:applicationId/fields/:fieldName/documents/:fileName
 * @desc    Downloads a specific document from a specific field in an application.
 * @access  Private
 */
export const downloadApplicationDocument = async (req, res) => {
    try {
        console.log('Downloading application');
      const { applicationId, fieldName, fileName } = req.params;
  
      const application = await SpApplication.findById(applicationId);
      if (!application) {
        return res.status(404).json({ error: 'Application not found.' });
      }
  
      // Find the specific field that holds the document
      const targetField = application.fields.find(f => f.name === fieldName);
      if (!targetField) {
        return res.status(404).json({ error: `Field '${fieldName}' not found.` });
      }
  
      // Find the specific document within that field by its file name
      const document = targetField.documents.find(doc => doc.fileName === fileName);
      if (!document) {
        return res.status(404).json({ error: `Document '${fileName}' not found in field '${fieldName}'.` });
      }
  
      // Use the S3 key (filePath) to retrieve the object
      const s3Key = document.filePath;
      if (!s3Key) {
          return res.status(400).send('Document metadata is missing the S3 file path.');
      }
  
      const s3Response = await getObjectFromS3(s3Key);
      if (!s3Response) {
        return res.status(404).send('File could not be found in storage.');
      }
  
      // Set headers to trigger a download in the browser
      res.setHeader('Content-Type', document.fileType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${document.fileName}"`);
      res.setHeader('Content-Length', s3Response.ContentLength);
  
      // Stream the file body from S3 directly to the client's response
      s3Response.Body.pipe(res);
  
    } catch (error) {
      console.error('S3 Download Error:', error);
      res.status(500).send('Failed to fetch file from storage.');
    }
  };