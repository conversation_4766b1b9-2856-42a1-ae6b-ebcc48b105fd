import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// POST /api/supplier/companyId/applications/applicationId/upload - upload file to application
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    const user = await requireSupplier(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const fieldName = formData.get('fieldName') as string;

    console.log('Uploading application file:', file?.name);
    console.log('Uploading application body:', { fieldName });

    // Validate file upload
    if (!file) {
      console.log('No file was uploaded.');
      return NextResponse.json({ error: 'No file was uploaded.' }, { status: 400 });
    }

    if (!fieldName) {
      console.log('The "fieldName" is required to identify the document\'s destination.');
      return NextResponse.json({ 
        error: 'The "fieldName" is required to identify the document\'s destination.' 
      }, { status: 400 });
    }

    console.log('fieldName', fieldName);

    // Find the application
    const application = await SpApplication.findById(applicationId);
    if (!application) {
      return NextResponse.json({ error: 'Application not found.' }, { status: 404 });
    }

    // Find the specific field within the application's 'fields' array
    const targetField = application.fields.find(f => f.name === fieldName);
    if (!targetField) {
      console.log(`Field '${fieldName}' does not exist in this application.`);
      return NextResponse.json({ 
        error: `Field '${fieldName}' does not exist in this application.` 
      }, { status: 404 });
    }

    // --- Replacement Logic: Delete old files from S3 and clear the documents array ---
    if (targetField.documents && targetField.documents.length > 0) {
      console.log(`Replacing ${targetField.documents.length} existing document(s) in field '${fieldName}'.`);
      // Create an array of deletion promises
      try {
        const deletionPromises = targetField.documents.map(doc => deleteFromS3(doc.filePath));
        await Promise.all(deletionPromises);
      } catch (error) {
        console.error(`Could not delete existing file`, error);
      }
    }
    // Clear the documents array for the target field
    targetField.documents = [];
    // --- End of replacement logic ---

    // Convert File to buffer for S3 upload
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    
    // Upload the new file to S3
    const folder = `application-documents/${applicationId}/${fieldName}`;
    const modifiedName = `${Date.now()}-${file.name}`;
    const fileUrl = await uploadToS3(fileBuffer, folder, modifiedName, companyId);
    console.log(`FILE URL`, fileUrl);

    // Create the new document object according to your schema
    const newDocument = {
      filePath: fileUrl, // The S3 Key for future retrieval/deletion
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      uploadedAt: new Date(),
    
      // 'url' is not in your sub-schema, so we omit it. filePath is the key.
    };

    // Add the new document to the target field's documents array
    targetField.documents.push(newDocument);
    
    application.updatedBy = user.userId; // Using the authenticated user
    
    // Mark the path as modified since it's a nested array
    application.markModified('fields');

    await application.save();
    console.log('Application document updated successfully.');

    // Return the exact same response structure as Express
    return NextResponse.json({
      message: `File uploaded successfully to field '${fieldName}'.`,
      document: newDocument,
    }, { status: 200 });

  } catch (error) {
    console.error('Error uploading application file:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}







/*

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// POST /api/supplier/companyId/applications/applicationId/upload - upload file to application
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
) {
  try {
    const user = await requireSupplier(request);
    await connectToDatabase();

    const { companyId, applicationId } = await params;
    const body = await request.json();

    if (!companyId || !applicationId) {
      return errorResponse('Company ID and Application ID are required', 400);
    }

    const {
      fieldName,
      file
    } = body;

    // Verify user has access to this company using roles








export const uploadApplicationFile = async (req, res) => {
    console.log('Uploading application file:', req.file);

    console.log('Uploading application body:', req.body);
  try {
    const { companyId, applicationId } = req.params;

    const { fieldName } = req.body; // The unique name of the field, e.g., 'companyRegistration'

    if (!req.file) {
        console.log('No file was uploaded.');
      return res.status(400).json({ error: 'No file was uploaded.' });
    }
    if (!fieldName) {
      console.log('The "fieldName" is required to identify the document\'s destination.');
      return res.status(400).json({ error: 'The "fieldName" is required to identify the document\'s destination.' });
    }
    console.log('fieldName', fieldName);

    const application = await SpApplication.findById(applicationId);
    if (!application) {
      return res.status(404).json({ error: 'Application not found.' });
    }

    // Find the specific field within the application's 'fields' array
    const targetField = application.fields.find(f => f.name === fieldName);
    if (!targetField) {
        coonsole.log(`Field '${fieldName}' does not exist in this application.`)
      return res.status(404).json({ error: `Field '${fieldName}' does not exist in this application.` });
    }

    // --- Replacement Logic: Delete old files from S3 and clear the documents array ---
    if (targetField.documents && targetField.documents.length > 0) {
      console.log(`Replacing ${targetField.documents.length} existing document(s) in field '${fieldName}'.`);
      // Create an array of deletion promises
      try {
        const deletionPromises = targetField.documents.map(doc => deleteFromS3(doc.filePath));
        await Promise.all(deletionPromises);
      } catch (error) {
        console.error(`Could not deleete existing file`, error);
      }
    }
    // Clear the documents array for the target field
    targetField.documents = [];
    // --- End of replacement logic ---

    const file = req.file;
    
    // Upload the new file to S3
    const folder = `application-documents/${applicationId}/${fieldName}`;
    const modifiedName = `${Date.now()}-${file.originalname}`;
    const fileUrl = await uploadToS3(file.buffer, folder, modifiedName, companyId);
    console.log(`FILE URL`, fileUrl);

    // Create the new document object according to your schema
    const newDocument = {
      filePath: fileUrl, // The S3 Key for future retrieval/deletion
      fileName: file.originalname,
      fileType: file.mimetype,
      fileSize: file.size,
      uploadedAt: new Date(),
      // 'url' is not in your sub-schema, so we omit it. filePath is the key.
    };

    // Add the new document to the target field's documents array
    targetField.documents.push(newDocument);
    
    application.updatedBy = req.user.userId; // Assuming auth middleware
    
    // Mark the path as modified since it's a nested array
    application.markModified('fields');

    await application.save();
    console.log('Application document updated successfully.')

    res.status(200).json({
      message: `File uploaded successfully to field '${fieldName}'.`,
      document: newDocument,
    });

  } catch (error) {
    console.error('Error uploading application file:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};


*/