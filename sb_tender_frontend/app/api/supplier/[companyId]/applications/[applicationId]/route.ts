
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpApplication from '@/lib/models/sp_application';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';


// GET /api/supplier/companyId/applications/applicationId - get application.
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
){
    try {
        console.log('=== GET Company Application By ID - Starting ===');
        const user = await requireSupplier(request);
        await connectToDatabase();

        const { companyId, applicationId } = await params;

        if (!companyId || !applicationId) {
            return errorResponse('Company ID and Application ID are required', 400);
        }

        // Verify user has access to this company using roles


        // Find application
        const application = await SpApplication.findOne({
            _id: applicationId,
            spSupplierCompanyId: companyId
        });
        if (!application) {
            return errorResponse('Application not found', 404);
        }

        return successResponse(application);

    } catch (error: any) {
        console.error('=== GET Company Application By ID - Error ===');
        console.error('Get company application error:', error);
        console.error('Error stack:', error.stack);
        return errorResponse(error.message || 'Failed to fetch application', 500);
    }
}


// PUT /api/supplier/companyId/applications/applicationId - update application.
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; applicationId: string }> }
){
    try {
        console.log('=== PUT Update Company Application - Starting ===');
        const user = await requireSupplier(request);
        await connectToDatabase();

        const { companyId, applicationId } = await params;
        //const body = await request.json();

        if (!companyId || !applicationId) {
            return errorResponse('Company ID and Application ID are required', 400);
        }
        const requestBody =await request.json();
        const { fields, status } = requestBody // Extract fields and the target status from the payload

        // 1. --- Validation and Authorization ---
        if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(applicationId)) {
            return errorResponse('Invalid company or application ID.', 400);
        }

        // Find the application, ensuring it belongs to the correct company
        const application = await SpApplication.findOne({ 
            _id: applicationId, 
            spSupplierCompanyId: companyId 
        });

        if (!application) {
            return  errorResponse('Application not found.', 404);
        }

        // 2. --- State Management Check ---
        // Crucial: Only allow updates if the application is in 'draft' status.
        // Once submitted, it should not be editable from this endpoint.
        if (application.status !== 'draft') {
            return errorResponse(`Application cannot be updated because its status is '${application.status}'.` 
            );
        }
        
        // 3. --- Update the Application Document ---
        
        // Replace the fields array with the new data from the frontend
        application.fields = fields;

        // Update the status
        application.status = status;
        
        // Set 'updatedBy' to the current user
        application.updatedBy = user.userId; // Assumes auth middleware provides req.user.userId

        // If the application is being submitted, set the submission timestamp
        if (status === 'submitted') {
            application.submittedAt = new Date();
            // The 'submittedBy' field should already be set, but you could re-affirm it here if needed
            application.submittedBy = user.userId;
            // **Call the scoring helper to calculate the initial system score**
            calculateAndUpdateScores(application);
        }

        // 4. --- Save and Respond ---
        // Save the updated document to the database
        const updatedApplication = await application.save();
        console.log('Updated bid');

        return successResponse({
            message: `Bid successfully ${status === 'submitted' ? 'submitted' : 'saved'}.`,
            success: true,
            applicationData: updatedApplication // Send the complete, updated application back to the client
    });

    } catch (error) {
        // Handle potential validation errors from Mongoose or other server errors
        console.error('Error updating company application:', error);
        if (error.name === 'ValidationError') {
            return errorResponse(`Validation Error: ${error.message}`, 400);
        }
        return errorResponse('An error occurred while updating the application', 500);
    }
};


/**
 * Calculates the system score based on the application's fields and updates the total score.
 * This function modifies the application document in-place but does not save it.
 * @param {object} application - The Mongoose application document to score.
 */
const calculateAndUpdateScores = (application) => {
    let calculatedSystemScore = 0;
    let calculatedComplianceScore = 0;

    // Iterate over each field in the application to calculate its score
    for (const field of application.fields) {
        // Only process fields that are marked as scoreable
        if (field.isScoreable) {
            let fieldScore = 0;
            const hasValue = field.value !== null && field.value !== undefined && field.value !== '';

            if (hasValue) {
                // If the field uses specific scores for options (e.g., radio buttons)
                if (field.optionScore && field.optionScore.length > 0) {
                    const matchedOption = field.optionScore.find(opt => opt.value === field.value);
                    if (matchedOption && typeof matchedOption.score === 'number') {
                        fieldScore = matchedOption.score;
                    }
                }
                // Otherwise, if the field is just filled (e.g., a file upload or text field), award its max score
                else {
                    fieldScore = field.maxScore || 0;
                }
            }
            // If a scoreable field is not filled, its score remains 0.

            //if it has compliance, calculate he compliance score
            if (field.requiresDd && field.complianceScore !== 0) {
                calculatedComplianceScore += field.complianceScore;
            }

            // Assign the calculated score to the field itself for traceability
            field.score = fieldScore;
            calculatedSystemScore += fieldScore;
        } else {
             // If a field is not scoreable, ensure its score is null
             field.score = null;
        }
    }

    // Update the main scores on the application document
    application.complianceScore = calculatedComplianceScore;
    application.systemScore = calculatedSystemScore;
    application.totalScore = (application.systemScore || 0) + (application.complianceScore || 0);
};