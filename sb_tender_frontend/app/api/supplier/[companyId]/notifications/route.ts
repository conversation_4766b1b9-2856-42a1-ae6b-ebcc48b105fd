import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import SpNotification from '@/lib/models/sp_notification';

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    //get search params
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || 1;
    const limit = searchParams.get('limit') || 20;
    const status = searchParams.get('status');
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') || 'desc';
    const resource = searchParams.get('resource');
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');

    
          const filter = { companyId };
          if (userId) filter.userId = userId;
          if (action) filter.action = action;
          if (resource) filter.resource = resource;
          const notifications = await SpNotification.find(filter)
            .populate('userId', 'name email')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .lean();
          
          const total = await SpNotification.countDocuments(filter);
          
          return successResponse({
            notifications,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total,
              pages: Math.ceil(total / limit)
            }
          });

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

