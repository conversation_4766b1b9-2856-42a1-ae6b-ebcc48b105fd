import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';


// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
    await requireSupplier(request);
    await connectToDatabase();

    const { orderId } = await params;
    console.log('order ID:', orderId);

    try {    
        // Validate orderId
        if (!orderId) {
          return errorResponse( "Order ID is required", 400);
        }
    
        // Find order
        const order = await SpOrder.findById(orderId);
        if (!order) {
          return errorResponse("Order not found", 404);
        }
    
        // Find associated payment
        const payment = await SpPayment.findOne({ spOrderId: orderId });
        if (!payment) {
          return errorResponse("Payment record not found for this order", 404);
        }
    
        // If payment is already marked as paid, return success
        if (payment.status === 'paid' || order.status === 'paid') {
    
          return successResponse({
            success: true,
            message: "Payment has already been completed",
            verified: true,
            status: 'paid',
            orderId: order._id,
            paymentId: payment._id
          });
        }
    
        
        return successResponse({
          success: true,
          message: "Payment not yet completed",
          verified: false,
          status: payment.status,
          orderId: order._id,
          paymentId: payment._id
        });
      } catch (error) {
        console.error("Error verifying payment:", error.message);
        return errorResponse(error.message || "Failed to verify payment status", 500);
      }


   
}

