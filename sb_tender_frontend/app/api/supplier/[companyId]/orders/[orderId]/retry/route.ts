import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import axios from 'axios';


// GET /api/admin/companies/:id - Get company by ID
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string, orderId: string }> }
) {
    const user = await requireSupplier(request);
    await connectToDatabase();

    const { companyId, orderId } = await params;
    console.log('order ID:', orderId);
    try {    
        const body = await request.json();
    const { mobile } = body;
    
    // Validate required fields
    if (!orderId) {
      return errorResponse("Order ID is required", 400);
    }

    if (!mobile) {
      return errorResponse("Mobile number is required", 400);
    }

    // Format mobile number (remove leading zero and add country code if needed)
    const formattedMobile = formatPhoneNumber(mobile);

    // Find order
    const order = await SpOrder.findById(orderId);
    if (!order) {
      return errorResponse("Order not found", 404);
    }

    // Check if order is already paid
    if (order.status === 'paid') {
      return errorResponse( "Order is already paid", 400);
    }

    // Find associated payment
    let payment = await SpPayment.findOne({ spOrderId: orderId });
    
    if (!payment) {
      // Create new payment record if it doesn't exist
      payment = new SpPayment({
        amount: order.totalAmount,
        mobile: formattedMobile,
        status: 'pending',
        spOrderId: order._id,
        userId: user.userId,
        paymentMethod: 'mpesa',
        reason: 'category'
      });
    } else {
      // Update existing payment record
      payment.mobile = formattedMobile;
      payment.status = 'pending';
      payment.updatedAt = new Date();
    }

    await payment.save();

    // Update order status
    order.status = 'pending';
    await order.save();

    // Initiate M-Pesa STK push
        const stkResponse = await mpesaPaymentRequest(
            formattedMobile,
            order.totalAmount,
            `${order._id}`
        );
      
        console.log('STK response', stkResponse.data);
        if (!stkResponse.status === 200 || !stkResponse.statusText === 'OK' ) {
            console.error('Bad Request');
            throw new Error(stkResponse.data.message);
        }

        // Update payment with M-Pesa reference
        payment.reference = stkResponse.data.MerchantRequestID;
        payment.accessCode = stkResponse.data.CheckoutRequestID;
        await payment.save();
        console.log('Payment updated');

        return successResponse({
            success: true,
            orderId: order._id,
            paymentId: payment._id,
            message: "Payment initiated successfully"
        });

    } catch (error) {
        console.error("Checkout failed:", error.message);
        return errorResponse(error.message || "Checkout processing failed", 500);
    }

   
}

const formatPhoneNumber = (value) => {
    const numericOnly = value.replace(/\D/g, '');
    const lastNineDigits = numericOnly.slice(-9);
    if (lastNineDigits.length !== 9) {
      throw new Error('Phone number must contain at least 9 digits');
    }
    return `254${lastNineDigits}`;
  };


async function mpesaPaymentRequest(mobile, amount, orderId) {
    console.log("New Mpesa payment request received");
    const timestamp = new Date().toISOString().replace(/[-:TZ]/g, "").slice(0, 14);
    console.log("Generated Timestamp:", timestamp);
    const passkey = process.env.MPESA_STK_PASSKEY;
    const shortCode = process.env.MPESA_SHORTCODE;
    const password = Buffer.from(shortCode + passkey + timestamp).toString("base64");
    console.log("Generated Password for Mpesa Request");

    const mpesaData = {
        BusinessShortCode: shortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: "CustomerPayBillOnline",
        Amount: parseInt(amount, 10),
        PartyA: mobile,
        PartyB: shortCode,
        PhoneNumber: mobile,
        CallBackURL: `${process.env.MPESA_STK_CALLBACK}`,
        AccountReference: `${orderId}`,
        TransactionDesc: "Category Payment",
    };

    console.log("Mpesa Request Data Prepared:", mpesaData);

    const token = await getMpesaToken();
    console.log("Mpesa Token Retrieved:", token);

    // Make the Mpesa request
    const response = await axios.post(process.env.MPESA_STK_URL, mpesaData, {
        headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
        },
    });

    return response;
}

async function getMpesaToken() {
    const secret = process.env.MPESA_CONSUMER_SECRET;
    const key = process.env.MPESA_CONSUMER_KEY;
    const credentials = btoa(`${key}:${secret}`); // base64 encode
    const url = process.env.MPESA_TOKEN_URL;
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                Authorization: `Basic ${credentials}`,
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const tokenResult = await response.json();
        const accessToken = tokenResult.access_token;
        console.log("Access Token:", accessToken);
        return accessToken;
    } catch (error) {
        console.error("Error fetching token:", error);
        throw error;
    }
}