import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import SpNotification from '@/lib/models/sp_notification';

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string, orderId: string }> }
) {
  try {

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId, orderId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company is missing');
      return errorResponse('Company ID is required', 400);
    }
    if (!orderId) {
      console.error('Order is missing');
      return errorResponse('Order ID is required', 400);
    }
        // Find order and populate relevant details
        const order = await SpOrder.findById({ _id: orderId, spCompanyId: companyId });
        
        if (!order) {
          return errorResponse("Order not found", 404);
        }
        const payment = await SpPayment.findOne({ spOrderId: orderId });
    
        // Combine order with payment information
        const orderWithPayment = {
          ...order.toObject(),
          payment: payment ? payment.toObject() : null
        };
        const orderData = {
          ...orderWithPayment,
          success: true,
          message: 'Order retrieved successfully'
        };
        return successResponse(orderData);
    

  } catch (error: any) {
    console.error('Get order details error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch order details', 500);
  }
}

