import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpOrder from '@/lib/models/sp_order';
import SpPayment from '@/lib/models/sp_payment';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    //get search params
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || 1;
    const limit = searchParams.get('limit') || 20;
    const status = searchParams.get('status');
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') || 'desc';

    // Validate companyId
          if (!companyId) {
            return errorResponse({
              success: false,
              message: "Company ID is required"
            });
          }
          const spCompanyId = companyId;
      
          // Build query
          const query = { spCompanyId };
          
          // Add status filter if provided
          if (status && status !== 'all') {
            query.status = status;
          }
      
          // Set up pagination
          const skip = (parseInt(page) - 1) * parseInt(limit);
          
          // Determine sort direction
          const sortDirection = order === 'asc' ? 1 : -1;
          const sortOptions = {};
          sortOptions[sort] = sortDirection;
      
          // Find orders with pagination and sorting
          const orders = await SpOrder.find(query)
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));
      
          // Count total matching orders for pagination info
          const totalOrders = await SpOrder.countDocuments(query);
      
          // Get all order IDs to fetch associated payments
          const orderIds = orders.map(order => order._id);
          
          // Find all payments for these orders in a single query
          const payments = await SpPayment.find({ spOrderId: { $in: orderIds } });
          
          // Create a map of order ID to payment for faster lookup
          const paymentMap = payments.reduce((map, payment) => {
            map[payment.spOrderId.toString()] = payment;
            return map;
          }, {});
      
          // Combine orders with their payment information
          const ordersWithPayments = orders.map(order => {
            const orderObject = order.toObject();
            const paymentForOrder = paymentMap[order._id.toString()];
            
            return {
              ...orderObject,
              payment: paymentForOrder ? paymentForOrder.toObject() : null
            };
          });
      
          return successResponse({
            success: true,
            data: ordersWithPayments,
            pagination: {
              total: totalOrders,
              page: parseInt(page),
              limit: parseInt(limit),
              pages: Math.ceil(totalOrders / parseInt(limit))
            }
          });




  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

