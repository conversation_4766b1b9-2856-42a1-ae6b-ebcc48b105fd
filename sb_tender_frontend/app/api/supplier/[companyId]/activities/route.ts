import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpUserRole from '@/lib/models/sp_user_role';
import SpActivityLog from '@/lib/models/sp_activity_log';

import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';
import _ from "lodash";

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    //get search params
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || 1;
    const limit = searchParams.get('limit') || 20;
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');
    const resource = searchParams.get('resource');

     
         const filter = { companyId };
         if (userId) filter.userId = userId;
         if (action) filter.action = action;
         if (resource) filter.resource = resource;
         
         const activities = await SpActivityLog.find(filter)
           .populate('userId', 'name email')
           .sort({ createdAt: -1 })
           .limit(limit * 1)
           .skip((page - 1) * limit)
           .lean();
         
         const total = await SpActivityLog.countDocuments(filter);
         
         const activityData = {
           activities,
           pagination: {
             page: parseInt(page),
             limit: parseInt(limit),
             total,
             pages: Math.ceil(total / limit)
           }
         };



    return successResponse(activityData);

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

