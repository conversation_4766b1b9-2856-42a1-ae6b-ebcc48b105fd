import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpJob from '@/lib/models/sp_job';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';

// GET /api/supplier/companyIid - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

      const query = { status: 'open' };
        let jobs = null;    
        // Find jobs that are in open status and populate company information
        jobs = await SpJob.find(query)
          .populate({
            path: 'spCompanyId',
            select: 'name' // Only get the company name
          })
          .lean(); // Convert to plain JavaScript objects
    
        if (!jobs || jobs.length === 0) {
          console.log('No jobs found');
          return errorResponse('No jobs found', 404);
        }
    
        // Transform the data to include companyName directly
        const jobsWithCompany = jobs.map(job => ({
          ...job,
          companyName: job.spCompanyId?.name || 'Unknown Company'
        }));
    
        console.log('Found jobs:', jobsWithCompany.length);
        return successResponse(jobsWithCompany);
      } catch (error) {
        console.error('Could not fetch jobs', error);
        return errorResponse(error.message || 'Failed to fetch jobs', 500);
      }
}


