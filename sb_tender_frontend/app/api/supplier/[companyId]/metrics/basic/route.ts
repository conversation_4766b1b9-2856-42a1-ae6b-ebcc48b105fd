import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpApplication from '@/lib/models/sp_application';
import SpUserRole from '@/lib/models/sp_user_role';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';
import SpJob from '@/lib/models/sp_job';


// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET metric Company By ID - Starting ===');
    //await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    console.log('Step 1: Finding company...');
    const company = await SpCompany.findById(companyId);
    
    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }

    console.log('Company found:', { _id: company._id, name: company.name, type: company.type });
    console.log('=== GET Company By ID - Success ===');
    
    // Use Promise.all to fetch all counts concurrently for efficiency
        const [
          totalApplications,
          pendingApplications, // Applications in 'draft', 'submitted', or 'under_review' state
          completedApplications,  // Applications that are  expired
          closedApplications,  // Applications that are 'approved' or 'rejected'
          approvedApplications, // Applications that are 'approved'
          totalUsers,
        ] = await Promise.all([
          // 1. Total Applications: Count all applications for the company
          SpApplication.countDocuments({ spSupplierCompanyId: companyId }),
    
          // 2. Pending Applications: Count applications that are not yet in a final state
          SpApplication.countDocuments({
            spSupplierCompanyId: companyId,
            status: { $in: ['draft', 'submitted', 'under_review'] }
          }),
    
          // 3. Closed awards / regret Applications: Count applications that have reached a final decision
          SpApplication.countDocuments({
            spSupplierCompanyId: companyId,
            status: { $in: ['approved', 'rejected'] }
          }),
    
          // 4. Closed Applications: Count applications where the 'ends' date is on or before now
          SpApplication.countDocuments({
            spSupplierCompanyId: companyId,
            ends: { $lte: now } // Check if the end date is less than or equal to the current date
          }),
    
          // 5. Approved Applications: Count applications specifically in the 'approved' state
          SpApplication.countDocuments({
            spSupplierCompanyId: companyId,
            status: 'approved'
          }),
    
          // 6. Total Users: Call the updated getTotalUsers function
          getTotalUsers(companyId)
        ]);
    
        // Send the calculated metrics in the response
        console.log('=== GET Supplier Dashboard Metrics - Success ===');

        const metrics = {
          totalApplications,
          pendingApplications,
          completedApplications,
          closedApplications,
          approvedApplications,
          totalUsers,
        };




    return successResponse(metrics);

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}


/**
 * Fetches the total number of UNIQUE users associated with a given company.
 * Since a user can have multiple roles within the same company, this function
 * uses `distinct` to count each user only once.
 * @param {string} companyId - The ID of the company.
 * @returns {Promise<number>} - The total count of unique users for the company.
 */
const getTotalUsers = async (companyId) => {
  try {
    // Use distinct to get unique user IDs for the given company
    const uniqueUserIds = await SpUserRole.distinct('userId', { companyId: companyId });
    return uniqueUserIds.length; // The count of unique users is the length of the distinct array
  } catch (error) {
    console.error("Error fetching total users:", error);
    throw new Error("Failed to get total users count.");
  }
};