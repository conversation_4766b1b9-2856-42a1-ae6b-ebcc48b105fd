import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpCompany from '@/lib/models/sp_company';
import SpApplication from '@/lib/models/sp_application';
import SpJobCategory from '@/lib/models/sp_job_category';
import SpUserRole from '@/lib/models/sp_user_role';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import { uploadToS3, deleteFromS3 } from '@/lib/services/s3Service';
import mongoose from 'mongoose';
import _ from "lodash";

// GET /api/admin/companies/:id - Get company by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const now = new Date(); 

    console.log('=== GET Company By ID - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId } = await params;
    console.log('Company ID:', companyId);

    if (!companyId) {
      console.error('Company ID is missing');
      return errorResponse('Company ID is required', 400);
    }

    console.log('Step 1: Finding company...');
    const company = await SpCompany.findById(companyId);
    
    if (!company) {
      console.error('Company not found:', companyId);
      return errorResponse('Company not found', 404);
    }

    console.log('Company found:', { _id: company._id, name: company.name, type: company.type });
    console.log('=== GET Company By ID - Success ===');
    
     const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
      
          // Fetch all applications and categories created in the last year
          const [applications, categories] = await Promise.all([
            SpApplication.find({
              spSupplierCompanyId: companyId,
              createdAt: { $gte: oneYearAgo, $lte: now }
            }),
            SpJobCategory.find({
              createdAt: { $gte: oneYearAgo, $lte: now }
            })
          ]);
      
          // Group by month
          const monthlyApplications = _.groupBy(applications, (app) => {
            const date = new Date(app.createdAt);
            return date.toLocaleString('default', { month: 'short' });
          });
      
          const monthlyCategories = _.groupBy(categories, (cat) => {
            const date = new Date(cat.createdAt);
            return date.toLocaleString('default', { month: 'short' });
          });
      
          // Prepare the final data
          const activityData = [];
          for (let i = 0; i < 12; i++) {
            const month = now.toLocaleString('default', { month: 'short' });
            activityData.unshift({
              name: month,
              applications: (monthlyApplications[month] || []).length,
              categories: (monthlyCategories[month] || []).length
            });
            now.setMonth(now.getMonth() - 1);
          }

    return successResponse(activityData);

  } catch (error: any) {
    console.error('=== GET Company By ID - Error ===');
    console.error('Get company error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(error.message || 'Failed to fetch company', 500);
  }
}

