import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import SpUser from '@/lib/models/sp_user';
import SpRole from '@/lib/models/sp_role';
import SpUserRole from '@/lib/models/sp_user_role';
import { requireSupplier } from '@/lib/middleware/auth';
import { successResponse, errorResponse } from '@/lib/utils/apiResponse';
import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';

// PUT /api/admin/companies/:companyId/users/:userId - Update company user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; userId: string }> }
) {
  try {
    console.log('=== PUT Update Company User - Starting ===');
    const adminUser = await requireSupplier(request);
    await connectToDatabase();

    const { companyId, userId } = await params;
    const body = await request.json();
    
    console.log('Request parameters:', { companyId, userId });
    console.log('Admin user:', { userId: adminUser.userId, type: adminUser.type });
    console.log('Update request body:', body);
    
    const {
      firstName,
      lastName,
      email,
      password,
      phone,
      type,
      status,
      roleIds,
      emailVerified,
      phoneVerified,
      otpVerified,
      mustUpdatePassword
    } = body;

    // Find existing user
    console.log('Step 1: Finding existing user...');
    const user = await SpUser.findById(userId);

    if (!user) {
      console.error('User not found:', userId);
      return errorResponse('User not found', 404);
    }
    
    console.log('User found:', { _id: user._id, email: user.email, type: user.type });

    // Check for duplicate email (excluding current user)
    console.log('Step 2: Checking for email conflicts...');
    if (email && email !== user.email) {
      const existingUser = await SpUser.findOne({
        email,
        _id: { $ne: userId }
      });
      if (existingUser) {
        console.error('Email conflict detected:', email);
        return errorResponse('User with this email already exists', 409);
      }
    }
    console.log('Email check passed');

    // Check for phone conflicts (if phone is being updated)
    console.log('Step 3: Checking for phone conflicts...');
    if (phone && phone !== user.phone) {
      const phoneConflict = await SpUser.findOne({ phone, _id: { $ne: userId } });
      if (phoneConflict) {
        console.error('Phone conflict detected:', phone);
        return errorResponse('User with this phone number already exists', 409);
      }
    }
    console.log('Phone check passed');

    // Prepare update object with only provided fields
    console.log('Step 4: Preparing update data...');
    const updateData: any = {};
    
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (type !== undefined) updateData.type = type;
    if (status !== undefined) updateData.status = status;
    if (emailVerified !== undefined) updateData.emailVerified = emailVerified;
    if (phoneVerified !== undefined) updateData.phoneVerified = phoneVerified;
    if (otpVerified !== undefined) updateData.otpVerified = otpVerified;
    if (mustUpdatePassword !== undefined) updateData.mustUpdatePassword = mustUpdatePassword;

    // Add updatedBy field
    updateData.updatedBy = adminUser.userId;

    console.log('Update data prepared:', Object.keys(updateData));

    // Handle password update if provided
    if (password) {
      console.log('Step 5: Hashing new password...');
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(password, salt);
      console.log('Password hashed successfully');
    }

    // Update the user
    console.log('Step 6: Updating user...');
    const updatedUser = await SpUser.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      console.error('Failed to update user');
      return errorResponse('Failed to update user', 404);
    }

    console.log('User updated successfully');

    // Handle role updates if roleIds are provided
    if (roleIds !== undefined) {
      console.log('Step 7: Processing role updates...');
      console.log('New role IDs:', roleIds);

      // Remove existing SpUserRole entries for this user and company
      console.log('Step 7a: Removing existing SpUserRole entries...');
      const deleteResult = await SpUserRole.deleteMany({
        userId: new mongoose.Types.ObjectId(userId),
        companyId: new mongoose.Types.ObjectId(companyId)
      });
      console.log(`Removed ${deleteResult.deletedCount} existing SpUserRole entries.`);

      // Add new roles if roleIds array is not empty
      if (Array.isArray(roleIds) && roleIds.length > 0) {
        console.log('Step 7b: Adding new roles...');
        
        // Find the roles based on provided roleIds and appropriate context
        let contextFilter = {};
        if (updatedUser.type === 'buyer') {
          contextFilter = { context: 'buyer' };
        } else if (updatedUser.type === 'supplier') {
          contextFilter = { context: 'supplier' };
        } else if (updatedUser.type === 'admin') {
          contextFilter = { context: { $in: ['admin', 'buyer', 'supplier'] } }; // Admin can have multiple contexts
        }

        const validRoles = await SpRole.find({ 
          _id: { $in: roleIds },
          ...contextFilter
        });

        console.log(`Found ${validRoles.length} valid roles for user type '${updatedUser.type}'.`);
        console.log('Valid roles:', validRoles.map(r => ({ 
          _id: r._id, 
          name: r.name, 
          context: r.context, 
          type: r.type 
        })));

        if (validRoles.length === 0) {
          console.warn(`No valid roles found for user type '${updatedUser.type}' with provided roleIds.`);
        } else {
          // Create new SpUserRole entries
          const newSpUserRoles = validRoles.map(role => ({
            userId: new mongoose.Types.ObjectId(userId),
            companyId: new mongoose.Types.ObjectId(companyId),
            roleId: role._id,
          }));

          console.log('Prepared SpUserRoles for insertion:', newSpUserRoles.length);
          
          await SpUserRole.insertMany(newSpUserRoles);
          console.log(`Successfully inserted ${newSpUserRoles.length} new SpUserRole entries.`);
        }

        // Warn about invalid role IDs if any
        const validRoleIds = validRoles.map(r => r._id.toString());
        const invalidRoleIds = roleIds.filter((id: string) => !validRoleIds.includes(id.toString()));
        if (invalidRoleIds.length > 0) {
          console.warn(`Invalid or incompatible role IDs provided: ${invalidRoleIds.join(', ')}`);
        }
      } else {
        console.log('No new roles to assign (empty or invalid roleIds array).');
      }
    }

    // Get updated user with current roles for response
    console.log('Step 8: Fetching updated user with roles...');
    const userRoles = await SpUserRole.find({ 
      userId: new mongoose.Types.ObjectId(userId), 
      companyId: new mongoose.Types.ObjectId(companyId) 
    });
    
    const roleIds_final = userRoles.map(ur => ur.roleId);
    const roles = await SpRole.find({ _id: { $in: roleIds_final } });

    console.log(`User update completed. User now has ${userRoles.length} roles assigned.`);

    // Helper function to exclude sensitive fields from the user object
    const excludeSensitiveFields = (user: any) => {
        if (!user) return null;
        const userObject = user.toObject ? user.toObject() : user; // Convert Mongoose doc to plain object
        delete userObject.password;
        return userObject;
    };

    const responseData = {
      ...excludeSensitiveFields(updatedUser),
      roles: roles,
      message: 'Company user updated successfully.'
    };

    console.log('=== PUT Update Company User - Success ===');
    return successResponse(responseData);

  } catch (error: any) {
    console.error('=== PUT Update Company User - Error ===');
    console.error('Update company user error:', error);
    console.error('Error stack:', error.stack);
    
    // Handle potential duplicate key errors
    if (error.code === 11000) {
      console.error('Duplicate key error detected:', error.message);
      return errorResponse('Duplicate field value entered', 409);
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      console.error('Validation error detected:', error.message);
      return errorResponse('Validation error: ' + error.message, 400);
    }
    
    return errorResponse(error.message || 'Failed to update company user', 500);
  }
}

// DELETE /api/admin/companies/:companyId/users/:userId - Delete company user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; userId: string }> }
) {
  try {
    console.log('=== DELETE Company User - Starting ===');
    await requireSupplier(request);
    await connectToDatabase();

    const { companyId, userId } = await params;

    console.log('Request parameters:', { companyId, userId });

    if (!companyId || !userId) {
      console.error('Missing required parameters:', { companyId, userId });
      return errorResponse('Company ID and User ID are required', 400);
    }

    console.log('Step 1: Finding user...');
    const user = await SpUser.findById(userId);

    if (!user) {
      console.error('User not found:', userId);
      return errorResponse('User not found', 404);
    }

    console.log('User found:', { _id: user._id, email: user.email, type: user.type });

    // Soft delete - set status to inactive instead of hard delete
    console.log('Step 2: Performing soft delete (setting status to inactive)...');
    user.status = 'inactive';
    await user.save();

    console.log('User status updated to inactive successfully');
    console.log('=== DELETE Company User - Success ===');

    return successResponse({ message: 'User deleted successfully' });

  } catch (error: any) {
    console.error('=== DELETE Company User - Error ===');
    console.error('Delete company user error:', error);
    console.error('Error stack:', error.stack);
    
    // Handle invalid ID format errors
    if (error.kind === 'ObjectId') {
      console.error('Invalid User ID format:', error.message);
      return errorResponse('Invalid User ID format', 400);
    }
    
    return errorResponse(error.message || 'Failed to delete company user', 500);
  }
}
