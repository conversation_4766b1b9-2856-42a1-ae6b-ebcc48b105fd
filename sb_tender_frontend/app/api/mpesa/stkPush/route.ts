import { NextResponse, NextRequest } from 'next/server';
import axios from 'axios';

const BACKEND_API_URL = process.env.NODE_API_URL || 'http://localhost:5000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const headers = new Headers(request.headers);
    const token = headers.get('authorization') || '';
    const newBody = JSON.stringify(body);
    const backendRes = await fetch(`${BACKEND_API_URL}/api/payment/checkout/mobile`, {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
      body: newBody,
    });
    
    const result = await backendRes.json();
    return NextResponse.json(result, { status: backendRes.status });
  } catch (error: any) {
    return NextResponse.json({ error: error.response?.data?.error || 'Error creating job.' }, { status: 401 });
  }
}

