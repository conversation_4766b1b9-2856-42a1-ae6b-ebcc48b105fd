"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Home, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 p-4 dark:bg-gray-900">
      <Card className="w-full max-w-md border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-950">
        <CardHeader>
          <div className="flex items-center gap-3">
            <AlertCircle className="h-6 w-6 text-gray-900 dark:text-gray-50" />
            <CardTitle className="text-gray-900 dark:text-gray-50">Something went wrong</CardTitle>
          </div>
        </CardHeader>
        
        <CardContent>
          <Alert variant="destructive" className="border-gray-200 bg-gray-100 dark:border-gray-800 dark:bg-gray-900">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle className="text-gray-900 dark:text-gray-50">Error</AlertTitle>
            <AlertDescription className="text-gray-700 dark:text-gray-300">
              {error.message || "An unexpected error occurred"}
            </AlertDescription>
          </Alert>
          
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            <p>Error code: {error.digest || "UNKNOWN"}</p>
            <p className="mt-2">Please try again or contact support if the problem persists.</p>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end gap-2">
          <Button
            variant="outline"
            className="border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800"
            onClick={() => router.push("/")}
          >
            <Home className="mr-2 h-4 w-4" />
            Home
          </Button>
          <Button
            variant="default"
            className="bg-gray-900 text-gray-50 hover:bg-gray-800 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-200"
            onClick={() => reset()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}