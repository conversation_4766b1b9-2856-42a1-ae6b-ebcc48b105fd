'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from '@/app/ui/public.module.css';
import toast, { Toaster } from 'react-hot-toast';
import Image from "next/image";
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Mail } from "lucide-react";
import Link from "next/link";

export default function ForgotPassword() {
  return (
    <div>
      <ForgotPasswordForm />
    </div>
  );
}

function ForgotPasswordForm() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const router = useRouter();
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  const notify = (message: string, icon: string) =>
    toast(message, {
      icon: icon,
    });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      notify('Email is required', '❌');
      return;
    }

    if (!validateEmail(email)) {
      notify('Please enter a valid email address', '❌');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${BACKEND_API_URL}/api/auth/forgot-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setEmailSent(true);
        notify('Password reset link sent to your email', '✅');
      } else {
        notify(data.message || 'Failed to send reset email', '❌');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      notify('An error occurred. Please try again.', '❌');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <div className="flex flex-col gap-6">
          <Toaster />
          <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
            <a href="/">Back Home</a>
          </div>
          <Card className="overflow-hidden">
            <CardContent className="grid p-0 md:grid-cols-2">
              <form onSubmit={handleSubmit} className="p-6 md:p-8">
                <div className="flex flex-col gap-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-4">
                      <Mail className="w-6 h-6 text-red-600" />
                    </div>
                    <h1 className="text-2xl font-bold">Forgot Password?</h1>
                    <p className="text-balance text-muted-foreground">
                      {emailSent 
                        ? "Check your email for reset instructions"
                        : "Enter your email address and we'll send you a link to reset your password"
                      }
                    </p>
                  </div>

                  {!emailSent ? (
                    <>
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          name="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={handleChange}
                          required
                          disabled={loading}
                        />
                      </div>
                      <Button 
                        type="submit" 
                        className="w-full bg-red-600 hover:bg-red-700"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Sending...
                          </>
                        ) : (
                          'Send Reset Link'
                        )}
                      </Button>
                    </>
                  ) : (
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <Mail className="h-5 w-5 text-green-400" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-green-800">
                              Email sent successfully!
                            </p>
                            <p className="text-sm text-green-700 mt-1">
                              We've sent a password reset link to <strong>{email}</strong>
                            </p>
                          </div>
                        </div>
                      </div>
                      <Button 
                        type="button"
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          setEmailSent(false);
                          setEmail('');
                        }}
                      >
                        Send to Different Email
                      </Button>
                    </div>
                  )}

                  <div className="text-center text-sm">
                    <Link 
                      href="/auth/login" 
                      className="inline-flex items-center text-muted-foreground hover:text-primary"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Login
                    </Link>
                  </div>
                </div>
              </form>
              <div className="relative hidden bg-muted md:block">
                <Image
                  src="/login-image.png"
                  alt="Forgot Password"
                  className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
                  width={800}
                  height={600}
                />
              </div>
            </CardContent>
          </Card>
          <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
            Don't have an account?{" "}
            <a href="/auth/register">Sign up</a>
          </div>
        </div>
      </div>
    </div>
  );
}
