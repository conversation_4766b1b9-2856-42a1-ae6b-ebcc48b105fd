'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation'; // Import useRouter for client-side navigation
import styles from '@/app/ui/public.module.css';
import toast, { Toaster } from 'react-hot-toast';
import { useAuthStore } from '@/lib/store/useAuthStore';

import Image from "next/image";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Eye, EyeOff } from "lucide-react"

export default function Register() {
  return (
    <div>
      <RegisterForm />
    </div>
  );
}

function RegisterForm() {
  const [formData, setFormData] = useState({ companyName: '', firstName: '', lastName: '', email: '', phone: '', password: '', type: 'supplier' });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter(); // Initialize useRouter for navigation
  const notify = (message: string, icon: string) =>
    toast(message, {
      icon: icon,
    });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
  
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
      const response = await fetch(`/api/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });
  
      const data = await response.json();
  
      if (data.success && data.data.token) {
        // Save token in cookie
        document.cookie = `token=${data.data.token}; path=/;`;

        // Access auth store methods directly
        const { setUser, setToken, setCompanies, setActiveCompanyId } = useAuthStore.getState();

        // Save user and token to store
        setUser(data.data.user);
        setToken(data.data.token);

        notify('Registration successful! Redirecting to your dashboard...', '👏');

        // Handle redirection based on user type
        if (data.data.user.type === 'admin') {
          // Admin users go to admin dashboard
          router.push('/admin/dashboard');
        } else {
          // Supplier/Buyer users go to their company dashboard
          if (data.data.user?.companies?.length > 0) {
            setCompanies(data.data.user.companies);
            setActiveCompanyId(data.data.user.companies[0]?.companyId);

            const firstCompanyId = data.data.user.companies[0]?.companyId;
            const firstCompanyType = data.data.user.companies[0]?.companyType;

            router.push(`/${firstCompanyType}/${firstCompanyId}/dashboard`);
          } else {
            // Fallback if no company data
            router.push(`/${data.data.user.type}/dashboard`);
          }
        }
      } else {
        notify('Error: ' + (data.error || 'Registration failed'), '😢');
      }
    } catch (error) {
      console.error('An error occurred:', error);
      notify('An error occurred. Please try again.', '😢');
    } finally {
      setLoading(false);
    }
  };
  

  const [error, setError] = useState(null);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';


  const signInWithGoogle = async (isRegister = true, companyName = formData.companyName ||'', phone = formData.phone ||'') => {
    setLoading(true);
    setError(null);

    try {
      if (companyName === '' || companyName.length < 3 || phone === '' || phone.length < 9) {
        notify('At least enter your Supplier Company name and Phone to Sign Up with Google.', '😢');
        return;
      }
      // Determine the backend endpoint based on whether it's a login or registration
      let backendEndpoint = `${BACKEND_API_URL}/api/login/federated/google?type=register&companyName=${companyName}&phone=${phone}`; // Default for login
      window.location.href = backendEndpoint ;
     
    } catch (error: any) {
      console.error('Google authentication error:', error);
      notify('An error occurred during Google authentication. Please try using email/password/phone to sign up or try again later.', '😢');
      setError(error.message || 'An unknown error occurred during Google authentication.'); // Set error state
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleRegister = () => {
    signInWithGoogle(false);
  };


  return (
    
<div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
<div className="w-full max-w-sm md:max-w-3xl">

    <div className="flex flex-col gap-6">
    <Toaster />
    <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        <a href="/">Back Home</a>
      </div>
      <Card className="overflow-hidden">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form  onSubmit={handleSubmit} className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Welcome to Supplier World</h1>
                <p className="text-balance text-muted-foreground">
                  Create your Tender Asili Supplier account
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Company Name</Label>
                <Input
                  id="companyName"
                  type="text"
                  name="companyName"
                  required
                  placeholder="Enter your supplier company name"
                  value={formData.companyName}
                  onChange={handleChange}
                />
              </div>
              <div className="grid grid-cols-2 gap-1">
              <div className="grid gap-2">
                <Label htmlFor="email">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  name="firstName"
                  required
                  placeholder="Enter your first name"
                  value={formData.firstName}
                  onChange={handleChange}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  name="lastName"
                  required
                  placeholder="Enter your last name"
                  value={formData.lastName}
                  onChange={handleChange}
                />
              </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  name="phone"
                  required
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  required
                  name="email"
                  placeholder="Enter <NAME_EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <a
                    href="/auth/forgot-password"
                    className="ml-auto text-sm underline-offset-2 hover:underline">
                    Forgot your password?
                  </a>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    required
                    name="password"
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleChange}
                    className="pr-10"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              <Button type="submit" 
              disabled={loading}
              className={`w-full ${ loading ? 'opacity-50 cursor-not-allowed' : '' }`}
            >
              {loading ? 'Signing up...' : 'Sign up'}
             
              </Button>
              <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
              <div className="grid grid-cols-1 gap-4">
              
                <Button variant="outline" className="w-full"
                                        onClick={(e)=> { e.preventDefault(); handleGoogleRegister(); }}
>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">Sign up with Google</span>
                </Button>
               
              </div>
              <div className="text-center text-sm">
                Already have an account?{" "}
                <a href="/auth/login" className="underline underline-offset-4">
                  Login
                </a>
              </div>
            </div>
          </form>
          <div className="relative hidden bg-muted md:block">
           
                <Image src="/login-image.png"
                  width={200}
                  height={200}
                  alt="Picture of the author"
                  className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale rounded-tl-lg rounded-bl-lg "

                />
          </div>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
        and <a href="#">Privacy Policy</a>.
      </div>
    </div>
    </div>
  </div>


  );
}