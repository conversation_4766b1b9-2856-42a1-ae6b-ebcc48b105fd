"use client"

import * as React from "react"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { useRouter } from 'next/navigation';

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Mail,
  Phone,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Bell,
  MessageSquare,
  Smartphone,
  Settings,
  AlertTriangle,
  Plus,
  Trash2
} from "lucide-react";

// Define the type for the user data
export type CurrentUser = {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    status: 'active' | 'inactive' | 'suspended';
    phoneVerified: boolean;
    emailVerified: boolean;
    lastLogin: string;
    createdAt: string;
    role: string;
    notificationPreferences?: {
      tenderAlerts: Array<{
        location: string;
        category: string;
      }>;
      emailAlerts: {
        enabled: boolean;
        categories: {
          system: boolean;
          orders: boolean;
          marketing: boolean;
          reminders: boolean;
          tender: boolean;
        };
      };
      smsAlerts: {
        enabled: boolean;
        categories: {
          system: boolean;
          orders: boolean;
          marketing: boolean;
          reminders: boolean;
          tender: boolean;
        };
      };
      appAlerts: {
        enabled: boolean;
        categories: {
          system: boolean;
          orders: boolean;
          marketing: boolean;
          reminders: boolean;
          tender: boolean;
        };
      };
      pushAlerts: {
        enabled: boolean;
        categories: {
          system: boolean;
          orders: boolean;
          marketing: boolean;
          reminders: boolean;
          tender: boolean;
        };
      };
    };
};

export default function UserPage() {
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('tender-alerts');
  const [notificationPrefs, setNotificationPrefs] = useState<CurrentUser['notificationPreferences']>();
  const [newTenderAlert, setNewTenderAlert] = useState({
    category: '',
    locations: [] as string[]
  });
  const [isSavingTenderAlert, setIsSavingTenderAlert] = useState(false);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  // Categories for tender alerts
  const TENDER_CATEGORIES = [
    { label: 'CONSULTANCY-Professional', value: 'consultancy_professional' },
    { label: 'GOODS-Agricultural', value: 'goods_agricultural' },
    { label: 'GOODS-ALL', value: 'goods_all' },
    { label: 'GOODS-Artifacts', value: 'goods_artifacts' },
    { label: 'GOODS-Automotive/Parts', value: 'goods_automotive_parts' },
    { label: 'GOODS-Branded Merchandise', value: 'goods_branded_merchandise' },
    { label: 'GOODS-Construction', value: 'goods_construction' },
    { label: 'GOODS-Cosmetics/Beauty products', value: 'goods_cosmetics_beauty_products' },
    { label: 'GOODS-Electrical accessories', value: 'goods_electrical_accessories' },
    { label: 'GOODS-Food/beverage', value: 'goods_food_beverage' },
    { label: 'GOODS-Furniture/office', value: 'goods_furniture_office' },
    { label: 'GOODS-Information Technology', value: 'goods_information_technology' },
    { label: 'GOODS-Kitchen supplies', value: 'goods_kitchen_supplies' },
    { label: 'GOODS-Land/building', value: 'goods_land_building' },
    { label: 'GOODS-Medical/pharmaceutical', value: 'goods_medical_pharmaceutical' },
    { label: 'GOODS-Minerals', value: 'goods_minerals' },
    { label: 'GOODS-Personal care/hygiene', value: 'goods_personal_care_hygiene' },
    { label: 'GOODS-Petroleum/Energy', value: 'goods_petroleum_energy' },
    { label: 'GOODS-Sports/Wellness', value: 'goods_sports_wellness' },
    { label: 'GOODS-Stationery/Publications', value: 'goods_stationery_publications' },
    { label: 'GOODS-Textile/apparel', value: 'goods_textile_apparel' },
    { label: 'SERVICES-Agricultural', value: 'services_agricultural' },
    { label: 'SERVICES-ALL', value: 'services_all' },
    { label: 'SERVICES-Asset audit', value: 'services_asset_audit' },
    { label: 'SERVICES-Auctioneering', value: 'services_auctioneering' },
    { label: 'SERVICES-Auto maintenance', value: 'services_auto_maintenance' },
    { label: 'SERVICES-Automobile rental', value: 'services_automobile_rental' },
    { label: 'SERVICES-Business Development/Strategy', value: 'services_business_development_strategy' },
    { label: 'SERVICES-Construction', value: 'services_construction' },
    { label: 'SERVICES-Digital Marketing', value: 'services_digital_marketing' },
    { label: 'SERVICES-Disposal of assets', value: 'services_disposal_of_assets' },
    { label: 'Services-Engineering', value: 'services_engineering' },
    { label: 'SERVICES-Equipment rental', value: 'services_equipment_rental' },
    { label: 'SERVICES-Events/entertainment', value: 'services_events_entertainment' },
    { label: 'SERVICES-Financial', value: 'services_financial' },
    { label: 'SERVICES-Gym & fitness', value: 'services_gym_fitness' },
    { label: 'SERVICES-Hospitality/Tourism/travel', value: 'services_hospitality_tourism_travel' },
    { label: 'SERVICES-HR/Training/development', value: 'services_hr_training_development' },
    { label: 'SERVICES-Import/export', value: 'services_import_export' },
    { label: 'SERVICES-Information technology', value: 'services_information_technology' },
    { label: 'SERVICES-Insurance', value: 'services_insurance' },
    { label: 'SERVICES-Land/building rental', value: 'services_land_building_rental' },
    { label: 'SERVICES-Legal', value: 'services_legal' },
    { label: 'SERVICES-Manufacturing', value: 'services_manufacturing' },
    { label: 'SERVICES-Marketing & communication', value: 'services_marketing_communication' },
    { label: 'SERVICES-Medical', value: 'services_medical' },
    { label: 'SERVICES-PROJECT MANAGEMENT', value: 'services_project_management' },
    { label: 'SERVICES-Research', value: 'services_research' },
    { label: 'SERVICES-Safety/security', value: 'services_safety_security' },
    { label: 'SERVICES-Sanitation/maintenance/Casual', value: 'services_sanitation_maintenance_casual' },
    { label: 'SERVICES-Transport/Courier/Relocation', value: 'services_transport_courier_relocation' },
    { label: 'WORKS-ALL', value: 'works_all' },
    { label: 'WORKS-Artifacts', value: 'works_artifacts' },
    { label: 'WORKS-Borehole/dams', value: 'works_borehole_dams' },
    { label: 'WORKS-Buildings/fixtures', value: 'works_buildings_fixtures' },
    { label: 'WORKS-Environmental/energy', value: 'works_environmental_energy' },
    { label: 'Works-Plumbing', value: 'works_plumbing' },
    { label: 'WORKS-Roads', value: 'works_roads' },
    { label: 'WORKS-Welding', value: 'works_welding' }
  ];

  // Locations for tender alerts
  const TENDER_LOCATIONS = [
    'countrywide', 'mombasa_county', 'kwale_county', 'kilifi_county', 'tana_river_county',
    'lamu_county', 'taita_taveta', 'garissa_county', 'wajir_county', 'mandera_county',
    'marsabit_county', 'isiolo_county', 'meru_county', 'tharaka_nithi', 'embu_county',
    'kitui_county', 'machakos_county', 'makueni_county', 'nyandarua_county', 'nyeri_county',
    'kirinyaga_county', 'muranga_county', 'kiambu_county', 'turkana_county', 'west_pokot_county',
    'samburu_county', 'transzoia_county', 'uasin_gishu', 'elgeyo_marakwet', 'nandi_county',
    'baringo_county', 'laikipia_county', 'nakuru_county', 'narok_county', 'kajiado_county',
    'kericho_county', 'bomet_county', 'vihiga_county', 'bungoma_county', 'busia_county',
    'siaya_county', 'kisumu_county', 'homabay_county', 'migori_county', 'kisii_county',
    'nyamira_county', 'nairobi_county', 'kakamega_county', 'south_sudan', 'tanzania',
    'uganda', 'rwanda', 'burundi', 'ethiopia'
  ];

  // Initialize react-hook-form
  const { register, handleSubmit, reset, formState: { isSubmitting } } = useForm<CurrentUser & { password?: string }>();

  // Get token from cookies and fetch user data on component mount
  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
      toast.error("Authentication token missing. Please log in.");
      setIsLoading(false);
      // Optional: Redirect to login page if no token
      // router.push('/login');
    }
  }, []);

  // Fetch user data when token is available
  useEffect(() => {
    if (token) {
      fetchCurrentUser();
    }
  }, [token]); // Depend on token

  // Function to fetch the current user's data
  const fetchCurrentUser = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      const { data } = await axios.get<CurrentUser>('/api/profile', {
      //const { data } = await axios.get<CurrentUser>(`${BACKEND_API_URL}/api/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      setCurrentUser(data);
      setNotificationPrefs(data.notificationPreferences);
      // Populate the form with fetched data
      reset(data);
    } catch (error) {
      console.error("Failed to fetch user data:", error);
      toast.error('Failed to fetch user data');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions for status indicators
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active', icon: CheckCircle },
      inactive: { color: 'bg-gray-100 text-gray-800', label: 'Inactive', icon: XCircle },
      suspended: { color: 'bg-red-100 text-red-800', label: 'Suspended', icon: AlertTriangle }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const IconComponent = config.icon;
    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getVerificationBadge = (verified: boolean, type: 'email' | 'phone') => {
    const IconComponent = verified ? CheckCircle : XCircle;
    const color = verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    const label = verified ? 'Verified' : 'Not Verified';

    return (
      <Badge className={color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Function to update notification preferences
  const updateNotificationPreferences = async (newPrefs: CurrentUser['notificationPreferences']) => {
    if (!token || !currentUser) {
      toast.error("Cannot save preferences. Authentication missing.");
      return;
    }

    try {
      //const { data } = await axios.put('/api/profile/user',
      const { data } = await axios.put(`${BACKEND_API_URL}/api/profile/user`,
        { notificationPreferences: newPrefs },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
        }
      );

      setCurrentUser(data);
      setNotificationPrefs(newPrefs);
      toast.success("Notification preferences updated successfully!");
    } catch (error: any) {
      console.error("Failed to update preferences:", error);
      const errorMessage = error.response?.data?.message || 'Failed to update preferences';
      toast.error(errorMessage);
    }
  };

  // Helper functions for tender alerts
  const formatLocationName = (location: string) => {
    return location.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatCategoryName = (category: string) => {
    const categoryObj = TENDER_CATEGORIES.find(cat => cat.value === category);
    return categoryObj ? categoryObj.label : category;
  };

  const addTenderAlert = async () => {
    if (!newTenderAlert.category || newTenderAlert.locations.length === 0) {
      toast.error("Please select a category and at least one location");
      return;
    }

    if (!notificationPrefs) return;

    setIsSavingTenderAlert(true);

    try {
      // Filter out locations that already exist for this category
      const existingAlerts = notificationPrefs.tenderAlerts || [];
      const newLocations = newTenderAlert.locations.filter(location =>
        !existingAlerts.some(alert =>
          alert.category === newTenderAlert.category && alert.location === location
        )
      );

      if (newLocations.length === 0) {
        toast.error("All selected locations already exist for this category");
        return;
      }

      // Create new tender alerts for each new location
      const newAlerts = newLocations.map(location => ({
        category: newTenderAlert.category,
        location: location
      }));

      const updatedPrefs = {
        ...notificationPrefs,
        tenderAlerts: [...existingAlerts, ...newAlerts]
      };

      await updateNotificationPreferences(updatedPrefs);

      // Reset form
      setNewTenderAlert({
        category: '',
        locations: []
      });

      if (newLocations.length < newTenderAlert.locations.length) {
        toast.success(`Added ${newLocations.length} new alerts. ${newTenderAlert.locations.length - newLocations.length} duplicates were skipped.`);
      } else {
        toast.success(`Added ${newLocations.length} new tender alerts successfully!`);
      }
    } catch (error) {
      console.error('Error adding tender alert:', error);
    } finally {
      setIsSavingTenderAlert(false);
    }
  };

  const removeTenderAlert = async (index: number) => {
    if (!notificationPrefs) return;

    const updatedAlerts = [...(notificationPrefs.tenderAlerts || [])];
    updatedAlerts.splice(index, 1);

    const updatedPrefs = {
      ...notificationPrefs,
      tenderAlerts: updatedAlerts
    };

    await updateNotificationPreferences(updatedPrefs);
  };

  const handleLocationToggle = (location: string) => {
    setNewTenderAlert(prev => ({
      ...prev,
      locations: prev.locations.includes(location)
        ? prev.locations.filter(loc => loc !== location)
        : [...prev.locations, location]
    }));
  };

  // Group tender alerts by category
  const groupedTenderAlerts = () => {
    if (!notificationPrefs?.tenderAlerts) return {};

    return notificationPrefs.tenderAlerts.reduce((acc, alert) => {
      if (!acc[alert.category]) {
        acc[alert.category] = [];
      }
      acc[alert.category].push(alert.location);
      return acc;
    }, {} as Record<string, string[]>);
  };

  // Check if a location is already selected for the current category
  const isLocationAlreadySelected = (location: string) => {
    if (!newTenderAlert.category || !notificationPrefs?.tenderAlerts) return false;

    return notificationPrefs.tenderAlerts.some(alert =>
      alert.category === newTenderAlert.category && alert.location === location
    );
  };

  // Get available locations (not already selected for current category)
  const getAvailableLocations = () => {
    if (!newTenderAlert.category) return TENDER_LOCATIONS;

    return TENDER_LOCATIONS.filter(location => !isLocationAlreadySelected(location));
  };

  // Function to handle form submission for updating user details
  const onUpdateSubmit = async (formData: CurrentUser & { password?: string }) => {
    if (!token || !currentUser) {
      toast.error("Cannot save changes. Authentication missing.");
      return;
    }

    setIsLoading(true);
    try {
      // Prepare the data payload for the PUT request
      const updatePayload: any = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        // Only include password if the user has typed something
        ...(formData.password && { password: formData.password }),
      };

      // Assuming your backend has a protected endpoint like /api/users/me
      const { data } = await axios.put('/api/profile', updatePayload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("User updated successfully:", data);
      toast.success("Profile updated successfully!");
      // Update current user state and reset form with potentially new data (e.g., if email changed)
      setCurrentUser(data);
      reset(data);
      // Clear the password field after successful update for security
      //setValue('password', ''); //to check

    } catch (error: any) {
      console.error("Failed to update user:", error);
       const errorMessage = error.response?.data?.message || 'Failed to update profile';
       toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

    // Function to open the delete confirmation dialog
    const handleDeleteAccountClick = () => {
        setIsDeleteDialogOpen(true);
    };

    // Function to confirm and send the delete request
    const confirmDeleteAccount = async () => {
        if (!token || !currentUser) {
             toast.error("Cannot delete account. Authentication missing.");
             return;
        }

        setIsLoading(true);
        try {
            // Assuming your backend has a protected DELETE endpoint like /api/users/me
            await axios.delete('/api/profile', {
                 headers: {
                    Authorization: `Bearer ${token}`,
                 },
            });

            console.log("User account deleted successfully");
            toast.success("Your account has been deleted.");
            setIsDeleteDialogOpen(false); // Close dialog
            // Redirect to home page or login page after deletion
            // In a real app, you'd also clear cookies/local storage session data
            router.push('/'); // Redirect to home page

        } catch (error: any) {
            console.error("Failed to delete account:", error);
            const errorMessage = error.response?.data?.message || 'Failed to delete account';
            toast.error(errorMessage);
            setIsDeleteDialogOpen(false); // Close dialog on error
        } finally {
            setIsLoading(false);
        }
    };


  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <Toaster />

      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
            <p className="text-gray-600">Manage your account and notification preferences</p>
          </div>
          <Button variant="outline" onClick={() => router.back()}>
            Go Back
          </Button>
        </div>

        {isLoading && !currentUser ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading profile...</p>
            </div>
          </div>
        ) : currentUser ? (
          <div className="space-y-6">
          <Tabs defaultValue="tender-alerts" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tender-alerts">General Tender Alerts</TabsTrigger>
              <TabsTrigger value="profile">Profile Information</TabsTrigger>
              <TabsTrigger value="notifications">Notification Preferences</TabsTrigger>
            </TabsList>

            {/* General Tender Alerts Tab */}
            <TabsContent value="tender-alerts" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    General Tender Alerts
                  </CardTitle>
                  <CardDescription>
                    These are alerts for public tenders available on TenderAsili and elsewhere.
                    You will receive an email/SMS for tenders that fall in your categories.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Current Tender Alerts */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Your Current Tender Alerts</h3>
                    {Object.keys(groupedTenderAlerts()).length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No tender alerts configured yet</p>
                        <p className="text-sm">Add your first alert below to get started</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {Object.entries(groupedTenderAlerts()).map(([category, locations], index) => (
                          <div key={category} className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">
                                {formatCategoryName(category)}
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                <span className="font-medium">Locations:</span> {locations.map(formatLocationName).join(', ')}
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const alertIndex = notificationPrefs?.tenderAlerts?.findIndex(
                                  alert => alert.category === category
                                );
                                if (alertIndex !== undefined && alertIndex !== -1) {
                                  removeTenderAlert(alertIndex);
                                }
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Add New Tender Alert */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Add New Category</h3>

                    {/* Category Selection */}
                    <div className="space-y-2">
                      <Label htmlFor="category">1. Select Category</Label>
                      <select
                        id="category"
                        value={newTenderAlert.category}
                        onChange={(e) => setNewTenderAlert(prev => ({ ...prev, category: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select a category...</option>
                        {TENDER_CATEGORIES.map((category) => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Location Selection */}
                    <div className="space-y-2">
                      <Label>2. Check all locations preferred</Label>
                      {newTenderAlert.category && (
                        <div className="text-sm text-gray-600">
                          {getAvailableLocations().length} locations available for this category
                          {TENDER_LOCATIONS.length - getAvailableLocations().length > 0 && (
                            <span className="text-gray-500">
                              {' '}({TENDER_LOCATIONS.length - getAvailableLocations().length} already selected)
                            </span>
                          )}
                        </div>
                      )}
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-64 overflow-y-auto border border-gray-200 rounded-md p-4">
                        {TENDER_LOCATIONS.map((location) => {
                          const isAlreadySelected = isLocationAlreadySelected(location);
                          const isChecked = newTenderAlert.locations.includes(location);

                          return (
                            <div key={location} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={location}
                                checked={isChecked}
                                disabled={isAlreadySelected && !isChecked}
                                onChange={() => handleLocationToggle(location)}
                                className={`rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
                                  isAlreadySelected && !isChecked ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                              />
                              <Label
                                htmlFor={location}
                                className={`text-sm ${
                                  isAlreadySelected && !isChecked
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'cursor-pointer'
                                }`}
                              >
                                {formatLocationName(location)}
                                {isAlreadySelected && !isChecked && (
                                  <span className="text-xs text-gray-400 ml-1">(already selected)</span>
                                )}
                              </Label>
                            </div>
                          );
                        })}
                      </div>
                      {newTenderAlert.locations.length > 0 && (
                        <div className="text-sm text-gray-600">
                          Selected: {newTenderAlert.locations.map(formatLocationName).join(', ')}
                        </div>
                      )}
                    </div>

                    {/* Add Button */}
                    <Button
                      onClick={addTenderAlert}
                      disabled={
                        !newTenderAlert.category ||
                        newTenderAlert.locations.length === 0 ||
                        isSavingTenderAlert
                      }
                      className="w-full md:w-auto"
                    >
                      {isSavingTenderAlert ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Saving...
                        </>
                      ) : (
                        'Add Tender Alert'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Profile Information Tab */}
            <TabsContent value="profile" className="space-y-6">
              {/* User Status Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Account Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600">Account Status</Label>
                      {getStatusBadge(currentUser.status)}
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600">Email Status</Label>
                      {getVerificationBadge(currentUser.emailVerified, 'email')}
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600">Phone Status</Label>
                      {getVerificationBadge(currentUser.phoneVerified, 'phone')}
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600">Role</Label>
                      <Badge variant="outline" className="capitalize">
                        <Shield className="h-3 w-3 mr-1" />
                        {currentUser.role}
                      </Badge>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Member Since
                      </Label>
                      <p className="text-sm text-gray-900">{formatDate(currentUser.createdAt)}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        Last Login
                      </Label>
                      <p className="text-sm text-gray-900">{formatDate(currentUser.lastLogin)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Profile Form */}
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>Update your personal details and contact information</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit(onUpdateSubmit)} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          type="text"
                          {...register('firstName', { required: true })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          type="text"
                          {...register('lastName', { required: true })}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="email" className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          {...register('email', { required: true })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          type="text"
                          {...register('phone', { required: true })}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">New Password</Label>
                      <Input
                        id="password"
                        type="password"
                        placeholder="Leave blank to keep current password"
                        {...register('password')}
                      />
                      <p className="text-sm text-gray-500">Leave blank to keep your current password</p>
                    </div>

                    <Button type="submit" disabled={isSubmitting || isLoading} className="w-full md:w-auto">
                      {isSubmitting ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Notification Preferences Tab */}
            <TabsContent value="notifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>
                    Control how and when you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {notificationPrefs && (
                    <>
                      {/* Email Notifications */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Mail className="h-5 w-5 text-blue-600" />
                            <div>
                              <h3 className="font-medium">Email Notifications</h3>
                              <p className="text-sm text-gray-600">Receive notifications via email</p>
                            </div>
                          </div>
                          <Switch
                            checked={notificationPrefs.emailAlerts?.enabled}
                            onCheckedChange={(checked: boolean) => {
                              const newPrefs = {
                                ...notificationPrefs,
                                emailAlerts: {
                                  ...notificationPrefs.emailAlerts,
                                  enabled: checked
                                }
                              };
                              setNotificationPrefs(newPrefs);
                              updateNotificationPreferences(newPrefs);
                            }}
                          />
                        </div>

                        {notificationPrefs.emailAlerts?.enabled && (
                          <div className="ml-7 space-y-3 border-l-2 border-gray-200 pl-4">
                            {Object.entries(notificationPrefs.emailAlerts.categories || {}).map(([category, enabled]) => (
                              <div key={category} className="flex items-center justify-between">
                                <Label className="capitalize text-sm">{category}</Label>
                                <Switch
                                  checked={enabled}
                                  onCheckedChange={(checked: boolean) => {
                                    const newPrefs = {
                                      ...notificationPrefs,
                                      emailAlerts: {
                                        ...notificationPrefs.emailAlerts,
                                        categories: {
                                          ...notificationPrefs.emailAlerts?.categories,
                                          [category]: checked
                                        }
                                      }
                                    };
                                    setNotificationPrefs(newPrefs);
                                    updateNotificationPreferences(newPrefs);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <Separator />

                      {/* SMS Notifications */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <MessageSquare className="h-5 w-5 text-green-600" />
                            <div>
                              <h3 className="font-medium">SMS Notifications</h3>
                              <p className="text-sm text-gray-600">Receive notifications via SMS</p>
                            </div>
                          </div>
                          <Switch
                            checked={notificationPrefs.smsAlerts?.enabled}
                            onCheckedChange={(checked: boolean) => {
                              const newPrefs = {
                                ...notificationPrefs,
                                smsAlerts: {
                                  ...notificationPrefs.smsAlerts,
                                  enabled: checked
                                }
                              };
                              setNotificationPrefs(newPrefs);
                              updateNotificationPreferences(newPrefs);
                            }}
                          />
                        </div>

                        {notificationPrefs.smsAlerts?.enabled && (
                          <div className="ml-7 space-y-3 border-l-2 border-gray-200 pl-4">
                            {Object.entries(notificationPrefs.smsAlerts.categories || {}).map(([category, enabled]) => (
                              <div key={category} className="flex items-center justify-between">
                                <Label className="capitalize text-sm">{category}</Label>
                                <Switch
                                  checked={enabled}
                                  onCheckedChange={(checked: boolean) => {
                                    const newPrefs = {
                                      ...notificationPrefs,
                                      smsAlerts: {
                                        ...notificationPrefs.smsAlerts,
                                        categories: {
                                          ...notificationPrefs.smsAlerts?.categories,
                                          [category]: checked
                                        }
                                      }
                                    };
                                    setNotificationPrefs(newPrefs);
                                    updateNotificationPreferences(newPrefs);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <Separator />

                      {/* App Notifications */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Bell className="h-5 w-5 text-purple-600" />
                            <div>
                              <h3 className="font-medium">In-App Notifications</h3>
                              <p className="text-sm text-gray-600">Receive notifications within the app</p>
                            </div>
                          </div>
                          <Switch
                            checked={notificationPrefs.appAlerts?.enabled}
                            onCheckedChange={(checked: boolean) => {
                              const newPrefs = {
                                ...notificationPrefs,
                                appAlerts: {
                                  ...notificationPrefs.appAlerts,
                                  enabled: checked
                                }
                              };
                              setNotificationPrefs(newPrefs);
                              updateNotificationPreferences(newPrefs);
                            }}
                          />
                        </div>

                        {notificationPrefs.appAlerts?.enabled && (
                          <div className="ml-7 space-y-3 border-l-2 border-gray-200 pl-4">
                            {Object.entries(notificationPrefs.appAlerts.categories || {}).map(([category, enabled]) => (
                              <div key={category} className="flex items-center justify-between">
                                <Label className="capitalize text-sm">{category}</Label>
                                <Switch
                                  checked={enabled}
                                  onCheckedChange={(checked: boolean) => {
                                    const newPrefs = {
                                      ...notificationPrefs,
                                      appAlerts: {
                                        ...notificationPrefs.appAlerts,
                                        categories: {
                                          ...notificationPrefs.appAlerts?.categories,
                                          [category]: checked
                                        }
                                      }
                                    };
                                    setNotificationPrefs(newPrefs);
                                    updateNotificationPreferences(newPrefs);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <Separator />

                      {/* Push Notifications */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-5 w-5 text-orange-600" />
                            <div>
                              <h3 className="font-medium">Push Notifications</h3>
                              <p className="text-sm text-gray-600">Receive push notifications on your device</p>
                            </div>
                          </div>
                          <Switch
                            checked={notificationPrefs.pushAlerts?.enabled}
                            onCheckedChange={(checked: boolean) => {
                              const newPrefs = {
                                ...notificationPrefs,
                                pushAlerts: {
                                  ...notificationPrefs.pushAlerts,
                                  enabled: checked
                                }
                              };
                              setNotificationPrefs(newPrefs);
                              updateNotificationPreferences(newPrefs);
                            }}
                          />
                        </div>

                        {notificationPrefs.pushAlerts?.enabled && (
                          <div className="ml-7 space-y-3 border-l-2 border-gray-200 pl-4">
                            {Object.entries(notificationPrefs.pushAlerts.categories || {}).map(([category, enabled]) => (
                              <div key={category} className="flex items-center justify-between">
                                <Label className="capitalize text-sm">{category}</Label>
                                <Switch
                                  checked={enabled}
                                  onCheckedChange={(checked: boolean) => {
                                    const newPrefs = {
                                      ...notificationPrefs,
                                      pushAlerts: {
                                        ...notificationPrefs.pushAlerts,
                                        categories: {
                                          ...notificationPrefs.pushAlerts?.categories,
                                          [category]: checked
                                        }
                                      }
                                    };
                                    setNotificationPrefs(newPrefs);
                                    updateNotificationPreferences(newPrefs);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Danger Zone */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Permanently remove your account and all of its contents from our platform. This action is not reversible.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="destructive"
                onClick={handleDeleteAccountClick}
                disabled={isLoading}
              >
                Delete Account
              </Button>
            </CardContent>
          </Card>

          {/* Delete Confirmation Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Are you sure you want to delete your account?</DialogTitle>
                <DialogDescription>
                  This action is irreversible. All your data will be permanently removed.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isLoading}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={confirmDeleteAccount} disabled={isLoading}>
                  {isLoading ? 'Deleting...' : 'Delete Account'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          </div>
        ) : (
          <div className="text-center text-red-500 py-12">
            <XCircle className="h-12 w-12 mx-auto mb-4" />
            <p>Failed to load user data.</p>
          </div>
        )}
      </div>
    </div>
  );
}
