"use client";

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Calendar, Clock, CreditCard, Home, MapPin, CheckCircle, AlertCircle, AlertTriangle, RefreshCcw, EyeIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

export default function OrderDetails() {
  const params = useParams();
  const router = useRouter();
  const { orderId, companyId } = params;
  //const { toast } = useToast();
  
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showRetryModal, setShowRetryModal] = useState(false);
  const [mobile, setMobile] = useState('');
  const [error, setError] = useState(null);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    fetchOrderDetails();
  }, [orderId]);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      
      // Get token from cookies
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }

      // Include token in authorization header
      const response = await fetch(`${BACKEND_API_URL}/api/supplier/${companyId}/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch order details');
      }
      const data = await response.json();
      console.log(data);

      setOrder(data);
    } catch (error) {
      console.error('Error fetching order details:', error);
      toast.error('Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const verifyPayment = async () => {
    try {
      // Get token from cookies
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      
      const response = await fetch(`${BACKEND_API_URL}/api/supplier/${companyId}/orders/${orderId}/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });
      

      const data = await response.json(); // Parse the JSON response
  
      if (!response.ok) {
        // Use the error message from backend if available
        throw new Error(data.message || 'Payment verification failed');
      }
      
      if (data.verified) {
        toast.success(data.message || 'Payment verification successful');
      } else {
        toast.error(data.message || 'Payment not yet completed');
      }
      
      // Refresh order details to show updated status
      fetchOrderDetails();
      
      // You can also use other data from the response if needed
      console.log('Payment status:', data.status);
      console.log('Order ID:', data.orderId);
      console.log('Payment ID:', data.paymentId);
      
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error(error.message || 'Payment verification failed');
    }
  };

  const retryPayment = async (enteredMobile) => {
    try {
      // Get token from cookies
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      
      const response = await fetch(`${BACKEND_API_URL}/api/supplier/${companyId}/orders/${orderId}/retry-payment`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mobile: enteredMobile }),
      });
      
      if (!response.ok) {
        throw new Error('Payment retry failed');
      }
      
      setShowRetryModal(false);
      toast.success('Payment request sent to your phone');
      // Refresh order details
      fetchOrderDetails();
    } catch (error) {
      console.error('Error retrying payment:', error);
      toast.error('Payment retry failed');
    }
  };

  const handleRetrySubmit = () => {
    if (!mobile || mobile.trim() === '') {
      toast.error('Please enter a valid mobile number');
      return;
    }
    retryPayment(mobile);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500 hover:bg-green-600"><CheckCircle className="h-3 w-3 mr-1" /> Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500 hover:bg-yellow-600"><AlertCircle className="h-3 w-3 mr-1" /> Pending</Badge>;
      case 'checkout':
        return <Badge className="bg-blue-500 hover:bg-blue-600"><CreditCard className="h-3 w-3 mr-1" /> Checkout</Badge>;
      case 'failed':
        return <Badge className="bg-red-500 hover:bg-red-600"><AlertTriangle className="h-3 w-3 mr-1" /> Failed</Badge>;
      default:
        return <Badge className="bg-gray-500 hover:bg-gray-600">{status}</Badge>;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4 mb-6">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-64" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      <div className="flex justify-between items-center">
        <Button 
          variant="outline" 
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl">Order Details</CardTitle>
              {order?.status && getStatusBadge(order.status)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Order Information</h3>
                <div className="space-y-2">
                  <div className="flex items-start">
                    <div className="w-32 text-sm text-gray-500">Order ID:</div>
                    <div className="font-medium">{order?._id}</div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-32 text-sm text-gray-500">Status:</div>
                    <div>{order?.status && getStatusBadge(order.status)}</div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-32 text-sm text-gray-500">Total Amount:</div>
                    <div className="font-medium">KES {order?.totalAmount?.$numberDecimal.toLocaleString()}</div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-32 text-sm text-gray-500">Created On:</div>
                    <div>{formatDate(order?.createdAt)}</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Payment Information</h3>
                <div className="space-y-2">
                  <div className="flex items-start">
                    <div className="w-32 text-sm text-gray-500">Payment Method:</div>
                    <div>M-Pesa</div>
                  </div>
                  {order?.payment && (
                    <>
                      <div className="flex items-start">
                        <div className="w-32 text-sm text-gray-500">Mobile:</div>
                        <div>{order.payment.mobile}</div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-32 text-sm text-gray-500">Transaction ID:</div>
                        <div>{order.payment.transID || 'Pending'}</div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-32 text-sm text-gray-500">Payment Status:</div>
                        <div>{order.payment.status && getStatusBadge(order.payment.status)}</div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Open</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead className="text-right">Price (KES)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order?.orderItems?.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium  whitespace-normal overflow-hidden text-ellipsis max-w-[200px] md:max-w-[200px] lg:max-w-[200px]">{item.title}
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center cursor-pointer underline-offset-auto"
                      onClick={() => { router.push(`/supplier/${companyId}/applications/?type=${item.type}`)}} >
                        <EyeIcon className="h-4 w-4 mr-1 text-blue-500" />
                        <span className="underline underline-offset-2 text-blue-500">Go to categories</span>
                      </div>
                    </TableCell>
                    <TableCell className="capitalize">{item.type}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                        {item.location || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-500" /> 
                          {formatDate(item.starts)} - {formatDate(item.ends)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">{item.price.$numberDecimal.toLocaleString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-6">
            <div className="text-lg font-semibold">Total</div>
            <div className="text-lg font-semibold">
              KES {order?.totalAmount?.$numberDecimal.toLocaleString()}
            </div>
          </CardFooter>
        </Card>

        {order?.status !== 'paid' && (
          <Card>
            <CardHeader>
              <CardTitle>Payment Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  onClick={verifyPayment}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Verify Payment
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowRetryModal(true)}
                >
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Retry Payment
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Dialog open={showRetryModal} onOpenChange={setShowRetryModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Retry Payment</DialogTitle>
            <DialogDescription>
              Enter your M-Pesa mobile number to receive a payment prompt
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              type="tel"
              placeholder="e.g. 0712345678"
              value={mobile}
              onChange={(e) => setMobile(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRetryModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleRetrySubmit}>
              <CreditCard className="h-4 w-4 mr-2" />
              Send Payment Request
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}