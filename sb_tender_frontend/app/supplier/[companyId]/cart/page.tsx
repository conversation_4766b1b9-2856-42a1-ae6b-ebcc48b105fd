"use client";

import { useEffect, useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Trash2, CreditCard, ShoppingBag, ShoppingCart, Search } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useCartStore } from '@/lib/store/cart';
import toast, { Toaster } from 'react-hot-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
export default function Cart() {
  const router = useRouter();
  const { items, removeItem, checkout } = useCartStore();
  const [companyId, setCompanyId] = useState<string>('');


  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedCompany = localStorage.getItem('activeCompanyId');
      if (storedCompany) {
        setCompanyId(storedCompany);
        console.log('Stored active company', storedCompany);
      } else {
        console.error("activeCompanyId not found in localStorage");
        const pathParts = window.location.pathname.split('/');
        const companyId = pathParts[2];
        setCompanyId(companyId);
        console.log('Company ID:', companyId);
      }

    }
  }, []);

  const getTotalAmount = () => {
    return items.reduce((total, item) => total + item.price, 0);
  };



  const [showModal, setShowModal] = useState(false);
  const [mobile, setMobile] = useState('');

  const validateMobile = (mobile: string) => {
    // Remove spaces, dashes, and leading +
    mobile = mobile.replace(/[\s\-+]/g, '');
    const mobileRegex = /^(\+?254|0)?[17]\d{8}$/; // Validate mobile number starting with +254, 0, or 1 or 7 followed by 8 digits
    return mobileRegex.test(mobile);
  };

  const handleCheckout = async (enteredMobile: string) => {
    try {
      setMobile(enteredMobile);
      if (!validateMobile(enteredMobile)) {
        toast.error('Invalid mobile number');
        throw new Error('Invalid mobile number');
      }
      const orderId = await checkout(enteredMobile, companyId);
      console.log('Order ID from checkout cart store', orderId);
      if (!orderId) {
        toast.error('Failed to create order. Please reload the page and try again.');
        throw new Error('Failed to create order');
      }
      toast.success('Payment initiated successfully. Please check your phone to complete the payment.', {
        duration: 5000
      });
      setTimeout(() => {
        router.push(`/supplier/${companyId}/orders/${orderId}`);
      }, 4000);

    } catch (error) {
      throw new Error(error.message);
    }
  };

  return (
    <div className="p-6 space-y-6">

      <div className="flex justify-between items-center">
        <Toaster />

        <h1 className="text-3xl font-bold">Categories Cart</h1>
        <Button onClick={() => router.push(`/supplier/${companyId}/dashboard`)}>
          <Search className="h-4 w-4 mr-2" />
          Continue Browsing
        </Button>

      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="p-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Price (KES)</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium  whitespace-normal overflow-hidden text-ellipsis max-w-[200px] md:max-w-[200px] lg:max-w-[200px]">{item.category}</TableCell>
                    <TableCell className="capitalize text-wrap">{item.type}</TableCell>
                    <TableCell className="text-wrap">{item.buyerCompany.name}</TableCell>
                    <TableCell>{item.price.toLocaleString()}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(item.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>

        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
            <div className="text-sm text-gray-500">
              {items.length} {items.length === 1 ? 'category' : 'categories'}
            </div>
            <div className="space-y-4">

              <div className="border-t pt-4">
                <div className="flex justify-between items-center font-semibold">
                  <span>Total</span>
                  <span>KES {(getTotalAmount()).toLocaleString()}</span>
                </div>
              </div>
              <Button
                className="w-full bg-green-600 hover:bg-green-700"
                onClick={() => setShowModal(true)}
                disabled={items.length === 0}>
                <CreditCard className="h-4 w-4 mr-2" />
                Pay with M-Pesa
              </Button>



              <CheckoutModal
                open={showModal}
                onClose={() => setShowModal(false)}
                onConfirm={handleCheckout}
              />


              <p className="text-sm text-gray-500 text-center mt-2">
                Secure payment powered by M-Pesa
              </p>
            </div>
          </Card>
        </div>
      </div>

      {items.length === 0 && (
        <Card className="p-6 text-center">
          <p className="text-gray-500">Your cart is empty</p>
          <Button
            className="mt-4"
            variant="outline"
            onClick={() => router.push(`/supplier/${companyId}/dashboard`)}
          >
            <Search className="h-4 w-4 mr-2" />
            Browse Categories
          </Button>
        </Card>
      )}
    </div>
  );
}




const CheckoutModal = ({ open, onClose, onConfirm }) => {
  const [mobile, setMobile] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!mobile) {
      toast.error('Please enter your mobile number');
      return;
    }
    setIsSubmitting(true);
    setError(null);
    try {
      await onConfirm(mobile);
      // If onConfirm throws, error will be caught below
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initiate payment');
      setIsSubmitting(false);
    }
  };

  // If payment fails, allow editing again
  // If modal is closed, reset state
  useEffect(() => {
    if (!open) {
      setMobile('');
      setIsSubmitting(false);
      setError(null);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Enter Mobile Number</DialogTitle>
          <DialogDescription>
            We need your mobile number to proceed with checkout
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <Input
            type="tel"
            placeholder="e.g. 0712345678"
            value={mobile}
            onChange={(e) => setMobile(e.target.value)}
            disabled={isSubmitting}
            readOnly={isSubmitting}
          />
          {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>

          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Initiating payment...' : 'Proceed to Checkout'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
