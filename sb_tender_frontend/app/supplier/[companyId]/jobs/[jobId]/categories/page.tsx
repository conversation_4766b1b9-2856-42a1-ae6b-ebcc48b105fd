"use client";

import { useEffect, useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ShoppingCart, Plus, Filter, Minus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from 'next/navigation';
import { useCartStore, CartItem } from '@/lib/store/cart';
import SupplierCategories from '@/components/supplier-categories';
import axios from 'axios';
import { useParams } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { Input } from '@/components/ui/input';

interface Category {
  _id: string;
  title: string;
  type: 'supplier_registration' | 'supplier_prequalification';
  companyName: string;
  spCompanyId: string;
  description: string;
  price: number;
  starts?: string;
  ends?: string;
}

export default function Categories() {
  const router = useRouter();
  const { addItem, removeItem } = useCartStore();
  const { items } = useCartStore();

  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedCompany, setSelectedCompany] = useState<string>('all');
  const [searchPhrase, setSearchPhrase] = useState<string>('');

  const [companyId, setCompanyId] = useState<string>('');
  const [token, setToken] = useState<string | null>(null);
const [jobId, setJobId] = useState<string | null>(null);
const params = useParams();

useEffect(() => {
  if (params?.jobId && typeof params.jobId === 'string') {
    console.log(jobId);
    setJobId(params.jobId);
  }
}, [params]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedCompany = localStorage.getItem('activeCompanyId');
      if (storedCompany) {
        setCompanyId( storedCompany);
        console.log('Stored active company', storedCompany);
      } else {
        console.error("activeCompanyId not found in localStorage");
        const pathParts = window.location.pathname.split('/');
        const companyId = pathParts[2];
        setCompanyId( companyId);
        console.log('Company ID:', companyId);
      }

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (cookieToken) {
        setToken(cookieToken);
      } else {
        console.error("Token not found in cookies");
      }
    }
  }, []);


  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);

  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`/api/jobs/categories?jobId=${jobId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log('Categoris', categories);
      setCategories(data);
    } catch (error) {
      toast.error('Failed to fetch categories');
      console.error('Error fetching categories:', error);
    }
  };

  const filteredCategories = categories.filter(category => {
    if (selectedType !== 'all' && category.type !== selectedType) return false;
    if (selectedCompany !== 'all' && category.companyName !== selectedCompany) return false;
    if (searchPhrase !== '') {
      const searchLower = searchPhrase.toLowerCase();
      const titleMatch = category.title.toLowerCase().includes(searchLower);
      const descriptionMatch = category.description.toLowerCase().includes(searchLower);
      
      if (!titleMatch && !descriptionMatch) return false;
    }
    return true;
  });

  const companies = Array.from(new Set(categories.map(c => c.companyName)));

  const handleAddToCart = (category: Category) => {
    const cartItem: CartItem = {
      id: category._id,
      type: category.type,
      category: category.title,
      buyerCompany: category.spCompanyId,
      supplierCompanyId: companyId,
      price: category.price,
    };
    addItem(cartItem);
  };

  const handleRemoveFromCart = (category: Category) => {
    removeItem(category._id);
  }


  const calculateDaysRemaining = (endDate: string) => {
    const today = new Date();
    const closingDate = new Date(endDate);
    // Remove time parts to compare just dates
    today.setHours(0, 0, 0, 0);
    closingDate.setHours(0, 0, 0, 0);
    
    const diffTime = closingDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0; // Return 0 if date has passed
  };
  return (
    <div className="p-6 space-y-6">
     <Toaster />
            

      <div className="">
        <h5 className="text-xl font-medium font-bold">Helpful Pointers</h5>
        <p  className="font-small" >1. Listed below are supplier registration and supplier prequalification categories available.
        </p>
        <p className="font-small">2. When you identify a category that captures your interest, click the add to cart button alongside it and it will be added to your personal cart.
        </p>
        <p className="font-small">3. Red cart icon means the Category is in your cart. Click it to remove the item from cart.
        </p>
        <p className="font-small">4. When you are happy with your Categories selection, click on "Proceed to Cart" button or the basket icon on your top menu or the cart icon on the left side main menu.
        </p>
      </div>

      <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Available Categories for {filteredCategories[filteredCategories.length - 1]?.companyName}
          </h1>
        <Button className={`${items.length > 0 ? 'bg-green-600 text-white hover:bg-green-700': 
        'bg-muted text-mued hover:bg-secondary cursor-not-allowed'}`} 
        onClick={() => router.push(`/supplier/${companyId}/cart`)}>
          <ShoppingCart className="h-4 w-4 mr-2" />
          ({items.length}) Proceed to Cart
        </Button>
      </div>

      <Card className="p-6">
        <div className="flex gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Filter by:</span>
          </div>
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Category Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="registration">Registration</SelectItem>
              <SelectItem value="prequalification">Prequalification</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCompany} onValueChange={setSelectedCompany}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Buyer Company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Companies</SelectItem>
              {companies.map(company => (
                <SelectItem key={company} value={company}>
                  {company}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input
          placeholder="Filter titles, ref, description..."
          value={searchPhrase ?? ""}
          onChange={(event) => setSearchPhrase(event.target.value)
          }
          className="max-w-sm"
        />
        </div>

        <Table>
          <TableHeader>
            <TableRow>
            <TableHead className="font-bold">Company</TableHead>
              <TableHead className="font-bold w-1/4">Category Title</TableHead>
              <TableHead className="font-bold">Type</TableHead>
              <TableHead className="font-bold">Closes</TableHead>
              <TableHead className="font-bold">Category Price (KES)</TableHead>
              <TableHead className="font-bold">Criteria</TableHead>
              <TableHead className="font-bold"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCategories.map((category) => (
              <TableRow key={category._id}>
                <TableCell><div className="font-medium text-wrap">{category.companyName}</div></TableCell>

                <TableCell>
                  <div className="">
                    <div className="font-medium text-wrap">{category.title}</div>
                    <div className="text-sm text-gray-500 text-wrap">REF: {category.ref} - {category.description}</div>
                  </div>
                </TableCell>
                <TableCell className="capitalize text-wrap">{category.type}</TableCell>
                <TableCell className="capitalize text-wrap">
                  {category.ends ? (
                    <div>
                      {new Date(category.ends).toLocaleDateString()}
                      <div className="text-sm text-gray-500">
                        {calculateDaysRemaining(category.ends)} days remaining
                      </div>
                    </div>
                  ) : (
                    "N/A"
                  )}
                </TableCell>
                <TableCell>{category.price.toLocaleString()}</TableCell>
                <TableCell>
                  <span className="text-success underline">Preview</span>
                </TableCell>

                <TableCell>
                {items.some(item => item.id === category._id) ? (
                  <Button 
                    onClick={() => handleRemoveFromCart(category)}
                    variant="destructive"
                  >
                    <Minus className="h-4 w-4 mr-2" />
                    Remove from Cart
                  </Button>
                ) : (
                  <Button 
                    onClick={() => handleAddToCart(category)}
                    variant="default"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                )}
              </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

    </div>
  );
}