"use client";

import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { 
  Badge 
} from "@/components/ui/badge";
import {
  Eye,
  RefreshCcw,
  Search,
  MoreVertical,
  Calendar,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Building
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Type definition for Application based on the mongoose schema
interface Application {
  _id: string;
  title: string;
  ref: string;
  description: string;
  buyerData: {
    name: string;
    logoUrl: string;
  };
  spBuyerCompanyId: string;
  starts: string;
  ends: string;
  type: 'supplier_registration' | 'supplier_prequalification' | 'rfq' | 'tender';
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  createdBy: {
    name: string;
  };
  updatedBy?: {
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

// CompanyLogo component for efficient logo rendering
interface CompanyLogoProps {
  companyName: string;
  logoUrl?: string;
  logoCache: Record<string, string>;
  logoLoading: Record<string, boolean>;
}

const CompanyLogo: React.FC<CompanyLogoProps> = ({ companyName, logoUrl, logoCache, logoLoading }) => {
  const cacheKey = companyName;
  const cachedLogo = logoCache[cacheKey];
  const isLoading = logoLoading[cacheKey];

  console.log('CompanyLogo render:', { companyName, cacheKey, cachedLogo: !!cachedLogo, isLoading, logoUrl });

  return (
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-muted rounded-md overflow-hidden">
        {isLoading ? (
          <div className="w-full h-full bg-muted animate-pulse" />
        ) : cachedLogo ? (
          <img
            src={cachedLogo}
            alt={`${companyName} logo`}
            className="w-full h-full object-cover"
            onLoad={() => console.log('Logo loaded successfully for:', companyName)}
            onError={() => console.error('Logo failed to load for:', companyName)}
          />
        ) : logoUrl ? (
          <div className="w-full h-full bg-muted animate-pulse" />
        ) : (
          <Building className="h-4 w-4 text-muted-foreground" />
        )}
      </div>
      <span className="font-medium truncate">{companyName || 'N/A'}</span>
    </div>
  );
};

export default function ApplicationsPage() {
  const params = useParams();
  const router = useRouter();
  const { companyId } = params;

  const [applications, setApplications] = useState<Application[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [closingFilter, setClosingFilter] = useState('all');
  const [type, setType] = useState<string | null >(null);
  const [logoCache, setLogoCache] = useState<Record<string, string>>({});
  const [logoLoading, setLogoLoading] = useState<Record<string, boolean>>({});

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const type = urlParams.get('type');
      console.log('Type', type);
      setType(type);
    }
  }, []);

  useEffect(() => {
    // Only fetch when we have companyId and type has been initialized
    if (companyId && type !== null) {
      fetchApplications();
    }
  }, [companyId, type]);

  
  useEffect(() => {
    // Apply filters when applications, searchQuery, statusFilter, typeFilter, or closingFilter changes
    filterApplications();
  }, [applications, searchQuery, statusFilter, typeFilter, closingFilter]);

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      Object.values(logoCache).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [logoCache]);

  const downloadLogo = async (buyerCompanyId: string, logoFileName: string, cacheKey: string) => {
    if (logoCache[cacheKey] || logoLoading[cacheKey]) {
      return logoCache[cacheKey];
    }

    try {
      setLogoLoading(prev => ({ ...prev, [cacheKey]: true }));

      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        console.error('No token found');
        return null;
      }

      console.log('Downloading logo for:', { buyerCompanyId, logoFileName, cacheKey });

      const response = await fetch(`/api/companies/download?companyId=${buyerCompanyId}&documentType=logoUrl&fileName=${logoFileName}`,
        {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
          },
        }
      );

      console.log('Logo download response:', response.status, response.ok);

      if (response.ok) {
        const blob = await response.blob();
        console.log('Logo blob size:', blob.size, 'type:', blob.type);

        if (blob.size === 0) {
          console.error('Received empty blob for logo');
          return null;
        }

        const imageUrl = URL.createObjectURL(blob);
        setLogoCache(prev => ({ ...prev, [cacheKey]: imageUrl }));
        console.log('Logo cached with key:', cacheKey, 'URL:', imageUrl);
        return imageUrl;
      } else {
        const errorText = await response.text();
        console.error('Failed to download logo, status:', response.status, 'error:', errorText);
        return null;
      }
    } catch (error) {
      console.error('Error downloading logo:', error);
      return null;
    } finally {
      setLogoLoading(prev => ({ ...prev, [cacheKey]: false }));
    }
  };

  const fetchApplications = async () => {
    try {
      setLoading(true);

      // Get token from cookies
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      const url = type ? `${BACKEND_API_URL}/api/supplier/${companyId}/applications?type=${type}` : `${BACKEND_API_URL}/api/supplier/${companyId}/applications`

      // Include token in authorization header
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });

      const responseData = await response.json();

      // Extract the data array from the response
      const applicationsData = responseData.applicationsData || [];
      setApplications(applicationsData);
      setFilteredApplications(applicationsData);

      // Download logos asynchronously without blocking UI
      applicationsData.forEach((app: Application) => {
        const cacheKey = app.buyerData?.name || app.spBuyerCompanyId;
        if (app.buyerData?.logoUrl && !logoCache[cacheKey]) {
          // Extract filename from logoUrl path
          const logoFileName = app.buyerData.logoUrl.split('/').pop();
          if (logoFileName) {
            console.log('Initiating logo download for:', cacheKey, 'Company ID:', app.spBuyerCompanyId);
            downloadLogo(app.spBuyerCompanyId, logoFileName, cacheKey);
          }
        }
      });
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const filterApplications = () => {
    let filtered = [...applications];
    console.log('Applications',applications);
    
    // Apply search query filter
    if (searchQuery) {
      filtered = filtered.filter(app => 
        app.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.ref.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(app => app.type === typeFilter);
    }

    // Apply closing filter
    if (closingFilter !== 'all') {
      const now = new Date();
      const oneWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      const oneMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      const threeMonths = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);

      filtered = filtered.filter(app => {
        const endsDate = new Date(app.ends);
        switch (closingFilter) {
          case 'closes_in_1_week':
            return endsDate <= oneWeek && endsDate > now;
          case 'closes_in_1_month':
            return endsDate <= oneMonth && endsDate > oneWeek;
          case 'closes_in_3_months':
            return endsDate <= threeMonths && endsDate > oneMonth;
          default:
            return true;
        }
      });
    }
    
    setFilteredApplications(filtered);
  };

  const viewApplicationDetails = (applicationId: string) => {
    router.push(`/supplier/${companyId}/applications/${applicationId}`);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge className="bg-gray-500 hover:bg-gray-600"><AlertCircle className="h-3 w-3 mr-1" /> Draft</Badge>;
      case 'submitted':
        return <Badge className="bg-blue-500 hover:bg-blue-600"><AlertCircle className="h-3 w-3 mr-1" /> Submitted</Badge>;
      case 'under_review':
        return <Badge className="bg-yellow-500 hover:bg-yellow-600"><AlertTriangle className="h-3 w-3 mr-1" /> Under Review</Badge>;
      case 'approved':
        return <Badge className="bg-green-500 hover:bg-green-600"><CheckCircle className="h-3 w-3 mr-1" /> Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-500 hover:bg-red-600"><AlertTriangle className="h-3 w-3 mr-1" /> Rejected</Badge>;
      default:
        return <Badge className="bg-gray-500 hover:bg-gray-600">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  const getRelativeTimeString = (dateString: string) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return formatDate(dateString);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Toaster />
      
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{type?.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ')} Categories You have Paid For</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => fetchApplications()}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Catgories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <div className="relative w-full sm:w-80">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search applications..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="supplier_registration">Supplier Registration</SelectItem>
                  <SelectItem value="supplier_prequalification">Supplier Prequalification</SelectItem>
                  <SelectItem value="rfq">RFQ</SelectItem>
                  <SelectItem value="tender">Tender</SelectItem>
                </SelectContent>
              </Select>
              <Select value={closingFilter} onValueChange={setClosingFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Closing Period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Periods</SelectItem>
                  <SelectItem value="closes_in_1_week">Closes in 1 Week</SelectItem>
                  <SelectItem value="closes_in_1_month">Closes in 1 Month</SelectItem>
                  <SelectItem value="closes_in_3_months">Closes in 3 Months</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead className="w-[100px]">View</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Closes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredApplications.length > 0 ? (
                  filteredApplications.map((application) => (
                    <TableRow
                      key={application._id}
                      className="cursor-pointer hover:bg-muted/50"
                    >
                      <TableCell className="min-w-[200px]">
                        <CompanyLogo
                          companyName={application.buyerData?.name || 'N/A'}
                          logoUrl={application.buyerData?.logoUrl}
                          logoCache={logoCache}
                          logoLoading={logoLoading}
                        />
                      </TableCell>
                      <TableCell className="font-medium whitespace-normal overflow-hidden text-ellipsis max-w-[200px] md:max-w-[250px] lg:max-w-[300px]">
                        {application.title.length > 40
                          ? `${application.title.substring(0, 40)}...`
                          : application.title}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewApplicationDetails(application._id)}
                          className="w-full"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{application.ref}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {application.type.replace(/_/g, ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{formatDate(application.ends)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(application.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm">{formatDate(application.createdAt)}</span>
                          <span className="text-xs text-muted-foreground">
                            {application.createdBy?.name || 'N/A'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {getRelativeTimeString(application.createdAt)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-end">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => viewApplicationDetails(application._id)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Open Details
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <p className="text-lg font-medium">No applications found</p>
                        <p className="text-sm text-muted-foreground">
                          {(searchQuery || statusFilter !== 'all' || typeFilter !== 'all' || closingFilter !== 'all')
                            ? 'Try adjusting your filters'
                            : 'You have no applications yet'}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}