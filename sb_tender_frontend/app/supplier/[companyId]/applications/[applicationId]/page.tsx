// app/supplier/[companyId]/applications/[applicationId]/page.tsx
"use client";

import { useEffect, useState, useMemo, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { AlertCircle, File, Loader, Loader2, MoreVertical } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils"; // Assuming you have a utils file with a cn helper

// Define TypeScript interfaces based on the Mongoose schema provided
// Note: These should ideally be generated from your Mongoose schema for robustness
interface ApplicationDocument {
  _id: string;
  title: string;
  ref: string;
  description: string;
  buyerData: {
    name: string;
    address?: string;
    logoUrl?: string;
  };
  starts: string;
  ends: string;
  type: 'supplier_registration' | 'supplier_prequalification' | 'rfq' | 'tender';
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  fields: ApplicationField[];
  submittedBy?: { name: string; };
  submittedAt?: string;
  reviewerNotes?: string;
  reviewer?: { name: string; };
  reviewDate?: string;
  reviewStatus?: 'pending' | 'approved' | 'rejected';
  systemScore?: number;
  complianceScore?: number;
  totalScore?: number;
  createdBy: { name: string; };
  updatedBy?: { name: string; };
  createdAt: string;
  updatedAt: string;
}

interface ApplicationField {
  name: string;
  value?: any;
  score?: number;
  comment?: string;
  reviewerComment?: string;
  documents?: Array<{
    fileName: string;
    fileType: string;
    fileSize: number;
    filePath: string; // Path on the server after upload
    expires?: string;
    uploadedAt?: string;
  }>;
  group: string;
  subGroup?: string;
  isParent: boolean;
  title?: string;
  order: number;
  placeholder: string;
  description: string; // Used for section/subsection titles as per new instruction
  helpText: string;
  tooltip: string;
  isVisible: boolean;
  isEditable: boolean;
  type: 'text' | 'textarea' | 'number' | 'date' | 'file' | 'checkbox' | 'radio' | 'select' | 'section';
  label: string;
  options?: Array<{
    label: string;
    value: string;
    score?: number;
  }>;
  isRequired: boolean;
  isScoreable: boolean;
  maxScore: number;
  validations?: {
    min?: number;
    max?: number;
    pattern?: string;
    fileTypes?: string[]; // e.g., ['image/*', 'application/pdf']
    maxFileSize?: number; // in bytes
  };
}

// Structure to hold fields grouped for rendering
interface GroupedFields {
  [groupName: string]: {
    sectionTitleField?: ApplicationField; // The parent field (isParent: true, type: 'section', no subGroup)
    subgroups: {
      [subGroupName: string]: ApplicationField[]; // Array of fields within this subgroup, ordered
    };
    fields: ApplicationField[]; // Array of fields directly under the section (no subgroup), ordered
  };
}


const FIELDS_PER_STEP = 3; // Number of sections to display per wizard step

const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';


// Custom File Input Component
const FileInput: React.FC<{
  field: ApplicationField;
  value?: ApplicationField['documents'];
  onChange: (documents: ApplicationField['documents']) => void;
  isEditable: boolean;
  companyId: string;
  applicationId: string;
}> = ({ field, value, onChange, isEditable, companyId, applicationId }) => {
  const [fileInfo, setFileInfo] = useState<string | null>(null);

  // CRITICAL DEBUG: Log when value prop changes
  useEffect(() => {
    console.log(`FileInput for ${field.name} - value prop changed:`, value);
  }, [value, field.name]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) {
      setFileInfo('No file chosen');
      setErrorMessage(null);
      onChange([]); // Clear the value in the parent state if no file is selected
      return;
    }

    setFileInfo(`Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
    setErrorMessage(null);
    setIsLoading(true);

    const validations = field.validations;
    let isValid = true;
    let error = '';


    // 1. Define the default allowed MIME types
    const defaultFileTypes = ['application/pdf', 'image/jpeg', 'image/png'];

    // 2. Determine which list of types to use for validation
    // If validations.fileTypes exists and is not empty, use it. Otherwise, use our default list.
    const allowedTypes = defaultFileTypes ;//(validations?.fileTypes && validations.fileTypes.length > 0) 
        //? validations.fileTypes 
        //: defaultFileTypes;

    // 3. Perform the validation using the determined list of allowed types
    if (!allowedTypes.some(type => {
        // Handle wildcard types like 'image/*'
        if (type.endsWith('/*')) {
            return file.type.startsWith(type.replace('/*', ''));
        }
        // Handle exact MIME type match (e.g., 'application/pdf')
        return file.type === type;
    })) {
        isValid = false;
        // The error message will now correctly show whichever list was used for validation
        error = `Invalid file type. Allowed types are: ${allowedTypes.map(t => t.split('/')[1] || t).join(', ').toUpperCase()}.`;
    }

    // Validate file size
    const defaultMaxFileSize = 10 * 1024 * 1024; // 10Mb
    //if (isValid && validations?.maxFileSize && (file.size > defaultMaxFileSize || file.size > validations.maxFileSize)) {
    if (isValid && (file.size > defaultMaxFileSize)) {
        isValid = false;
        error = `File size exceeds limit. Max size: ${(defaultMaxFileSize / 1024 / 1024).toFixed(2)} MB.`;
    }

    if (!isValid) {
      setErrorMessage(error);
      setIsLoading(false);
      onChange([]); // Clear previous file if invalid
      toast.error(`File "${file.name}" failed validation.`);
      return;
    }

    // --- File Upload Logic ---
    // For this example, we simulate a successful upload.
    console.log(`Uplooading file : ${file.name}`);

    // Example of how you might use FormData for a real upload:
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fieldName', field.name); // Useful for backend association

    try {
        // Get token from cookies
        const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
       // file upload API call
       const uploadResponse = await fetch(`${BACKEND_API_URL}/api/supplier/${companyId}/applications/${applicationId}/upload`, 
        { method: 'POST', 
          headers: {
            'Authorization': `Bearer ${cookieToken}`
          },
          body: formData });
       if (!uploadResponse.ok) {
           const errorData = await uploadResponse.json();
           throw new Error(errorData.message || 'Upload failed');
       }
       const uploadResult = await uploadResponse.json(); // Assuming backend returns { filePath: '...' }
       console.log('Upload result:', uploadResult);
       const uploadedDocument = {
           fileName: uploadResult?.document?.fileName || file.name,
           fileType: uploadResult?.document?.fileType || file.type,
           fileSize: uploadResult?.document?.fileSize || file.size,
           filePath: uploadResult?.document?.filePath, // Use the actual file path from uploadResult
           uploadedAt: uploadResult?.document?.uploadedAt || new Date().toISOString(),
           // expires: ... // if applicable
       };

       // CRITICAL FIX 1: Replace existing document for this field (not append)
       // Since the API uploads to a specific field, we should replace, not append
       const updatedDocuments = [uploadedDocument]; // Replace existing document since your API replaces

       console.log('Current value before update:', value);
       console.log('New uploaded document:', uploadedDocument);
       console.log('Updated documents array:', updatedDocuments);
       
       // CRITICAL FIX 2: Call onChange immediately and force re-render
       console.log('About to call onChange with:', updatedDocuments);
       onChange(updatedDocuments);
       console.log('onChange called successfully');

    // CRITICAL FIX: Force a small delay to ensure state propagation and verify update
    setTimeout(() => {
      console.log('Checking if state was updated after onChange call');
      console.log('Current value prop after timeout:', value);
      // This log will help verify if the parent state was updated
    }, 100);
       
       // CRITICAL FIX 3: Update local state to reflect the change immediately
       setFileInfo(`Uploaded: ${uploadedDocument.fileName}`);
       setErrorMessage(null);
       
       // CRITICAL FIX 4: Clear the file input to allow re-uploading same file if needed
       event.target.value = '';
       
       toast.success(`File "${file.name}" uploaded successfully.`);

/*
       // Assuming a single file input for this field, we add to the documents array
      console.log('Value:', value);

       const updatedDocuments = value ? [...value, uploadedDocument] : [uploadedDocument];
       console.log('Updated documents:', updatedDocuments);
       onChange(updatedDocuments); // Update the field's documents array in the parent state
       setErrorMessage(null);
       toast.success(`File "${file.name}" uploaded.`);
       */

    } catch (uploadError: any) {
        console.error('File upload failed:', uploadError);
        setErrorMessage(`Upload failed: ${uploadError.message || 'An error occurred during upload.'}`);
        // CRITICAL FIX: Don't call onChange on error - keep existing documents
        // onChange([]); // This was causing the documents to be cleared!
        // Decide whether to clear the selected file on upload failure
        // onChange(value); // Keep previous valid documents if any
        toast.error(`Failed to upload file "${file.name}".`);
    } finally {
       setIsLoading(false);
    }
  };
  // This is the function that will handle the download logic
  const handleDownload = async (companyId, applicationId, fieldName, fileName) => {
    try {
      setIsDownloading(true);
      // Get token from cookies (re-using your existing logic)
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!cookieToken) {
        console.error('Authentication token not found.');
        toast.error('Authentication required. Please log in.');
        // router.push('/login'); // Uncomment if you want to redirect
        return; // Stop execution
      }

      const fileUrl = `${BACKEND_API_URL}/api/supplier/${companyId}/applications/${applicationId}/fields/${fieldName}/documents/${fileName}`;

      const response = await fetch(fileUrl, {
        method: 'GET', // Or 'POST' if your API requires it for downloads (less common)
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          // 'Content-Type': 'application/json' // No need for Content-Type when fetching a file
        },
      });

      if (!response.ok) {
        // Try to parse error message if available, but file endpoints might not send JSON
        try {
          const errorData = await response.json();
          console.error('API Error Response:', errorData);
          toast.error(errorData.message || `Failed to download file (Status: ${response.status})`);
        } catch (e) {
          // Fallback if response is not JSON (e.g., plain text error)
          console.error('Failed to download file, non-JSON error response:', response.statusText);
          toast.error(`Failed to download file (Status: ${response.status})`);
        }
        return; // Stop processing after error
      }

      // Crucial step: Get the file content as a Blob
      const blob = await response.blob();

      // Determine the file name for the download
      // Best practice: Try to get it from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let downloadFileName = fileName; // Fallback to original fileName
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*?=(?:UTF-8'')?([\w.%-]+)/i);
        if (filenameMatch && filenameMatch[1]) {
          downloadFileName = decodeURIComponent(filenameMatch[1]);
        }
      }

      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a temporary anchor element
      const a = document.createElement('a');
      a.href = url;
      a.download = downloadFileName; // Set the download file name
      document.body.appendChild(a); // Append to body to make it clickable

      // Programmatically click the anchor to trigger download
      a.click();

      // Clean up: revoke the object URL and remove the anchor
      window.URL.revokeObjectURL(url);
      a.remove();

      toast.success('File downloaded successfully!');

    } catch (error) {
      console.error('Error during file download:', error);
      toast.error('An unexpected error occurred during download.');
    } finally {
      setIsDownloading(false);
    }
  };

  // Display currently saved documents if any
  const savedDocuments = value && value.length > 0 ? (
    <div className="mt-4">
      <p className="text-sm font-medium text-gray-700">Uploaded Files:</p>
      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
        {value.map((doc, index) => (
          <li key={index} className="flex items-center justify-between break-words">
           
           <button
              disabled={isDownloading} // Correct: No curly braces around the boolean value itself
              onClick={() => handleDownload(companyId, applicationId, field.name, doc.fileName)}
              className="text-blue-600 hover:underline flex items-center max-w-[80%] focus:outline-none" // Add focus styles for accessibility
              title={`Download ${doc.fileName}`} // Add a title for accessibility
            >
              <File className="h-4 w-4 mr-1 shrink-0" /> {/* Prevent icon from shrinking */}
              <span
                className={`truncate ${isDownloading ? 'cursor-not-allowed' : 'cursor-pointer'}`} // Corrected class string literal and cursor
              >
                {doc.fileName}
              </span>
              <span>
              {isDownloading && (
                  <Loader className="ml-1 animate-spin h-6 w-6 inline-block" /> // Corrected Loader usage and added styling
                )}
              </span>
            </button>

            {/* Optionally add a remove button */}
            {/* {isEditable && (
                <Button variant="ghost" size="sm" onClick={() => handleRemoveFile(index)}>Remove</Button>
            )} */}
          </li>
        ))}
      </ul>
    </div>
  ) : null;


  return (
    <div className="w-full space-y-2">
       {/* Label, tooltip, helpText are handled in FieldRenderer */}
      {isEditable ? (
        <div className="w-full max-w-md p-6 bg-white rounded-xl shadow-sm space-y-4 border border-gray-200"> {/* Adjusted styling */}
          <div
            onClick={() => !isLoading && document.getElementById(`file-upload-${field.name}`)?.click()}
            className={cn(
                "flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer transition duration-300 ease-in-out",
                isLoading ? 'opacity-60 cursor-not-allowed' : 'hover:border-maroon-700 border-gray-300',
                errorMessage ? 'border-red-500' : ''
            )}
          >
            {isLoading ? (
              <Loader2 className="h-12 w-12 text-gray-400 animate-spin" />
            ) : (
              <>
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 15a4 4 0 004 4h10a4 4 0 004-4m-4-4l-4-4m0 0l-4 4m4-4v12"></path>
                </svg>
                <p className="mt-2 text-sm text-gray-600">Click to upload or drag and drop</p>
                {field.validations?.fileTypes && field.validations?.maxFileSize ? (
                   <p className="text-xs text-gray-500">
                     {field.validations.fileTypes.map(type => type.split('/')[1] || type).join(', ').toUpperCase()} up to {(field.validations.maxFileSize / 1024 / 1024).toFixed(0) || '10'}MB
                   </p>
                 ) : (
                   <p className="text-xs text-gray-500">Select a file</p>
                 )}
              </>
            )}
          </div>

          <input
            type="file"
            id={`file-upload-${field.name}`}
            className="hidden"
            accept={field.validations?.fileTypes?.join(',') || '*/*'}
            onChange={handleFileChange}
            disabled={isLoading || !isEditable}
          />

          {fileInfo && <div className="text-sm text-gray-700">{fileInfo}</div>}
          {errorMessage && <div className="text-sm text-red-500">{errorMessage}</div>}
          {savedDocuments} {/* Display already uploaded documents */}
        </div>
      ) : (
        // Read-only view
        <div className="w-full space-y-2">
           {/* Label is handled in FieldRenderer */}
            {savedDocuments ? savedDocuments : <p className="text-sm text-gray-600">No file uploaded</p>}
        </div>
      )}
    </div>
  );
};


// Component to render individual fields
const FieldRenderer: React.FC<{
  field: ApplicationField;
  formData: any; // Current form data object
  onFieldChange: (name: string, value: any, documents?: ApplicationField['documents']) => void;
  validationErrors: { [key: string]: string };
  isEditable: boolean; // Passed from parent, indicates if the whole form step is editable
  companyId: string;
  applicationId: string;
}> = ({ field, formData, onFieldChange, validationErrors, isEditable, companyId, applicationId }) => {
  // Do not render fields that are section titles
  if (field.isParent && field.type === 'section' && !field.subGroup) {
      return null;
  }

   // Do not render invisible fields
   if (!field.isVisible) {
       return null;
   }


  const fieldError = validationErrors[field.name];
  // Ensure we use the combined isEditable status (page editable AND field editable)
  const canEditField = isEditable && field.isEditable;

  const fieldValue = formData[field.name]?.value; // Access the value property
  const fieldDocuments = formData[field.name]?.documents; // Access the documents property

  const handleValueChange = useCallback((value: any) => {
     onFieldChange(field.name, value);
  }, [field.name, onFieldChange]);

   const handleDocumentsChange = useCallback((documents: ApplicationField['documents']) => {
      console.log('handleDocumentsChange called with:', documents);
      console.log('Current fieldValue:', fieldValue);
      console.log('Current fieldDocuments:', fieldDocuments);

      // CRITICAL FIX: For file fields, preserve the existing value and only update documents
      // Don't pass fieldValue as it might be null/undefined and overwrite the field incorrectly
      onFieldChange(field.name, fieldValue, documents);
   }, [field.name, onFieldChange]); // Removed fieldValue from dependencies to prevent unnecessary re-creation


  const commonInputProps = {
    id: field.name,
    name: field.name,
    placeholder: field.placeholder,
    disabled: !canEditField, // Disable based on combined editability
    'aria-describedby': field.helpText ? `${field.name}-help` : undefined,
    'aria-invalid': !!fieldError,
    className: cn(fieldError && 'border-red-500'), // Add error class if validation failed
  };

  let inputElement;
  let labelElement = (
     <Label htmlFor={field.name}>
         {field.label}
         {field.isRequired && <span className="text-red-500 ml-1">*</span>} {/* Indicate required fields */}
     </Label>
  );

  switch (field.type) {
    case 'text':
      inputElement = (
        <Input
          type="text"
          value={fieldValue ?? ''}
          onChange={(e) => handleValueChange(e.target.value)}
          {...commonInputProps}
        />
      );
      break;
    case 'textarea':
      inputElement = (
        <Textarea
          value={fieldValue ?? ''}
          onChange={(e) => handleValueChange(e.target.value)}
          {...commonInputProps}
        />
      );
      break;
    case 'number':
      inputElement = (
        <Input
          type="number"
           value={fieldValue ?? ''}
           onChange={(e) => handleValueChange(e.target.value === '' ? undefined : Number(e.target.value))} // Allow empty string input, store undefined
          {...commonInputProps}
        />
      );
      break;
    case 'date':
        inputElement = (
         <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                   "w-full justify-start text-left font-normal",
                   !fieldValue && "text-muted-foreground",
                   commonInputProps.className // Include error class
                 )}
                disabled={commonInputProps.disabled}
                id={commonInputProps.id} // Pass id for Label association
              >
                <Calendar className="mr-2 h-4 w-4" />
                {fieldValue ? format(new Date(fieldValue), "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
               <Calendar
                 mode="single"
                 selected={fieldValue ? new Date(fieldValue) : undefined}
                 onSelect={(date) => handleValueChange(date ? date.toISOString() : undefined)}
                 initialFocus
                 disabled={commonInputProps.disabled}
               />
            </PopoverContent>
         </Popover>
        );
      break;
    case 'file':
       // FileInput component includes its own label and structure
       return (
           <div key={field.name} className="space-y-2 mb-6">
                {/* Description for non-parent fields (less common but possible) */}
                {field.description && !field.isParent && <p className="text-sm text-gray-600 mb-2">{field.description}</p>}

                {/* Label and tooltip for FileInput */}
                 <div className="flex items-center space-x-1">
                    <Label htmlFor={`file-upload-${field.name}`}> {/* FileInput uses a specific ID format */}
                        {field.label}
                        {field.isRequired && <span className="text-red-500 ml-1">*</span>}
                     </Label>
                     {field.tooltip && (
                       <TooltipProvider>
                         <Tooltip>
                           <TooltipTrigger asChild>
                              <span className="text-gray-500 cursor-help">
                               <AlertCircle className="h-4 w-4 inline-block" />
                              </span>
                           </TooltipTrigger>
                           <TooltipContent>
                             <p>{field.tooltip}</p>
                           </TooltipContent>
                         </Tooltip>
                       </TooltipProvider>
                     )}
                  </div>
                 {field.helpText && <p className="text-xs text-gray-500 mt-1" id={`${field.name}-help`}>{field.helpText}</p>}

                <FileInput
                    key={field.name} // Simple key based on field name
                    field={field}
                    value={fieldDocuments}
                    onChange={handleDocumentsChange}
                    isEditable={canEditField} // Pass combined editability
                    companyId={companyId}
                    applicationId={applicationId}
                 />
                 {fieldError && (
                    <p className="text-sm font-medium text-red-500 mt-1" role="alert">
                        {fieldError}
                    </p>
                 )}
           </div>
       );
    case 'checkbox':
         // Checkbox rendering is handled inline below with label
         inputElement = (
             <Checkbox
                 id={field.name}
                 checked={!!fieldValue}
                 onCheckedChange={(checked) => handleValueChange(checked)}
                 disabled={!canEditField} // Disable based on combined editability
                 // className already handled by Shadcn UI
                 {...('aria-describedby' in commonInputProps && {'aria-describedby': commonInputProps['aria-describedby']})}
                 {...('aria-invalid' in commonInputProps && {'aria-invalid': commonInputProps['aria-invalid']})}
             />
         );
         labelElement = (
             <Label
                 htmlFor={field.name}
                 className="flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
             >
                 {field.label}
                 {field.isRequired && <span className="text-red-500 ml-1">*</span>}
             </Label>
         );

         // Checkbox rendering needs a specific flex container
         return (
              <div key={field.name} className="space-y-2 mb-6">
                   {field.description && !field.isParent && <p className="text-sm text-gray-600 mb-2">{field.description}</p>}

                   <div className="flex flex-row items-start space-x-3 space-y-0"> {/* Adjusted spacing */}
                        {inputElement} {/* The checkbox */}
                        <div className="grid gap-1.5 leading-none">
                             {labelElement} {/* The label */}
                             {field.helpText && <p className="text-xs text-gray-500" id={`${field.name}-help`}>{field.helpText}</p>}
                             {field.tooltip && (
                                 <TooltipProvider>
                                   <Tooltip>
                                     <TooltipTrigger asChild>
                                        <span className="ml-1 text-gray-500 cursor-help">
                                         <AlertCircle className="h-4 w-4 inline-block" />
                                        </span>
                                     </TooltipTrigger>
                                     <TooltipContent>
                                       <p>{field.tooltip}</p>
                                     </TooltipContent>
                                   </Tooltip>
                                 </TooltipProvider>
                              )}
                        </div>
                   </div>
                   {fieldError && (
                       <p className="text-sm font-medium text-red-500 mt-1" role="alert">
                           {fieldError}
                       </p>
                   )}
              </div>
          );
    case 'radio':
         if (!field.options) return null;
         // RadioGroup rendering is handled inline below with label
         inputElement = (
            <RadioGroup
               value={fieldValue ?? ''}
               onValueChange={handleValueChange}
               disabled={!canEditField} // Disable based on combined editability
                // No commonInputProps spread here as RadioGroup has different props
            >
               {field.options.map(option => (
                   <div key={option.value} className="flex items-center space-x-2">
                       <RadioGroupItem
                           value={option.value}
                           id={`${field.name}-${option.value}`}
                           disabled={!canEditField}
                       />
                       <Label htmlFor={`${field.name}-${option.value}`}>{option.label}</Label>
                   </div>
               ))}
            </RadioGroup>
         );
          // RadioGroup needs its label and help text rendered separately
         return (
              <div key={field.name} className="space-y-2 mb-6">
                  {field.description && !field.isParent && <p className="text-sm text-gray-600 mb-2">{field.description}</p>}

                  {/* Label, tooltip for RadioGroup */}
                  <div className="flex items-center space-x-1">
                     <Label> {/* No htmlFor needed for the group label */}
                         {field.label}
                         {field.isRequired && <span className="text-red-500 ml-1">*</span>}
                     </Label>
                      {field.tooltip && (
                         <TooltipProvider>
                           <Tooltip>
                             <TooltipTrigger asChild>
                                <span className="text-gray-500 cursor-help">
                                 <AlertCircle className="h-4 w-4 inline-block" />
                                </span>
                             </TooltipTrigger>
                             <TooltipContent>
                               <p>{field.tooltip}</p>
                             </TooltipContent>
                           </Tooltip>
                         </TooltipProvider>
                       )}
                   </div>
                   {field.helpText && <p className="text-xs text-gray-500 mt-1" id={`${field.name}-help`}>{field.helpText}</p>}

                   {inputElement} {/* The RadioGroup */}

                  {fieldError && (
                       <p className="text-sm font-medium text-red-500 mt-1" role="alert">
                           {fieldError}
                       </p>
                   )}
              </div>
          );
    case 'select':
        if (!field.options) return null;
        inputElement = (
          <Select
             value={fieldValue ?? ''}
             onValueChange={handleValueChange}
             disabled={!canEditField} // Disable based on combined editability
             // No commonInputProps spread here as Select component has different props
          >
             <SelectTrigger
                 className={cn("w-full", fieldError && 'border-red-500')} // Include error class
                 id={field.name} // Pass id for Label association
                 disabled={!canEditField}
             >
                <SelectValue placeholder={field.placeholder} />
             </SelectTrigger>
             <SelectContent>
                {field.options.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                        {option.label}
                    </SelectItem>
                ))}
             </SelectContent>
          </Select>
        );
      break;
    case 'section':
        // Fields with type 'section' and isParent: true and no subGroup are section titles, handled elsewhere.
        // Other fields with type 'section' are not expected as input fields.
        console.warn(`Encountered unexpected field of type 'section' that is not a main section title: ${field.name}`);
        return null; // Do not render as input
    default:
      inputElement = <p className="text-red-500">Unknown field type: {field.type}</p>;
      break; // Ensure break for default case
  }

   // Read-only rendering for non-file inputs when not editable (page or field) or in preview
    if (!canEditField) {
        let displayValue: React.ReactNode = fieldValue !== undefined && fieldValue !== null && fieldValue !== '' ? String(fieldValue) : 'N/A';

         if (field.type === 'checkbox') {
             displayValue = fieldValue ? 'Yes' : 'No';
         } else if (field.type === 'select' || field.type === 'radio') {
              const selectedOption = field.options?.find(opt => opt.value === fieldValue);
              displayValue = selectedOption ? selectedOption.label : 'N/A';
         } else if (field.type === 'date' && fieldValue) {
             try {
                 displayValue = format(new Date(fieldValue), "PPP");
             } catch (e) {
                 displayValue = 'Invalid Date';
             }
         }
         // File type is handled separately in its own return statement

        return (
             <div key={field.name} className="space-y-2 mb-6">
                  {/* Description for non-parent fields */}
                  {field.description && !field.isParent && <p className="text-sm text-gray-600 mb-2">{field.description}</p>}
                  <p className="text-sm font-medium text-gray-700">
                       {field.label}: {/* Use label as title in read-only */}
                  </p>
                  <div className="text-sm text-gray-600">{displayValue}</div> {/* Use a div for value */}
             </div>
        );
    }


  // Default rendering for editable inputs (excluding checkbox, radio, file which are handled above)
  return (
    <div key={field.name} className="space-y-2 mb-6">
         {/* Description for non-parent fields */}
         {field.description && !field.isParent && <p className="text-sm text-gray-600 mb-2">{field.description}</p>}

        {/* Label and tooltip (excluding checkbox, radio, file handled above) */}
        {field.type !== 'checkbox' && field.type !== 'radio' && field.type !== 'file' && (
           <div className="flex items-center space-x-1">
                {labelElement} {/* Label with required indicator */}
                 {field.tooltip && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                           <span className="text-gray-500 cursor-help">
                            <AlertCircle className="h-4 w-4 inline-block" />
                           </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{field.tooltip}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
           </div>
         )}

      {inputElement} {/* The input field */}

      {field.helpText && field.type !== 'checkbox' && field.type !== 'radio' && field.type !== 'file' && (
         <p className="text-xs text-gray-500 mt-1" id={`${field.name}-help`}>{field.helpText}</p>
      )}

      {fieldError && (
        <p className="text-sm font-medium text-red-500 mt-1" role="alert">
          {fieldError}
        </p>
      )}
    </div>
  );
};


export default function ApplicationPage() {
  const params = useParams();
  const router = useRouter();
  const { companyId, applicationId } = params;

  const [application, setApplication] = useState<ApplicationDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<{ [key: string]: { value: any; documents?: ApplicationField['documents'] } }>({});
  const [currentStep, setCurrentStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';


   // Group fields by section and subsection, ordered
   // Sections are defined by isParent: true, type: 'section', no subGroup. Their 'group' is the section key.
   // Subsections are implicit - fields with the same subGroup within a section's group.
  const groupedFields = useMemo(() => {
    // Only process if application data and fields array are valid
    if (!application || !Array.isArray(application.fields)) {
        console.log("groupedFields: Application or fields are not valid", application);
        return {};
    }

    const groups: GroupedFields = {};

    // 1. Identify potential main section title fields (isParent: true, type: 'section', no subGroup).
    const potentialMainSectionTitleFields = application.fields
      .filter(field => field.isParent && field.type === 'section');

    // Group potential titles by their 'group' name
    const mainSectionTitlesByGroup: { [key: string]: ApplicationField[] } = {};
    potentialMainSectionTitleFields.forEach(field => {
        if (!mainSectionTitlesByGroup[field.group]) {
            mainSectionTitlesByGroup[field.group] = [];
        }
        mainSectionTitlesByGroup[field.group].push(field);
    });

    // For each group, select the main section title field with the minimum order
    // and initialize the groups object with this canonical title field.
    Object.keys(mainSectionTitlesByGroup).forEach(groupName => {
        const fieldsInGroup = mainSectionTitlesByGroup[groupName];
        if (fieldsInGroup.length > 1) {
             // Warn if duplicates exist, show orders
             console.warn(`Multiple main section title fields found for group "${groupName}". Orders: ${fieldsInGroup.map(f => f.order).join(', ')}. Selecting the one with the lowest order (${fieldsInGroup.sort((a, b) => a.order - b.order)[0].order}).`);
             // Sort to reliably get the one with the lowest order
             fieldsInGroup.sort((a, b) => a.order - b.order);
        }
        // Select the field with the minimum order (always the first after sorting)
        const sectionTitleField = fieldsInGroup[0];

        groups[groupName] = {
           sectionTitleField: sectionTitleField,
           subgroups: {},
           fields: [],
        };
    });


    // 2. Add all other fields to their respective groups/subgroups.
    application.fields
      .filter(field => !(field.isParent && field.type === 'section')) // Exclude main section title fields
      .sort((a, b) => a.order - b.order) // Sort these fields for consistent rendering order
      .forEach(field => {
        const parentGroup = groups[field.group];
        if (parentGroup) {
          if (field.subGroup) {
            // Field belongs to a subgroup. Ensure the subgroup array exists.
            if (!parentGroup.subgroups[field.subGroup]) {
               parentGroup.subgroups[field.subGroup] = [];
            }
            parentGroup.subgroups[field.subGroup].push(field);
          } else {
            // Field belongs directly to the main section (no subgroup)
            parentGroup.fields.push(field);
          }
        } else {
          // This field belongs to a group for which no main section title field was found.
          console.warn(`Field "${field.name}" (group: "${field.group}", subgroup: "${field.subGroup || 'none'}") belongs to a group with no defined main section title field. It will not be displayed.`);
           // It will not be included in groupedFields structure if its group has no main section title.
        }
    });

    // 3. Sort fields within each subgroup (already done in step 2 due to initial sort, but re-sorting explicitly for clarity/safety)
     Object.values(groups).forEach(group => {
         // group.fields is already sorted from step 2
         Object.values(group.subgroups).forEach(subgroupFields => {
             subgroupFields.sort((a, b) => a.order - b.order); // Ensure subgroup fields are sorted
         });
     });


    console.log("Grouped Fields (Processed):", groups); // Keep logging for debugging processed structure
    return groups;

  }, [application]); // Depend on application data


  // Get ordered list of section names (group names) based on the order of the selected main section title field
  // This array determines the order and count of wizard steps.
  const sectionNames = useMemo(() => {
      if (!application || !Array.isArray(application.fields)) return [];

      // Get the main section title fields, sort them by their order property,
      // and map to their 'group' name. Filter for uniqueness as the grouping logic
      // selected one parent per group name.
      const names = application.fields
          .filter(field => field.isParent && field.type === 'section')
          .sort((a, b) => a.order - b.order)
          .map(field => field.group);

      // Ensure uniqueness of section names in the list
      return names.filter((value, index, self) => self.indexOf(value) === index);

  }, [application]); // Depend on application data


  // Determine the group names (section names) to display in the current step
  const sectionNamesInCurrentStep = useMemo(() => {
      const start = currentStep * FIELDS_PER_STEP;
      const end = start + FIELDS_PER_STEP;
      return sectionNames.slice(start, end);
  }, [currentStep, sectionNames]);


  const totalSteps = Math.ceil(sectionNames.length / FIELDS_PER_STEP);
  const isLastStep = currentStep >= totalSteps - 1; // Use >= for safety


  useEffect(() => {
    // Fetch application data on component mount and when companyId/applicationId change
    if (!companyId || !applicationId) {
        console.warn("Missing companyId or applicationId in route parameters.");
        // Handle missing params case, e.g., redirect or show error
        // router.push('/some-error-page');
        return;
    }
    const fetchApplication = async () => {
      try {
        setLoading(true);
        // Get token from cookies
        const cookieToken = document.cookie
          .split('; ')
          .find((row) => row.startsWith('token='))
          ?.split('=')[1];

        if (!cookieToken) {
          // Handle authentication error, redirect to login
           console.error('Authentication token not found.');
           toast.error('Authentication required. Please log in.');
           // router.push('/login'); // Redirect to login page
           setLoading(false); // Stop loading animation
           return; // Stop execution
        }

        const response = await fetch(`/api/supplier/${companyId}/applications/${applicationId}`, {
          headers: {
            'Authorization': `Bearer ${cookieToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('API Error Response:', errorData); // Log error response from API
            // Handle specific API error statuses (e.g., 404 not found, 403 forbidden)
             if (response.status === 404) {
                toast.error('Application not found.');
                 // router.push(`/supplier/${companyId}/applications`); // Redirect to list
             } else if (response.status === 403) {
                 toast.error('You do not have permission to view this application.');
                 // router.push(`/supplier/${companyId}/applications`); // Redirect to list or dashboard
             }
             else {
                toast.error(errorData.message || `Failed to fetch application (Status: ${response.status})`);
             }
             setApplication(null); // Ensure application state is null on fetch error
             setFormData({}); // Clear form data on fetch error

             // Depending on desired flow, might not redirect automatically but show error on page

             return; // Stop processing after error
        }

        const responseData = await response.json();
        const data = responseData.data || [];
        console.log('Fetched raw data:', data); // Log the raw data structure for debugging

        // Validate fetched data structure
        if (data && Array.isArray(data.fields)) {
            setApplication(data);
            console.log('Processed Application Data:', data);

            // Initialize form data from fetched application fields
            const initialFormData: { [key: string]: { value: any; documents?: ApplicationField['documents'] } } = {};
            data.fields.forEach((field: ApplicationField) => {
                initialFormData[field.name] = {
                     value: field.value,
                     documents: field.documents,
                };
            });
            setFormData(initialFormData);
            setValidationErrors({}); // Clear any previous validation errors
            console.log('initialFormData', initialFormData);

        } else {
            // Handle the case where data or data.fields is missing/invalid from a successful response
            console.error("Fetched data does not contain a valid 'fields' array structure:", data);
            setApplication(null); // Set application to null
            setFormData({}); // Initialize form data as empty
             toast.error("Application data structure is invalid.");
             // Show error message on page or redirect
        }

      } catch (error: any) {
        console.error('Error during application fetch process:', error);
         if (!error.message.includes('Authentication token not found') &&
             !error.message.includes('Failed to fetch application')) {
              // Avoid showing generic error if a specific error (like auth or API status) was already toasted
              toast.error(`An unexpected error occurred: ${error.message}`);
         }
         setApplication(null); // Ensure application state is null
         setFormData({}); // Ensure form data is empty

      } finally {
        setLoading(false); // Always stop loading
      }
    };
    fetchApplication();
    // Cleanup function (optional but good practice)
     return () => {
         // Any cleanup needed when component unmounts or dependencies change
     };
  }, [companyId, applicationId, router, BACKEND_API_URL]); // Add dependencies


   // Check if application is editable based on status and end date
   const isApplicationEditable = useMemo(() => {
      if (!application) return false;
      const endDate = new Date(application.ends);
      const now = new Date();
      const status = application.status;

      // Editable if draft or submitted and before or exactly on the end date
      return (status === 'draft' || status === 'submitted') && now <= endDate;

   }, [application]);

   const validateField = useCallback((field: ApplicationField, value: any, documents?: ApplicationField['documents']): string | null => {
       // Only validate fields that are visible, editable, and NOT section titles
       if (!field.isVisible || !field.isEditable || (field.isParent && field.type === 'section')) {
           return null;
       }

       // Check for required fields
       if (field.isRequired) {
           if (field.type === 'file') {
               if (!documents || documents.length === 0) {
                   return `${field.label} is required.`;
               }
           } else if (value === undefined || value === null || value === '') {
               // For numbers, 0 might be a valid value if required
                if (field.type === 'number' && value === 0) {
                   // 0 is valid for number type if required
                } else if (field.type === 'checkbox') {
                    // Checkbox required means it must be true
                    if (value !== true) {
                         return `${field.label} must be checked.`;
                    }
                }
                else {
                    return `${field.label} is required.`;
                }
           }
       }

       const validations = field.validations;

       // Numeric validations
       if (field.type === 'number' && value !== undefined && value !== null && value !== '') {
            const numValue = Number(value);
             if (!isNaN(numValue)) {
                if (validations?.min !== undefined && numValue < validations.min) {
                    return `${field.label} must be at least ${validations.min}.`;
                }
                if (validations?.max !== undefined && numValue > validations.max) {
                    return `${field.label} cannot exceed ${validations.max}.`;
                }
             } else {
                 // If value is not empty but is not a valid number
                  return `${field.label} must be a valid number.`;
             }
       }

       // Pattern validation for text/textarea types
       if ((field.type === 'text' || field.type === 'textarea') && value && validations?.pattern) {
           try {
            console.log(`Validating ${field.name} with pattern: ${validations?.pattern}`);

               // Add anchors to pattern if not present to validate the whole string
               const pattern = validations.pattern.startsWith('^') && validations.pattern.endsWith('$')
                               ? validations.pattern
                               : `^${validations.pattern}$`;
               const regex = new RegExp(pattern);
               if (!regex.test(value)) {
                   // Use field's helpText or a generic message for pattern mismatch
                   return field.helpText && field.helpText !== field.label // Avoid using label as help text message
                            ? field.helpText
                            : `${field.label} format is invalid.`;
               }
           } catch (e) {
               console.error(`Invalid regex pattern for field ${field.name}: ${validations.pattern}`, e);
               // If regex is invalid, treat field as valid but log the error
           }
       }

       // File validations (size and type) are primarily handled in the FileInput component
       // but can be re-validated here if necessary before submission. The FileInput
       // component is responsible for setting the initial validation error message.

       return null; // No error
   }, []);

    // Validate fields in the current step's sections
    const validateCurrentStep = useCallback(() => {
        const errors: { [key: string]: string } = {};
        let isValid = true;

        sectionNamesInCurrentStep.forEach(sectionName => {
            const section = groupedFields[sectionName];
            if (!section) return; // Should not happen if sectionNamesInCurrentStep is correct

            // Validate fields directly under the section
            section.fields.forEach(field => {
                const error = validateField(field, formData[field.name]?.value, formData[field.name]?.documents);
                if (error) {
                    errors[field.name] = error;
                    isValid = false;
                }
            });
            // Validate fields within subgroups
            Object.values(section.subgroups).forEach(subgroupFields => {
                 subgroupFields.forEach(field => {
                    const error = validateField(field, formData[field.name]?.value, formData[field.name]?.documents);
                     if (error) {
                         errors[field.name] = error;
                         isValid = false;
                     }
                 });
            });
        });

        // Merge current step errors with existing errors (to keep errors from previous steps)
        // This ensures errors from previous steps are still visible in the validation summary.
         setValidationErrors(prev => ({ ...prev, ...errors }));

        return isValid;
    }, [sectionNamesInCurrentStep, groupedFields, formData, validateField]);


     // Validate ALL fields (used for submission)
    const validateAllFields = useCallback(() => {
         // Only validate fields that are visible and editable, and not section titles
        const fieldsToValidate = application?.fields.filter(field =>
             field.isVisible && field.isEditable && !(field.isParent && field.type === 'section')
        ) || [];

        const errors: { [key: string]: string } = {};
        let isValid = true;

        fieldsToValidate.forEach(field => {
             const error = validateField(field, formData[field.name]?.value, formData[field.name]?.documents);
              if (error) {
                  errors[field.name] = error;
                  isValid = false;
              }
         });

        setValidationErrors(errors);
        return isValid;
    }, [application?.fields, formData, validateField]);


    useEffect(() => {
      console.log('formData has been updated:', formData);
    }, [formData]); // This effect runs whenever formData changes
      
    // 1. FIXED: handleFieldChange function in main component
    const handleFieldChange = useCallback((name: string, value: any, documents?: ApplicationField['documents']) => {
      console.log('handleFieldChange', name, value, documents);
      setFormData(prev => {
        const fieldData = prev[name] || {}; // Get existing field data for this field name
        
        // CRITICAL FIX: Create new object to ensure React detects the change
        // Don't spread fieldData first as it might have old documents that override our new ones
        const updatedFieldData = {
          value: value, // Always update value
          documents: fieldData.documents || [], // Start with existing documents or empty array
        };

        console.log('Before documents check - updatedFieldData:', updatedFieldData);
        console.log('arguments.length:', arguments.length);
        console.log('documents parameter:', documents);

        // CRITICAL FIX: Always update documents when provided, even if undefined
        if (arguments.length > 2) {
          console.log('Setting documents to:', documents);
          console.log('Documents is array?', Array.isArray(documents));
          console.log('Documents length:', documents?.length);
          updatedFieldData.documents = documents;
          console.log('updatedFieldData.documents after setting:', updatedFieldData.documents);
          console.log('updatedFieldData after setting documents:', updatedFieldData);
        } else {
          console.log('Not updating documents - arguments.length <= 2');
        }
        
        const newFormData = {
          ...prev,
          [name]: updatedFieldData
        };

        // CRITICAL FIX: Log the state change to verify it's happening
        console.log('FormData updated for field:', name);
        console.log('Previous state:', prev[name]);
        console.log('New state:', updatedFieldData);
        console.log('New state documents specifically:', updatedFieldData.documents);
        console.log('Full new formData:', newFormData);

        return newFormData;
      });

      // Clear validation error for this field when its value or documents change
      setValidationErrors(prev => {
        const newState = { ...prev };
        delete newState[name];
        return newState;
      });
    }, []);


    const updateApplication = async (status: ApplicationDocument['status']) => {
       // Prevent saving/submitting if already in process or application is null
       if (!application || isSaving || isSubmitting) {
           console.log("Update prevented: Application not loaded or already saving/submitting.");
           return false; // Indicate update was not attempted
       }

        console.log(`Attempting to update application with status: ${status}`);
       const isSubmit = status === 'submitted';
       console.log('application', application);

       if (isSubmit) {
            // For submission, validate all fields first
           const allFieldsValid = validateAllFields();
           if (!allFieldsValid) {
               toast.error('Please fix all errors before submitting the application.');
                // Scroll to the first error if needed - validateAllFields updates validationErrors state
                const firstErrorFieldName = Object.keys(validationErrors)[0];
                if (firstErrorFieldName) {
                    const firstErrorElement = document.getElementById(firstErrorFieldName);
                     if (firstErrorElement) {
                        firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                     }
                }
               return false; // Stop if validation fails
           }
            // Also check editability before final submit
            if (!isApplicationEditable) {
                toast.error('Application is no longer editable.');
                return false; // Stop if not editable
            }

           setIsSubmitting(true);
       } else {
           setIsSaving(true);
       }

       // Prepare data to send
       // Iterate through the original application.fields to ensure all fields are included,
       // but use values/documents from formData state.
       const updatedFieldsData = application.fields.map(field => {
           const fieldData = formData[field.name];
           return {
               ...field, // Include all original field properties
               value: fieldData?.value ?? (field.type === 'checkbox' ? false : null), // Use formData value, default checkbox to false, others to null
               documents: fieldData?.documents ?? [], // Use formData documents, default to empty array
                // Note: Backend should handle setting submittedAt when status is 'submitted'
           };
       });

       const payload = {
           ...application, // Start with existing application data
            // Explicitly include only mutable top-level fields you allow updating via this form
           title: application.title,
           description: application.description,
           // ... other top-level fields if editable via this form ...
           fields: updatedFieldsData, // Overlay with updated field data
           status: status, // Set the target status
           // Backend should handle updatedBy and submittedAt (if submitting)
       };

        // Optional: Clean up mongoose-specific _id properties from nested objects
        // A robust backend shouldn't rely on client-sent _ids for creation/update logic,
        // but removing them client-side can prevent potential issues with some APIs.
        // Iterate through a deep copy to avoid modifying original state prematurely if needed
        const payloadToSend = JSON.parse(JSON.stringify(payload)); // Simple deep copy
        payloadToSend.fields = payloadToSend.fields.map((field: any) => {
             const { _id, ...rest } = field; // Remove _id from field itself
             if (rest.documents) {
                 rest.documents = rest.documents.map((doc: any) => {
                      const { _id: docId, ...docRest } = doc; // Remove _id from documents
                      return docRest;
                 });
             }
             // Ensure options also don't have client-generated _ids if they were added accidentally
             if (rest.options) {
                 rest.options = rest.options.map((option: any) => {
                     const { _id: optionId, ...optionRest } = option;
                     return optionRest;
                 });
             }
              if (rest.optionScore) {
                  rest.optionScore = rest.optionScore.map((option: any) => {
                      const { _id: optionId, ...optionRest } = option;
                      return optionRest;
                  });
              }
             return rest;
        });
        // Assuming top-level _id is handled by the API route (present in URL)


       try {
         // Get token from cookies
         const cookieToken = document.cookie
           .split('; ')
           .find((row) => row.startsWith('token='))
           ?.split('=')[1];

         if (!cookieToken) {
           throw new Error('Authentication token not found');
         }

         console.log("Sending payload:", payloadToSend);

         const response = await fetch(`${BACKEND_API_URL}/api/supplier/${companyId}/applications/${applicationId}`, {
           method: 'PUT', // Use PUT for updating the existing application
           headers: {
             'Authorization': `Bearer ${cookieToken}`,
             'Content-Type': 'application/json'
             // For file uploads, the FileInput component handles the upload
             // and the filePath is included in the documents array within the JSON payload.
             // The backend must be designed to process this JSON, recognize the file paths,
             // and associate the correct files with the application fields.
             // If your backend expects multi-part form data for files combined with JSON for fields,
             // the structure of this request and payload would need to be different.
           },
           body: JSON.stringify(payloadToSend),
         });

         if (!response.ok) {
             const errorData = await response.json();
             console.error('API Error Response:', errorData); // Log error response from API
            throw new Error(errorData.error || `Failed to ${isSubmit ? 'submit' : 'save'} application (Status: ${response.status})`);
         }
         const rawupdatedApplicationData = await response.json();

         const updatedApplicationData = rawupdatedApplicationData.data.applicationData;
         setApplication(updatedApplicationData); // Update local state with fresh data from backend
         toast.success(`Application ${isSubmit ? 'submitted' : 'saved'} successfully!`);
          console.log(`Application ${isSubmit ? 'submitted' : 'saved'} successfully`, updatedApplicationData);

          if (isSubmit) {
              // Optional: Redirect user after successful submission
               router.push(`/supplier/${companyId}/applications?type=${updatedApplicationData.type}`); // Redirect to the list page
          }
          return true; // Indicate success
       } catch (error: any) {
         console.error(`Error ${isSubmit ? 'submitting' : 'saving'} application:`, error);
         toast.error(`Failed to ${isSubmit ? 'submit' : 'save'} application: ${error.message}`);
          return false; // Indicate failure
       } finally {
         if (isSubmit) {
           setIsSubmitting(false);
         } else {
           setIsSaving(false);
         }
       }
    };


    const handleNext = useCallback(async () => {
        // Validate current step's sections before moving
        const currentStepValid = true;//UNCOMMENT validateCurrentStep();

        if (!currentStepValid) {
            toast.error('Please fix the errors in the current section(s) before proceeding.');
            // Scroll to the first error in the current step
              const firstErrorFieldName = Object.keys(validationErrors).find(fieldName => {
                  // Check if the field belongs to one of the sections in the current step
                  return sectionNamesInCurrentStep.some(sectionName => {
                      const section = groupedFields[sectionName];
                      if (!section) return false;
                      // Check main fields
                      if (section.fields.some(f => f.name === fieldName)) return true;
                      // Check subgroup fields
                      return Object.values(section.subgroups).some(subgroupFields =>
                        subgroupFields.some(f => f.name === fieldName)
                      );
                  });
              });

            if (firstErrorFieldName) {
                const firstErrorElement = document.getElementById(firstErrorFieldName);
                if (firstErrorElement) {
                    firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
            return; // Stop if current step validation fails
        }

          // If current step is valid, save draft and move to the next step
        const saveSuccess = true;//UNCOMMENT await updateApplication('draft');

          // Only move to the next step if save was attempted and successful
        if (saveSuccess && !isLastStep) {
            setCurrentStep(prev => prev + 1);
            // Scroll to the top of the form area for the new step
            window.scrollTo({ top: 0, behavior: 'smooth' }); // Or scroll to a specific form container div
          }

    }, [validateCurrentStep, updateApplication, isLastStep, sectionNamesInCurrentStep, groupedFields, validationErrors]);


    const handlePrevious = useCallback(() => {
       if (currentStep > 0) {
           setCurrentStep(prev => prev - 1);
           // Optional: Save draft when going back? Usually not required but depends on UX.
           // updateApplication('draft');

            // Scroll to the top of the form area for the previous step
           window.scrollTo({ top: 0, behavior: 'smooth' }); // Or scroll to a specific form container div
       }
    }, [currentStep]);

    const handleSaveDraft = useCallback(async () => {
        // Decide if you want to validate the current step before saving draft
        // const currentStepValid = validateCurrentStep();
        // if (!currentStepValid) {
        //    toast.error('Current section(s) have errors. Saving draft anyway.');
        // }
        await updateApplication('draft');
    }, [updateApplication]);

    const handleSubmit = useCallback(async () => {
        // validateAllFields() and isApplicationEditable check are called inside updateApplication('submitted')
        await updateApplication('submitted');
    }, [updateApplication]);


    // Determine if the "Submit" button should be disabled
    const isSubmitDisabled = useMemo(() => {
       if (!application) return true; // Cannot submit if application data isn't loaded
       if (!isApplicationEditable) return true; // Cannot submit if not editable (closed date or status)
       if (isSubmitting || isSaving) return true; // Disable while saving or submitting
        // Additional checks could be added, e.g., if certain critical fields are empty across the whole form,
        // but validateAllFields handles most of this on click.
       return false;
    }, [application, isApplicationEditable, isSubmitting, isSaving]);


  if (loading || !application) {
    return (
      <div className="p-6 space-y-6 container mx-auto">
        <Skeleton className="h-10 w-1/3 mb-6" />
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/4" />
            <Skeleton className="h-6 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
             {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
          </CardContent>
           <CardFooter className="flex justify-between">
               <Skeleton className="h-10 w-24" />
               <Skeleton className="h-10 w-24" />
           </CardFooter>
        </Card>
         {/* Optional: Show a specific message if application is null after loading finishes with error */}
          {!loading && !application && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-6" role="alert">
                 <strong className="font-bold">Error loading application:</strong>
                 <span className="block sm:inline"> Please try again or contact support.</span>
               </div>
          )}
      </div>
    );
  }

  const applicationClosed = new Date() > new Date(application.ends);
  const canEdit = isApplicationEditable && !applicationClosed; // Final check for editability based on date


  return (
    <div className="p-6 space-y-6 container mx-auto">
      <Toaster />

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">{application.title}</h1>
          <p className="text-gray-600">{application.description}</p>
          <p className="text-sm text-gray-500 mt-1">Reference: {application.ref}</p>
           <p className="text-sm text-gray-500">Status: <span className="capitalize">{application.status.replace(/_/g, ' ')}</span></p>
             {application.buyerData?.name && (
                  <p className="text-sm text-gray-500">
                      Buyer: {application.buyerData.logoUrl && (
                         <img src={application.buyerData.logoUrl} alt={`${application.buyerData.name} logo`} className="h-4 w-4 inline-block mr-1 rounded-full" />
                      )}
                      {application.buyerData.name}
                      {application.buyerData?.address && `, ${application.buyerData.address}`}
                  </p>
             )}
             <p className={cn("text-sm font-semibold", applicationClosed ? 'text-red-600' : 'text-green-600')}>
                Closes: {format(new Date(application.ends), "PPP")} {applicationClosed ? '(Closed)' : ''}
             </p>

        </div>
         <div className="flex items-center gap-2">
              <Button
                 variant="outline"
                 onClick={() => setIsPreviewMode(!isPreviewMode)}
                 disabled={isSaving || isSubmitting}
              >
                  {isPreviewMode ? 'Back to Form' : 'Preview Application'}
              </Button>
             {canEdit && !isPreviewMode && ( // Only show Save Draft button if editable and not in preview
                <Button
                   variant="outline"
                   onClick={handleSaveDraft}
                   disabled={isSaving || isSubmitting || application.status === 'submitted'}
                >
                  {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Save Draft
                </Button>
             )}
         </div>
      </div>

       {/* Alert if application is closed */}
       {applicationClosed && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
              <strong className="font-bold">Application Closed:</strong>
              <span className="block sm:inline"> This application is no longer editable as the closing date has passed.</span>
            </div>
       )}

       {/* Display validation errors summary from any step if not in preview mode and errors exist */}
      {!isPreviewMode && Object.keys(validationErrors).length > 0 && (
           <Card className="border-red-500 bg-red-50">
               <CardHeader>
                   <CardTitle className="text-red-700 flex items-center">
                       <AlertCircle className="h-5 w-5 mr-2 shrink-0" /> {/* Prevent icon shrinking */}
                       Validation Errors
                   </CardTitle>
                    <p className="text-sm text-red-600">Please fix the following errors:</p>
               </CardHeader>
               <CardContent>
                   <ul className="list-disc list-inside text-sm text-red-600 space-y-1">
                       {/* Sort errors alphabetically by field label for consistency */}
                       {Object.entries(validationErrors)
                           .sort(([nameA], [nameB]) => {
                               const labelA = application?.fields.find(f => f.name === nameA)?.label || nameA;
                               const labelB = application?.fields.find(f => f.name === nameB)?.label || nameB;
                               return labelA.localeCompare(labelB);
                           })
                           .map(([fieldName, errorMessage]) => (
                           <li key={fieldName}>
                               <strong>{application?.fields.find(f => f.name === fieldName)?.label || fieldName}:</strong> {errorMessage}
                           </li>
                       ))}
                   </ul>
               </CardContent>
           </Card>
       )}


      {isPreviewMode ? (
         <Card>
            <CardHeader>
               <CardTitle>Application Preview</CardTitle>
                <p className="text-sm text-gray-600">Review your complete application. Scroll down to see all sections.</p>
            </CardHeader>
            <CardContent className="max-h-[60vh] overflow-y-auto pr-4"> {/* Added max-height and overflow for scrollability */}
               {/* Render all sections in preview mode */}
               {sectionNames.map(sectionName => {
                   const section = groupedFields[sectionName];
                   if (!section || !section.sectionTitleField) return null;

                   return (
                       <div key={sectionName} className="mb-8 pb-4 border-b border-gray-200 last:border-b-0 last:pb-0">
                           {/* Render Section Title */}
                           <h2 className="text-2xl font-bold mb-4">{section.sectionTitleField.description}</h2> {/* Use description as title */}
                           {section.sectionTitleField.helpText && <p className="text-gray-700 mb-6">{section.sectionTitleField.helpText}</p>}


                           {/* Render fields directly under the section */}
                           {section.fields.map(field => (
                                <FieldRenderer
                                   key={field.name}
                                   field={field}
                                   formData={formData}
                                   onFieldChange={() => {}} // No changes in preview
                                   validationErrors={{}} // No errors in preview display
                                   isEditable={false} // Render read-only
                                   companyId={companyId}
                                   applicationId={applicationId}
                                 />
                           ))}

                           {/* Render subgroups and their fields */}
                            {/* Get subgroup names, sort them, and render */}
                           {Object.keys(section.subgroups).sort().map(subgroupName => {
                                const subgroupFields = section.subgroups[subgroupName];
                                if (!subgroupFields || subgroupFields.length === 0) return null;

                                return (
                                   <div key={subgroupName} className="mb-6 ml-4 pl-4 border-l-2 border-gray-200 last:mb-0">
                                       {/* Render Subgroup Header using subgroup name */}
                                        <h4 className="text-lg font-semibold mb-4 capitalize">{subgroupName.replace(/([A-Z])/g, ' $1').trim()}</h4> {/* Basic capitalization */}

                                       {subgroupFields.map(field => (
                                           <FieldRenderer
                                               key={field.name}
                                               field={field}
                                               formData={formData}
                                               onFieldChange={() => {}} // No changes in preview
                                               validationErrors={{}} // No errors in preview display
                                               isEditable={false} // Render read-only
                                               companyId={companyId}
                                               applicationId={applicationId}
                                            />
                                        ))}
                                   </div>
                                );
                           })}
                       </div>
                   );
               })}
            </CardContent>
             <CardFooter>
                 <Button onClick={() => setIsPreviewMode(false)}>Back to Form</Button>
             </CardFooter>
         </Card>

      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Application Form</CardTitle>
             <p className="text-sm text-gray-600 mb-2">
                Step {currentStep + 1} of {totalSteps}
             </p>
             {/* Progress Bar */}
             <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                   className="bg-maroon-700 h-2.5 rounded-full transition-all duration-500 ease-in-out"
                   style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
                ></div>
             </div>
             {/* Display current section titles */}
              <div className="mt-4 text-sm font-medium text-gray-800">
                 Current Sections: {' '}
                 {sectionNamesInCurrentStep.map((sectionName, index) => {
                     const section = groupedFields[sectionName];
                     return section?.sectionTitleField?.description || `Section ${sectionName}`; // Fallback to group name if desc missing
                 }).join(' | ')}
              </div>

          </CardHeader>
          <CardContent>
            {/* Render sections for the current step */}
            {sectionNamesInCurrentStep.map(sectionName => {
                const section = groupedFields[sectionName];
                if (!section || !section.sectionTitleField) {
                    // This case indicates a problem in grouping or sectionNames if it occurs
                    console.error(`Section data missing for group: ${sectionName}`);
                    return <p key={sectionName} className="text-red-500">Error rendering section: {sectionName}</p>;
                }

                return (
                    <div key={sectionName} className="mb-8"> {/* Key by sectionName (group name) */}
                         {/* Render Section Title using sectionTitleField */}
                         <h2 className="text-2xl font-bold mb-4">{section.sectionTitleField.description}</h2> {/* Use description as title */}
                         {section.sectionTitleField.helpText && <p className="text-gray-700 mb-6">{section.sectionTitleField.helpText}</p>}


                        {/* Render fields directly under the section */}
                        {section.fields.map(field => (
                             <FieldRenderer
                                key={field.name}
                                field={field}
                                formData={formData}
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                                isEditable={canEdit}
                                companyId={companyId}
                                applicationId={applicationId}
                              />
                        ))}

                        {/* Render subgroups and their fields */}
                         {/* Get subgroup names, sort them alphabetically, and render */}
                        {Object.keys(section.subgroups).sort().map(subgroupName => {
                           const subgroupFields = section.subgroups[subgroupName];
                            if (!subgroupFields || subgroupFields.length === 0) return null;

                           return (
                              <div key={subgroupName} className="mb-6 ml-4 pl-4 border-l-2 border-gray-200 last:mb-0">
                                  {/* Render Subgroup Header using subgroup name */}
                                   <h4 className="text-lg font-semibold mb-4 capitalize">{subgroupName.replace(/([A-Z])/g, ' $1').trim()}</h4> {/* Basic capitalization */}

                                 {subgroupFields.map(field => (
                                     <FieldRenderer
                                         key={field.name}
                                         field={field}
                                         formData={formData}
                                         onFieldChange={handleFieldChange}
                                         validationErrors={validationErrors}
                                         isEditable={canEdit}
                                         companyId={companyId}
                                         applicationId={applicationId}
                                      />
                                  ))}
                              </div>
                          );
                        })}
                    </div>
                );
            })}
          </CardContent>
          <CardFooter className="flex justify-between">
             <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0 || isSaving || isSubmitting}
             >
               Previous
             </Button>
             {isLastStep ? (
               <Button
                  onClick={handleSubmit}
                  disabled={isSubmitDisabled} // Use the memoized disabled state
               >
                  {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  {applicationClosed ? 'Application Closed' : application?.status === 'submitted' ? 'Update Submission' : 'Submit Application'}
               </Button>
             ) : (
               <Button
                  onClick={handleNext}
                   disabled={isSaving || isSubmitting || !canEdit} // Disable if not editable
               >
                  {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Next
               </Button>
             )}
          </CardFooter>
        </Card>
      )}
    </div>
  );
}