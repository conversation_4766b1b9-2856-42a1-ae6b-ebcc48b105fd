"use client";

import { Card } from "@/components/ui/card";
import {
  FileText,
  ShoppingBag,
  AlertCircle,
  Clock,
  CheckCircle2,
  XCircle,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import JobsPage from '../../../admin/jobs/page'
import SupplierJobs from "@/components/supplier-jobs";
import ActivityFeed from "@/components/supplier-activity-feed";
import { useParams, useRouter } from 'next/navigation';
import DashboardMetrics from "@/components/supplier-metrics";


export default function SupplierDashboard() {
    const params = useParams();
    const router = useRouter();
  const { orderId, companyId } = params;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Supplier Dashboard</h1>

      {/* Stats Cards */}
      <DashboardMetrics companyId={companyId} />

      <SupplierJobs />

      {/* Recent Activity */}
      <Card className="p-6">
      <ActivityFeed companyId={companyId}/>
       
      </Card>
    </div>
  );
}
