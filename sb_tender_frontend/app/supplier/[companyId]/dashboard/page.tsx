"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Building,
  FileText,
  AlertCircle,
  Activity,
  DollarSign,
  Award,
  BarChart as ChartIcon,
  Settings,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Bell,
  CreditCard,
  Eye,
  MoreHorizontal,
  Calendar,
  Target,
  Briefcase,
  Badge,
  MapPin,
  Smartphone,
  Mail,
  Loader2,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import Link from "next/link";
import toast from "react-hot-toast";
import SupplierJobs from "@/components/supplier-jobs";
import { useAuthStore } from "@/lib/store/useAuthStore";
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation";

interface DashboardStats {
  totalUsers: number;
  totalCompanies: number;
  totalJobs: number;
  totalApplications: number;
  weeklyGrowth: number;
  monthlyRevenue: number;
  revenueGrowth: number;
}

interface ActivityData {
  name: string;
  applications: number; //sales
  categories: number; //views
}

interface RecentActivity {
  _id: string;
  description: string;
  timestamp: string;
  action: string;
  userId?: {
    name: string;
  };
}

// Fix type for activities
interface Activity {
  _id: string;
  description: string;
  createdAt: string;
  userId?: { name: string };
}

interface Order {
  _id: string;
  spCompanyId: string;
  status: string;
  totalAmount: number;
  orderItems: OrderItem[];
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
}

interface OrderItem {
  _id: string;
  title: string;
  price: number;
  quantity: number;
}

// Specific Skeleton Loaders
const BidActivitySkeleton = () => (
  <CardContent className="p-6">
    <div className="flex items-center justify-between mb-4">
      <div>
        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="flex items-center space-x-2">
          <div className="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 w-12 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
    <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
  </CardContent>
);

const StatCardsSkeleton = () => (
  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
    {Array.from({ length: 4 }).map((_, i) => (
      <Card key={i} className="bg-white border-0 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse mb-3"></div>
            <div className="h-6 w-12 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

const BidsSkeleton = () => (
  <CardContent className="p-6">
    <div className="flex items-center justify-between mb-6">
      <div className="h-5 w-24 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
    </div>
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
            <div>
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
        </div>
      ))}
      <div className="h-10 w-full bg-gray-200 rounded animate-pulse mt-4"></div>
    </div>
  </CardContent>
);

const NotificationsSkeleton = () => (
  <CardContent className="p-6">
    <div className="flex items-center justify-between mb-6">
      <div className="h-5 w-32 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
    </div>
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
          <div className="flex-1">
            <div className="h-4 w-40 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      ))}
      <div className="h-10 w-full bg-gray-200 rounded animate-pulse mt-4"></div>
    </div>
  </CardContent>
);

const OrdersSkeleton = () => (
  <CardContent className="p-6">
    <div className="flex items-center justify-between mb-6">
      <div className="h-5 w-36 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
    </div>
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
            <div>
              <div className="h-4 w-28 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 w-20 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 w-32 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
        </div>
      ))}
      <div className="h-10 w-full bg-gray-200 rounded animate-pulse mt-4"></div>
    </div>
  </CardContent>
);
export default function SupplierDashboard() {
  const [loading, setLoading] = useState(false);
  const [loadingDashboardMetrics, setLoadingDashboardMetrics] = useState(true);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [loadingOrders, setLoadingOrders] = useState(true);
  const [loadingApplications, setLoadingApplications] = useState(true);
  const [loadingNotifications, setLoadingNotifications] = useState(true);
  const [loadingBidActivityData, setLoadingBidActivityData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const params = useParams();
  const { orderId, companyId } = params;
  const [activities, setActivities] = useState<Activity[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalCompanies: 0,
    totalJobs: 0,
    totalApplications: 0,
    weeklyGrowth: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
  });

  const [metrics, setMetrics] = useState({
    totalUsers: 0,
    totalApplications: 0,
    pendingApplications: 0,
    approvedApplications: 0,
    monthlyTrends: null,
    closedApplications: 0,
    completedApplications: 0,
  });

  const [orders, setOrders] = useState<any[]>([]);
  const [applications, setApplications] = useState<any[]>([]);

  const [activityData, setActivityData] = useState<ActivityData[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [categories, setCategories] = useState([]);

  interface RecentJob {
    _id: string;
    title: string;
    status: string;
    createdAt: string;
  }
  const [recentJobs, setRecentJobs] = useState<RecentJob[]>([]);
  const [filters, setFilters] = useState({
    page: '1',
    limit: '20',
    action: '',
    resource: ''
  });

  const user = useAuthStore((state) => state.user);
  const activeCompanyName = useAuthStore((state) => state.user?.companies[0]?.companyName);
  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';
  const router = useRouter();
  useEffect(() => {
    if (companyId) {
      fetchDashboardMetrics();
      fetchActivities();
      fetchOrders();
      fetchApplications();
      fetchNotifications();
      fetchSupplierBidActivityData();
    }
  }, [companyId]);
  useEffect(() => {
    console.log('Updated Activity data: ', activityData);
  }, [activityData]);

  const getAuthToken = () => {
    return document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];
  };

  const fetchDashboardMetrics = async () => {
    try {
      setLoadingDashboardMetrics(true);
      setError(null);

      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/supplier/${companyId}/metrics/basic`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard metrics');
      }

      const data = await response.json();
      console.log('Dashboard data:', data);

      setMetrics(data.metrics);
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      setError(error.message);
    } finally {
      setLoadingDashboardMetrics(false);
    }
  };


  const fetchActivities = async () => {
    try {
        setLoadingActivities(true);
      const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (!token) {
      throw new Error('Authentication token not found');
    }
          const queryParams = new URLSearchParams(filters).toString();

      const response = await fetch(
        `${BACKEND_API_URL}/api/supplier/${companyId}/activities?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();
      console.log('Activities response', data);
      setActivities(data.activities);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoadingActivities(false);
    }
  };

  const fetchOrders = async () => {
    try {
        setLoadingOrders(true);
      const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (!token) {
      throw new Error('Authentication token not found');
    }
          const queryParams = new URLSearchParams(filters).toString();

      const response = await fetch(
        `${BACKEND_API_URL}/api/supplier/${companyId}/orders?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

    const responseData = await response.json();
    console.log('Order response', responseData);
    const ordersData = responseData.data || [];
    setOrders(ordersData);

    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoadingOrders(false);
    }
  };

  const fetchApplications = async () => {
    try {
      setLoadingApplications(true);
            const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];
      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      const url = `${BACKEND_API_URL}/api/supplier/${companyId}/applications`
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      const responseData = await response.json();
      console.log('Applications response', responseData);
      const applicationsData = responseData.applicationsData || [];
      setApplications(applicationsData);
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Failed to load applications');
    } finally {
      setLoadingApplications(false);
    }
  };

const fetchNotifications = async () => {
    try {
      setLoadingNotifications(true);
            const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];
      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      const url = `${BACKEND_API_URL}/api/supplier/${companyId}/notifications`
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      const responseData = await response.json();
      console.log('Notifications response', responseData);
      const applicationsData = responseData.applicationsData || [];
      setNotifications(applicationsData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setLoadingNotifications(false);
    }
  };


const fetchSupplierBidActivityData = async () => {
    try {
      setLoadingBidActivityData(true);
      const cookieToken = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];
      if (!cookieToken) {
        throw new Error('Authentication token not found');
      }
      const url = `${BACKEND_API_URL}/api/supplier/${companyId}/metrics/bid-activity`
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${cookieToken}`,
          'Content-Type': 'application/json'
        }
      });
      const responseData = await response.json();
      console.log('Supplier bid activity response data: ', responseData);
      const categoriesData = responseData.activityData || [];

      setActivityData(categoriesData);
    
    } catch (error) {
      console.error('Error fetching supplier bid activity data:', error);
      toast.error('Failed to load supplier bid activity data');
    } finally {
      setLoadingBidActivityData(false);
    }
  };

  // Fix formatTimeAgo type
  const formatTimeAgo = (date: string) => {
    const now = new Date().getTime();
    const activityDate = new Date(date).getTime();
    const diffInHours = Math.floor((now - activityDate) / (1000 * 60 * 60));
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };
    const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  // Get a short formatted time
  const formatTime = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit', minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4 animate-pulse">
              <div className="flex items-center space-x-4">
                <div className="h-8 w-8 bg-muted rounded"></div>
                <div>
                  <div className="h-4 w-16 bg-muted rounded mb-2"></div>
                  <div className="h-6 w-8 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Supplier Dashboard</h1>
          <p className="text-muted-foreground mt-1">Welcome back {user?.firstName}! Here's what's happening at {activeCompanyName}..</p>
        </div>
      </div>

      {/* Main Grid Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* Weekly Sales Card - Large */}
        <div className="col-span-12 lg:col-span-5">

          <Card className="border shadow-sm">
            {loadingBidActivityData ? (
                <BidActivitySkeleton />
            ) : (
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-muted-foreground">Bid Activity</p>
                  <div className="flex items-center space-x-2">
                    <h2 className="text-3xl font-bold">
                      {(() => {
                        const totalApplications = activityData.reduce((sum, month) => sum + month.applications, 0);
                        const totalCategories = activityData.reduce((sum, month) => sum + month.categories, 0);
                        return `${totalApplications}/${totalCategories}`;
                      })()}
                    </h2>
                    <div className="flex items-center text-green-600 text-sm">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {(() => {
                        const totalApplications = activityData.reduce((sum, month) => sum + month.applications, 0);
                        const totalCategories = activityData.reduce((sum, month) => sum + month.categories, 0);
                        const bidRate = totalCategories > 0 ? ((totalApplications / totalCategories) * 100).toFixed(1) : '0.0';
                        return `${bidRate}%`;
                      })()}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">Your Bids vs Categories (last 12 months)</p>
                </div>
              </div>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={activityData}>
                    <defs>
                      <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="applications"
                      stroke="#10b981"
                      strokeWidth={2}
                      fill="url(#salesGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
             )}
          </Card>
        </div>

        {/* Stats Cards - 4 small cards */}
        <div className="col-span-12 lg:col-span-7">
            {loadingDashboardMetrics ? (
                <StatCardsSkeleton />
            ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-blue-50 dark:bg-blue-950 rounded-lg flex items-center justify-center mb-3">
                    <Briefcase className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-2xl font-bold">{metrics.pendingApplications}</p>
                  <p className="text-sm text-muted-foreground">Open Categories</p>
                </div>
              </CardContent>
            </Card>

            <Card className="border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-green-50 dark:bg-green-950 rounded-lg flex items-center justify-center mb-3">
                    <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-2xl font-bold">{metrics.closedApplications}</p>
                  <p className="text-sm text-muted-foreground">Closed Categories</p>
                </div>
              </CardContent>
            </Card>

            <Card className="border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-orange-50 dark:bg-orange-950 rounded-lg flex items-center justify-center mb-3">
                    <Award className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <p className="text-2xl font-bold">{metrics.completedApplications || 0}</p>
                  <p className="text-sm text-muted-foreground">Awards & Regrets</p>
                </div>
              </CardContent>
            </Card>

            <Card className="border shadow-sm">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-purple-50 dark:bg-purple-950 rounded-lg flex items-center justify-center mb-3">
                    <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-2xl font-bold">{metrics.totalUsers}</p>
                  <p className="text-sm text-muted-foreground">Company Users</p>
                </div>
              </CardContent>
            </Card>
          </div>
          )}
        </div>
      </div>

    {/* Seecond row - Supplier Jobs */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        <div className="col-span-12 lg:col-span-12">
          <Card className="border shadow-sm">
            <CardContent className="p-6">
              <SupplierJobs />
            </CardContent>
          </Card>
        </div>
      </div>


      {/* Fourth Row - Operations Sections */}
      <div className="grid grid-cols-12 gap-6 mt-6">
        {/* Recent Bids */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="border shadow-sm">

            {loadingApplications ? (
                <BidsSkeleton />
            ) : (
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">Your Recent Bids</h3>

              </div>
              <div className="space-y-4">
                 {applications.length > 0 ? (
                  applications.map((application) => (
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-950 rounded-lg flex items-center justify-center">
                      <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">{ application.title.slice(0, 30) + '...' }</p>
                      <p className="text-xs text-muted-foreground">{formatTimeAgo(application.createdAt)} &nbsp;&nbsp;
                        <span className="{application.status === 'approved' ? 'bg-green-500' : 'bg-blue-500'}">
                          <b>{application.status.toLowerCase().replace(/_/g, ' ')}</b>
                        </span>
                        </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm"
                  onClick={() => router.push(`/supplier/${companyId}/applications/${application._id}`)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                </div> 
                ))
                ) : (
                <div className="">
                <p className="text-lg font-medium">No bids found. Browse the available jobs for new opportunities.</p>
                </div>  
                )}

                
                {applications.length > 0 && (
                    <Link href={`/supplier/${companyId}/applications?type=${applications[0].type}`}>
                    <Button variant="outline" className="w-full mt-4">
                        View All Bids
                    </Button>
                    </Link>
                )}

              </div>
            </CardContent>
            )}
          </Card>
        </div>

        {/* System Notifications */}

        <div className="col-span-12 lg:col-span-4">
          <Card className="border shadow-sm">
            {loadingNotifications ? (
                <NotificationsSkeleton />
            ) : (
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">System Notifications</h3>
              </div>
              <div className="space-y-4">

                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <div key={notification._id} className="flex items-center space-x-3 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{notification.title}</p>
                        <p className="text-sm font-medium">{notification.message}</p>
                            {notification.recipient.email && (
                            <p className="flex items-center gap-1 text-muted-foreground">
                                <Mail className="h-3 w-3" />
                                {notification.recipient.email}
                            </p>
                            )}
                            {notification.recipient.phone && (
                            <p className="flex items-center gap-1 text-muted-foreground">
                                <Smartphone className="h-3 w-3" />
                                {notification.recipient.phone}
                            </p>
                            )}
                            <p className="text-muted-foreground">{notification.status}</p>

                        <p className="text-xs text-muted-foreground">{notification.channel} - {formatTimeAgo(notification.createdAt)}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="">
                    <p className="text-sm font-medium">No notifications found.</p>
                  </div>
                )
                }
                <Button variant="outline" className="w-full mt-4"
                  onClick={() => router.push(`/supplier/${companyId}/notifications`)}>
                  View All Notifications
                </Button>
              </div>
            </CardContent>
            )}
          </Card>
        </div>

        {/* Recent Transactions */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="border shadow-sm">
            {loadingOrders ? (
                <OrdersSkeleton />
            ) : (
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">Your Recent Category Orders</h3>
              </div>
              <div className="space-y-4">
                {orders.length > 0 ? (
                  orders.map((order) => (
                <div key={order._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-950 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">Category Order</p>
                      <p className="text-xs text-muted-foreground">{formatDate(order.createdAt)}</p>
                      <p className="text-xs text-muted-foreground">
                          {order.orderItems?.slice(0, 2).map((item, index) => (
                            <span key={index} className="whitespace-nowrap">
                              {item.title.length > 25 ? `${item.title.substring(0, 25)}...` : item.title}<br/>
                            </span>
                          ))}
                          {order.orderItems?.length > 2 && (
                            <span className="whitespace-nowrap">
                              +{order.orderItems.length - 2} more
                            </span>
                          )}
                      </p>
                        <p className="text-xs text-muted-foreground">
                          <span className="">{order.customerName || ''}</span>
                          {order.payment && order.payment.mobile && (
                            <span className="">M-Pesa: {order.payment.mobile}</span>
                          )}
                        </p>
                    </div>
                  </div>
                  <span className="text-sm font-medium text-green-600">KES {order.totalAmount?.$numberDecimal.toLocaleString()}</span>
                </div>
                   ))
                ) : (
                  <div><p className="text-lg font-medium">No orders found. Browse categories for new opportunities.</p></div>
                )}

                <Button variant="outline" className="w-full mt-4"
                onClick={() => router.push(`/supplier/${companyId}/orders`)}>
                  View All Orders
                </Button>
              </div>
            </CardContent>
            )}
          </Card>
        </div>


      </div>
    </div>
  );
}