import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "TenderAsili | Africa's Leading eProcurement Platform for Suppliers & Buyers",
  description: "Streamline your procurement process with TenderAsili - Africa's award-winning eTendering platform. Suppliers gain access to 300K+ opportunities, while buyers achieve 60% faster processing with 22% avg. cost savings.",
  keywords: [
    "eProcurement Africa",
    "eTendering platform",
    "supplier registration",
    "tender management system",
    "public procurement Kenya",
    "source to pay solution",
    "contract management Africa",
    "supplier prequalification",
    "government tenders",
    "RFQ management"
  ],
  openGraph: {
    title: "TenderAsili | Transform Your Procurement Process",
    description: "Join Africa's largest eProcurement network with 300K+ suppliers and 100+ premium buyers. Automated tendering for better efficiency and transparency.",
    url: "https://www.tenderasili.com",
    siteName: "TenderAsili",
    images: [
      {
        url: "https://www.tenderasili.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "TenderAsili eProcurement Platform",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "TenderAsili | Africa's Leading eProcurement Platform",
    description: "Automated tendering solution connecting 300K+ suppliers with premium buyers across Africa",
    images: ["https://www.tenderasili.com/twitter-card.jpg"],
  },
  alternates: {
    canonical: "https://www.tenderasili.com",
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  themeColor: "#DC2626", // Using your red brand color
  category: "procurement technology",
  metadataBase: new URL("https://www.tenderasili.com"),
  authors: [{ name: "TenderAsili", url: "https://www.tenderasili.com" }],
  applicationName: "TenderAsili eProcurement",
  formatDetection: {
    email: true,
    address: false,
    telephone: true,
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
         <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
        {children}

          </ThemeProvider>
      </body>
    </html>
  );
}
