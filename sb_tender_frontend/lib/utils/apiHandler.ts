import { NextRequest } from 'next/server';
import { requireAnyRole } from '../middleware/roleMiddleware';
import { successResponse, errorResponse, unauthorizedResponse } from './apiResponse';

export interface ApiHandlerOptions {
  allowedRoles: string[];
  requireCompany?: boolean;
}

export interface ApiContext {
  user: {
    userId: string;
    type: 'admin' | 'buyer' | 'supplier';
    email: string;
    firstName: string;
    lastName: string;
  };
  company?: {
    companyId: string;
    companyName: string;
    companyType: string;
  };
  params: Record<string, string>;
  searchParams: URLSearchParams;
}

/**
 * Unified API handler that manages authentication and role-based access
 */
export function createApiHandler(
  handler: (context: ApiContext) => Promise<any>,
  options: ApiHandlerOptions
) {
  return async (request: NextRequest, { params }: { params: Promise<Record<string, string>> }) => {
    try {
      const resolvedParams = await params;
      const { searchParams } = new URL(request.url);
      const companyId = resolvedParams.companyId || searchParams.get('companyId');

      // Authenticate and authorize
      const authResult = await requireAnyRole(
        request, 
        options.allowedRoles, 
        options.requireCompany ? companyId : undefined
      );

      // Build context
      const context: ApiContext = {
        user: authResult,
        company: 'company' in authResult ? authResult.company : undefined,
        params: resolvedParams,
        searchParams
      };

      // Call the actual handler
      const result = await handler(context);
      return successResponse(result);

    } catch (error: any) {
      console.error('API Handler Error:', error);
      
      if (error.message.includes('token') || error.message.includes('access')) {
        return unauthorizedResponse(error.message);
      }
      
      return errorResponse(error.message || 'Internal server error', 500);
    }
  };
}

/**
 * Role-specific handler creators
 */
export const createAdminHandler = (handler: (context: ApiContext) => Promise<any>) =>
  createApiHandler(handler, { allowedRoles: ['admin'] });

export const createBuyerHandler = (handler: (context: ApiContext) => Promise<any>) =>
  createApiHandler(handler, { allowedRoles: ['buyer'], requireCompany: true });

export const createSupplierHandler = (handler: (context: ApiContext) => Promise<any>) =>
  createApiHandler(handler, { allowedRoles: ['supplier'], requireCompany: true });

export const createMultiRoleHandler = (
  handler: (context: ApiContext) => Promise<any>,
  allowedRoles: string[]
) => createApiHandler(handler, { allowedRoles });

/**
 * Data filtering based on user role
 */
export function filterDataByRole(data: any[], userType: string, userId?: string, companyId?: string) {
  switch (userType) {
    case 'admin':
      // Admin sees everything
      return data;
      
    case 'buyer':
    case 'supplier':
      // Users see only their own data
      return data.filter(item => {
        if (item.createdBy?.toString() === userId) return true;
        if (item.spCompanyId?.toString() === companyId) return true;
        if (item.companyId?.toString() === companyId) return true;
        return false;
      });
      
    default:
      return [];
  }
}

/**
 * Build query filters based on user role
 */
export function buildRoleBasedQuery(userType: string, userId?: string, companyId?: string) {
  const query: any = {};
  
  switch (userType) {
    case 'admin':
      // Admin can query everything - no additional filters
      break;
      
    case 'buyer':
    case 'supplier':
      // Users can only query their own data
      query.$or = [
        { createdBy: userId },
        { spCompanyId: companyId },
        { companyId: companyId }
      ];
      break;
  }
  
  return query;
}
