import User from '../models/sp_user';
import SpCompany from '../models/sp_company';
import SpRole from '../models/sp_role';
import SpU<PERSON><PERSON>ole from '../models/sp_user_role';


export interface UserCompanyRole {
  companyId: string;
  companyName: string;
  companyType: 'buyer' | 'supplier';
  companyRegistrationNumber: string;
  roles: Array<{
    roleId: string;
    roleName: string;
    roleContext: string;
    roleType: string;
    permissions: string[];
    description: string;
  }>;
}

export interface UserWithCompanies {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  type: 'admin' | 'buyer' | 'supplier';
  companies: UserCompanyRole[];
}

// Helper function to exclude sensitive user fields
const excludeSensitiveFields = (user: any): any => {
  if (!user) return null;
  const userObject = user.toObject ? user.toObject() : user;
  delete userObject.password;
  delete userObject.otp;
  delete userObject.__v;
  return userObject;
};



/**
 * Fetches detailed information for a user, including their companies and roles.
 * @param {string} userId - The ID of the user.
 * @returns {Promise<object|null>} - An object containing user details and a list of companies with roles, or null if user not found.
 */
/**
 * Fetches detailed information for a user, including their companies and roles.
 */
export const getUserCompanyRoles = async (userId: string): Promise<UserWithCompanies | null> => {
  try {
    // 1. Fetch basic user details (excluding sensitive fields)
    console.log('Fetching user details for user ID:', userId);
    const user = await User.findById(userId).select('-password -otp');

    if (!user) {
      console.warn(`User with ID ${userId} not found during company/role fetch.`);
      return null; // User not found
    }

    // Convert to plain object to add properties easily
    const userDetails = excludeSensitiveFields(user);

    // 2. Find all SpUserRole entries for this user
    const spUserRoles = await SpUserRole.find({ userId: userId })
      .populate('companyId', 'name type registrationNumber') // Populate company details needed for frontend
      .populate('roleId', 'name context type permissions description'); // Populate role details

      console.log('SpUserRoles:', spUserRoles);

    // 3. Structure the data grouped by company
    const companiesData = {};

    spUserRoles.forEach(spUserRole => {
      const company = spUserRole.companyId; // Populated company document
      const role = spUserRole.roleId;     // Populated role document

      if (!company || !role) {
          console.warn(`SpUserRole entry ${spUserRole._id} has missing company or role data.`);
          return; // Skip incomplete entries
      }

      const companyIdString = company._id.toString();

      if (!companiesData[companyIdString]) {
        // Initialize company entry if not exists
        companiesData[companyIdString] = {
          companyId: company._id,
          companyName: company.name,
          companyType: company.type,
          companyRegistrationNumber: company.registrationNumber, // Include relevant company info
          // Add other company fields needed by frontend (e.g., logo, address summary)
          roles: [], // Array to hold roles for this user within this company
        };
      }

      // Add the role details to the company's roles array
      companiesData[companyIdString].roles.push({
        roleId: role._id,
        roleName: role.name,
        roleContext: role.context,
        roleType: role.type,
        permissions: role.permissions,
        description: role.description,
      });
    });

    // Convert the companiesData object into an array of company objects
    const companiesArray = Object.values(companiesData);

    // 4. Check if the user is the owner (createdBy) of any companies
    // This might be a separate flag if the concept of "owner" vs just "member" is important
    // For simplicity here, we'll rely on their assigned roles within the company
    // If you need a dedicated 'isOwner' flag, you'd fetch companies where createdBy === userId
    // and add a flag to the corresponding entry in companiesArray.

    // 5. Set primary companyId if user doesn't have one but has companies
    if (!userDetails.companyId && companiesArray.length > 0) {
      // Set the first company as the primary company
      const primaryCompanyId = companiesArray[0].companyId.toString();

      // Update the user's companyId in the database
      await User.findByIdAndUpdate(userId, { companyId: primaryCompanyId });
      userDetails.companyId = primaryCompanyId;

      console.log(`Set primary companyId ${primaryCompanyId} for user ${userId}`);
    }

    // 6. Return the combined user and company/role data
    return {
      ...userDetails, // Spread user details
      companies: companiesArray, // Add the structured companies and roles
    };

  } catch (error) {
    console.error('Error in getUserCompanyRoles:', error);
    throw error; // Re-throw the error for the caller to handle
  }
};