import { NextResponse } from 'next/server';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Success response helper
 */
export function successResponse<T>(data: T, message?: string, status: number = 200) {
  return NextResponse.json({
    success: true,
    data,
    message,
  } as ApiResponse<T>, { status });
}

/**
 * Error response helper
 */
export function errorResponse(error: string, status: number = 400) {
  return NextResponse.json({
    success: false,
    error,
  } as ApiResponse, { status });
}

/**
 * Validation error response
 */
export function validationError(errors: string[]) {
  return NextResponse.json({
    success: false,
    error: 'Validation failed',
    errors,
  }, { status: 422 });
}

/**
 * Unauthorized response
 */
export function unauthorizedResponse(message: string = 'Unauthorized') {
  return NextResponse.json({
    success: false,
    error: message,
  } as ApiResponse, { status: 401 });
}

/**
 * Forbidden response
 */
export function forbiddenResponse(message: string = 'Forbidden') {
  return NextResponse.json({
    success: false,
    error: message,
  } as ApiResponse, { status: 403 });
}

/**
 * Not found response
 */
export function notFoundResponse(message: string = 'Resource not found') {
  return NextResponse.json({
    success: false,
    error: message,
  } as ApiResponse, { status: 404 });
}

/**
 * Internal server error response
 */
export function serverErrorResponse(message: string = 'Internal server error') {
  return NextResponse.json({
    success: false,
    error: message,
  } as ApiResponse, { status: 500 });
}
