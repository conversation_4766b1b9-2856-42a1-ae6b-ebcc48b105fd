import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth';
import { NextRequest } from 'next/server';

/**
 * Get session from NextAuth
 */
export async function getSession() {
  return await getServerSession(authOptions);
}

/**
 * Authenticate using NextAuth session
 */
export async function authenticateSession(request?: NextRequest) {
  const session = await getSession();
  
  if (!session || !session.user) {
    throw new Error('No session found');
  }

  return {
    userId: session.user.id,
    type: session.user.type,
    email: session.user.email,
    firstName: session.user.firstName,
    lastName: session.user.lastName,
  };
}

/**
 * Require specific role using NextAuth session
 */
export function requireSessionRole(allowedRoles: string[]) {
  return async (request?: NextRequest) => {
    const user = await authenticateSession(request);
    
    if (!allowedRoles.includes(user.type)) {
      throw new Error('Insufficient permissions');
    }
    
    return user;
  };
}

/**
 * Admin only middleware using session
 */
export const requireSessionAdmin = requireSessionRole(['admin']);

/**
 * Buyer only middleware using session
 */
export const requireSessionBuyer = requireSessionRole(['buyer']);

/**
 * Supplier only middleware using session
 */
export const requireSessionSupplier = requireSessionRole(['supplier']);
