import { NextRequest } from 'next/server';
import { authenticateToken } from './auth';
import { connectToDatabase } from '../mongodb';
import SpCompany from '../models/sp_company';

export interface AuthenticatedRequest extends NextRequest {
  user: {
    userId: string;
    type: 'admin' | 'buyer' | 'supplier';
    email: string;
    firstName: string;
    lastName: string;
  };
  company?: {
    companyId: string;
    companyName: string;
    companyType: string;
  };
}

/**
 * Admin middleware - full access to all data
 */
export async function requireAdmin(request: NextRequest) {
  const user = await authenticateToken(request);
  
  if (user.type !== 'admin') {
    throw new Error('Admin access required');
  }
  
  return user;
}

/**
 * Buyer middleware - access to own company data only
 */
export async function requireBuyer(request: NextRequest, companyId?: string) {
  const user = await authenticateToken(request);
  
  if (user.type !== 'buyer') {
    throw new Error('Buyer access required');
  }
  
  // If companyId is provided, verify ownership
  if (companyId) {
    await connectToDatabase();
    const company = await SpCompany.findOne({
      _id: companyId,
      createdBy: user.userId,
      type: 'buyer'
    });
    
    if (!company) {
      throw new Error('Company not found or access denied');
    }
    
    return {
      ...user,
      company: {
        companyId: company._id.toString(),
        companyName: company.name,
        companyType: company.type
      }
    };
  }
  
  return user;
}

/**
 * Supplier middleware - access to own company data only
 */
export async function requireSupplier(request: NextRequest, companyId?: string) {
  const user = await authenticateToken(request);
  
  if (user.type !== 'supplier') {
    throw new Error('Supplier access required');
  }
  
  // If companyId is provided, verify ownership
  if (companyId) {
    await connectToDatabase();
    const company = await SpCompany.findOne({
      _id: companyId,
      createdBy: user.userId,
      type: 'supplier'
    });
    
    if (!company) {
      throw new Error('Company not found or access denied');
    }
    
    return {
      ...user,
      company: {
        companyId: company._id.toString(),
        companyName: company.name,
        companyType: company.type
      }
    };
  }
  
  return user;
}

/**
 * Flexible role middleware - accepts multiple roles
 */
export async function requireAnyRole(request: NextRequest, allowedRoles: string[], companyId?: string) {
  const user = await authenticateToken(request);
  
  if (!allowedRoles.includes(user.type)) {
    throw new Error(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
  }
  
  // For non-admin users, verify company ownership if companyId provided
  if (user.type !== 'admin' && companyId) {
    await connectToDatabase();
    const company = await SpCompany.findOne({
      _id: companyId,
      createdBy: user.userId
    });
    
    if (!company) {
      throw new Error('Company not found or access denied');
    }
    
    return {
      ...user,
      company: {
        companyId: company._id.toString(),
        companyName: company.name,
        companyType: company.type
      }
    };
  }
  
  return user;
}
