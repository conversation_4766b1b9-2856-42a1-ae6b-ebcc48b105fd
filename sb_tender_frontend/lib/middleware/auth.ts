import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '../mongodb';
import User from '../models/sp_user';

export interface AuthenticatedUser {
  userId: string;
  type: 'admin' | 'buyer' | 'supplier';
  email: string;
  firstName: string;
  lastName: string;
}

/**
 * Authenticate JWT token from request headers
 */
export async function authenticateToken(request: NextRequest): Promise<AuthenticatedUser> {
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '');

  if (!token) {
    throw new Error('No token provided');
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    // Connect to database and get user details
    await connectToDatabase();
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      throw new Error('User not found');
    }

    if (user.status !== 'active') {
      throw new Error('User account is not active');
    }

    return {
      userId: user._id.toString(),
      type: user.type,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
    };
  } catch (error) {
    throw new Error('Invalid token');
  }
}

/**
 * Middleware to check if user has required role
 */
export function requireRole(allowedRoles: string[]) {
  return async (request: NextRequest): Promise<AuthenticatedUser> => {
    const user = await authenticateToken(request);
    
    if (!allowedRoles.includes(user.type)) {
      throw new Error('Insufficient permissions');
    }
    
    return user;
  };
}

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole(['admin']);

/**
 * Buyer only middleware
 */
export const requireBuyer = requireRole(['buyer']);

/**
 * Supplier only middleware
 */
export const requireSupplier = requireRole(['supplier']);

/**
 * Buyer or Admin middleware
 */
export const requireBuyerOrAdmin = requireRole(['buyer', 'admin']);

/**
 * Supplier or Admin middleware
 */
export const requireSupplierOrAdmin = requireRole(['supplier', 'admin']);
