import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@next-auth/mongodb-adapter';
import { MongoClient } from 'mongodb';
import bcrypt from 'bcryptjs';
import { connectToDatabase } from './mongodb';
import User from './models/sp_user';

const client = new MongoClient(process.env.MONGODB_URI!);
const clientPromise = client.connect();

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          await connectToDatabase();
          
          const user = await User.findOne({ email: credentials.email });
          if (!user) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
          if (!isPasswordValid) {
            return null;
          }

          if (user.status !== 'active') {
            return null;
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            firstName: user.firstName,
            lastName: user.lastName,
            type: user.type,
            image: null,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.type = user.type;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
      }
      
      // Handle Google OAuth
      if (account?.provider === 'google' && user) {
        try {
          await connectToDatabase();
          
          // Check if user exists in our database
          let dbUser = await User.findOne({ email: user.email });
          
          if (!dbUser) {
            // Create new user from Google OAuth
            const [firstName, ...lastNameParts] = (user.name || '').split(' ');
            dbUser = new User({
              firstName: firstName || '',
              lastName: lastNameParts.join(' ') || '',
              email: user.email,
              emailVerified: true,
              password: await bcrypt.hash(Math.random().toString(36), 10), // Random password
              phone: '', // Will need to be updated by user
              type: 'supplier', // Default type, can be changed
              status: 'active',
            });
            await dbUser.save();
          }
          
          token.type = dbUser.type;
          token.firstName = dbUser.firstName;
          token.lastName = dbUser.lastName;
          token.id = dbUser._id.toString();
        } catch (error) {
          console.error('Google OAuth user creation error:', error);
        }
      }
      
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.type = token.type as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  events: {
    async signIn({ user, account, profile }) {
      // Update last login
      if (user.id) {
        try {
          await connectToDatabase();
          await User.findByIdAndUpdate(user.id, { lastLogin: new Date() });
        } catch (error) {
          console.error('Error updating last login:', error);
        }
      }
    },
  },
};
