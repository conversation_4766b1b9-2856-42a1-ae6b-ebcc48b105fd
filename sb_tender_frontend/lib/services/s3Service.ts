import AWS from 'aws-sdk';

// Configure AWS
const s3 = new AWS.S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_KEY,
  secretAccessKey: process.env.AWS_SECRET,
});

export const uploadToS3 = async (
  buffer: Buffer,
  folder: string,
  fileName: string,
  companyId?: string
): Promise<string> => {
  console.log("S3 ENV CONFIG:", {
    region: process.env.AWS_REGION,
    bucket: process.env.AWS_BUCKET,
    endpoint: process.env.AWS_ENDPOINT, // should be undefined or null
  });
  const key = companyId ? `${folder}/${companyId}/${fileName}` : `${folder}/${fileName}`;

  const params = {
    Bucket: process.env.AWS_BUCKET!,
    Key: key,
    Body: buffer,
    ACL: 'private',
  };

  try {
    await s3.upload(params).promise();
    return `https://${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw new Error('Upload to S3 failed');
  }
};

export const deleteFromS3 = async (fileUrl: string): Promise<void> => {
  const s3Key = fileUrl.split('.com/')[1];
  const params = {
    Bucket: process.env.AWS_BUCKET,
    Key: s3Key,
  };

  try {
    await s3.deleteObject(params).promise();
  } catch (error) {
    console.error('Error deleting from S3:', error);
    throw new Error('File deletion from S3 failed');
  }
};





export const getObjectFromS3 = async (s3Key: string): Promise<AWS.S3.GetObjectOutput> => {
  const params = {
    Bucket: process.env.AWS_BUCKET!,
    Key: s3Key,
  };

  try {
    const s3Response = await s3.getObject(params).promise();
    return s3Response;
  } catch (error) {
    console.error('S3 Download Error:', error);
    throw new Error('Failed to fetch file from S3');
  }

  
};
