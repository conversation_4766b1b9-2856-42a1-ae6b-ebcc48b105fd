import SpNotification from '../models/sp_notification.js';
import nodemailer from 'nodemailer';import { Queue } from 'bullmq';
import Redis from 'ioredis';


class NotificationService {
  constructor() {
    this.emailTransporter = this.initializeEmailTransporter();
    // Create an ioredis connection instance
    this.redisConnection = new Redis(process.env.REDIS_URL, { maxRetriesPerRequest: null }); // Recommended for BullMQ

    this.notificationQueue = this.initializeQueue();
    // this.redisClient is no longer directly needed here as BullMQ uses ioredis internally
  }

  // Initialize email transporter
  initializeEmailTransporter() {
    return nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587, //port 587 => secure: false, // Use STARTTLS (explicit TLS)
      secure: process.env.SMTP_SECURE === 'true', //port: 465 =>  secure: true, // Use implicit TLS (SMTPS)
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      },
      tls: {
        // Do not fail on invalid certs
        rejectUnauthorized: false,
      },
    });
  }

// Initialize BullMQ Queue (for adding jobs)
initializeQueue() {
  const queue = new Queue('notification processing', { connection: this.redisConnection });
  // Queue event handlers (for monitoring jobs added by this service instance)
  queue.on('completed', this.onJobCompleted.bind(this));
  queue.on('failed', this.onJobFailed.bind(this));
  
  // NOTE: queue.process() is NOT called here
  // Processing logic lives in a separate Worker instance.
  
  return queue;
}

  // Main method to send notifications
  async sendNotification(notificationData, options = {}) {
    try {
      // Validate notification data
      const validation = this.validateNotificationData(notificationData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Create notification record
      const notification = new SpNotification(notificationData);
      await notification.save();
      // Decide processing method
      if (options.useQueue || notificationData.processing?.isQueued) {
        return await this.queueNotification(notification, options);
      } else {
        return await this.sendImmediately(notification);
      }
    } catch (error) {
      console.error('Notification service error:', error);
      throw error;
    }
  }

  // Send notification immediately (synchronous)
  async sendImmediately(notification) {
    try {
      let result;
      
      switch (notification.channel) {
        case 'email':
          result = await this.sendEmail(notification);
          break;
        case 'sms':
          result = await this.sendSMS(notification);
          break;
        case 'app':
          result = await this.sendAppNotification(notification);
          break;
        case 'push':
          result = await this.sendPushNotification(notification);
          break;
        default:
          throw new Error(`Unsupported notification channel: ${notification.channel}`);
      }

      await notification.markAsDelivered();
      return { success: true, notification, result };
    } catch (error) {
      await notification.markAsFailed(error.message);
      throw error;
    }
  }

  // Queue notification for background processing
  async queueNotification(notification, options = {}) {
    try {
      const jobOptions = {
        delay: options.delay || notification.processing?.delay || 0,
        attempts: notification.delivery?.maxAttempts || 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        },
        removeOnComplete: { count: 100 }, // BullMQ expects an object for removeOnComplete/Fail
        removeOnFail: { count: 50 }
      };

      // In BullMQ, the job name is the first argument to .add() if you're using it
      // as a named job, otherwise the data is the first argument.
      // Keeping 'send-notification' as the job name as in your original code.
      const job = await this.notificationQueue.add('send-notification', {
        notificationId: notification._id.toString()
      }, jobOptions);

      // Update notification with queue info
      notification.processing.isQueued = true;
      notification.processing.jobId = job.id.toString();
      await notification.save();

      return { success: true, notification, jobId: job.id };
    } catch (error) {
      await notification.markAsFailed(error.message);
      throw error;
    }
  }

  // Process queued notification
  async processQueuedNotification(job) {
    const { notificationId } = job.data;
    
    try {
      const notification = await SpNotification.findById(notificationId);
      if (!notification) {
        throw new Error('Notification not found');
      }

      if (notification.isExpired) {
        throw new Error('Notification expired');
      }

      notification.processing.processedAt = new Date();
      await notification.save();

      return await this.sendImmediately(notification);
    } catch (error) {
      console.error('Queue processing error:', error);
      throw error;
    }
  }

    // Queue event handlers
    async onJobCompleted(job, result) {
      console.log(`Notification job ${job.id} completed successfully`);
    }
  
    async onJobFailed(job, error) {
      console.error(`Notification job ${job.id} failed:`, error);
      
      const { notificationId } = job.data;
      const notification = await SpNotification.findById(notificationId);
      
      if (notification) {
        await notification.markAsFailed(error.message);
      }
    }



    

  // Send email notification
  async sendEmail(notification) {
    const mailOptions = {
      from: notification.sender?.email || process.env.DEFAULT_FROM_EMAIL,
      to: notification.recipient.email,
      subject: notification.title,
      text: notification.message,
      html: notification.richContent?.html || notification.message,
      attachments: notification.attachments?.map(att => ({
        filename: att.filename,
        path: att.path,
        contentType: att.contentType
      }))
    };

    const result = await this.emailTransporter.sendMail(mailOptions);
    console.log('Email sent by service:', result);
    
    notification.delivery.providerResponse = {
      messageId: result.messageId,
      response: result.response
    };
    
    return result;
  }

  // Send SMS notification
  async sendSMS(notification) {
    try {
      const apiUrl = process.env.SOZURI_API_URL;
      const bearerToken = process.env.SOZURI_API_KEY; // Get your API key from .env
      const fromNumber = process.env.SMS_FROM_NUMBER; // Get your 'from' number from .env
      const project = process.env.SOZURI_PROJECT; 
      const type = process.env.SOZURI_TYPE; 

      if (!bearerToken || !fromNumber) {
        console.error('Missing SOZURI_API_KEY or SMS_FROM_NUMBER in environment variables.');
        throw new Error('Missing SOZURI_API_KEY or SMS_FROM_NUMBER in environment variables.');
      }

      const payload = {
        project: project,
        from: fromNumber,
        to: notification.recipient.phone,
        campaign: "Promo Nai", 
        channel: "sms",
        message: notification.title + ' - ' + notification.message,
        type: type
      };

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (response.ok) {
        // SMS sent successfully
        notification.delivery.providerResponse = result; // Store the full response
        console.log('SMS sent successfully:', result);
        return { success: true, providerResponse: result };
      } else {
        // SMS sending failed
        notification.delivery.providerResponse = result; // Store the error response
        console.error('Failed to send SMS:', result);
        // Depending on the API's error structure, you might want to throw a more specific error
        throw new Error(`SMS delivery failed: ${result.message || JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('Error in sendSMS function:', error);
      notification.delivery.providerResponse = { error: error.message };
      return { success: false, error: error.message };
    }
  }

  // Send app notification
  async sendAppNotification(notification) {
    // Store in-app notification (already stored in database)
    // Optionally emit via WebSocket or Server-Sent Events
    
    // Example WebSocket emission (if you have socket.io)
    /*
    if (global.io && notification.userId) {
      global.io.to(notification.userId.toString()).emit('notification', {
        id: notification._id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        createdAt: notification.createdAt
      });
    }
    */

    notification.delivery.providerResponse = { 
      delivered: true,
      channel: 'app'
    };
    
    return { success: true, channel: 'app' };
  }

  // Send push notification
  async sendPushNotification(notification) {
    // Implement push notification logic here (Firebase, APNs, etc.)
    // This is a placeholder
    
    const pushData = {
      token: notification.recipient.deviceToken,
      title: notification.title,
      body: notification.message,
      data: notification.metadata?.customData
    };

    console.log('Push notification would be sent:', pushData);
    
    notification.delivery.providerResponse = { 
      status: 'sent',
      platform: 'firebase'
    };
    
    return { success: true, platform: 'firebase' };
  }

  // Bulk send notifications
  async sendBulkNotifications(notifications, options = {}) {
    const results = [];
    
    for (const notificationData of notifications) {
      try {
        const result = await this.sendNotification(notificationData, options);
        results.push({ success: true, ...result });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message,
          notificationData 
        });
      }
    }
    
    return results;
  }

  // Retry failed notifications
  async retryFailedNotifications(options = {}) {
    const failedNotifications = await SpNotification.find({
      status: 'failed',
      'delivery.attempts': { $lt: options.maxAttempts || 3 },
      'settings.retryOnFailure': true
    }).limit(options.limit || 100);

    const results = [];
    for (const notification of failedNotifications) {
      try {
        const result = await this.sendImmediately(notification);
        results.push({ success: true, notificationId: notification._id, result });
      } catch (error) {
        results.push({ 
          success: false, 
          notificationId: notification._id, 
          error: error.message 
        });
      }
    }
    
    return results;
  }

  // Get notification statistics
  async getStats(filters = {}) {
    const pipeline = [];
    
    if (filters.companyId) {
      pipeline.push({ $match: { companyId: filters.companyId } });
    }
    
    if (filters.userId) {
      pipeline.push({ $match: { userId: filters.userId } });
    }
    
    if (filters.dateFrom || filters.dateTo) {
      const dateMatch = {};
      if (filters.dateFrom) dateMatch.$gte = new Date(filters.dateFrom);
      if (filters.dateTo) dateMatch.$lte = new Date(filters.dateTo);
      pipeline.push({ $match: { createdAt: dateMatch } });
    }

    pipeline.push({
      $group: {
        _id: { status: '$status', channel: '$channel' },
        count: { $sum: 1 }
      }
    });

    const stats = await SpNotification.aggregate(pipeline);
    return this.formatStats(stats);
  }

  // Validate notification data
  validateNotificationData(data) {
    const errors = [];
    
    if (!data.title) errors.push('Title is required');
    if (!data.message) errors.push('Message is required');
    if (!data.channel) errors.push('Channel is required');
    if (!['email', 'sms', 'app', 'push'].includes(data.channel)) {
      errors.push('Invalid channel');
    }

    // Channel-specific validation
    if (data.channel === 'email' && !data.recipient?.email) {
      errors.push('Email address is required for email notifications');
    }
    if (data.channel === 'sms' && !data.recipient?.phone) {
      errors.push('Phone number is required for SMS notifications');
    }
    if (data.channel === 'push' && !data.recipient?.deviceToken) {
      errors.push('Device token is required for push notifications');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Format statistics
  formatStats(rawStats) {
    const formatted = {
      total: 0,
      byStatus: {},
      byChannel: {},
      combined: {}
    };

    rawStats.forEach(stat => {
      const { status, channel } = stat._id;
      const count = stat.count;
      
      formatted.total += count;
      formatted.byStatus[status] = (formatted.byStatus[status] || 0) + count;
      formatted.byChannel[channel] = (formatted.byChannel[channel] || 0) + count;
      formatted.combined[`${channel}_${status}`] = count;
    });

    return formatted;
  }

}

export default new NotificationService();
