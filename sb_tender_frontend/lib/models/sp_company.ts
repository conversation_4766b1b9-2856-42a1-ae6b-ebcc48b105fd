import mongoose, { Document, Schema } from 'mongoose';

export interface IDocument {
  type: string;
  name: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  filePath: string;
  url: string;
  expires?: Date;
  uploadedAt: Date;
}

export interface ICompany extends Document {
  name: string;
  registrationNumber: string;
  type: 'buyer' | 'supplier';
  address: string;
  logoUrl?: string;
  contactPerson: string;
  email: string;
  phone: string;
  categories: mongoose.Types.ObjectId[];
  status: 'active' | 'inactive' | 'suspended';
  verified: boolean;
  documents: IDocument[];
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const companySchema = new Schema<ICompany>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  registrationNumber: {
    type: String,
    required: true,
  },
  type: {
    enum: ['buyer', 'supplier'],
    type: String,
    required: true
  },
  address: {
    type: String,
    required: true
  },
  logoUrl: {
    type: String,
  },
  contactPerson: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true
  },
  categories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCategory'
  }],
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  verified: {
    type: Boolean,
    default: false
  },
  documents: [{
    type: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    fileName: {
      type: String,
      required: true
    },
    fileType: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    filePath: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    expires: {
      type: Date,
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SpCompany = mongoose.models.SpCompany || mongoose.model<ICompany>('SpCompany', companySchema);
export default SpCompany;