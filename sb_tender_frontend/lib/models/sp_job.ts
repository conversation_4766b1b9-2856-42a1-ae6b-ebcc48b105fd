import mongoose from 'mongoose';
    
    const jobSchema = new mongoose.Schema({
    
      title: { 
        type: String, 
        required: true 
      },
      description: {
        type: String
      },
      contract: {
        type: String,
      },
      starts: {
        type: Date,
        required: true
      },
      ends: {
        type: Date,
        required: true
      },
      location: {
        type: String,
        required: true
      },
      type: {
        type: String,
        enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
      },    
      status: {
        type: String,
        enum: ['draft', 'open', 'closed', 'completed'],
        default: 'draft',
    },
      spCompanyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpCompany',
        required: true
      },
      categoryPrice: {
        type: Number,
        required: true
      },
      passMark: {
        type: Number,
        default: 0
      },
      createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }, {
      timestamps: true
    });

    
    const SpJob = mongoose.models.SpJob || mongoose.model('SpJob', jobSchema);
    export default SpJob;