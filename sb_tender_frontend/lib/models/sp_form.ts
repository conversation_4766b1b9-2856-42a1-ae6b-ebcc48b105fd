import mongoose from 'mongoose';

const formSchema = new mongoose.Schema({
  tender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpJob',
    required: true
  },
  name: { 
    type: String, 
    required: true 
  },
  fields: [{
    name: {
      type: String,
      required: true
    },
    group: {
      type: String,
      enum: ['company', 'declaration', 'contact', 'bank', 'statutory', 'directors', 'shareholders', 'risk', 'litigation', 'integrity', 'esg', 'env', 'social',
        'governance', 'sla', 'hr', 'experience', 'reference', 'submission', 'financial', 'technical', 
         'system', 'compliance', 'attachments', 'outcome', 'other'],
      required: true
    },
    isParent: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      enum: ['text', 'textarea', 'number', 'date', 'file', 'checkbox', 'radio', 'select'],
      required: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: String,
      default: ''
    },
    optionsLabel: {
      type: String,
      default: ''
    },
    optionsValue: {
      type: String,
      default: ''
    },
    options: {
      type: String,
      default: ''
    },
    isVisible: {
      type: Boolean,
      default: true
    },
    isEditable: {
      type: Boolean,
      default: true
    },
    isVisibleToSupplier: {
      type: Boolean,
      default: true
    },
    isEditableToSupplier: {
      type: Boolean,
      default: true
    },
    isVisibleToBuyer: {
      type: Boolean,
      default: true
    },
    isEditableToBuyer: {
      type: Boolean,
      default: true
    },
    isVisibleToAdmin: {
      type: Boolean,
      default: true
    },
    isEditableToAdmin: {
      type: Boolean,
      default: true
    },
    isVisibleToEvaluator: {
      type: Boolean,
      default: true
    },
    isEditableToEvaluator: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      required: true
    },
    options: [{
      label: String,
      value: String
    }],
    isRequired: {
      type: Boolean,
      default: false
    },
    isScoreable: {
      type: Boolean,
      default: false
    },
    maxScore: {
      type: Number,
      default: 0
    },
    validations: {
      min: Number,
      max: Number,
      pattern: String,
      fileTypes: [String],
      maxFileSize: Number
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SpForm = mongoose.models.SpForm || mongoose.model('SpForm', formSchema);
export default SpForm;