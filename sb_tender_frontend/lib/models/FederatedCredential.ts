import mongoose from 'mongoose';


  const FederatedCredentialSchema = new mongoose. Schema({
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    provider: {
      type: String,
      required: true
    },
    subject: {
      type: String,
      required: true
    },
    // Storing the profile data can be useful for future reference
    profile: {
      type: Object,
      required: false
    }
  }, {
    timestamps: true
  });

  
  const FederatedCredential = mongoose.models.FederatedCredential || mongoose.model('FederatedCredential', FederatedCredentialSchema);
  
  export default FederatedCredential;
  