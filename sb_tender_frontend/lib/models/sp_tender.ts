import mongoose from 'mongoose';

const tenderSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['supplier_registration', 'prequalification', 'rfq', 'tender'],
    required: true
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCategory',
    required: true
  },
  buyerCompany: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpBuyerCompany',
    required: true
  },
  closeDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'closed', 'cancelled'],
    default: 'draft'
  },
  maxSystemScore: {
    type: Number,
    default: 0
  },
  needsCompliance: {
    type: Boolean,
    default: false
  },
  complianceMaxScore: {
    type: Number,
    default: 0
  },
  documents: [{
    type: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SpTender = mongoose.models.SpTender || mongoose.model('SpTender', tenderSchema);
export default SpTender;