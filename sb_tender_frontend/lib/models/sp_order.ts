import mongoose from 'mongoose';

const orderSchema = new mongoose.Schema({
    spCompanyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpCompany',
        required: true
    },
    status: {
        type: String,
        enum: [ 'checkout', 'pending', 'paid', 'cancelled', 'suspend'],
        default: 'checkout'
    },
    totalAmount: {
        type: mongoose.Schema.Types.Decimal128,
        default: 0
    },notes: {
        type: String,
        default: ''
    },
    orderItems: [{
        title: { type: String, required: true, trim: true },     
        price: {
            type: mongoose.Schema.Types.Decimal128,
            required: true
        },
        starts: {
            type: Date,
            required: true
        },
        ends: {
            type: Date,
            required: true
        },
        location: {
            type: String,
            required: true
        },
        type: {
            type: String,
            enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
            required: true
        },
        supplierCompanyId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpCompany',
            required: true
        },
        buyerCompanyId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpCompany',
            required: true
        },
        spJobCategoryId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SpJobCategory',
            required: true
        },

    }],
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

const SpOrder = mongoose.models.SpOrder || mongoose.model('SpOrder', orderSchema);
export default SpOrder;