const mongoose = require('mongoose');

const cartSchema = new mongoose.Schema({
  spCompanyId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'SpCompany',
          required: true
      },
  items: [{
    spJobCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SpJobCategory'
    },
    spJobId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SpJob'
    },
    price: {
      type: Number,
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['active', 'checkout', 'paid', 'cancelled'],
    default: 'active'
  },
  totalAmount: {
    type: Number,
    default: 0
  },
  paymentDetails: {
    transactionId: String,
    paymentMethod: String,
    paidAt: Date,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SpCart = mongoose.models.SpCart || mongoose.model('SpCart', cartSchema);
export default SpCart;