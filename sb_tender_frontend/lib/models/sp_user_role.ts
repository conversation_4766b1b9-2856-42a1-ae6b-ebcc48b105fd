import mongoose from 'mongoose';

const userRoleSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'SpCompany', required: true },
  roleId: { type: mongoose.Schema.Types.ObjectId, ref: 'SpRole', required: true },
}, {
  timestamps: true
});

// IMPORTANT: Use 'UserRole' model name to match Express backend exactly
const SpUserRole = mongoose.models.SpUserRole || mongoose.model('SpUserRole', userRoleSchema);
//const SpUserRole = mongoose.models.SpUserRole || mongoose.model('UserRole', userRoleSchema);

export default SpUserRole;
