import mongoose from 'mongoose';

const userRoleSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'SpCompany', required: true },
  roleId: { type: mongoose.Schema.Types.ObjectId, ref: 'SpRole', required: true },
}, { timestamps: true });

const SpUserRole = mongoose.models.SpUserRole || mongoose.model('SpUserRole', userRoleSchema);
export default SpUserRole;
