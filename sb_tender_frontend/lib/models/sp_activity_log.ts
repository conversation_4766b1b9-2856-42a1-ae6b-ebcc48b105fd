import mongoose from 'mongoose';

const activityLogSchema = new mongoose.Schema({
  companyId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'SpCompany', 
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  action: { 
    type: String, 
    required: true,
    enum: ['create', 'update', 'delete', 'view', 'login', 'logout', 'export', 'approve', 'reject']
  },
  resource: { 
    type: String, 
    required: true // e.g., 'application', 'user', 'company', 'dashboard'
  },
  resourceId: { 
    type: String // ID of the specific resource affected
  },
  description: { 
    type: String, 
    required: true 
  },
  metadata: {
    ip: String,
    userAgent: String,
    oldValue: mongoose.Schema.Types.Mixed, // For updates
    newValue: mongoose.Schema.Types.Mixed, // For updates
    additionalInfo: mongoose.Schema.Types.Mixed
  },
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  }
});

// Compound index for efficient querying
activityLogSchema.index({ companyId: 1, createdAt: -1 });
activityLogSchema.index({ userId: 1, createdAt: -1 });


const SpActivityLog = mongoose.models.SpActivityLog || mongoose.model('SpActivityLog', activityLogSchema);

 export default SpActivityLog;
