import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  firstName: string;
  lastName: string;
  email: string;
  emailVerified: boolean;
  password: string;
  phone: string;
  phoneVerified: boolean;
  otp?: string;
  otpVerified: boolean;
  mustUpdatePassword: boolean;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  type: 'admin' | 'buyer' | 'supplier';
  status: 'active' | 'inactive' | 'suspended';
  lastLogin?: Date;
  createdBy?: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  notificationPreferences: {
    tenderAlerts: Array<{
      location: string;
      category: string;
    }>;
    emailAlerts: {
      enabled: boolean;
      categories: {
        system: boolean;
        orders: boolean;
        marketing: boolean;
        reminders: boolean;
        tender: boolean;
      };
    };
    smsAlerts: {
      enabled: boolean;
      categories: {
        system: boolean;
        orders: boolean;
        marketing: boolean;
        reminders: boolean;
        tender: boolean;
      };
    };
    appAlerts: {
      enabled: boolean;
      categories: {
        system: boolean;
        orders: boolean;
        marketing: boolean;
        reminders: boolean;
        tender: boolean;
      };
    };
    pushAlerts: {
      enabled: boolean;
      categories: {
        system: boolean;
        orders: boolean;
        marketing: boolean;
        reminders: boolean;
        tender: boolean;
      };
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const userSchema = new Schema<IUser>({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  password: {
    type: String,
    required: true
  },
  phone: {
    type: String,
    required: true
  },
  phoneVerified: {
    type: Boolean,
    default: false
  },
  otp: {
    type: String,
  },
  otpVerified: {
    type: Boolean,
    default: false
  },
  mustUpdatePassword: {
    type: Boolean,
    default: false
  },
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  type: {
    type: String,
    enum: ['admin', 'buyer', 'supplier'],
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  lastLogin: {
    type: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
 notificationPreferences: {
    tenderAlerts: [{
      location: { type: String, required: true },
      category: { type: String, required: true },
      _id: false
    }],
    emailAlerts: {
      enabled: { type: Boolean, default: true },
      categories: {
        system: { type: Boolean, default: true },
        orders: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false },
        reminders: { type: Boolean, default: true },
        tender: { type: Boolean, default: true },
        _id: false
      },
      _id: false
    },
    smsAlerts: {
      enabled: { type: Boolean, default: true },
      categories: {
        system: { type: Boolean, default: true },
        orders: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false },
        reminders: { type: Boolean, default: true },
        tender: { type: Boolean, default: true },
        _id: false
      },
      _id: false
    },
    appAlerts: {
      enabled: { type: Boolean, default: true },
      categories: {
        system: { type: Boolean, default: true },
        orders: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false },
        reminders: { type: Boolean, default: true },
        tender: { type: Boolean, default: true }, 
        _id: false
      },
      _id: false
    },
    pushAlerts: {
      enabled: { type: Boolean, default: true },
      categories: {
        system: { type: Boolean, default: true },
        orders: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false },
        reminders: { type: Boolean, default: true },
        tender: { type: Boolean, default: true },
        _id: false
      },
      _id: false
    },
  },
}, 
{
  timestamps: true
});

const User = mongoose.models.User || mongoose.model<IUser>('User', userSchema);
export default User;

