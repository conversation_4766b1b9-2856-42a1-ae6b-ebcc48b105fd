import mongoose from 'mongoose';


const applicationFieldSchema = new mongoose.Schema({
  name: { type: String, required: true }, // field name from SpJobCategory
  value: mongoose.Schema.Types.Mixed,     // can be string, number, date, file path, etc.
  score: Number,                          // optional: if this field is scoreable
  comment: String,                         // optional comment
  reviewerComment: String,                // optional reviewer comment
  documents: [{
    fileName: {
      type: String,
      required: true
    },
    fileType: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    filePath: {
      type: String,
      required: true
    },
    expires: {
      type: Date,
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  group: {
    type: String,
    enum: ['company', 'declaration', 'statutory', 'shareholders', 'integrity', 'esg',
      'sla', 'hr', 'experience', 'reference', 'submission', 'financial', 'technical',
      'system', 'compliance', 'attachments', 'outcome', 'other'],
    required: true
  },
  subGroup: {
    type: String,
    enum: ['info', 'contact', 'bank', 'declaration', 'statutory', 'director1', 'director2', 'director3', 'shareholder1', 'shareeholder2', 'shareholder3', 'risk', 'litigation', 'env', 'social',
      'governance', 'sla', 'hr', 'experience', 'reference1', 'reference2', 'reference3', 'submission', 'financial', 'technical',
      'system', 'compliance', 'attachments', 'outcome', 'other'],
  },
  isParent: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
  },
  order: {
    type: Number,
    required: true
  },
  placeholder: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  helpText: {
    type: String,
    required: true
  },
  tooltip: {
    type: String,
    required: true
  },
  isVisible: {
    type: Boolean,
    default: true
  },
  isEditable: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    enum: ['text', 'textarea', 'number', 'date', 'file', 'checkbox', 'radio', 'select', 'section'],
    required: true
  },
  label: {
    type: String,
    required: true
  },
  options: [{
    label: String,
    value: String
  }],
  optionScore: [{
    label: String,
    value: String,
    score: Number
  }],
  isRequired: {
    type: Boolean,
    default: false
  },
  isScoreable: {
    type: Boolean,
    default: false
  },
  maxScore: {
    type: Number,
    default: 0
  },
  complianceScore: {
    type: Number,
    default: 0
  },
  validations: {
    min: Number,
    max: Number,
    pattern: String,
    fileTypes: [String],
    maxFileSize: Number
  }
}, { _id: false });

const fileDocumentSchema = {
  type: {
    type: String,
    description: "The type/category of the document"
  },
  name: {
    type: String,
    description: "The display name of the document"
  },
  fileName: {
    type: String,
    description: "The original name of the uploaded file"
  },
  fileType: {
    type: String,
    description: "MIME type of the file"
  },
  fileSize: {
    type: Number,
    description: "Size of the file in bytes"
  },
  filePath: {
    type: String,
    description: "Path where the file is stored"
  },
  uploadedAt: {
    type: Date,
    description: "Timestamp when the file was uploaded"
  }
};

const applicationSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  ref: {
    type: String,
    required: true
  },
  description: { type: String, required: true },
  spJobId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpJob',
    required: true
  },
  spJobCategoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpJobCategory',
    required: true
  },
  spOrder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpOrder',
    required: true
  },
  spBuyerCompanyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCompany',
    required: true
  },
  spSupplierCompanyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SpCompany',
    required: true
  },
  starts: {
    type: Date,
    required: true
  },
  ends: {
    type: Date,
    required: true
  },
  location: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
    required: true
  },
  passMark: {
    type: Number,
    default: 0
  },
  technicalResponse: fileDocumentSchema,
  responseLetter: fileDocumentSchema,
  ddLetter:  fileDocumentSchema,
  paymentReceipt: fileDocumentSchema,
  contractDocument: fileDocumentSchema,
  fields: [applicationFieldSchema], // responses to each dynamic field
  status: {
    type: String,
    enum: ['draft', 'submitted', 'under_review', 'approved', 'rejected'],
    default: 'draft'
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  reviewerNotes: String,
  reviewer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: {
    type: Date
  },
  reviewStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  systemScore: {
    type: Number,
    default: 0
  },
  complianceScore: {
    type: Number,
    default: 0
  },
  totalScore: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Custom validation to ensure unique 'name' in 'fields'
applicationSchema.path('fields').validate(function (fields) {
  const names = fields.map(field => field.name);
  return names.length === new Set(names).size; // Check for duplicates
}, 'Field names must be unique.');



const SpApplication = mongoose.models.SpApplication || mongoose.model('SpApplication', applicationSchema);
export default SpApplication;
