import mongoose from 'mongoose';

const jobCategorySchema = new mongoose.Schema({
    title: { type: String, required: true, trim: true },
    ref: {
        type:String,
        required: true
      },
    description: { type: String, required: true },
    status: {
        type: String,
        enum: ['draft', 'open', 'closed', 'completed'],
        default: 'draft',
    },
    price: {
        type: Number,
        required: true
    }, starts: {
        type: Date,
        required: true
    },
    ends: {
        type: Date,
        required: true
    },
    location: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['supplier_registration', 'supplier_prequalification', 'rfq', 'tender'],
        required: true
    },
    spJobId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpJob',
        required: true
    },
    spCompanyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpCompany',
        required: true
    },
    spBuyerCategoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpBuyerCategory',
        required: true
    },
    spBuyerCategoryTemplateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SpBuyerCategoryTemplate',
        required: true
    },
    passMark: {
        type: Number,
        default: 0
    },
    fields: [{
        name: {
            type: String,
            required: true
        },
        group: {
            type: String,
            enum: ['company', 'declaration', 'statutory', 'shareholders', 'integrity', 'esg',
                'sla', 'hr', 'experience', 'reference', 'submission', 'financial', 'technical',
                'system', 'compliance', 'attachments', 'outcome', 'other'],
            required: true
        },
        subGroup: {
            type: String,
            enum: ['info', 'contact', 'bank', 'declaration', 'statutory', 'director1', 'director2', 'director3', 'shareholder1', 'shareeholder2', 'shareholder3', 'risk', 'litigation', 'env', 'social',
                'governance', 'sla', 'hr', 'experience', 'reference1', 'reference2', 'reference3', 'submission', 'financial', 'technical',
                'system', 'compliance', 'attachments', 'outcome', 'other'],
        },
        isParent: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
        },
        order: {
            type: Number,
            required: true
        },
        placeholder: {
            type: String,
            required: true
        },
        description: {
            type: String,
            required: true
        },
        helpText: {
            type: String,
            required: true
        },
        tooltip: {
            type: String,
            required: true
        },
        isVisible: {
            type: Boolean,
            default: true
        },
        isEditable: {
            type: Boolean,
            default: true
        },
        type: {
            type: String,
            enum: ['text', 'textarea', 'number', 'date', 'file', 'checkbox', 'radio', 'select', 'section'],
            required: true
        },
        label: {
            type: String,
            required: true
        },
        options: [{
            label: String,
            value: String
        }],
        optionScore: [{
            label: String,
            value: String,
            score: Number
        }],
        isRequired: {
            type: Boolean,
            default: false
        },
        isScoreable: {
            type: Boolean,
            default: false
        },
        maxScore: {
            type: Number,
            default: 0
        },
        maxComplianceScore: {
            type: Number,
            default: 0
        },
        requiresDd: {
            type: Boolean,
            default: false
        },
        validations: {
            min: Number,
            max: Number,
            pattern: String,
            fileTypes: [String],
            maxFileSize: Number
        }
    }],

    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
}, { timestamps: true });

// Custom validation to ensure unique 'name' in 'fields'
jobCategorySchema.path('fields').validate(function (fields) {
    const names = fields.map(field => field.name);
    return names.length === new Set(names).size; // Check for duplicates
}, 'Field names must be unique.');



const SpJobCategory = mongoose.models.SpJobCategory || mongoose.model('SpJobCategory', jobCategorySchema);
export default SpJobCategory;
