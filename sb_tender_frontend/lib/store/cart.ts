import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import axios from 'axios';
import toast from 'react-hot-toast';

export interface CartItem {
  id: string;
  type: 'supplier_registration' | 'supplier_prequalification';
  category: string;
  buyerCompany: string;
  supplierCompanyId: string;
  price: number;
}

interface CartStore {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  clearCart: () => void;
  checkout: (mobile: string, companyId: string) => Promise<void>;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      addItem: (item) => {
        const items = get().items;
        const exists = items.find((i) => i.id === item.id);
        
        if (exists) {
          toast.error('Item already in cart');
          return;
        }
        
        set({ items: [...items, item] });
        toast.success('Added to cart');
      },
      removeItem: (id) => {
        set({ items: get().items.filter((item) => item.id !== id) });
        toast.success('Removed from cart');
      },
      clearCart: () => {
        set({ items: [] });
      },
      checkout: async (mobile, companyId) => {
        try {
          const items = get().items;
          if (items.length === 0) {
            toast.error('Cart is empty');
            throw new Error('Cart is empty');
          }

          const total = items.reduce((sum, item) => sum + item.price, 0);
          
          // Get the auth token from cookies
          const token = document.cookie
            .split('; ')
            .find(row => row.startsWith('token='))
            ?.split('=')[1];

          if (!token) {
            toast.error('Authentication required');
            throw new Error('No authentication token found');
          }

          // Call checkout API
          const response = await axios.post('/api/mpesa/stkPush', {
            amount: total,
            mobile,
            items,
            companyId
          }, {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.data.success) {
            throw new Error(response.data.message || 'Payment failed');
          }
          // Clear cart only after successful payment initiation
          set({ items: [] });
          toast.success('Payment initiated. Please check your Mpesa phone to complete the payment.');


          const orderId = response.data.orderId;
         return orderId;
        
        } catch (error) {
          console.error('Checkout error:', error);
          
          let errorMessage = 'Payment failed. Please try again.';
          if (axios.isAxiosError(error)) {
            errorMessage = error.response?.data?.message || error.message;
          } else if (error instanceof Error) {
            errorMessage = error.message;
          }

          toast.error(errorMessage);
          throw error;
        }
      }
    }),
    {
      name: 'cart-storage',
    }
  )
);