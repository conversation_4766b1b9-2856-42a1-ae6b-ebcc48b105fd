import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'sonner';
// Import the useShallow middleware for better React 18 compatibility
import { useStore } from 'zustand';
import { createJSONStorage } from 'zustand/middleware';

export type RoleData = {
  roleId: string;
  roleName: string;
  roleContext: 'buyer' | 'supplier' | 'admin';
  roleType: string;
  permissions: ('r' | 'w' | 'd')[];
  description: string;
};

export type CompanyData = {
  companyId: string;
  companyName: string;
  companyType: 'buyer' | 'supplier';
  companyRegistrationNumber: string;
  roles: RoleData[];
};

export type UserData = {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  type: 'admin' | 'buyer' | 'supplier';
  companies: CompanyData[];
};

type AuthStore = {
  token: string | null;
  user: UserData | null;
  activeCompanyId: string | null;
  isLoading: boolean;

  // Main auth actions
  login: (token: string, userData: UserData) => void;
  logout: () => void;
  switchCompany: (companyId: string) => void;

  // Extra setters for flexible updates
  setToken: (token: string | null) => void;
  setUser: (user: UserData | null) => void;
  setCompanies: (companies: CompanyData[]) => void;
  setActiveCompanyId: (id: string | null) => void;

  can: (
    permission: 'r' | 'w' | 'd',
    type: string,
    context?: 'buyer' | 'supplier' | 'admin'
  ) => boolean;
};

// Create the store with improved Next.js compatibility
export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      token: null,
      user: null,
      activeCompanyId: null,
      isLoading: false,

      login: (newToken, userData) => {
        const firstCompanyId = userData.companies[0]?.companyId || null;
        set({
          token: newToken,
          user: userData,
          activeCompanyId: firstCompanyId,
        });
      },

      logout: () => {
        set({ token: null, user: null, activeCompanyId: null });
      },

      switchCompany: (companyId) => {
        const { user } = get();
        if (user?.companies.some(c => c.companyId === companyId)) {
          set({ activeCompanyId: companyId });
          try {
            localStorage.setItem('activeCompanyId', companyId);
          } catch (e) {
            console.warn('Could not save activeCompanyId to localStorage');
          }
        } else {
          toast.error("You do not have access to that company.");
        }
      },

      // Setters
      setToken: (token) => set({ token }),
      setUser: (user) => set({ user }),
      setCompanies: (companies) => {
        const currentUser = get().user;
        if (currentUser) {
          set({ user: { ...currentUser, companies } });
        }
      },
      setActiveCompanyId: (id) => set({ activeCompanyId: id }),

      can: (permission, type, context) => {
        const { user, activeCompanyId } = get();
        if (!user) return false;

        let currentContext = context;
        const activeCompany = user.companies.find(c => c.companyId === activeCompanyId);

        if (!currentContext && activeCompany) {
          currentContext = activeCompany.companyType;
        }

        if (!currentContext) {
          currentContext = user.type;
        }

        if (!currentContext || (!activeCompanyId && user.type !== 'admin' && currentContext !== 'admin')) {
          return false;
        }

        const companyData = activeCompanyId
          ? user.companies.find(c => c.companyId === activeCompanyId)
          : null;

        if ((currentContext === 'buyer' || currentContext === 'supplier') && !companyData) {
          return false;
        }

        let rolesToCheck: RoleData[] = [];
        if (activeCompanyId && companyData && (context === companyData.companyType || !context)) {
          rolesToCheck = companyData.roles.filter(r => r.roleContext === (context || companyData.companyType));
        } else if (context === 'admin' && user.type === 'admin') {
          rolesToCheck = user.companies.flatMap(c => c.roles).filter(r => r.roleContext === 'admin');
        } else if (user.type === 'admin' && !activeCompanyId && !context) {
          console.warn("Admin user checking permissions without context or active company.");
          return false;
        }

        return rolesToCheck.some(role =>
          role.roleType === type && role.permissions.includes(permission)
        );
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => {
        // Safe localStorage access for SSR environments
        if (typeof window !== 'undefined') {
          return localStorage;
        }
        return {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {}
        };
      }),
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        activeCompanyId: state.activeCompanyId,
      }),
    }
  )
);