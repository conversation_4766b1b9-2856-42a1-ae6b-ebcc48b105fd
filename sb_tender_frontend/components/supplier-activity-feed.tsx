import { useState, useEffect } from 'react';
const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

export default function ActivityFeed({ companyId }) {
  console.log('Company ID feed:', companyId);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    action: '',
    resource: ''
  });

  useEffect(() => {
    fetchActivities();
  }, [filters]);



  const fetchActivities = async () => {
    try {
      const token = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];
  
    if (!token) {
      throw new Error('Authentication token not found');
    }
          const queryParams = new URLSearchParams(filters).toString();
      
      const response = await fetch(
        `${BACKEND_API_URL}/api/supplier/${companyId}/activities?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const data = await response.json();
      setActivities(data.activities);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (action) => {
    const icons = {
      create: '➕',
      update: '✏️',
      delete: '🗑️',
      approve: '✅',
      reject: '❌',
      login: '🔐',
      export: '📥'
    };
    return icons[action] || 'ℹ️';
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const activityDate = new Date(date);
    const diffInHours = Math.floor((now - activityDate) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div className="bg-white">
      <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
      
      <div className="space-y-4">
      {activities && activities.slice(0, 6).map((activity) => (
          <div key={activity._id} className="flex items-start space-x-3 p-3 border-l-4 border-blue-200 bg-gray-50 rounded">
            {/*<span className="text-xl">{getActivityIcon(activity.action)}</span>*/}
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>

            <div className="flex-1">
              <p className="text-sm text-gray-900">
                <span className="font-medium">{activity.userId?.name}</span>
                {' '}
                <span className="text-gray-600">{activity.description}</span>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {formatTimeAgo(activity.createdAt)}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
