"use client";

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import toast from 'react-hot-toast';

interface BuyerRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  subject: string;
  message: string;
}

export default function BuyerRequestModal({ isOpen, onClose }: BuyerRequestModalProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('Name is required');
      return false;
    }
    if (!formData.email.trim()) {
      toast.error('Email is required');
      return false;
    }
    if (!formData.message.trim()) {
      toast.error('Message is required');
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${BACKEND_API_URL}/api/notifications/public`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'buyer_inquiry',
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          company: formData.company,
          subject: formData.subject,
          message: formData.message
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Your inquiry has been submitted successfully! We will contact you within 24 hours.');
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          subject: '',
          message: ''
        });
        
        onClose();
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to submit inquiry. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting buyer inquiry:', error);
      toast.error('Failed to submit inquiry. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto bg-gradient-to-br from-slate-50 via-emerald-50/30 to-teal-50/50 border-2 border-emerald-200/30 shadow-2xl shadow-emerald-500/10 backdrop-blur-sm">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-emerald-700 via-teal-600 to-green-600 bg-clip-text text-transparent">
                Join TenderAsili as a Buyer
              </DialogTitle>
              <DialogDescription className="mt-3 text-slate-600 leading-relaxed">
                Get access to qualified suppliers and streamline your procurement process.
                Fill out the form below and our team will contact you within 24 hours.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="name" className="text-emerald-800 font-semibold">Full Name *</Label>
              <Input
                id="name"
                type="text"
                placeholder="Enter your full name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={isSubmitting}
                required
                className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300"
              />
            </div>
            <div className="space-y-3">
              <Label htmlFor="email" className="text-emerald-800 font-semibold">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={isSubmitting}
                required
                className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="phone" className="text-emerald-800 font-semibold">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={isSubmitting}
                className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300"
              />
            </div>
            <div className="space-y-3">
              <Label htmlFor="company" className="text-emerald-800 font-semibold">Company/Organization</Label>
              <Input
                id="company"
                type="text"
                placeholder="Enter your company name"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                disabled={isSubmitting}
                className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300"
              />
            </div>
          </div>

          <div className="space-y-3">
            <Label htmlFor="subject" className="text-emerald-800 font-semibold">Subject</Label>
            <Input
              id="subject"
              type="text"
              placeholder="Brief subject of your inquiry"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              disabled={isSubmitting}
              className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="message" className="text-emerald-800 font-semibold">Message *</Label>
            <Textarea
              id="message"
              placeholder="Tell us about your procurement needs, industry, or any specific requirements..."
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              disabled={isSubmitting}
              rows={4}
              required
              className="border-2 border-emerald-200/50 focus:border-emerald-400 focus:ring-emerald-400/20 focus:ring-4 bg-white/70 backdrop-blur-sm transition-all duration-200 rounded-xl shadow-sm hover:shadow-md hover:border-emerald-300 resize-none"
            />
          </div>

          <DialogFooter className="flex gap-4 pt-8 border-t border-emerald-100/50 mt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 border-2 border-emerald-200/60 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-300 hover:text-emerald-800 transition-all duration-200 rounded-xl py-3 font-semibold shadow-sm hover:shadow-md backdrop-blur-sm"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-bold py-3 rounded-xl shadow-lg hover:shadow-xl hover:shadow-emerald-500/25 transition-all duration-300 border-0 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Submitting...
                </>
              ) : (
                'Submit Inquiry'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
