"use client"

import * as React from "react"
import { Suspense } from "react"
import {
  LayoutDashboard,
  Building2,
  Shield,
  CreditCard,
  Settings,
  Bell,
  FileText,
  List,
  Key,
  Network,
  ClipboardList,
  FileSearch
} from "lucide-react"

import { NavNormal } from "@/components/nav-normal"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

import { useAuthStore } from "@/lib/store/useAuthStore"
import { usePathname, useSearchParams } from "next/navigation"

const getUser = (state: any) => state.user;

// Internal sidebar component that uses useSearchParams
function AppSidebarInternal({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const user = useAuthStore(getUser);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get the current type from query parameters
  const currentType = searchParams.get('type');

  const data = {
    dashboard: [
      {
        title: "Dashboard",
        url: "/admin/dashboard",
        icon: LayoutDashboard,
        isActive: pathname === "/admin/dashboard",
      }
    ],
    management: [
      {
        title: "All Jobs",
        url: "/admin/jobs",
        icon: Building2,
        isActive: pathname === "/admin/jobs",
      },
      {
        title: "Buyers",
        url: "/admin/companies?type=buyer",
        icon: ClipboardList,
        isActive: pathname === "/admin/companies" && currentType === "buyer",
      },
      {
        title: "Suppliers",
        url: "/admin/companies?type=supplier",
        icon: Shield,
        isActive: pathname === "/admin/companies" && currentType === "supplier",
      },
    ],
    financials: [
      {
        title: "Transactions",
        url: "/admin/transactions",
        icon: CreditCard,
        isActive: pathname === "/admin/transactions",
      },
      {
        title: "Orders (Invoices)",
        url: "/admin/orders",
        icon: FileText,
        isActive: pathname === "/admin/orders",
      },
    ],

  notifications: [
    {
      title: "Notifications",
      url: "/admin/notifications",
      icon: Bell,
    }
  ],
  navAdmin: [
    {
      title: "Activity Logs",
      url: "/admin/activity",
      icon: FileSearch,
    }
  ],
  administration: [

    {
      title: "Notifications",
      url: "/admin/notifications",
      icon: Bell,
      isActive: pathname === "/admin/notifications",
    },{
      title: "Activity Logs",
      url: "/admin/activity",
      icon: FileSearch,
      isActive: pathname === "/admin/activity",
    }
  ],

  system: [
    {
      title: "System Settings",
      url: "/admin/settings",
      icon: Settings,
      items: [
        {
          title: "General",
          url: "/admin/settings/general",
          icon: Settings,
        },
        {
          title: "Integrations",
          url: "/admin/settings/integrations",
          icon: Network,
        },
        {
          title: "Categories",
          url: "/admin/categories",
          icon: List,
        },
        {
          title: "Roles & Permissions",
          url: "/admin/roles",
          icon: Key,
        },
      ],
    }
  ],
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        {/* Admin-specific header content can go here */}
      </SidebarHeader>
      <SidebarContent>
        <NavNormal items={data.dashboard} />
        <NavNormal
          items={data.management}
          title="Management"
          className="mt-4"
        />
        <NavNormal
          items={data.financials}
          title="Financials"
          className="mt-4"
        />
        <NavNormal
          items={data.administration}
          title="Administration"
          className="mt-4"
        />
        <NavNormal
          items={data.system}
          title="System"
          className="mt-4"
        />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Fallback component for Suspense
function SidebarFallback(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <div className="size-4 animate-pulse bg-current rounded" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <div className="h-4 bg-sidebar-accent rounded animate-pulse" />
            <div className="h-3 bg-sidebar-accent/50 rounded animate-pulse mt-1" />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <div className="space-y-2 p-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="h-8 bg-sidebar-accent rounded animate-pulse" />
          ))}
        </div>
      </SidebarContent>
      <SidebarFooter>
        <div className="h-12 bg-sidebar-accent rounded animate-pulse m-2" />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Main export with Suspense boundary
export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Suspense fallback={<SidebarFallback {...props} />}>
      <AppSidebarInternal {...props} />
    </Suspense>
  )
}