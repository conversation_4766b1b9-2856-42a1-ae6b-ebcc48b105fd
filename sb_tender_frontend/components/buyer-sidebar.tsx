"use client"

import * as React from "react"
import { Suspense } from "react"
import {
  LayoutDashboard,
  ClipboardList,
  FileCheck,
  Gavel,
  ShoppingCart,
  Users,
  Building2,
  FileText,
  Settings,
  CircleDollarSign,
  ListChecks,
  ShieldCheck,
  Warehouse,
  FileSearch,
  Library
} from "lucide-react"

import { NavNormal } from "@/components/nav-normal"
import { NavUser } from "@/components/nav-user"
import { CompanySwitcher } from "@/components/company-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useParams, usePathname, useSearchParams } from "next/navigation"
import { useAuthStore } from "@/lib/store/useAuthStore"

const getUser = (state) => state.user;

// Internal sidebar component that uses useSearchParams
function BuyerSidebarInternal({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const params = useParams()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const buyerId = params.companyId
  const user = useAuthStore(getUser);

  // Get the current type from query parameters
  const currentType = searchParams.get('type');

  const data = {
    dashboard: [
      {
        title: "Dashboard",
        url: `/buyer/${buyerId}/dashboard`,
        icon: LayoutDashboard,
        isActive: pathname === `/buyer/${buyerId}/dashboard`,
      }
    ],
    buyerManagement: [
      {
        title: "Supplier Registration",
        url: `/buyer/${buyerId}/jobs?type=supplier_registration`,
        icon: ClipboardList,
        isActive: pathname === `/buyer/${buyerId}/jobs` && currentType === 'supplier_registration',
      },
      {
        title: "Supplier Prequalification",
        url: `/buyer/${buyerId}/jobs?type=supplier_prequalification`,
        icon: ShieldCheck,
        isActive: pathname === `/buyer/${buyerId}/jobs` && currentType === 'supplier_prequalification',
      },

    ],
    procurement: [
      {
        title: "RFQs [soon]",
        url: `/buyer/${buyerId}/rfqs`,
        icon: ShoppingCart,
        isActive: pathname === `/buyer/${buyerId}/rfqs`,
      },
      {
        title: "Tenders [soon]",
        url: `/buyer/${buyerId}/tenders`,
        icon: Gavel,
        isActive: pathname === `/buyer/${buyerId}/tenders`,
      },
      {
        title: "Contracts [soon]",
        url: `/buyer/${buyerId}/contracts`,
        icon: FileText,
        isActive: pathname === `/buyer/${buyerId}/contracts`,
      }
    ],
    administration: [

      {
        title: "Document Management",
        url: `#`,
        icon: Library,
        isActive: pathname === `/buyer/${buyerId}/documents`,
      },
      {
        title: "Company Profile",
        url: `/buyer/${buyerId}/profile`,
        icon: Building2,
        isActive: pathname === `/buyer/${buyerId}/profile`,
      },{
        title: "Users",
        url: `/buyer/${buyerId}/users`,
        icon: Users,
        isActive: pathname === `/buyer/${buyerId}/users`,
      },
      {
        title: "Audit Logs",
        url: `/buyer/${buyerId}/activity`,
        icon: FileSearch,
        isActive: pathname === `/buyer/${buyerId}/activity`,
      }
    ]
  }
    
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <CompanySwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavNormal items={data.dashboard} />
        <NavNormal 
          items={data.buyerManagement} 
          title="Job Management"
          className="mt-4"
        />
        <NavNormal 
          items={data.administration} 
          title="Administration"
          className="mt-4"
        />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} /> 
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Fallback component for Suspense
function BuyerSidebarFallback(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <div className="size-4 animate-pulse bg-current rounded" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <div className="h-4 bg-sidebar-accent rounded animate-pulse" />
            <div className="h-3 bg-sidebar-accent/50 rounded animate-pulse mt-1" />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <div className="space-y-2 p-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="h-8 bg-sidebar-accent rounded animate-pulse" />
          ))}
        </div>
      </SidebarContent>
      <SidebarFooter>
        <div className="h-12 bg-sidebar-accent rounded animate-pulse m-2" />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Main export with Suspense boundary
export function BuyerSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Suspense fallback={<BuyerSidebarFallback {...props} />}>
      <BuyerSidebarInternal {...props} />
    </Suspense>
  )
}