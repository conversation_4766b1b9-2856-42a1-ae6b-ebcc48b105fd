import { useState, useEffect } from 'react';
import { FileText, Clock, CheckCircle, TrendingUp, Loader2, Refresh<PERSON>w } from 'lucide-react';
const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

// Simple Card component
const Card = ({ children, className = "" }) => (
  <div className={`bg-white rounded-lg shadow border ${className}`}>
    {children}
  </div>
);

export default function DashboardMetrics({ companyId }) {
  console.log('Company ID metrics:', companyId);
  
  const [metrics, setMetrics] = useState({
    totalUsers: 0,
    totalCategories: 0,
    openCategories: 0,
    closedCategories: 0,
    savings: 0,
  
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (companyId) {
      fetchDashboardMetrics();
    }
  }, [companyId]);

  const fetchDashboardMetrics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = document.cookie
        .split('; ')
        .find((row) => row.startsWith('token='))
        ?.split('=')[1];

      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(
        `${BACKEND_API_URL}/api/buyer/${companyId}/metrics/basic`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch buyer dashboard metrics');
      }

      const data = await response.json();
      console.log('Dashboard data:', data);
      
      setMetrics(data.metrics);
    } catch (error) {
      console.error('Error fetching buyer dashboard metrics:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4 animate-pulse">
              <div className="flex items-center space-x-4">
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
                <div>
                  <div className="h-4 w-16 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 w-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error loading metrics: {error}</p>
          <button 
            onClick={fetchDashboardMetrics}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="">      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card className="p-4">
          <div className="flex items-center space-x-4">
            <FileText className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-gray-500">Users</p>
              <p className="text-2xl font-bold">{metrics.totalUsers}</p>
              <p className="text-xs text-green-500">+12% this month</p>

            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <Clock className="h-8 w-8 text-yellow-500" />
            <div>
              <p className="text-sm text-gray-500">Open Categories</p>
              <p className="text-2xl font-bold">{metrics.openCategories}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-gray-500">Closed categories</p>
              <p className="text-2xl font-bold">{metrics.closedCategories}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <TrendingUp className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-gray-500">Savings</p>
              <p className="text-2xl font-bold">
                {metrics.savings || 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Refresh Button */}
      <div className="mt-6">
        {loading ? (
        <>
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
            Refreshing...
        </>
        ) : (
        <>
            <RefreshCw className="w-4 h-4 mr-2"
              onClick={fetchDashboardMetrics} />
        </>
        )}
        
      </div>
    </div>
  );
}