"use client"

import * as React from "react"
import { Suspense } from "react"
import {
  LayoutDashboard,
  ClipboardList,
  FileCheck,
  Gavel,
  ShoppingCart,
  Users,
  Building2,
  FileText,
  Settings,
  CircleDollarSign,
  Library,
  FileSearch,
  LucideShoppingCart
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavNormal } from "@/components/nav-normal"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { CompanySwitcher } from "@/components/company-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useParams, usePathname, useSearchParams } from "next/navigation"
import { useAuthStore } from "@/lib/store/useAuthStore"

const getUser = (state) => state.user;

// Internal sidebar component that uses useSearchParams
function SupplierSidebarInternal({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const params = useParams()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const supplierId = params.companyId
  const user = useAuthStore(getUser);

  // Get the current type from query parameters
  const currentType = searchParams.get('type');
  
  
  const data = {
    dashboard: [
      {
        title: "Dashboard",
        url: `/supplier/${supplierId}/dashboard`,
        icon: LayoutDashboard,
        isActive: pathname === `/supplier/${supplierId}/dashboard`,
      },
      {
        title: "Cart",
        url: `/supplier/${supplierId}/cart`,
        icon: LucideShoppingCart,
        isActive: pathname === `/supplier/${supplierId}/cart`,
      },

      {
        title: "Orders",
        url: `/supplier/${supplierId}/orders`,
        icon: CircleDollarSign,
        isActive: pathname === `/supplier/${supplierId}/orders`,
      },
    ],
    navJobs: [
      {
        title: "Supplier Registration",
        url: `/supplier/${supplierId}/applications?type=supplier_registration`,
        icon: FileText,
        isActive: pathname === `/supplier/${supplierId}/applications` && currentType === 'supplier_registration',
      },
      {
        title: "Supplier Prequalification",
        url: `/supplier/${supplierId}/applications?type=supplier_prequalification`,
        icon: FileCheck,
        isActive: pathname === `/supplier/${supplierId}/applications` && currentType === 'supplier_prequalification',
      }, ],

    navTenders: [
      {
        title: "RFQs [soon]",
        url: `#`,
        icon: ShoppingCart,
        badge: "Soon",
        isActive: false,
      },
      {
        title: "Tenders [soon]",
        url: `#`,
        icon: Gavel,
        badge: "Soon",
        isActive: false,
      },
    ],
      navAdmin: [
      {
        title: "Document Management",
        url: `#`,
        icon: Library,
        isActive: pathname === `/supplier/${supplierId}/documents`,
      },
      {
        title: "Company Profile",
        url: `/supplier/${supplierId}/profile`,
        icon: Building2,
        isActive: pathname === `/supplier/${supplierId}/profile`,
      },
      {
        title: "Users",
        url: `/supplier/${supplierId}/users`,
        icon: Users,
        isActive: pathname === `/supplier/${supplierId}/users`,
      },
      {
        title: "Audit Logs",
        url: `/supplier/${supplierId}/activity`,
        icon: FileSearch,
        isActive: pathname === `/supplier/${supplierId}/activity`,
      }
    ],
  }
  
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <CompanySwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavNormal items={data.dashboard} />
        <NavNormal items={data.navJobs} />
        <NavNormal items={data.navAdmin} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Fallback component for Suspense
function SupplierSidebarFallback(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <div className="size-4 animate-pulse bg-current rounded" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <div className="h-4 bg-sidebar-accent rounded animate-pulse" />
            <div className="h-3 bg-sidebar-accent/50 rounded animate-pulse mt-1" />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <div className="space-y-2 p-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="h-8 bg-sidebar-accent rounded animate-pulse" />
          ))}
        </div>
      </SidebarContent>
      <SidebarFooter>
        <div className="h-12 bg-sidebar-accent rounded animate-pulse m-2" />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

// Main export with Suspense boundary
export function SupplierSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Suspense fallback={<SupplierSidebarFallback {...props} />}>
      <SupplierSidebarInternal {...props} />
    </Suspense>
  )
}