'use client';
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Co<PERSON><PERSON>, Pen, Trash2, } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button"
import { useParams } from 'next/navigation';
import Link from 'next/link';

const SupplierCategories = () => {
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [token, setToken] = useState<string | null>(null);
//ttemplate


const params = useParams();
const [jobId, setJobId] = useState<string | null>(null);

useEffect(() => {
  if (params?.jobId && typeof params.jobId === 'string') {
    console.log(jobId);
    setJobId(params.jobId);
  }
}, [params]);

  const form = useForm({
    defaultValues: {
      name: '',
      description: '',
      type: '',
      status: 'active'
    }
  });

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchCategories();
    }
  }, [token]);


  const fetchCategories = async () => {
    try {
      const { data } = await axios.get(`/api/jobs/categories?jobId=${jobId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log(data);
      setCategories(data);
    } catch (error) {
      toast.error('Failed to fetch categories');
    }
  };
  
  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const filteredCategories = categories.filter((category) =>
    category.title.toLowerCase().includes(search.toLowerCase())
  );


  return (
    <div className="p-4">
            <Toaster />
      <div className="flex items-center justify-between mb-6">
        <div className="relative w-64">
          <input
            type="text"
            placeholder="Search categories"
            value={search}
            onChange={handleSearch}
            className="w-full p-2 border rounded"
          />
        </div>
        <Link
          href="/admin/dashboard"
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Back home
        </Link>

      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border">
          <thead>
            <tr>
              <th className="px-4 py-2 border">Title</th>
              <th className="px-4 py-2 border">Description</th>
              <th className="px-4 py-2 border">Opens</th>
              <th className="px-4 py-2 border">Closes</th>
              <th className="px-4 py-2 border">Type</th>
              <th className="px-4 py-2 border">Price</th>
              <th className="px-4 py-2 border">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredCategories.map((category) => (
              <tr key={category._id}>
                <td className="px-4 py-2 border">{category.title}</td>
                <td className="px-4 py-2 border">{category.description}</td>
                <td className="px-4 py-2 border">{category.starts}</td>
                <td className="px-4 py-2 border">{category.closes}</td>
                <td className="px-4 py-2 border">{category.type}</td>
                <td className="px-4 py-2 border">{category.price}</td>
                <td className="px-4 py-2 border">
                  <div className="flex space-x-2">
                  
                  <Button variant="outline" size="icon" onClick={() => {console.log('clicked'); handleEditCategory(category) ;}} >
                    <Pen />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() =>  openTemplateFieldsModal(category) } >
                    <Combine />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() => {console.log('clicked'); handleDeleteCategory(category._id) ;}} >
                    <Trash2 />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

     

    </div>
  );
};

export default SupplierCategories;
