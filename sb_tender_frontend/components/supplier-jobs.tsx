"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { Combine, Pen, Trash2, } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"

export type Job = {
    _id: string
    title: string
    description: string
    status: "draft" | "published" | "completed" | "cancelled"
    categoryPrice: number
    contract: string
    location: string
    starts: string
    ends: string
    createdBy: string
    spCompanyId: string
    createdAt: string
    updatedAt: string
    companyName: string
  }

// Skeleton Loader Component
const JobsTableSkeleton = () => (
  <div className="w-full">
    <div className="flex">
      <div className="h-6 w-32 bg-muted rounded animate-pulse mb-4"></div>
    </div>
    <div className="flex items-center py-4">
      <div className="h-10 w-64 bg-muted rounded animate-pulse"></div>
      <div className="h-10 w-24 bg-muted rounded animate-pulse ml-auto"></div>
    </div>
    <div className="rounded-md border">
      <div className="p-4">
        {/* Header skeleton */}
        <div className="flex space-x-4 mb-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-4 w-20 bg-muted rounded animate-pulse"></div>
          ))}
        </div>
        {/* Rows skeleton */}
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex space-x-4 mb-3 py-2">
            {Array.from({ length: 6 }).map((_, j) => (
              <div key={j} className="h-4 w-20 bg-muted rounded animate-pulse"></div>
            ))}
          </div>
        ))}
      </div>
    </div>
    <div className="flex items-center justify-end space-x-2 py-4">
      <div className="h-8 w-20 bg-muted rounded animate-pulse"></div>
      <div className="h-8 w-16 bg-muted rounded animate-pulse"></div>
    </div>
  </div>
);

export function SupplierJobs() {
const [companyId, setCompanyId] = useState<string>('');

useEffect(() => {
  if (typeof window !== 'undefined') {
    const storedCompany = localStorage.getItem('activeCompanyId');
    if (storedCompany) {
      setCompanyId( storedCompany);
      console.log('Stored active company', storedCompany);
    } else {
      console.error("activeCompanyId not found in localStorage");
      const pathParts = window.location.pathname.split('/');
      const companyId = pathParts[2];
      setCompanyId( companyId);
      console.log('Company ID:', companyId);
    
    }
  }
}, []);


  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);

  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchJobs();
    }
  }, [token]);


  const fetchJobs = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get('/api/supplier/jobs', {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log('Jobs', data);
      setJobs(data);

    } catch (error) {
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };


  const [editingJob, setEditingJob] = useState<Job | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);


const columns: ColumnDef<Job>[] = [
   
    {
      accessorKey: "companyName",
      header: () => <div className="font-bold">Company</div>,
      cell: ({ row }) => <div>{row.getValue("companyName")}</div>,
    },
    {
      accessorKey: "title",
      header: () => <div className="font-bold">Title</div>,
      cell: ({ row }) => <div>{row.getValue("title")}</div>,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-bold">Satus</div>,
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue("status")}</div>
      ),
    },
    {
      accessorKey: "location",
      header: () => <div className="font-bold">Location</div>,
      cell: ({ row }) => <div>{row.getValue("location")}</div>,
    },
    {
      accessorKey: "categoryPrice",
      header: () => <div className="font-bold">Price</div>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("categoryPrice"))

        // Format the amount as a kes amount
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "KES",
        }).format(amount)

        return <div className="font-medium">{formatted}</div>
      },
    },
    {
      accessorKey: "ends",
      header: () => <div className="font-bold">Closes</div>,
      cell: ({ row }) => {
        const date = new Date(row.getValue("ends"))
        const formatted = date.toLocaleDateString()
        return <div>{formatted}</div>
      },
    },
    {
        id: "actions",
        header: () => <div className="font-bold">Categories</div>,
        enableHiding: false,
        cell: ({ row }) => {
          const job = row.original;

          return (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                window.location.href = `/supplier/${companyId}/jobs/${job._id}/categories`;
              }}
              className="w-full sm:w-auto"
            >
              <Combine className="h-4 w-4 mr-2" />
              View Categories
            </Button>
          );
        },
      }
  ]


    const table = useReactTable({
        data: jobs,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
          sorting,
          columnFilters,
          columnVisibility,
          rowSelection,
        },
      })

  

  // Show skeleton loader while loading
  if (loading) {
    return <JobsTableSkeleton />;
  }

  return (
    <div className="w-full">
      <Toaster />
        <div className="flex">
            <h5 className="text-xl font-semibold mb-4">Open Jobs</h5>
        </div>
      <div className="flex flex-col gap-4 py-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Input
            placeholder="Filter titles..."
            value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("title")?.setFilterValue(event.target.value)
            }
            className="w-full sm:w-80"
          />
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-[140px]">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={`font-medium whitespace-normal overflow-hidden text-ellipsis ${
                        cell.column.id === 'actions'
                          ? 'min-w-[160px]'
                          : 'max-w-[200px] md:max-w-[200px] lg:max-w-[200px]'
                      }`}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
     
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>

     

    </div>
  )
}

export default SupplierJobs;
