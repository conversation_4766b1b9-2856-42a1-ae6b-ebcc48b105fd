'use client';

import * as React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ChevronsUpDown, Plus } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';

import { useAuthStore } from '@/lib/store/useAuthStore';

// Create a stable selector function outside the component
// This prevents the selector from being recreated on each render
const getUser = (state) => state.user;
const getSwitchCompany = (state) => state.switchCompany;
const getIsLoading = (state) => state.isLoading;

export function CompanySwitcher() {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();

  // Use individual selectors with stable references
  const user = useAuthStore(getUser);
  const switchCompanyFn = useAuthStore(getSwitchCompany);
  const isLoading = useAuthStore(getIsLoading);

  // Derive companies from user safely
  const companies = user?.companies || [];

  // Extract companyId from pathname: /supplier/xyz/dashboard → xyz
  const pathParts = pathname.split('/');
  const currentCompanyId = pathParts[2] || null;

  // Find active company
  const activeCompany = React.useMemo(() => 
    companies.find((c) => c.companyId === currentCompanyId) || companies[0],
    [companies, currentCompanyId]
  );

  // Effect to switch company when needed
  React.useEffect(() => {
    if (activeCompany && activeCompany.companyId !== currentCompanyId) {
      switchCompanyFn(activeCompany.companyId);
    }
  }, [activeCompany, currentCompanyId, switchCompanyFn]);

  if (!activeCompany) return null;

  const handleCompanySwitch = (companyId: string) => {
    if (companyId === currentCompanyId) return;

    // Replace the current companyId in the URL
    const newPath = pathname.replace(
      `/${currentCompanyId}/`,
      `/${companyId}/`
    );

    switchCompanyFn(companyId);
    router.push(newPath);
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                {activeCompany.companyName.charAt(0).toUpperCase()}
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{activeCompany.companyName}</span>
                <span className="truncate text-xs">{activeCompany.companyType}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Companies
            </DropdownMenuLabel>
            {companies.map((company, index) => (
              <DropdownMenuItem
                key={company.companyId}
                onClick={() => handleCompanySwitch(company.companyId)}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  {company.companyName.charAt(0).toUpperCase()}
                </div>
                {company.companyName} <small>{company.companyType}</small>
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">Add company <sub>coming soon</sub></div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}