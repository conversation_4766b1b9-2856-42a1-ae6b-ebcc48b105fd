"use client"

import * as React from "react"
import {
  LayoutDashboard,
  ClipboardList,
  FileCheck,
  Gavel,
  ShoppingCart,
  Users,
  Building2,
  FileText,
  Settings,
  CircleDollarSign,
  Library,
  FileSearch,
  LucideShoppingCart
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavNormal } from "@/components/nav-normal"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { CompanySwitcher } from "@/components/company-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { useParams } from "next/navigation"
import { useAuthStore } from "@/lib/store/useAuthStore"

const getUser = (state) => state.user;

export function PlainSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const params = useParams()
  const supplierId = params.companyId
  const user = useAuthStore(getUser);
  
  
  
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <CompanySwitcher />
      </SidebarHeader>
      <SidebarContent>
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}