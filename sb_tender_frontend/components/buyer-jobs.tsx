"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useState, useEffect } from 'react';
import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';

export type Job = {
    _id: string
    title: string
    description: string
    status: "draft" | "published" | "completed" | "cancelled"
    categoryPrice: number
    contract: string
    location: string
    starts: string
    ends: string
    createdBy: string
    spCompanyId: string
    createdAt: string
    updatedAt: string
    companyName: string
  }

export function BuyerJobs() {
const [companyId, setCompanyId] = useState<string>('');

useEffect(() => {
  if (typeof window !== 'undefined') {
    const storedCompany = localStorage.getItem('activeCompanyId');
    if (storedCompany) {
      setCompanyId( storedCompany);
      console.log('Stored active company', storedCompany);
    } else {
      console.error("activeCompanyId not found in localStorage");
      const pathParts = window.location.pathname.split('/');
      const companyId = pathParts[2];
      setCompanyId( companyId);
      console.log('Company ID:', companyId);
    
    }
  }
}, []);


  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [jobs, setJobs] = useState([]);

  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const cookieToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('token='))
      ?.split('=')[1];

    if (cookieToken) {
      setToken(cookieToken);
    } else {
      console.error("Token not found in cookies");
    }
  }, []);


  const BACKEND_API_URL = process.env.NEXT_PUBLIC_NODE_API_URL || 'http://localhost:5001';

  const fetchJobs = async () => {
    try {
      if(companyId) {
        console.log('Requesting CompanyId', companyId);
      const { data } = await axios.get(`${BACKEND_API_URL}/api/buyer/${companyId}/jobs`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      console.log('Jobs', data);
      setJobs(data);
    }
    
    } catch (error) {
      toast.error('Failed to fetch jobs');
    }
  };


  useEffect(() => {
    if (token) {
      fetchJobs();
    }
  }, [token, companyId]);


const columns: ColumnDef<Job>[] = [
   
    {
      accessorKey: "companyName",
      header: () => <div className="font-bold">Company</div>,
      cell: ({ row }) => <div>{row.getValue("companyName")}</div>,
    },
    {
      accessorKey: "title",
      header: () => <div className="font-bold">Title</div>,
      cell: ({ row }) => <div>{row.getValue("title")}</div>,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-bold">Satus</div>,
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue("status")}</div>
      ),
    },
    {
      accessorKey: "type",
      header: () => <div className="font-bold">Type</div>,
      cell: ({ row }) => <div>{row.getValue("type")}</div>,
    },
    {
      accessorKey: "location",
      header: () => <div className="font-bold">Location</div>,
      cell: ({ row }) => <div>{row.getValue("location")}</div>,
    },
    {
      accessorKey: "categoryPrice",
      header: () => <div className="font-bold">Price</div>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("categoryPrice"))

        // Format the amount as a kes amount
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "KES",
        }).format(amount)

        return <div className="font-medium">{formatted}</div>
      },
    },
    {
      accessorKey: "ends",
      header: () => <div className="font-bold">Closes</div>,
      cell: ({ row }) => {
        const date = new Date(row.getValue("ends"))
        const formatted = date.toLocaleDateString()
        return <div>{formatted}</div>
      },
    },
    {
        id: "actions",
        header: () => <div className="font-bold">Categories</div>,
        enableHiding: false,
        cell: ({ row }) => {
          const job = row.original;

          return (
                <span className="text-green-700 underline cursor-pointer" onClick={() => {
                      window.location.href = `/buyer/${companyId}/jobs/${job._id}/categories`;
                    }}>View Categories</span>
             
          );
        },
      }
  ]


    const table = useReactTable({
        data: jobs,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
          sorting,
          columnFilters,
          columnVisibility,
          rowSelection,
        },
      })

  

  return (
    <div className="bg-white w-full">
      <Toaster />
        <div className="flex">
            <h5  className="text-xl font-semibold mb-4">Open Jobs</h5>
        </div>
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter titles..."
          value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="font-medium  whitespace-normal overflow-hidden text-ellipsis max-w-[200px] md:max-w-[200px] lg:max-w-[200px]">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
     
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>

     

    </div>
  )
}

export default BuyerJobs;
