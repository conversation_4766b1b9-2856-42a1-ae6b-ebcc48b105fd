{"name": "my-app2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.21.3", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "express-validator": "^7.2.1", "framer-motion": "^12.11.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "mongoose": "^8.16.2", "multer": "^2.0.1", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/express-validator": "^2.20.33", "@types/jsonwebtoken": "^9.0.10", "@types/jszip": "^3.4.0", "@types/lodash": "^4.17.20", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}